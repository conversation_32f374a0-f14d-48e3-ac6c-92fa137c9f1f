# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

This is a multi-module Java project for Volvo's vehicle connectivity platform. It contains four main components that handle message routing, transaction protocols, and MT (Mobile Terminated) services for vehicle communication.

## Build System

The project uses Maven with a multi-module structure. Each main component has its own parent POM with submodules.

### Common Build Commands

```bash
# Build all modules
mvn clean install

# Build with tests
mvn clean install -U

# Build deployable assembly
mvn clean install -Pdeployable-assembly

# Run tests only
mvn test

# Run integration tests
mvn verify -Pcomponent-tests

# Format check (for vwtp-lib)
mvn clean install -Pformat-check

# Skip tests during build
mvn clean install -DskipTests
```

### Running Integration Tests

Integration tests use Docker Compose for test infrastructure:
- MongoDB (replica set)
- InfluxDB
- Zookeeper
- WireMock
- Apache Artemis

Start test dependencies: `docker-compose up -d` from the respective test resources directory.

## Architecture

### Main Components

1. **mt-router-server**: Message routing service for MT messages
   - Uses Spring WebFlux (reactive)
   - MongoDB for persistence
   - Service discovery via Zookeeper
   - Apache Artemis for JMS messaging

2. **tgw-mt-server**: Telematics Gateway MT server
   - Java 21 based
   - PostgreSQL database with Flyway migrations
   - Protocol Buffer support
   - ShedLock for distributed locking

3. **vwtp-initiator-server**: VWTP (Volvo Wireless Transaction Protocol) initiator
   - Handles UDP and Scooter protocol messages
   - Integration with subscription repository
   - Metrics via InfluxDB

4. **vwtp-lib**: Core VWTP library with protocol implementation
   - ASN.1 codec for message encoding/decoding
   - Transaction state management
   - Performance benchmarking tools

### Key Technologies

- **Spring Boot**: Main application framework
- **Project Reactor**: Reactive programming (WebFlux)
- **MongoDB**: Primary database for mt-router-server
- **PostgreSQL**: Database for tgw-mt-server
- **Apache Artemis**: JMS messaging
- **Protocol Buffers**: Message serialization
- **Docker**: Integration testing environment
- **Maven**: Build system and dependency management

## Module Structure

Each server component follows this pattern:
- `*-app`: Main application module with Spring Boot configuration
- `*-impl`: Implementation module containing business logic
- `*-database`: Database schema and migrations (tgw-mt-server only)
- `*-integration-tests`: Integration test suite
- `deployable-assembly`: Deployment packaging

## Configuration

### Environment Profiles
- `de_component-test_eu-west-1`: Component testing
- Development/QA/Production environments with region-specific configs

### Key Configuration Files
- `application.yaml`: Spring Boot configuration
- `docker-compose.yaml`: Test infrastructure
- Environment-specific keystore files for security

## Testing

### Test Categories
- Unit tests: Standard JUnit tests in `src/test`
- Integration tests: Full stack tests with Docker dependencies
- Component tests: Cross-component integration testing

### Test Infrastructure
Integration tests require Docker and use TestContainers pattern with:
- MongoDB replica sets
- PostgreSQL with embedded test instances
- WireMock for external service mocking

## Database Migrations

The tgw-mt-server uses Flyway for database schema management:
- Migration files in `src/main/resources/db/migration/`
- Versioned SQL scripts following `V{version}__{description}.sql` pattern

## Key Dependencies

- **TISP Framework**: Volvo's internal platform libraries
- **Connectivity APIs**: Vehicle connectivity abstractions  
- **Service Discovery**: Zookeeper-based service registration
- **Monitoring**: InfluxDB metrics and observability
- **Security**: PKI-based message encryption and transport security

## Development Notes

- Uses Lombok for code generation - ensure IDE plugin is installed
- Reactive programming patterns with Reactor
- Protobuf compilation may require Protocol Buffers compiler
- Some modules require specific JDK versions (Java 17/21)
- Integration tests need Docker daemon running