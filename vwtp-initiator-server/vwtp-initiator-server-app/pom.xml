<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information                 -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.connectivity</groupId>
    <artifactId>vwtp-initiator-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>vwtp-initiator-server-app</artifactId>
  <name>VWTP Initiator :: Server :: Application</name>

  <dependencies>
    <dependency>
      <groupId>${project.groupId}</groupId>
      <artifactId>vwtp-initiator-server-impl</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>

  <!-- +=============================================== -->
  <!-- | Section 3: Build plug-ins                      -->
  <!-- +=============================================== -->
  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <mainClass>com.volvo.connectivity.vwtpinit.conf.AppConfig</mainClass>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Class-Path>..</Class-Path>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
