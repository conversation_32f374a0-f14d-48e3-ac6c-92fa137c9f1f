#!/bin/bash -e
echo "Executing: ${BASH_SOURCE[0]} $@"
echo "Working directory is: $(pwd)"
RUNTIME_DIR="$(cd $(dirname ${BASH_SOURCE[0]})/../.. && pwd)"
echo "Runtime directory is: ${RUNTIME_DIR}"
RUNTIME_JAR_DIR="${RUNTIME_DIR}/jar"
mkdir -p "${RUNTIME_JAR_DIR}"
CONREPO2_SUBNETS_ENDPOINT="http://conrepo2:33581/api/v1/subnets"
SUBNETS_FILE="${RUNTIME_JAR_DIR}/subnets.txt"
TEMP_FILE="subnet_response.txt"

echo "Fetching subnets from $CONREPO2_SUBNETS_ENDPOINT"
http_status=$(curl -s -o "$TEMP_FILE" -w "%{http_code}" "$CONREPO2_SUBNETS_ENDPOINT")
if [ "$http_status" -eq 200 ]; then
  mv "$TEMP_FILE" "$SUBNETS_FILE"
  echo "Subnets successfully written to $SUBNETS_FILE"
else
  rm -f "$TEMP_FILE"
  echo "Failed to fetch subnets from $CONREPO2_SUBNETS_ENDPOINT. HTTP status code: $http_status"
fi
