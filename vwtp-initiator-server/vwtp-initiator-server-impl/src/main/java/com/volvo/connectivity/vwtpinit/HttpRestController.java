package com.volvo.connectivity.vwtpinit;

import com.google.protobuf.TextFormat;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.proto.NetworkMessage;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.connectivity.vwtpinit.util.Converter;
import com.volvo.connectivity.vwtpinit.util.Validator;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.initiator.NetworkMessageFlow;
import com.volvo.tisp.vwtp.initiator.UserMessageFlow;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.Disposable;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.core.scheduler.Scheduler;
import reactor.util.context.Context;

/** Implementation of HTTP Rest Controller */
@RestController
public class HttpRestController implements DisposableBean {
  public static final String USER_MESSAGE_PATH = "/userMessage";
  public static final String NETWORK_MESSAGE_PATH = "/networkMessage";

  private static final Logger logger = LoggerFactory.getLogger(HttpRestController.class);

  private UserMessageFlow userMessageFlow;
  private NetworkMessageFlow networkMessageFlow;
  private Map<Thread, Sinks.Many<UserMessageDto>> userMessageSinkMap;
  private Map<Thread, Sinks.Many<NetworkMessageDto>> networkMessageSinkMap;
  private List<Disposable> disposablesList;

  /**
   * Constructor
   *
   * @param userMessageFlow instance of initiators {@link UserMessageFlow}
   * @param networkMessageFlow instance of initiators {@link NetworkMessageFlow}
   * @param schedulerMap map of {@link Scheduler}'s that Netty is running on
   */
  @SuppressFBWarnings("WOC_WRITE_ONLY_COLLECTION_FIELD")
  public HttpRestController(
      UserMessageFlow userMessageFlow,
      NetworkMessageFlow networkMessageFlow,
      Map<Thread, Scheduler> schedulerMap) {
    this.userMessageFlow = userMessageFlow;
    this.networkMessageFlow = networkMessageFlow;
    this.disposablesList = new ArrayList<>(schedulerMap.size());
    userMessageSinkMap = new HashMap<>();
    networkMessageSinkMap = new HashMap<>();

    schedulerMap.forEach(
        (thread, scheduler) -> {
          Sinks.Many<UserMessageDto> userMessageSink =
              Sinks.unsafe().many().unicast().onBackpressureError();
          disposablesList.add(
              userMessageSink
                  .asFlux()
                  .transform(this.userMessageFlow)
                  .doOnError(throwable -> logger.error(throwable.getLocalizedMessage(), throwable))
                  .contextWrite(Context.of(Scheduler.class, scheduler))
                  .subscribe());
          userMessageSinkMap.put(thread, userMessageSink);
          Sinks.Many<NetworkMessageDto> networkMessageSink =
              Sinks.unsafe().many().unicast().onBackpressureError();
          disposablesList.add(
              networkMessageSink
                  .asFlux()
                  .transform(this.networkMessageFlow)
                  .doOnError(throwable -> logger.error(throwable.getLocalizedMessage(), throwable))
                  .contextWrite(Context.of(Scheduler.class, scheduler))
                  .subscribe());
          networkMessageSinkMap.put(thread, networkMessageSink);
        });
  }

  /**
   * Handler for {@link ServiceConstant#VWTP_INITIATOR_SERVICE}
   *
   * @param userMessageMono instance of {@link Mono}&lt;{@link UserMessage}&gt;
   * @return instance of {@link Mono}&lt;{@link Void}&gt;
   */
  @PostMapping(USER_MESSAGE_PATH)
  @SuppressWarnings("NullAway")
  public Mono<Void> handleUserMessage(@RequestBody final Mono<UserMessage> userMessageMono) {
    return userMessageMono
        .doOnNext(
            message ->
                logger
                    .atDebug()
                    .setMessage("Received UserMessage:{ {} }")
                    .addArgument(() -> TextFormat.shortDebugString(message))
                    .log())
        .doOnNext(Validator::checkUserMessage)
        .map(Converter::toUserMessageDto)
        .flatMap(Converter::writeTispContextToProperties)
        .map(message -> userMessageSinkMap.get(Thread.currentThread()).tryEmitNext(message))
        .filter(EmitResult::isFailure)
        .doOnNext(this::handleEmitResult)
        .doOnError(throwable -> logger.error(throwable.getLocalizedMessage(), throwable))
        .then();
  }

  /**
   * Handler for {@link ServiceConstant#VWTP_INITIATOR_RESPONSE_SERVICE} service
   *
   * @param networkMessageMono instance of {@link Mono}&lt;{@link NetworkMessage}&gt;
   * @return instance of {@link Mono}&lt;{@link Void}&gt;
   */
  @PostMapping(NETWORK_MESSAGE_PATH)
  @SuppressWarnings("NullAway")
  public Mono<Void> handleNetworkMessage(
      @RequestBody final Mono<NetworkMessage> networkMessageMono) {
    return networkMessageMono
        .doOnNext(
            message ->
                logger
                    .atDebug()
                    .setMessage("Received NetworkMessage:{ {} }")
                    .addArgument(() -> TextFormat.shortDebugString(message))
                    .log())
        .doOnNext(Validator::checkNetworkMessage)
        .map(Converter::toNetworkMessageDto)
        .map(message -> networkMessageSinkMap.get(Thread.currentThread()).tryEmitNext(message))
        .filter(EmitResult::isFailure)
        .doOnNext(this::handleEmitResult)
        .doOnError(throwable -> logger.error(throwable.getLocalizedMessage(), throwable))
        .then();
  }

  /**
   * Error handler for all service handlers
   *
   * @param throwable instance of {@link Throwable}
   * @return instance of {@link ResponseEntity}&lt;{@link String}&gt;
   */
  @ExceptionHandler
  protected ResponseEntity<String> handleException(final Throwable throwable) {
    if (throwable instanceof ResponseStatusException rse) {
      return ResponseEntity.status(rse.getStatusCode()).<String>body(rse.getReason());
    } else {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .<String>body(throwable.getMessage());
    }
  }

  /**
   * {@link EmitResult} handler that returns {@link HttpStatus#SERVICE_UNAVAILABLE} when emitting
   * onto a reactive sink fails
   *
   * @param emitResult instance of {@link EmitResult}
   */
  private void handleEmitResult(EmitResult emitResult) {
    throw new ResponseStatusException(
        HttpStatus.SERVICE_UNAVAILABLE, "Sink emit failed with code: " + emitResult.name());
  }

  @Override
  public void destroy() {
    disposablesList.forEach(Disposable::dispose);
  }
}
