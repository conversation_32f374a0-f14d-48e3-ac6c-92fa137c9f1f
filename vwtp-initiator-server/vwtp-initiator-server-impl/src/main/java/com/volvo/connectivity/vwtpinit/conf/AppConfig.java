package com.volvo.connectivity.vwtpinit.conf;

import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.turbo.DuplicateMessageFilter;
import ch.qos.logback.core.spi.FilterReply;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.vwtpinit.HttpRestController;
import com.volvo.connectivity.vwtpinit.util.TispHeadersFilter;
import com.volvo.tisp.framework.autoconfigure.web.client.WebClientAutoConfiguration;
import com.volvo.tisp.framework.servicediscovery.ServiceRegistry;
import com.volvo.tisp.framework.web.client.ServiceDiscoveryExchangeFilter;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.ServiceName;
import com.volvo.tisp.tce.discovery.ServiceNameBuilder;
import com.volvo.tisp.tce.discovery.conf.DiscoveryConfig;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistrationBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import com.wirelesscar.config.spi.EnvironmentConstants;
import java.net.URI;
import java.util.Locale;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.slf4j.LoggerFactory;
import org.slf4j.Marker;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.reactive.function.client.WebClientCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.web.reactive.config.EnableWebFlux;

@SpringBootApplication(exclude = WebClientAutoConfiguration.class)
@EnableWebFlux
@Import({
  DiscoveryConfig.class,
  InfluxEventReporter.class,
  AppEvent.class,
  FlowConfig.class,
  TispHeadersFilter.class
})
public class AppConfig {

  public static void main(final String[] args) {
    final String flavour = System.getProperty(EnvironmentConstants.COMPONENT_FLAVOUR);
    SpringApplication application = new SpringApplication(AppConfig.class);
    if (StringUtils.isNoneBlank(flavour)) {
      application.setAdditionalProfiles(flavour.strip());
    }
    application.run(args);
  }

  private static ServiceName createServiceName(final String name) {
    final Config config = ConfigFactory.getConfig();
    final String flavour = config.getFlavour();
    String serviceName = StringUtils.isBlank(flavour) ? name : name + "-" + flavour.strip();
    return new ServiceNameBuilder()
        .setEnvironment(config.getEnvironmentId())
        .setSite(config.getSite())
        .setSolution(config.getSolution())
        .setName(serviceName)
        .build();
  }

  /**
   * Since we have excluded {@link WebClientAutoConfiguration}, we need this for MessagePublisher to
   * work
   *
   * @param serviceRegistry instance of {@link ServiceRegistry}
   * @return instance of {@link WebClientCustomizer}
   */
  @Bean
  WebClientCustomizer serviceDiscoveryExchangeFilterCustomizer(ServiceRegistry serviceRegistry) {
    return builder -> builder.filter(new ServiceDiscoveryExchangeFilter(serviceRegistry));
  }

  @Bean(destroyMethod = "close")
  ServiceDiscoveryRegistration mtMessageserviceDiscoveryRegistration(
      final ServiceDiscovery<InstanceDetails> serviceDiscovery,
      @Value("${VGTPRIVATEIP:127.0.0.1}") final String serverHost,
      @Value("${server.port}") final int serverPort) {
    return new ServiceDiscoveryRegistrationBuilder()
        .setServiceDiscovery(serviceDiscovery)
        .setServiceName(createServiceName(ServiceConstant.VWTP_INITIATOR_SERVICE))
        .setServiceUri(
            URI.create(
                "http://" + serverHost + ":" + serverPort + HttpRestController.USER_MESSAGE_PATH))
        .build();
  }

  @Bean(destroyMethod = "close")
  ServiceDiscoveryRegistration wtpAckserviceDiscoveryRegistration(
      final ServiceDiscovery<InstanceDetails> serviceDiscovery,
      @Value("${VGTPRIVATEIP:127.0.0.1}") final String serverHost,
      @Value("${server.port}") final int serverPort) {
    return new ServiceDiscoveryRegistrationBuilder()
        .setServiceDiscovery(serviceDiscovery)
        .setServiceName(createServiceName(ServiceConstant.VWTP_INITIATOR_RESPONSE_SERVICE))
        .setServiceUri(
            URI.create(
                "http://"
                    + serverHost
                    + ":"
                    + serverPort
                    + HttpRestController.NETWORK_MESSAGE_PATH))
        .build();
  }

  @Bean
  MessagePublisher<MtStatus> mtStatusMessagePublisher(MessagePublisher.Builder builder) {
    final String flavour = ConfigFactory.getConfig().getFlavour();
    final String messageType =
        StringUtils.isBlank(flavour)
            ? ServiceConstant.MT_STATUS_MESSAGE_TYPE
            : ServiceConstant.MT_STATUS_MESSAGE_TYPE
                + "_"
                + flavour.strip().toUpperCase(Locale.ENGLISH);
    return builder.messageType(messageType, MtStatus.class).version("1.0").build();
  }

  @Bean
  DuplicateMessageFilter duplicateExceptionMessageFilter() {
    LoggerContext loggerContext = (LoggerContext) LoggerFactory.getILoggerFactory();
    DuplicateMessageFilter filter =
        new DuplicateMessageFilter() {
          @Override
          public FilterReply decide(
              Marker marker,
              Logger logger,
              Level level,
              String format,
              Object[] params,
              Throwable throwable) {
            if (throwable != null) {
              return super.decide(marker, logger, level, format, params, throwable);
            } else {
              return FilterReply.NEUTRAL;
            }
          }
        };
    filter.setContext(loggerContext);
    filter.setAllowedRepetitions(1);
    filter.setCacheSize(1);
    filter.start();
    loggerContext.addTurboFilter(filter);
    return filter;
  }
}
