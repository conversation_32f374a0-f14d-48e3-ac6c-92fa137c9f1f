package com.volvo.connectivity.vwtpinit.conf;

import com.volvo.tisp.framework.servicediscovery.ServiceActivatedEvent;
import com.volvo.tisp.framework.servicediscovery.ServiceDeactivatedEvent;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxEventSender;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxParameters;
import com.volvo.tisp.vc.influxdb.event.reporter.InfluxParametersBuilder;
import com.volvo.tisp.vc.influxdb.event.reporter.RetentionPolicy;
import com.volvo.tisp.vc.influxdb.event.reporter.RuntimeParameters;
import com.volvo.tisp.vc.influxdb.event.reporter.RuntimeParametersBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import java.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;

@ConditionalOnProperty(prefix = "influx.event", name = "enabled", matchIfMissing = true)
public class InfluxEventReporter {
  private static final Logger logger = LoggerFactory.getLogger(InfluxEventReporter.class);
  private final InfluxEventSender influxEventSender;

  public InfluxEventReporter(
      @Value("${influx.connect.timeout:PT5S}") final String connectTimeoutString,
      @Value("${influx.read.timeout:PT5S}") final String readTimeoutString,
      @Value("${management.influx.metrics.export.db:connectivity_services}") final String dbName,
      @Value("${management.influx.metrics.export.retention-policy:60days}")
          final String retentionPolicyString,
      @Value("${management.influx.metrics.export.uri}") final String serverUrl) {
    final Config config = ConfigFactory.getConfig();
    final RuntimeParameters runtimeParameters =
        new RuntimeParametersBuilder()
            .setComponentShortName(config.getComponentShortName())
            .setComponentVersion(config.getComponentVersion())
            .setEnvironmentId(config.getEnvironmentId())
            .setNodeId(config.getNodeId())
            .setSite(config.getSite())
            .setSolution(config.getSolution())
            .build();

    final Duration connectTimeout = Duration.parse(connectTimeoutString);
    final Duration readTimeout = Duration.parse(readTimeoutString);
    final RetentionPolicy retentionPolicy =
        RetentionPolicy.fromValue(retentionPolicyString).orElseThrow();

    final InfluxParameters influxParameters =
        new InfluxParametersBuilder()
            .setConnectTimeout(connectTimeout)
            .setDbName(dbName)
            .setReadTimeout(readTimeout)
            .setServerUrl(serverUrl)
            .setRetentionPolicy(retentionPolicy)
            .build();

    logger.info("runtimeParameters: {}", runtimeParameters);
    logger.info("influxParameters: {}", influxParameters);

    influxEventSender = new InfluxEventSender(runtimeParameters, influxParameters);
  }

  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReadyEvent() {
    influxEventSender.sendStarted();
  }

  @EventListener(ContextClosedEvent.class)
  public void onContextClosed() {
    influxEventSender.sendStopped();
  }

  @EventListener(ServiceActivatedEvent.class)
  public void onServiceActivatedEvent() {
    influxEventSender.sendActivated();
  }

  @EventListener(ServiceDeactivatedEvent.class)
  public void onServiceDeactivatedEvent() {
    influxEventSender.sendDeactivated();
  }
}
