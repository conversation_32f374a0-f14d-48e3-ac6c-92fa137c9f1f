package com.volvo.connectivity.vwtpinit.conf;

import com.volvo.connectivity.vwtpinit.HttpRestController;
import com.volvo.connectivity.vwtpinit.MtStatusJmsClientFlow;
import com.volvo.connectivity.vwtpinit.NetworkMessageScooterClientFlow;
import com.volvo.connectivity.vwtpinit.NetworkMessageUdpClientFlow;
import com.volvo.connectivity.vwtpinit.util.NettyUdpMetricsRecorder;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.configuration.InitiatorConfiguration;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import io.netty.channel.EventLoopGroup;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.http.client.ReactorResourceFactory;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import reactor.netty.resources.LoopResources;
import reactor.netty.udp.UdpClient;

@Configuration
@Import({
  HttpRestController.class,
  MtStatusJmsClientFlow.class,
  NetworkMessageUdpClientFlow.class,
  NetworkMessageScooterClientFlow.class,
  InitiatorConfiguration.class
})
public class FlowConfig {

  /**
   * Wraps every reactive thread into it's own scheduler and returns an {@link Map} mapping that
   * {@link Thread} to a {@link Scheduler}
   *
   * @param reactorResourceFactory instance of {@link ReactorResourceFactory}
   * @return instance of {@link Map}&lt;{@link Thread}, {@link Scheduler};&gt;
   */
  @Bean
  Map<Thread, Scheduler> schedulerMap(final ReactorResourceFactory reactorResourceFactory) {
    final EventLoopGroup eventLoopGroup =
        reactorResourceFactory.getLoopResources().onServer(LoopResources.DEFAULT_NATIVE);
    final Map<Thread, Scheduler> tempMap = new HashMap<>();
    eventLoopGroup.forEach(
        eventExecutor ->
            tempMap.put(
                eventExecutor.submit(Thread::currentThread).awaitUninterruptibly().getNow(),
                Schedulers.fromExecutorService(eventExecutor, eventExecutor.toString())));
    return Collections.unmodifiableMap(tempMap);
  }

  /**
   * Creates Netty {@link UdpClient} for sending UDP traffic
   *
   * @param reactorResourceFactory instance of {@link ReactorResourceFactory}
   * @return instance of {@link UdpClient}
   */
  @Bean
  UdpClient udpClient(final ReactorResourceFactory reactorResourceFactory) {
    return UdpClient.create()
        .metrics(true, NettyUdpMetricsRecorder::getInstance)
        .runOn(reactorResourceFactory.getLoopResources().onServer(LoopResources.DEFAULT_NATIVE));
  }

  /**
   * exposes {@link NetworkMessageUdpClientFlow} as "outgoingInitiatorNetworkMessageFlow"
   *
   * @param udpFlow instance of {@link NetworkMessageUdpClientFlow}
   * @return instance of {@link FlowComposer}&lt;{@link NetworkMessageDto}, {@link Void};&gt;
   */
  @Bean("outgoingInitiatorNetworkMessageFlow")
  @ConditionalOnBean(NetworkMessageUdpClientFlow.class)
  FlowComposer<NetworkMessageDto, Void> networkMessageUdpClientFlow(
      NetworkMessageUdpClientFlow udpFlow) {
    return udpFlow;
  }

  /**
   * exposes {@link NetworkMessageScooterClientFlow} as "outgoingInitiatorNetworkMessageFlow"
   *
   * @param scooterFlow instance of {@link NetworkMessageScooterClientFlow}
   * @return instance of {@link FlowComposer}&lt;{@link NetworkMessageDto}, {@link Void};&gt;
   */
  @Bean("outgoingInitiatorNetworkMessageFlow")
  @ConditionalOnBean(NetworkMessageScooterClientFlow.class)
  FlowComposer<NetworkMessageDto, Void> networkMessageScooterClientFlow(
      NetworkMessageScooterClientFlow scooterFlow) {
    return scooterFlow;
  }

  /**
   * exposes {@link MtStatusJmsClientFlow} as "outgoingInitiatorUserStatusFlow"
   *
   * @param jmsMessageFlow instance of {@link MtStatusJmsClientFlow}
   * @return instance of {@link FlowComposer}&lt;{@link UserStatusDto}, {@link Void};&gt;
   */
  @Bean
  FlowComposer<UserStatusDto, Void> outgoingInitiatorUserStatusFlow(
      MtStatusJmsClientFlow jmsMessageFlow) {
    return jmsMessageFlow;
  }
}
