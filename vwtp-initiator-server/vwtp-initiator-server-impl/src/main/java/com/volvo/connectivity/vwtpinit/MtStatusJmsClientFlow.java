package com.volvo.connectivity.vwtpinit;

import com.google.protobuf.TextFormat;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.vwtpinit.util.Converter;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.http.TispHttpHeaders;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.identifier.WorkflowIdentifier;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import java.util.Map;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.function.Tuple2;

/** Reactive flow that sends out {@link MtStatus}'s over JMS */
public class MtStatusJmsClientFlow implements FlowComposer<UserStatusDto, Void> {
  private static final Logger logger = LoggerFactory.getLogger(MtStatusJmsClientFlow.class);

  private MessagePublisher<MtStatus> statusMessagePublisher;

  /**
   * Constructor
   *
   * @param statusMessagePublisher instance of {@link MessagePublisher}&lt;{@link MtStatus}&gt;
   */
  public MtStatusJmsClientFlow(MessagePublisher<MtStatus> statusMessagePublisher) {
    this.statusMessagePublisher = statusMessagePublisher;
  }

  @Override
  public Publisher<Void> apply(Flux<UserStatusDto> flow) {
    logger.info("Composing reactive flow");
    return flow.map(Converter::toMtStatus).flatMap(this::sendMtStatusOverJms).then();
  }

  /**
   * Performs sending of JMS message using subscription repository client API
   *
   * @param tuple instance of {@link Tuple2}
   * @return instance of {@link Mono}&lt;{@link Integer}&gt;
   */
  private Mono<Integer> sendMtStatusOverJms(Tuple2<MtStatus, Map<String, String>> tuple) {
    return Mono.fromFuture(
            () ->
                TispContext.supplyInContext(
                    () -> statusMessagePublisher.newMessage().publish(tuple.getT1()),
                    builder -> {
                      Map<String, String> properties = tuple.getT2();
                      String tid = properties.get(TispHttpHeaders.TRACKING_ID.name());
                      if (tid != null) {
                        builder.tid(TrackingIdentifier.fromString(tid));
                      }
                      String wid = properties.get(TispHttpHeaders.WORKFLOW_ID.name());
                      if (wid != null) {
                        builder.wid(WorkflowIdentifier.fromString(wid));
                      }
                      return builder;
                    }))
        .doOnNext(
            count ->
                logger
                    .atDebug()
                    .setMessage("Successfully published MtStatus: { {} }, to {} subscribers")
                    .addArgument(() -> TextFormat.shortDebugString(tuple.getT1()))
                    .addArgument(count)
                    .log())
        .doOnError(e -> logger.error(e.getLocalizedMessage(), e))
        .onErrorComplete()
        .subscribeOn(Schedulers.boundedElastic());
  }
}
