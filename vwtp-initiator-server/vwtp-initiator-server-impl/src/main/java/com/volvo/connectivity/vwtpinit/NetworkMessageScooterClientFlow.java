package com.volvo.connectivity.vwtpinit;

import static com.volvo.connectivity.vwtpinit.NetworkMessageUdpClientFlow.*;
import static java.util.Locale.ENGLISH;

import com.volvo.connectivity.vwtpinit.util.Converter;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.wirelesscar.tce.module.scooteragent.ScooterMessage;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.Unpooled;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import reactor.core.publisher.BufferOverflowStrategy;
import reactor.core.publisher.Flux;
import reactor.netty.udp.UdpClient;

/** Reactive flow that sends out Scooter packets over UDP */
@ConditionalOnProperty(prefix = "scooter", name = "enabled", havingValue = "true")
public class NetworkMessageScooterClientFlow
    implements FlowComposer<NetworkMessageDto, Void>, DisposableBean {
  private static final Logger logger =
      LoggerFactory.getLogger(NetworkMessageScooterClientFlow.class);
  static final String FORMAT = "%-45s: %s";

  private UdpClient udpClient;
  private final String scooterHost;
  private final int scooterPort;

  /**
   * Constructor
   *
   * @param udpClient instance of {@link UdpClient}
   * @param scooterHost host name of mo-udp-router-server that is running with Scooter module
   *     enabled, or host name of load test server
   * @param scooterPort port of mo-udp-router-server that is running with Scooter module enabled, or
   *     host name of load test server
   */
  public NetworkMessageScooterClientFlow(
      UdpClient udpClient,
      @Value("${scooter.host}") String scooterHost,
      @Value("${scooter.port}") int scooterPort) {
    Validate.notEmpty(scooterHost, "scooter.host");
    Validate.isPositivePortNumber(scooterPort, "scooter.port");

    this.udpClient = udpClient;
    this.scooterHost = scooterHost;
    this.scooterPort = scooterPort;

    logProperties();
  }

  @Override
  public Publisher<Void> apply(Flux<NetworkMessageDto> flow) {
    logger.info("Composing reactive flow");
    return flow.map(Converter::toScooterMessage)
        .map(ScooterMessage::encode)
        .map(Unpooled::wrappedBuffer)
        .onBackpressureBuffer(
            OUTBOUND_BUFFER_SIZE,
            message -> bufferEvictedCounter.increment(),
            BufferOverflowStrategy.DROP_OLDEST)
        .concatMap(this::sendUdp);
  }

  /**
   * Performs sending of Scooter packet over UDP
   *
   * @param networkMessage instance of {@link NetworkMessageDto}
   * @return instance of {@link Publisher}&lt;{@link Void}&gt;
   */
  private Publisher<Void> sendUdp(ByteBuf networkMessage) {
    return udpClient
        .host(scooterHost)
        .port(scooterPort)
        .handle((inbound, outbound) -> outbound.sendObject(networkMessage).then())
        .connect()
        .doOnError(e -> logger.error(e.getLocalizedMessage(), e))
        .onErrorComplete()
        .then();
  }

  private void logProperties() {
    if (logger.isInfoEnabled()) {
      logger.info("");
      logger.info(String.format(ENGLISH, FORMAT, "SCOOTER CLIENT FLOW CONFIG", ""));
      logger.info("");
      logger.info(String.format(ENGLISH, FORMAT, "scooter.host", scooterHost));
      logger.info(String.format(ENGLISH, FORMAT, "scooter.port", scooterPort));
    }
  }

  @Override
  public void destroy() throws Exception {
    logProperties();
  }
}
