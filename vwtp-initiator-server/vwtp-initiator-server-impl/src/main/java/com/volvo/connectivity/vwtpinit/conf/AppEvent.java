package com.volvo.connectivity.vwtpinit.conf;

import com.github.benmanes.caffeine.cache.Cache;
import com.volvo.connectivity.vwtpinit.util.MetricNames;
import com.volvo.tisp.framework.servicediscovery.ServiceActivatedEvent;
import com.volvo.tisp.framework.servicediscovery.ServiceDeactivatedEvent;
import com.volvo.tisp.framework.servicediscovery.ServicePausedEvent;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tags;
import java.util.List;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import reactor.core.publisher.Hooks;
import reactor.util.Logger;
import reactor.util.Loggers;

public class AppEvent {
  private static final Logger logger = Loggers.getLogger(AppEvent.class);
  private final HealthContributorRegistry healthContributorRegistry;
  private final List<ServiceDiscoveryRegistration> serviceDiscoveryRegistrations;
  private final List<Cache<?, ?>> caches;

  public AppEvent(
      final HealthContributorRegistry healthContributorRegistry,
      final List<ServiceDiscoveryRegistration> serviceDiscoveryRegistrations,
      List<Cache<?, ?>> caches) {
    this.healthContributorRegistry = healthContributorRegistry;
    this.serviceDiscoveryRegistrations = serviceDiscoveryRegistrations;
    this.caches = caches;
  }

  @EventListener(ApplicationStartedEvent.class)
  public void registerReactorMetrics() {
    logger.info("Registering \"onNextDropped\" metrics hook");
    Hooks.onNextDropped(
        object ->
            Metrics.counter(
                    MetricNames.ANOMALIES,
                    Tags.of(
                        MetricNames.TYPE_TAG,
                        "reactor-dropped-" + object.getClass().getSimpleName()))
                .increment());
  }

  @EventListener(ServiceActivatedEvent.class)
  public void registerServiceDiscovery() {
    serviceDiscoveryRegistrations.forEach(ServiceDiscoveryRegistration::open);
  }

  @EventListener({
    ServiceDeactivatedEvent.class,
    ServicePausedEvent.class,
    ContextClosedEvent.class
  })
  void unregisterServiceDiscovery() {
    serviceDiscoveryRegistrations.forEach(ServiceDiscoveryRegistration::close);
    caches.forEach(Cache::invalidateAll);
    caches.forEach(Cache::cleanUp);
  }

  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReadyEvent() {
    healthContributorRegistry.unregisterContributor("serviceDiscovery");
  }
}
