package com.volvo.connectivity.vwtpinit.util;

import com.volvo.connectivity.proto.Address;
import com.volvo.connectivity.proto.NetworkMessage;
import com.volvo.connectivity.proto.NetworkMessageOrBuilder;
import com.volvo.connectivity.proto.TransactionClass;
import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.connectivity.proto.UserMessageOrBuilder;
import com.volvo.connectivity.proto.WtpVersion;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import org.springframework.web.server.ServerWebInputException;

/** Utility class for validating server input objects */
public final class Validator {

  private static final Predicate<String> INVALID_HOSTNAME =
      Pattern.compile("^(?![.-])[\\p{Alnum}.-]{1,255}(?<![.-])$").asMatchPredicate().negate();

  /**
   * Validate {@link Address} protobuf object
   *
   * @param uri instance of {@link Address}
   * @param attributeName name of the attribute where {@link Address} is stored
   */
  public static final void checkUri(Address uri, String attributeName) {
    if (INVALID_HOSTNAME.test(uri.getDestination())) {
      throw new ServerWebInputException("Invalid hostname in " + attributeName);
    }
    String port = uri.getQualifier();
    if (uri.getTransport() == Transport.UDP && port.isBlank()) {
      throw new ServerWebInputException("Port number must be specified in " + attributeName);
    }
    if (uri.getTransport() == Transport.UDP && Integer.parseInt(port) <= 0) {
      throw new ServerWebInputException("Port number must be positive in " + attributeName);
    }
    if (uri.getTransport() == Transport.UNRECOGNIZED) {
      throw new ServerWebInputException("Unrecognized protocol name in " + attributeName);
    }
  }

  /**
   * Validate {@link NetworkMessage} protobuf object
   *
   * @param networkMessage instance of {@link NetworkMessage}
   */
  public static final void checkNetworkMessage(NetworkMessageOrBuilder networkMessage) {
    checkUri(networkMessage.getAddress(), "NetworkMessage.address");
  }

  /**
   * Validate {@link UserMessage} protobuf object
   *
   * @param userMessage instance of {@link UserMessage}
   */
  public static final void checkUserMessage(UserMessageOrBuilder userMessage) {
    checkUri(userMessage.getAddress(), "UserMessage.address");
    if (userMessage.getMessageId().isEmpty()) {
      throw new ServerWebInputException("UserMessage.trackingId must not be empty");
    }
    if (userMessage.getTransactionClass() == TransactionClass.UNRECOGNIZED) {
      throw new ServerWebInputException("Unrecognized UserMessage.transactionClass");
    }
    if (userMessage.getWtpVersion() == WtpVersion.UNRECOGNIZED) {
      throw new ServerWebInputException("Unrecognized UserMessage.wtpVersion");
    }
  }

  /** Private constructor to prevent instantiation of the utility class */
  private Validator() {}
}
