package com.volvo.connectivity.vwtpinit.util;

import com.volvo.connectivity.metric.OperatorMetrics;
import com.volvo.connectivity.proto.Address;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.NetworkMessage;
import com.volvo.connectivity.proto.NetworkMessageOrBuilder;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.connectivity.proto.UserMessageOrBuilder;
import com.volvo.tisp.framework.http.TispHttpHeaders;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.MessageDto;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.wirelesscar.tce.module.scooteragent.ScooterMessage;
import java.net.URI;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import reactor.core.publisher.Mono;
import reactor.util.context.ContextView;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/** Utility class for converting to and from protobuf objects */
public final class Converter {
  private static final OperatorMetrics operatorMetrics = OperatorMetrics.getInstance();

  /**
   * Convert {@link UserMessage} to {@link UserMessageDto}
   *
   * @param userMessage instance of {@link UserMessageOrBuilder}
   * @return instance of {@link UserMessageDto}
   */
  @SuppressWarnings("removal")
  public static final UserMessageDto toUserMessageDto(UserMessageOrBuilder userMessage) {
    return UserMessageDto.builder()
        .withAddress(Converter.toUri(userMessage.getAddress()))
        .withPayload(userMessage.getPayload().toByteArray())
        .withMessageId(userMessage.getMessageId())
        .withTransactionClass(
            TransactionClass.fromValue(userMessage.getTransactionClass().getNumber()))
        .withVehicleId(userMessage.getVehicleIdentifier())
        .withWtpVersion(WtpVersion.valueOf(userMessage.getWtpVersion().name()))
        .withProperties(new HashMap<>(2))
        .build();
  }

  /**
   * Convert {@link NetworkMessage} to {@link NetworkMessageDto}
   *
   * @param networkMessage instance of {@link NetworkMessageOrBuilder}
   * @return instance of {@link NetworkMessageDto}
   */
  public static final NetworkMessageDto toNetworkMessageDto(
      NetworkMessageOrBuilder networkMessage) {
    return NetworkMessageDto.builder()
        .withAddress(Converter.toUri(networkMessage.getAddress()))
        .withPayload(networkMessage.getPayload().toByteArray())
        .withMessageId(networkMessage.getMessageId())
        .build();
  }

  /**
   * Convert {@link UserStatusDto} to {@link MtStatus}
   *
   * @param userStatus instance of {@link UserStatusDto}
   * @return instance of {@link MtStatus}
   */
  @SuppressWarnings("removal")
  public static final Tuple2<MtStatus, Map<String, String>> toMtStatus(UserStatusDto userStatus) {
    final Status status =
        userStatus.isDelivered()
            ? Status.DELIVERED
            : switch (userStatus.getAbortCode()) {
              case TCE_PROVIDER_CAPACITY_EXCEEDED,
                      TGW_PROVIDER_CAPACITY_EXCEEDED,
                      TGW_PROVIDER_INVALID_WTP_TID,
                      TGW_PROVIDER_TRANSACTION_TIMEOUT ->
                  Status.THROTTLED;
              case TCE_PROVIDER_NO_RESPONSE -> Status.CANCELED;
              case TGW_USER_UNSUPPORTED_SERVICE_VERSION -> Status.SERVICE_UNSUPPORTED;
              default -> Status.REJECTED;
            };
    final Transport transport =
        operatorMetrics.isVpnIp(userStatus.getAddress()) ? Transport.VPN : Transport.UDP;
    return Tuples.of(
        MtStatus.newBuilder()
            .setMessageId(userStatus.getMessageId())
            .setStatus(status)
            .setTransport(transport)
            .build(),
        userStatus.getProperties());
  }

  /**
   * Convert {@link Address} to {@link URI}
   *
   * @param uri instance of {@link Address}
   * @return instance of {@link URI}
   */
  public static final URI toUri(Address uri) {
    if (uri.getQualifier().isBlank()) {
      return URI.create(
          String.format(Locale.ENGLISH, "%s://%s", uri.getTransport(), uri.getDestination()));
    } else {
      return URI.create(
          String.format(
              Locale.ENGLISH,
              "%s://%s:%s",
              uri.getTransport(),
              uri.getDestination(),
              uri.getQualifier()));
    }
  }

  /**
   * Convert {@link NetworkMessageDto} to {@link URI}
   *
   * @param networkMessage instance of {@link NetworkMessageDto}
   * @return instance of {@link ScooterMessage}
   */
  public static final ScooterMessage toScooterMessage(NetworkMessageDto networkMessage) {
    URI address = networkMessage.getAddress();
    return new ScooterMessage(address.getHost(), address.getPort(), networkMessage.getPayload());
  }

  /**
   * Write {@link TispHttpHeaders#TRACKING_ID} and {@link TispHttpHeaders#WORKFLOW_ID} values from
   * reactive {@link ContextView} to {@link MessageDto#getProperties()}
   *
   * @param message instance of {@link MessageDto}
   * @return instance of {@link Mono}
   */
  @SuppressWarnings("removal")
  public static final <M extends MessageDto> Mono<M> writeTispContextToProperties(M message) {
    return Mono.deferContextual(
        context -> {
          Map<String, String> properties = message.getProperties();
          context
              .<String>getOrEmpty(TispHttpHeaders.TRACKING_ID)
              .ifPresent(tid -> properties.put(TispHttpHeaders.TRACKING_ID.name(), tid));
          context
              .<String>getOrEmpty(TispHttpHeaders.WORKFLOW_ID)
              .ifPresent(wid -> properties.put(TispHttpHeaders.WORKFLOW_ID.name(), wid));
          return Mono.just(message);
        });
  }

  private Converter() {}
}
