package com.volvo.connectivity.vwtpinit.util;

import static reactor.netty.Metrics.ADDRESS_RESOLVER;
import static reactor.netty.Metrics.CONNECT_TIME;
import static reactor.netty.Metrics.DATA_RECEIVED;
import static reactor.netty.Metrics.DATA_SENT;
import static reactor.netty.Metrics.ERRORS;
import static reactor.netty.Metrics.REGISTRY;
import static reactor.netty.Metrics.STATUS;
import static reactor.netty.Metrics.UDP_CLIENT_PREFIX;

import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Timer;
import java.net.SocketAddress;
import java.time.Duration;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import reactor.netty.channel.ChannelMeters;
import reactor.netty.channel.ChannelMetricsRecorder;
import reactor.netty.internal.util.MapUtils;
import reactor.netty.udp.UdpClient;

/**
 * A {@link ChannelMetricsRecorder} implementation for integration with Micrometer for {@link
 * UdpClient}
 */
public final class NettyUdpMetricsRecorder implements ChannelMetricsRecorder {

  private static final NettyUdpMetricsRecorder INSTANCE = new NettyUdpMetricsRecorder();
  private static final DistributionSummary dataReceivedDistributionSummary =
      DistributionSummary.builder(UDP_CLIENT_PREFIX + DATA_RECEIVED)
          .baseUnit(ChannelMeters.DATA_RECEIVED.getBaseUnit())
          .register(REGISTRY);
  private static final DistributionSummary dataSentDistributionSummary =
      DistributionSummary.builder(UDP_CLIENT_PREFIX + DATA_SENT)
          .baseUnit(ChannelMeters.DATA_SENT.getBaseUnit())
          .register(REGISTRY);
  private static final Counter errorsCounter =
      Counter.builder(UDP_CLIENT_PREFIX + ERRORS).register(REGISTRY);
  private static final ConcurrentMap<String, Timer> connectTimeCache = new ConcurrentHashMap<>();
  private static final ConcurrentMap<String, Timer> addressResolverTimeCache =
      new ConcurrentHashMap<>();

  /**
   * Returns instance of {@link NettyUdpMetricsRecorder}
   *
   * @return instance of {@link NettyUdpMetricsRecorder}
   */
  @SuppressFBWarnings("SING_SINGLETON_GETTER_NOT_SYNCHRONIZED")
  public static final NettyUdpMetricsRecorder getInstance() {
    return INSTANCE;
  }

  @Override
  public void recordDataReceived(SocketAddress remoteAddress, long bytes) {
    dataReceivedDistributionSummary.record((double) bytes);
  }

  @Override
  public void recordDataSent(SocketAddress remoteAddress, long bytes) {
    dataSentDistributionSummary.record((double) bytes);
  }

  @Override
  public void incrementErrorsCount(SocketAddress remoteAddress) {
    errorsCounter.increment();
  }

  @Override
  public void recordTlsHandshakeTime(SocketAddress remoteAddress, Duration time, String status) {
    /*
     * Not used in reactor.netty.udp.UdpClient
     */
  }

  @Override
  public void recordConnectTime(SocketAddress remoteAddress, Duration time, String status) {
    Timer timer =
        MapUtils.computeIfAbsent(
            connectTimeCache,
            status,
            key ->
                Timer.builder(UDP_CLIENT_PREFIX + CONNECT_TIME)
                    .tags(STATUS, status)
                    .register(REGISTRY));
    timer.record(time);
  }

  @Override
  public void recordResolveAddressTime(SocketAddress remoteAddress, Duration time, String status) {
    Timer timer =
        MapUtils.computeIfAbsent(
            addressResolverTimeCache,
            status,
            key ->
                Timer.builder(UDP_CLIENT_PREFIX + ADDRESS_RESOLVER)
                    .tags(STATUS, status)
                    .register(REGISTRY));
    timer.record(time);
  }

  @Override
  public void recordServerConnectionOpened(SocketAddress serverAddress) {
    /*
     * Not used in reactor.netty.udp.UdpClient
     */
  }

  @Override
  public void recordServerConnectionClosed(SocketAddress serverAddress) {
    /*
     * Not used in reactor.netty.udp.UdpClient
     */
  }

  /** Hiding public constructor */
  private NettyUdpMetricsRecorder() {}
}
