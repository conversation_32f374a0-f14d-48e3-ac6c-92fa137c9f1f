package com.volvo.connectivity.vwtpinit;

import com.volvo.connectivity.vwtpinit.util.MetricNames;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import io.netty.buffer.Unpooled;
import org.reactivestreams.Publisher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import reactor.core.publisher.BufferOverflowStrategy;
import reactor.core.publisher.Flux;
import reactor.netty.udp.UdpClient;

/** Reactive flow that sends out VWTP packets over UDP */
@ConditionalOnProperty(
    prefix = "scooter",
    name = "enabled",
    havingValue = "false",
    matchIfMissing = true)
@ConditionalOnMissingBean(NetworkMessageScooterClientFlow.class)
public class NetworkMessageUdpClientFlow implements FlowComposer<NetworkMessageDto, Void> {
  private static final Logger logger = LoggerFactory.getLogger(NetworkMessageUdpClientFlow.class);
  static final Counter bufferEvictedCounter =
      Counter.builder(MetricNames.OUTGOING_ANOMALIES)
          .description(
              "Count of network messages evicted from the buffer due to lack of requests from udp client")
          .tag(MetricNames.TYPE_TAG, "buffer-evicted-network-message")
          .register(Metrics.globalRegistry);
  static final int OUTBOUND_BUFFER_SIZE = 0x02FFFF;

  private UdpClient udpClient;

  /**
   * Constructor
   *
   * @param udpClient instance of {@link UdpClient}
   */
  public NetworkMessageUdpClientFlow(UdpClient udpClient) {
    this.udpClient = udpClient;
  }

  @Override
  public Publisher<Void> apply(Flux<NetworkMessageDto> flow) {
    logger.info("Composing reactive flow");
    return flow.onBackpressureBuffer(
            OUTBOUND_BUFFER_SIZE,
            message -> bufferEvictedCounter.increment(),
            BufferOverflowStrategy.DROP_OLDEST)
        .concatMap(this::sendUdp);
  }

  /**
   * Performs sending of VWTP packet over UDP
   *
   * @param networkMessage instance of {@link NetworkMessageDto}
   * @return instance of {@link Publisher}&lt;{@link Void}&gt;
   */
  private Publisher<Void> sendUdp(NetworkMessageDto networkMessage) {
    return udpClient
        .host(networkMessage.getAddress().getHost())
        .port(networkMessage.getAddress().getPort())
        .handle(
            (inbound, outbound) ->
                outbound.sendObject(Unpooled.wrappedBuffer(networkMessage.getPayload())).then())
        .connect()
        .doOnError(e -> logger.error(e.getLocalizedMessage(), e))
        .onErrorComplete()
        .then();
  }
}
