---
###############
# Defaults
###############
spring.cloud.zookeeper.discovery.enabled: false
tisp.service-discovery.type: servicediscovery-lib

---
###############
# DE variables
###############
spring:
  config.activate.on-profile: de_component-test_eu-west-1
  artemis:
    broker-url: tcp://mockhost:61616
    user: admin
    password: admin

server.port: 20690
servicediscovery.active-by-default: true
management.influx.metrics.export.enabled: false
influx.event.enabled: false
scooter.enabled: false
