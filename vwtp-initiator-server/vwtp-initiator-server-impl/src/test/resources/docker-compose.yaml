---
version: '3.3'
services:
  artemis:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/apache/activemq-artemis:latest-alpine
    environment:
      - ARTEMIS_USER=admin
      - ARTEMIS_PASSWORD=admin
    ports:
      - 3161:8161/tcp
      - 31616:61616/tcp
  influxdb:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/influxdb:1.8-alpine
    ports:
      - 3086:8086/tcp
  zoo:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/zookeeper:3.8
    ports:
      - 3181:2181/tcp
  wiremock:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/wiremock/wiremock:latest-alpine
    command:
      - --async-response-enabled=true
      - --global-response-templating
    ports:
      - 3080:8080/tcp
