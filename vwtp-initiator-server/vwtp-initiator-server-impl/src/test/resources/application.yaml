---
management.influx.metrics.export:
  db: connectivity_services
  uri: http://${container.host}:3086
  retention-policy: 30days
  auto-create-db: false

spring:
  artemis:
    mode: native
    broker-url: tcp://${container.host}:31616

subscription.cache.enabled: false
servicediscovery:
  subr: http://${container.host}:3080
  auth: http://${container.host}:3080
  active-by-default: true

logging.level:
    com.volvo: DEBUG

wtp:
  timer:
    r: 15000
