package com.volvo.connectivity.vwtpinit.integration.util;

import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.responder.UserStatusFlow;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;

/** Flow that just converts {@link UserMessageDto} to {@link UserStatusDto} with delivered status */
public class UserAckFlow implements FlowComposer<UserMessageDto, Void> {

  private UserStatusFlow userStatusFlow;

  /**
   * Constructor
   *
   * @param userStatusFlow instance of {@link UserStatusFlow}
   */
  public UserAckFlow(UserStatusFlow userStatusFlow) {
    this.userStatusFlow = userStatusFlow;
  }

  @Override
  public Publisher<Void> apply(Flux<UserMessageDto> flow) {
    return flow.map(this::toUser).transform(this.userStatusFlow);
  }

  /**
   * Converts {@link UserMessageDto} to {@link UserStatusDto} with delivered flag set to true
   *
   * @param userMessage instance of {@link UserMessageDto}
   * @return instance of {@link UserStatusDto}
   */
  private UserStatusDto toUser(UserMessageDto userMessage) {
    return UserStatusDto.builder()
        .withDelivered(true)
        .withMessageId(userMessage.getMessageId())
        .build();
  }
}
