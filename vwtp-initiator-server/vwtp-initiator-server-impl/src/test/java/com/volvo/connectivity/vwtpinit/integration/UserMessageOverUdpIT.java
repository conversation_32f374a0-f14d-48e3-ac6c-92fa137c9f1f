package com.volvo.connectivity.vwtpinit.integration;

import static org.junit.Assert.assertEquals;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.proto.Address;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.NetworkMessage;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.TransactionClass;
import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.connectivity.proto.WtpVersion;
import com.volvo.connectivity.vwtpinit.HttpRestController;
import com.volvo.connectivity.vwtpinit.integration.util.TestcontainersBase;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import jakarta.jms.Message;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;
import reactor.util.Logger;
import reactor.util.Loggers;

/** {@link HttpRestController} integration test for messages that should be transfered over UDP */
@ExtendWith(TispContextExtension.class)
class UserMessageOverUdpIT extends TestcontainersBase {

  private static final Logger logger = Loggers.getLogger(UserMessageOverUdpIT.class);

  @Autowired private JmsTemplate jmsTemplate;

  /**
   * Test {@link ServiceConstant#VWTP_INITIATOR_SERVICE} successfully sending of {@link UserMessage}
   * over UDP
   *
   * @throws Exception when error occurs
   */
  @Test
  void sendUserMessageTest() throws Exception {
    UserMessage userMessage =
        UserMessage.newBuilder()
            .setMessageId("testMessageId")
            .setAddress(
                Address.newBuilder()
                    .setTransport(Transport.UDP)
                    .setDestination("127.0.0.1")
                    .setQualifier("20000"))
            .setTransactionClass(TransactionClass.CLASS_1)
            .setWtpVersion(WtpVersion.VERSION_2)
            .setPayload(ByteString.copyFromUtf8("Test Message"))
            .setVehicleIdentifier(99)
            .build();
    WEB_TEST_CLIENT
        .post()
        .uri(HttpRestController.USER_MESSAGE_PATH)
        .bodyValue(userMessage)
        .exchange()
        .expectStatus()
        .is2xxSuccessful();

    Message message = jmsTemplate.receive("MT.STATUSES.IN");
    logger.info("JMS Message: {}", message);
    byte[] jmsPayload = message.getBody(byte[].class);
    MtStatus mtStatusMessage = MtStatus.parseFrom(jmsPayload);
    assertEquals(Status.DELIVERED, mtStatusMessage.getStatus());
    assertEquals("testMessageId", mtStatusMessage.getMessageId());
    assertEquals(
        TispContext.current().tid().toString(),
        message.getStringProperty(TispJmsHeader.TRACKING_ID.value()));
  }

  /** Test {@link ServiceConstant#VWTP_INITIATOR_SERVICE} with invalid {@link UserMessage} */
  @Test
  void sendInvalidUserMessageTest() {
    String tispContextValue = TispContext.current().tid().toString();
    UserMessage userMessage =
        UserMessage.newBuilder()
            .setMessageId(tispContextValue)
            .setAddress(
                Address.newBuilder()
                    .setTransport(Transport.UDP)
                    .setDestination("127.0.0.1")
                    .setQualifier("20000"))
            .setTransactionClassValue(-16)
            .setWtpVersion(WtpVersion.VERSION_2)
            .setPayload(ByteString.copyFromUtf8("Test Message"))
            .setVehicleIdentifier(99)
            .build();
    WEB_TEST_CLIENT
        .post()
        .uri(HttpRestController.USER_MESSAGE_PATH)
        .bodyValue(userMessage)
        .exchange()
        .expectStatus()
        .is4xxClientError();
  }

  /**
   * Test {@link ServiceConstant#VWTP_INITIATOR_RESPONSE_SERVICE} service with invalid {@link
   * NetworkMessage}
   */
  @Test
  void sendInvalidNetworkMessageTest() {
    String tispContextValue = TispContext.current().tid().toString();
    NetworkMessage networkMessage =
        NetworkMessage.newBuilder()
            .setMessageId(tispContextValue)
            .setAddress(
                Address.newBuilder()
                    .setTransportValue(-55)
                    .setDestination("127.0.0.1")
                    .setQualifier("20000"))
            .setPayload(ByteString.copyFromUtf8("Test Message"))
            .build();
    WEB_TEST_CLIENT
        .post()
        .uri(HttpRestController.NETWORK_MESSAGE_PATH)
        .bodyValue(networkMessage)
        .exchange()
        .expectStatus()
        .is4xxClientError();
  }
}
