package com.volvo.connectivity.vwtpinit.util;

import static com.volvo.tisp.vwtp.constants.TransactionClass.CLASS_2;
import static com.volvo.tisp.vwtp.constants.WtpVersion.VERSION_2;
import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.proto.Address;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.NetworkMessage;
import com.volvo.connectivity.proto.NetworkMessageOrBuilder;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.TransactionClass;
import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.connectivity.proto.UserMessageOrBuilder;
import com.volvo.connectivity.proto.WtpVersion;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.util.Map;
import org.junit.jupiter.api.Test;
import reactor.util.function.Tuple2;

/** Test class for {@link Converter} */
class ConverterTest {

  /** Test method for {@link Converter#toUserMessageDto(UserMessageOrBuilder)} */
  @Test
  void testToUserMessageDto() {
    URI address = URI.create("UDP://127.0.0.1:9999");
    UserMessageDto userMessage =
        Converter.toUserMessageDto(
            UserMessage.newBuilder()
                .setAddress(
                    Address.newBuilder()
                        .setTransport(Transport.valueOf(address.getScheme()))
                        .setDestination(address.getHost())
                        .setQualifier(Integer.toString(address.getPort())))
                .setPayload(ByteString.copyFromUtf8("Test payload testToUserMessageDto"))
                .setMessageId("testToUserMessageDto")
                .setTransactionClass(TransactionClass.CLASS_2)
                .setVehicleIdentifier(19999L)
                .setWtpVersion(WtpVersion.VERSION_2));
    assertThat(userMessage)
        .describedAs("UserMessageDto")
        .isNotNull()
        .hasFieldOrPropertyWithValue("address", address)
        .hasFieldOrPropertyWithValue(
            "payload", "Test payload testToUserMessageDto".getBytes(StandardCharsets.UTF_8))
        .hasFieldOrPropertyWithValue("messageId", "testToUserMessageDto")
        .hasFieldOrPropertyWithValue("transactionClass", CLASS_2)
        .hasFieldOrPropertyWithValue("vehicleId", 19999L)
        .hasFieldOrPropertyWithValue("wtpVersion", VERSION_2);
  }

  /** Test method for {@link Converter#toNetworkMessageDto(NetworkMessageOrBuilder)} */
  @Test
  void testToNetworkMessageDto() {
    URI address = URI.create("SMS://0046768123123");
    NetworkMessageDto networkMessage =
        Converter.toNetworkMessageDto(
            NetworkMessage.newBuilder()
                .setAddress(
                    Address.newBuilder()
                        .setTransport(Transport.valueOf(address.getScheme()))
                        .setDestination(address.getHost()))
                .setPayload(ByteString.copyFromUtf8("Test payload testToNetworkMessageDto"))
                .setMessageId("testToNetworkMessageDto"));
    assertThat(networkMessage)
        .describedAs("NetworkMessageDto")
        .isNotNull()
        .hasFieldOrPropertyWithValue("address", address)
        .hasFieldOrPropertyWithValue(
            "payload", "Test payload testToNetworkMessageDto".getBytes(StandardCharsets.UTF_8))
        .hasFieldOrPropertyWithValue("messageId", "testToNetworkMessageDto");
  }

  @Test
  /** Test method for {@link Converter#toMtStatus(UserStatusDto)} with {@link Status#DELIVERED} */
  void testToMtStatusMessageDelivered() {
    URI address = URI.create("UDP://*******:9999");
    Tuple2<MtStatus, Map<String, String>> statusMessageTuple =
        Converter.toMtStatus(
            UserStatusDto.builder()
                .withDelivered(true)
                .withMessageId("testToMtStatusMessageDelivered")
                .withAddress(address)
                .build());
    assertThat(statusMessageTuple.getT1())
        .describedAs("MtStatusMessage")
        .isNotNull()
        .hasFieldOrPropertyWithValue("status", Status.DELIVERED)
        .hasFieldOrPropertyWithValue("messageId", "testToMtStatusMessageDelivered")
        .hasFieldOrPropertyWithValue("transport", Transport.UDP);
  }

  /** Test method for {@link Converter#toMtStatus(UserStatusDto)} with {@link Status#THROTTLED} */
  @Test
  void testToMtStatusMessageCanceled() {
    URI address = URI.create("UDP://*********:89");
    Tuple2<MtStatus, Map<String, String>> statusMessageTuple =
        Converter.toMtStatus(
            UserStatusDto.builder()
                .withDelivered(false)
                .withAbortCode(AbortCode.TCE_PROVIDER_CAPACITY_EXCEEDED)
                .withMessageId("testToMtStatusMessageThrottled")
                .withAddress(address)
                .build());
    assertThat(statusMessageTuple.getT1())
        .describedAs("MtStatusMessage")
        .isNotNull()
        .hasFieldOrPropertyWithValue("status", Status.THROTTLED)
        .hasFieldOrPropertyWithValue("messageId", "testToMtStatusMessageThrottled")
        .hasFieldOrPropertyWithValue("transport", Transport.VPN);
  }

  /** Test method for {@link Converter#toMtStatus(UserStatusDto)} with {@link Status#CANCELED} */
  @Test
  void testToMtStatusMessageTimeout() {
    URI address = URI.create("VPN://*********:8080");
    Tuple2<MtStatus, Map<String, String>> statusMessageTuple =
        Converter.toMtStatus(
            UserStatusDto.builder()
                .withDelivered(false)
                .withAbortCode(AbortCode.TCE_PROVIDER_NO_RESPONSE)
                .withMessageId("testToMtStatusMessageCanceled")
                .withAddress(address)
                .build());
    assertThat(statusMessageTuple.getT1())
        .describedAs("MtStatusMessage")
        .isNotNull()
        .hasFieldOrPropertyWithValue("status", Status.CANCELED)
        .hasFieldOrPropertyWithValue("messageId", "testToMtStatusMessageCanceled")
        .hasFieldOrPropertyWithValue("transport", Transport.VPN);
  }

  /** Test method for {@link Converter#toMtStatus(UserStatusDto)} with {@link Status#REJECTED} */
  @Test
  void testToMtStatusMessageRejected() {
    URI address = URI.create("UDP://********:9999");
    Tuple2<MtStatus, Map<String, String>> statusMessageTuple =
        Converter.toMtStatus(
            UserStatusDto.builder()
                .withDelivered(false)
                .withAbortCode(AbortCode.TGW_PROVIDER_MESSAGE_TOO_LARGE)
                .withMessageId("testToMtStatusMessageRejected")
                .withAddress(address)
                .build());
    assertThat(statusMessageTuple.getT1())
        .describedAs("MtStatusMessage")
        .isNotNull()
        .hasFieldOrPropertyWithValue("status", Status.REJECTED)
        .hasFieldOrPropertyWithValue("messageId", "testToMtStatusMessageRejected")
        .hasFieldOrPropertyWithValue("transport", Transport.UDP);
  }
}
