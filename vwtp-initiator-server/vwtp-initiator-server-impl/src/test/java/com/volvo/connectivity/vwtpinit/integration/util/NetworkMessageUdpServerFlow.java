package com.volvo.connectivity.vwtpinit.integration.util;

import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.responder.NetworkMessageFlow;
import io.netty.buffer.ByteBuf;
import io.netty.channel.socket.DatagramPacket;
import java.net.InetSocketAddress;
import java.net.URI;
import java.util.Locale;
import java.util.Map;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.http.client.ReactorResourceFactory;
import reactor.core.scheduler.Scheduler;
import reactor.netty.Connection;
import reactor.netty.resources.LoopResources;
import reactor.netty.udp.UdpServer;
import reactor.util.Logger;
import reactor.util.Loggers;
import reactor.util.context.Context;

/** A mock flow that listens for VWTP packets over UDP */
public class NetworkMessageUdpServerFlow implements DisposableBean {

  private static final Logger logger = Loggers.getLogger(NetworkMessageUdpServerFlow.class);

  private NetworkMessageFlow networkMessageFlow;
  private Connection connection;

  /**
   * Constructor
   *
   * @param networkMessageFlow instance of {@link NetworkMessageFlow}
   * @param schedulerMap instance of {@link Map}&lt;{@link Thread}, {@link Scheduler}&gt;
   * @param resourceFactory instance of {@link ReactorResourceFactory}
   */
  public NetworkMessageUdpServerFlow(
      NetworkMessageFlow networkMessageFlow,
      Map<Thread, Scheduler> schedulerMap,
      final ReactorResourceFactory resourceFactory) {
    this.networkMessageFlow = networkMessageFlow;

    connection =
        UdpServer.create()
            .wiretap(this.getClass().getName())
            .host("127.0.0.1")
            .port(20_000)
            .runOn(resourceFactory.getLoopResources().onServer(LoopResources.DEFAULT_NATIVE))
            .handle(
                (inbound, outbound) ->
                    inbound
                        .receiveObject()
                        .cast(DatagramPacket.class)
                        .map(this::toNetworkMessageDto)
                        .transform(this.networkMessageFlow)
                        .doOnError(
                            throwable -> logger.error(throwable.getLocalizedMessage(), throwable))
                        .contextWrite(
                            Context.of(Scheduler.class, schedulerMap.get(Thread.currentThread()))))
            .bindNow();
  }

  /**
   * Convert {@link DatagramPacket} to {@link NetworkMessageDto}
   *
   * @param datagramPacket instance of {@link DatagramPacket}
   * @return instance of {@link NetworkMessageDto}
   */
  private NetworkMessageDto toNetworkMessageDto(DatagramPacket datagramPacket) {
    InetSocketAddress address = datagramPacket.recipient();
    ByteBuf byteBufContent = datagramPacket.content();
    byte[] payload = new byte[byteBufContent.readableBytes()];
    byteBufContent.readBytes(payload);
    return NetworkMessageDto.builder()
        .withAddress(
            URI.create(
                String.format(
                    Locale.ENGLISH, "UDP://%s:%d", address.getHostString(), address.getPort())))
        .withPayload(payload)
        .build();
  }

  @Override
  public void destroy() throws Exception {
    connection.disposeNow();
  }
}
