package com.volvo.connectivity.vwtpinit.integration.util;

import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.vwtpinit.conf.AppConfig;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.ServiceName;
import com.volvo.tisp.tce.discovery.ServiceNameBuilder;
import com.volvo.tisp.vwtp.configuration.ResponderConfiguration;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import java.util.concurrent.TimeUnit;
import org.apache.curator.x.discovery.DownInstancePolicy;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.apache.curator.x.discovery.ServiceProvider;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import({
  AppConfig.class,
  ResponderConfiguration.class,
  NetworkMessageUdpServerFlow.class,
  NetworkMessageHttpClientFlow.class,
  UserAckFlow.class
})
public class TestConfig {

  @Bean(initMethod = "start", destroyMethod = "close")
  ServiceProvider<InstanceDetails> ackServiceProvider(
      final ServiceDiscovery<InstanceDetails> serviceDiscovery) {
    final Config config = ConfigFactory.getConfig();
    ServiceName serviceName =
        new ServiceNameBuilder()
            .setEnvironment(config.getEnvironmentId())
            .setSite(config.getSite())
            .setSolution(config.getSolution())
            .setName(ServiceConstant.VWTP_INITIATOR_RESPONSE_SERVICE)
            .build();
    return serviceDiscovery
        .serviceProviderBuilder()
        .serviceName(serviceName.getZNodeForService())
        .downInstancePolicy(new DownInstancePolicy(1, TimeUnit.SECONDS, 10))
        .build();
  }

  /**
   * exposes {@link UserAckFlow} as "outgoingResponderUserMessageFlow"
   *
   * @param userAckFlow instance of {@link UserAckFlow}
   * @return instance of {@link FlowComposer}&lt;{@link UserMessageDto}, {@link Void};&gt;
   */
  @Bean
  FlowComposer<UserMessageDto, Void> outgoingResponderUserMessageFlow(UserAckFlow userAckFlow) {
    return userAckFlow;
  }

  /**
   * exposes {@link NetworkMessageHttpClientFlow} as "outgoingResponderNetworkMessageFlow"
   *
   * @param httpClientFlow instance of {@link NetworkMessageHttpClientFlow}
   * @return instance of {@link FlowComposer}&lt;{@link NetworkMessageDto}, {@link Void};&gt;
   */
  @Bean
  FlowComposer<NetworkMessageDto, Void> outgoingResponderNetworkMessageFlow(
      NetworkMessageHttpClientFlow httpClientFlow) {
    return httpClientFlow;
  }
}
