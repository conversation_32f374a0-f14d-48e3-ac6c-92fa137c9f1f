package com.volvo.connectivity.vwtpinit.integration;

import com.volvo.connectivity.vwtpinit.integration.util.TestcontainersBase;
import org.junit.jupiter.api.Test;

/** Spring actuator health check endpoint integration test */
class HealthCheckIT extends TestcontainersBase {

  /** Test calling /actuator/health on a running spring boot application */
  @Test
  void springActuatorHealthTest() {
    WEB_TEST_CLIENT.get().uri("/actuator/health").exchange().expectStatus().is2xxSuccessful();
  }
}
