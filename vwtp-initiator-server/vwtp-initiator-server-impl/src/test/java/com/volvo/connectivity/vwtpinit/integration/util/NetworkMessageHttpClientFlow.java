package com.volvo.connectivity.vwtpinit.integration.util;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.proto.Address;
import com.volvo.connectivity.proto.NetworkMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import java.net.URI;
import org.apache.curator.x.discovery.ServiceInstance;
import org.apache.curator.x.discovery.ServiceProvider;
import org.reactivestreams.Publisher;
import org.springframework.lang.Nullable;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.Exceptions;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * A mock flow that sends {@link NetworkMessage}'s to {@link
 * ServiceConstant#VWTP_INITIATOR_RESPONSE_SERVICE} service
 */
public class NetworkMessageHttpClientFlow implements FlowComposer<NetworkMessageDto, Void> {

  private ServiceProvider<InstanceDetails> ackServiceProvider;
  private WebClient webClient;

  /**
   * Constructor
   *
   * @param webClientBuilder instance of {@link
   *     org.springframework.web.reactive.function.client.WebClient.Builder}
   * @param ackServiceProvider instance of {@link ServiceProvider}&lt;{@link InstanceDetails}&gt;
   */
  public NetworkMessageHttpClientFlow(
      WebClient.Builder webClientBuilder, ServiceProvider<InstanceDetails> ackServiceProvider) {
    this.ackServiceProvider = ackServiceProvider;
    this.webClient = webClientBuilder.build();
  }

  @Override
  public Publisher<Void> apply(Flux<NetworkMessageDto> flow) {
    return flow.map(this::toNetworkMessage).flatMap(this::sendNetworkMessage);
  }

  /**
   * Send {@link NetworkMessage} via HTTP
   *
   * @param networkMessage instance of {@link NetworkMessage}
   * @return instance of {@link Publisher}&lt;{@link Void}&gt;
   */
  private Publisher<Void> sendNetworkMessage(NetworkMessage networkMessage) {
    return Mono.fromSupplier(this::getServiceInstance)
        .switchIfEmpty(Mono.error(Exceptions::failWithRejected))
        .map(ServiceInstance::buildUriSpec)
        .map(URI::create)
        .flatMap(
            uri ->
                webClient
                    .post()
                    .uri(uri)
                    .bodyValue(networkMessage)
                    .retrieve()
                    .toBodilessEntity()
                    .then());
  }

  /**
   * Returns {@link ServiceInstance} from {@link ServiceProvider}
   *
   * @return instance of {@link ServiceInstance}&lt;{@link InstanceDetails}&gt; or null if not
   *     found, or error occurred
   */
  private @Nullable ServiceInstance<InstanceDetails> getServiceInstance() {
    try {
      return ackServiceProvider.getInstance();
    } catch (final Exception e) {
      throw Exceptions.propagate(e);
    }
  }

  /**
   * Convert {@link NetworkMessageDto} to {@link NetworkMessage}
   *
   * @param networkMessage instance of {@link NetworkMessageDto}
   * @return instance of {@link NetworkMessage}
   */
  private NetworkMessage toNetworkMessage(NetworkMessageDto networkMessage) {
    return NetworkMessage.newBuilder()
        .setAddress(
            Address.newBuilder()
                .setTransport(Transport.valueOf(networkMessage.getAddress().getScheme()))
                .setDestination(networkMessage.getAddress().getHost())
                .setQualifier(Integer.toString(networkMessage.getAddress().getPort())))
        .setPayload(ByteString.copyFrom(networkMessage.getPayload()))
        .setMessageId(networkMessage.getMessageId())
        .build();
  }
}
