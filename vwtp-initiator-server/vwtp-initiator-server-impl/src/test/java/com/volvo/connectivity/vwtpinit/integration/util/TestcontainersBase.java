package com.volvo.connectivity.vwtpinit.integration.util;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.tisp.framework.web.client.TispHeadersExchangeFilter;
import com.volvo.tisp.subscriptionrepository.client.Destination;
import com.volvo.tisp.subscriptionrepository.client.SubscriptionStubber;
import io.netty.handler.logging.LogLevel;
import io.opentelemetry.api.GlobalOpenTelemetry;
import java.io.File;
import java.util.concurrent.atomic.AtomicReference;
import org.junit.jupiter.api.BeforeAll;
import org.junit.platform.launcher.LauncherSession;
import org.junit.platform.launcher.LauncherSessionListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.containers.DockerComposeContainer;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.containers.output.WaitingConsumer;
import org.testcontainers.containers.wait.strategy.Wait;
import reactor.blockhound.BlockHound;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

@SuppressWarnings("resource")
@SpringBootTest(
    classes = TestConfig.class,
    properties = "server.port=" + TestcontainersBase.SERVER_PORT,
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class TestcontainersBase implements LauncherSessionListener, ApplicationContextAware {
  private static final Logger log = LoggerFactory.getLogger(TestcontainersBase.class);
  private static final AtomicReference<ConfigurableApplicationContext> contextReference =
      new AtomicReference<>();

  protected static final String SERVER_PORT = "20690";
  protected static final DockerComposeContainer<?> DOCKER_COMPOSE;
  protected static final String CONTAINER_HOST;
  protected static final WebTestClient WEB_TEST_CLIENT;

  static {
    DOCKER_COMPOSE =
        new DockerComposeContainer<>(
                new File(TestcontainersBase.class.getResource("/docker-compose.yaml").getPath()))
            .withLogConsumer("artemis", new WaitingConsumer())
            .withLogConsumer("influxdb", new WaitingConsumer())
            .withLogConsumer("wiremock", new Slf4jLogConsumer(LoggerFactory.getLogger("wiremock")))
            .withLogConsumer("zoo", new WaitingConsumer())
            .waitingFor("artemis", Wait.forLogMessage(".*Console available at.*", 1))
            .waitingFor("influxdb", Wait.forLogMessage(".*Listening for signals.*", 1))
            .waitingFor("wiremock", Wait.forLogMessage(".*verbose.*", 1))
            .waitingFor("zoo", Wait.forLogMessage(".*PrepRequestProcessor.*", 1))
            .withLocalCompose(true);

    /**
     * Would skip starting docker-compose so you could run docker-compose separately and then build
     * with "mvn clean install -DskipDocker"
     */
    if (System.getProperty("skipDocker") == null) {
      DOCKER_COMPOSE.start();
    }

    final String containerHost = DOCKER_COMPOSE.getServiceHost(null, null);
    CONTAINER_HOST = "localhost".equals(containerHost) ? "127.0.0.1" : containerHost;

    log.info("Container Host: {}", CONTAINER_HOST);

    System.setProperty("container.host", CONTAINER_HOST);
    System.setProperty("VGTZOOKEEPER", CONTAINER_HOST + ":3181");
    System.setProperty("VGTCOMPVERSION", "0-SNAPSHOT");

    WireMock.configureFor(CONTAINER_HOST, 3080);

    WEB_TEST_CLIENT =
        WebTestClient.bindToServer(
                new ReactorClientHttpConnector(
                    HttpClient.create()
                        .wiretap("web.test.client", LogLevel.INFO, AdvancedByteBufFormat.TEXTUAL)))
            .filter(new TispHeadersExchangeFilter())
            .baseUrl("http://127.0.0.1:" + SERVER_PORT)
            .build();

    WEB_TEST_CLIENT
        .post()
        .uri(
            "http://{host}:3086/query?q=CREATE DATABASE \"connectivity_services\" WITH NAME \"30days\"",
            CONTAINER_HOST)
        .exchange()
        .expectStatus()
        .is2xxSuccessful();

    stubSubrepo();
  }

  private static void stubSubrepo() {
    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(ServiceConstant.MT_STATUS_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                ServiceConstant.MT_STATUS_MESSAGE_TYPE, "1.0", "activemq:queue:MT.STATUSES.IN"));
  }

  @BeforeAll
  private static void beforeAll() {
    BlockHound.install(
        builder ->
            builder
                .allowBlockingCallsInside("com.volvo.tisp.vwtp.util.InitiatorMetrics", "<clinit>")
                .allowBlockingCallsInside("com.volvo.tisp.vwtp.util.ResponderMetrics", "<clinit>")
                .allowBlockingCallsInside(
                    "com.volvo.connectivity.vwtpinit.util.Converter", "<clinit>"));

    GlobalOpenTelemetry.resetForTest();
  }

  @Override
  public void setApplicationContext(ApplicationContext context) throws BeansException {
    if (context instanceof ConfigurableApplicationContext configurableContext) {
      TestcontainersBase.contextReference.set(configurableContext);
    }
  }

  @Override
  public void launcherSessionClosed(LauncherSession session) {
    ConfigurableApplicationContext context = TestcontainersBase.contextReference.get();
    if (context != null && context.isActive()) {
      log.info("Stopping Spring Boot");
      context.close();
    }
    log.info("Stopping Testcontainers");
    DOCKER_COMPOSE.stop();
  }
}
