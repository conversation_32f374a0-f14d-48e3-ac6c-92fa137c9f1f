package com.volvo.connectivity.vwtpinit.util;

import static org.junit.jupiter.api.Assertions.*;

import com.volvo.connectivity.proto.Address;
import com.volvo.connectivity.proto.NetworkMessage;
import com.volvo.connectivity.proto.TransactionClass;
import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.connectivity.proto.WtpVersion;
import org.junit.jupiter.api.Test;
import org.springframework.web.server.ServerWebInputException;

/** Test class for {@link Validator} */
class ValidatorTest {

  /** Test method for {@link Validator#checkUri(Address, String)} */
  @Test
  void testCheckUri() {
    Address.Builder uriBuilder =
        Address.newBuilder().setTransport(Transport.SAT).setDestination("someSateliteID");
    assertDoesNotThrow(() -> Validator.checkUri(uriBuilder.build(), "Address.uri"));
    uriBuilder.setTransport(Transport.SMS).setDestination("0046768123123");
    assertDoesNotThrow(() -> Validator.checkUri(uriBuilder.build(), "Address.uri"));
    uriBuilder
        .setTransport(Transport.UDP)
        .setDestination("127.0.0.1-hostname")
        .setQualifier("9999");
    assertDoesNotThrow(() -> Validator.checkUri(uriBuilder.build(), "Address.uri"));
  }

  /** Test method for {@link Validator#checkUri(Address, String)} with bad port number */
  @Test
  void testCheckUri_badPort() {
    Address uri =
        Address.newBuilder()
            .setTransport(Transport.UDP)
            .setDestination("127.0.0.1-hostname")
            .setQualifier("0")
            .build();
    assertThrows(ServerWebInputException.class, () -> Validator.checkUri(uri, "Address.uri"));
  }

  /** Test method for {@link Validator#checkUri(Address, String)} with unrecognized protocol */
  @Test
  void testCheckUri_badTransport() {
    Address uri =
        Address.newBuilder()
            .setTransportValue(-10)
            .setDestination("127.0.0.1-hostname")
            .setQualifier("9999")
            .build();
    assertThrows(ServerWebInputException.class, () -> Validator.checkUri(uri, "Address.uri"));
  }

  /** Test method for {@link Validator#checkUri(Address, String)} with invalid host */
  @Test
  void testCheckUri_badHost() {
    Address uri =
        Address.newBuilder()
            .setTransport(Transport.UDP)
            .setDestination(".127.0.0.1-hostname-")
            .setQualifier("9999")
            .build();
    assertThrows(ServerWebInputException.class, () -> Validator.checkUri(uri, "Address.uri"));
  }

  /** Test method for {@link Validator#checkNetworkMessage(NetworkMessage)}. */
  @Test
  void testCheckNetworkMessage() {
    NetworkMessage networkMessage =
        NetworkMessage.newBuilder()
            .setAddress(
                Address.newBuilder()
                    .setTransport(Transport.UDP)
                    .setDestination("127.0.0.1-hostname")
                    .setQualifier("9999"))
            .setMessageId("abcdef0123456789ABCDEF0123456789")
            .build();
    assertDoesNotThrow(() -> Validator.checkNetworkMessage(networkMessage));
  }

  /** Test method for {@link Validator#checkUserMessage(UserMessage)} */
  @Test
  void testCheckUserMessage() {
    UserMessage userMessage =
        UserMessage.newBuilder()
            .setAddress(
                Address.newBuilder()
                    .setTransport(Transport.UDP)
                    .setDestination("127.0.0.1-hostname")
                    .setQualifier("9999"))
            .setMessageId("abcdef0123456789ABCDEF0123456789")
            .setTransactionClass(TransactionClass.CLASS_2)
            .setWtpVersion(WtpVersion.VERSION_2)
            .build();
    assertDoesNotThrow(() -> Validator.checkUserMessage(userMessage));
  }

  /** Test method for {@link Validator#checkUserMessage(UserMessage)} with invalid TrackingId */
  @Test
  void testCheckUserMessage_badTrackingId() {
    UserMessage userMessage =
        UserMessage.newBuilder()
            .setAddress(
                Address.newBuilder()
                    .setTransport(Transport.UDP)
                    .setDestination("127.0.0.1-hostname")
                    .setQualifier("9999"))
            .setTransactionClass(TransactionClass.CLASS_2)
            .setWtpVersion(WtpVersion.VERSION_2)
            .build();
    assertThrows(ServerWebInputException.class, () -> Validator.checkUserMessage(userMessage));
  }

  /**
   * Test method for {@link Validator#checkUserMessage(UserMessage)} with unknown TransactionClass
   */
  @Test
  void testCheckUserMessage_badTransactionClass() {
    UserMessage userMessage =
        UserMessage.newBuilder()
            .setAddress(
                Address.newBuilder()
                    .setTransport(Transport.UDP)
                    .setDestination("127.0.0.1-hostname")
                    .setQualifier("9999"))
            .setMessageId("abcdef0123456789ABCDEF0123456789")
            .setTransactionClassValue(-6)
            .setWtpVersion(WtpVersion.VERSION_2)
            .build();
    assertThrows(ServerWebInputException.class, () -> Validator.checkUserMessage(userMessage));
  }

  /** Test method for {@link Validator#checkUserMessage(UserMessage)} with unknown WtpVersion */
  @Test
  void testCheckUserMessage_badWtpVersion() {
    UserMessage userMessage =
        UserMessage.newBuilder()
            .setAddress(
                Address.newBuilder()
                    .setTransport(Transport.UDP)
                    .setDestination("127.0.0.1-hostname")
                    .setQualifier("9999"))
            .setMessageId("abcdef0123456789ABCDEF0123456789")
            .setTransactionClass(TransactionClass.CLASS_2)
            .setWtpVersionValue(-5)
            .build();
    assertThrows(ServerWebInputException.class, () -> Validator.checkUserMessage(userMessage));
  }
}
