<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>147</version>
    <relativePath/>
  </parent>

  <groupId>com.volvo.connectivity</groupId>
  <artifactId>vwtp-initiator-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>VWTP Initiator :: Server</name>
  <description>${project.name}</description>
  <url>https://vgcs-confluence.it.volvo.net/x/7okmD</url>

  <properties>
    <component.long.name>${project.artifactId}</component.long.name>
    <component.short.name>vwtpinit</component.short.name>

    <maven.compiler.failOnWarning>true</maven.compiler.failOnWarning>
    <nullaway.level>ERROR</nullaway.level>
    <spotbugs.failOnError>true</spotbugs.failOnError>

    <tisp-dependencies.version>146</tisp-dependencies.version>
    <connectivity-api.version>25</connectivity-api.version>
    <subscriptionrepository-client.version>657</subscriptionrepository-client.version>
    <tce-common-discovery.version>332</tce-common-discovery.version>
    <tce-module-scooter-agent.version>1789</tce-module-scooter-agent.version>
    <vc-influxdb-event-reporter-lib.version>46</vc-influxdb-event-reporter-lib.version>
    <vwtp-impl.version>752</vwtp-impl.version>

    <blockhound.version>1.0.10.RELEASE</blockhound.version>
    <unit-test-lib.version>59</unit-test-lib.version>

    <deploy.engine.jar.version>243</deploy.engine.jar.version>
    <deploy-engine-bash-deployer.version>44</deploy-engine-bash-deployer.version>
    <deploy.engine.maven.plugin.version>46</deploy.engine.maven.plugin.version>
    <deploy.engine.version>57</deploy.engine.version>
    <jdk17.version>17</jdk17.version>
    <supervisor3x.version>51</supervisor3x.version>
  </properties>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-impl</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-test-util</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.vwtp</groupId>
        <artifactId>vwtp-impl</artifactId>
        <version>${vwtp-impl.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-common-discovery</artifactId>
        <version>${tce-common-discovery.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-influxdb-event-reporter-lib</artifactId>
        <version>${vc-influxdb-event-reporter-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.connectivity</groupId>
        <artifactId>connectivity-api</artifactId>
        <version>${connectivity-api.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-module-scooter-agent</artifactId>
        <version>${tce-module-scooter-agent.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>unit-test-lib</artifactId>
        <version>${unit-test-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.tools</groupId>
        <artifactId>blockhound</artifactId>
        <version>${blockhound.version}</version>
      </dependency>

      <dependency>
        <groupId>com.wirelesscar.framework.deploy-engine</groupId>
        <artifactId>deploy-engine-jar-deployer</artifactId>
        <version>${deploy.engine.jar.version}</version>
        <classifier>bundle</classifier>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.framework.deploy-engine</groupId>
        <artifactId>deploy-engine-bash-deployer</artifactId>
        <version>${deploy-engine-bash-deployer.version}</version>
        <classifier>bundle</classifier>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.framework.deploy-engine</groupId>
        <artifactId>deploy-engine</artifactId>
        <version>${deploy.engine.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.supervisord</groupId>
        <artifactId>supervisor3x</artifactId>
        <version>${supervisor3x.version}</version>
        <type>npm</type>
      </dependency>
      <dependency>
        <groupId>net.java.jdk</groupId>
        <artifactId>jdk17</artifactId>
        <version>${jdk17.version}</version>
        <type>npm</type>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>com.wirelesscar.framework.deploy-engine</groupId>
          <artifactId>deploy-engine-maven-plugin</artifactId>
          <version>${deploy.engine.maven.plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <!-- +=============================================== -->
  <!-- | Section 3: Module definitions -->
  <!-- +=============================================== -->

  <profiles>
    <profile>
      <id>default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>vwtp-initiator-server-app</module>
        <module>vwtp-initiator-server-impl</module>
      </modules>
    </profile>
    <profile>
      <id>deployable-assembly</id>
      <activation>
        <property>
          <name>deployable-assembly</name>
        </property>
      </activation>
      <modules>
        <module>deployable-assembly</module>
      </modules>
    </profile>
    <profile>
      <id>component-tests</id>
    </profile>
  </profiles>
</project>
