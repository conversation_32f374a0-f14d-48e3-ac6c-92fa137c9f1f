version: '3.3'
services:
  influxdb:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/influxdb:1.8.6
    ports:
      - 8086:8086
  postgres:
    image:  artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/postgres:latest
    environment:
      - POSTGRES_DB=postgres
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=postgres
    ports:
      - 5432:5432
  zoo:
    image: artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/zookeeper
    ports:
      - 2181:2181/tcp