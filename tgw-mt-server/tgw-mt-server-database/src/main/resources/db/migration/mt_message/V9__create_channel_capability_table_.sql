CREATE TABLE channel_capability
(
    channel_capability_id   BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
    vehicle_lock_id         BIGINT                      NOT NULL,
    channel                 VARCHAR(32)                 NOT NULL,
    is_enabled              BOOLEAN                     NOT NULL,
    created_at              TIMESTAMP(3) WITH TIME ZONE NOT NULL,
    updated_at              TIMESTAMP(3) WITH TIME ZONE NOT NULL,

    UNIQUE (vehicle_lock_id, channel),
    CONSTRAINT fk_channel_capability_vpi_lock_id FOREIGN KEY (vehicle_lock_id) REFERENCES vehicle_lock (vehicle_lock_id) ON DELETE CASCADE
);