CREATE TABLE vehicle_lock (
  vehicle_lock_id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  vehicle_lock_created TIMESTAMP (3) WITH TIME ZONE NOT NULL,
  vpi VARCHAR(32) NOT NULL,

  UNIQUE (vpi)
);

CREATE TABLE mt_message (
  mt_message_id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  vehicle_lock_id BIGINT NOT NULL,
  mt_message_created TIMESTAMP(3) WITH TIME ZONE NOT NULL,
  tid VARCHAR(32) NOT NULL,
  send_schema_name VARCHAR(64) NOT NULL,
  queue_id VARCHAR(64) NOT NULL,
  srp_destination_service INTEGER NOT NULL,
  srp_destination_version SMALLINT NOT NULL,
  srp_level_12 BOOLEAN NOT NULL,
  srp_payload BYTEA NOT NULL,
  correlation_id VARCHAR(64),
  reply_to <PERSON><PERSON><PERSON><PERSON>(200),

  CONSTRAINT fk_mt_message_vehicle_lock_id FOREIGN KEY (vehicle_lock_id) REFERENCES vehicle_lock (vehicle_lock_id) ON DELETE CASCADE
);

CREATE INDEX mt_message_vehicle_lock_id_created ON mt_message (vehicle_lock_id, mt_message_created);

CREATE TABLE active_mt_message (
  active_mt_message_id BIGINT PRIMARY KEY GENERATED ALWAYS AS IDENTITY,
  mt_message_id BIGINT NOT NULL,
  active_mt_message_created TIMESTAMP(3) WITH TIME ZONE NOT NULL,
  active_mt_message_updated TIMESTAMP(3) WITH TIME ZONE NOT NULL,
  send_schema_step_id SMALLINT NOT NULL,
  retry_attempt SMALLINT NOT NULL,
  timeout TIMESTAMP(3) WITH TIME ZONE NOT NULL,

  UNIQUE (mt_message_id),
  CONSTRAINT fk_active_mt_message_mt_message_id FOREIGN KEY (mt_message_id) REFERENCES mt_message (mt_message_id) ON DELETE CASCADE
);

CREATE INDEX active_mt_message_timeout ON active_mt_message (timeout);
