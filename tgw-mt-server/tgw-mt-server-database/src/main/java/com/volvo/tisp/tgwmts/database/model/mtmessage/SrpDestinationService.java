package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SrpDestinationService {
  public static final int MAX_VALUE = 65_535;

  private final int srpDestinationServiceValue;

  private SrpDestinationService(int srpDestinationServiceValue) {
    this.srpDestinationServiceValue = srpDestinationServiceValue;
  }

  /**
   * @throws IllegalArgumentException if {@code srpDestinationServiceValue} is less than zero or greater than {@value #MAX_VALUE}.
   */
  public static SrpDestinationService ofInt(int srpDestinationServiceValue) {
    Validate.notNegativeAndNotGreaterThan(srpDestinationServiceValue, MAX_VALUE, "srpDestinationServiceValue");

    return new SrpDestinationService(srpDestinationServiceValue);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    SrpDestinationService other = (SrpDestinationService) object;
    return srpDestinationServiceValue == other.srpDestinationServiceValue;
  }

  @Override
  public int hashCode() {
    return Integer.hashCode(srpDestinationServiceValue);
  }

  public int toInt() {
    return srpDestinationServiceValue;
  }

  @Override
  public String toString() {
    return Integer.toString(srpDestinationServiceValue);
  }
}
