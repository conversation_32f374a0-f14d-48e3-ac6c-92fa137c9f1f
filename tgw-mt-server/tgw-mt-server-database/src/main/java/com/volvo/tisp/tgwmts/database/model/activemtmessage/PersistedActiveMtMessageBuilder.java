package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class PersistedActiveMtMessageBuilder {
  private ActiveMtMessage activeMtMessage;
  private ActiveMtMessageId activeMtMessageId;
  private Instant created;
  private Instant updated;

  public PersistedActiveMtMessage build() {
    Validate.notNull(activeMtMessage, "activeMtMessage");
    Validate.notNull(activeMtMessageId, "activeMtMessageId");
    Validate.notNull(created, "created");
    Validate.notNull(updated, "updated");

    return new PersistedActiveMtMessage(this);
  }

  public ActiveMtMessage getActiveMtMessage() {
    return activeMtMessage;
  }

  public ActiveMtMessageId getActiveMtMessageId() {
    return activeMtMessageId;
  }

  public Instant getCreated() {
    return created;
  }

  public Instant getUpdated() {
    return updated;
  }

  public PersistedActiveMtMessageBuilder setActiveMtMessage(ActiveMtMessage activeMtMessage) {
    Validate.notNull(activeMtMessage, "activeMtMessage");

    this.activeMtMessage = activeMtMessage;
    return this;
  }

  public PersistedActiveMtMessageBuilder setActiveMtMessageId(ActiveMtMessageId activeMtMessageId) {
    Validate.notNull(activeMtMessageId, "activeMtMessageId");

    this.activeMtMessageId = activeMtMessageId;
    return this;
  }

  public PersistedActiveMtMessageBuilder setCreated(Instant created) {
    Validate.notNull(created, "created");

    this.created = created;
    return this;
  }

  public PersistedActiveMtMessageBuilder setUpdated(Instant updated) {
    Validate.notNull(updated, "updated");

    this.updated = updated;
    return this;
  }
}
