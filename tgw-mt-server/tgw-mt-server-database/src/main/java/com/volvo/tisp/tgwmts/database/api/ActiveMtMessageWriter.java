package com.volvo.tisp.tgwmts.database.api;

import java.sql.Statement;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

public interface ActiveMtMessageWriter extends ActiveMtMessageReader, Transaction {
  /**
   * Deletes the corresponding row from the {@code mt_message} table plus all rows from the {@code active_mt_message} table that reference it due to
   * {@code ON DELETE CASCADE}.
   *
   * @return the number of deleted rows in the {@code mt_message} table.
   */
  int deleteMtMessageById(MtMessageId mtMessageId);

  /**
   * Deletes the corresponding rows from the {@code mt_message} table plus all rows from the {@code active_mt_message} table that reference it due to
   * {@code ON DELETE CASCADE}.
   *
   * @return an array integers corresponding to the count of rows effected per statement in the batch update.
   */
  int[] deleteMtMessageByIds(List<MtMessageId> mtMessageIds);

  /**
   * Deletes the matched row(s) from the {@code mt_message} table, plus all the referenced rows from the {@code active_mt_message} table.
   *
   * @return the number of deleted rows in the {@code mt_message} table.
   */
  int deleteMtMessagesByVpiAndQueueId(Vpi vpi, QueueId queueId);

  /**
   * Deletes the corresponding row from the {@code vehicle_lock} table plus all rows from the {@code mt_message} and {@code active_mt_message} tables that
   * reference it due to {@code ON DELETE CASCADE}.
   *
   * @return the number of deleted rows in the {@code vehicle_lock} table.
   */
  int deleteVehicleLockByVpi(Vpi vpi);

  /**
   * Finds an {@code active_mt_message} and locks its reference chain of rows. See {@link ActiveMtMessageWriter#findAndLockByVpis} for exclusive lock description
   */
  Optional<JoinedActiveMtMessage> findActiveMtMessageWithVpiLock(ActiveMtMessageId activeMtMessageId);

  /**
   * Finds all {@code active_mt_message} for the given vpi and locks its reference chain of rows. See {@link ActiveMtMessageWriter#findAndLockByVpis} for exclusive lock description
   */
  List<JoinedActiveMtMessage> findActiveMtMessagesByVpiWithVpiLock(Vpi vpi);

  /**
   * Locks and returns all matching rows. If the row(s) to be locked is currently locked by another database transaction, this method blocks until the row is
   * released by the other transaction and the exclusive lock was successfully acquired by this database transaction.
   * <p>
   * The exclusive lock on the rows is held until the current database transaction is either {@link Transaction#commitTransaction() committed} or
   * {@link Transaction#rollbackTransaction() rolled back}.
   *
   * @return list of all rows with matching unique keys (or clause)
   */
  List<PersistedVehicleLock> findAndLockByVpis(Set<Vpi> vpis);

  /**
   * Finds all {@link PersistedMtMessage} with the provided {@code vpi} and {@code queueId}
   *
   * @return a list of mt messages
   */
  List<PersistedMtMessage> findMtMessagesByVpiAndQueueId(Vpi vpi, QueueId queueId);

  /**
   * Locks and returns all rows where {@link ActiveMtMessage#getTimeout()} <= now, limited by {@code limit}. If the row to be locked is currently locked by
   * another database transaction, the row will be skipped. See {@link ActiveMtMessageWriter#findAndLockByVpis} for exclusive lock description
   *
   * @return list of at most {@code limit} {@link JoinedActiveMtMessage}
   */
  List<JoinedActiveMtMessage> findTimedOutActiveMtMessagesWithVpiLock(int limit);

  /**
   * @return the generated id for the inserted {@link ActiveMtMessage}.
   */
  Either<InsertionFailure, ActiveMtMessageId> insertActiveMtMessage(ActiveMtMessage activeMtMessage);

  /**
   * @return the generated id for the inserted {@link MtMessage}.
   */
  Either<InsertionFailure, MtMessageId> insertMtMessage(MtMessage mtMessage);

  /**
   * @return the generated id for the inserted {@link VehicleLock} if the insertion was successful.
   */
  Either<InsertionFailure, VehicleLockId> insertVehicleLock(VehicleLock vehicleLock);

  /**
   * Starts a transaction and sets a maximum time that a statement will wait to acquire a lock on a table or set of rows for the current transaction.
   *
   * @see <a href="https://www.postgresql.org/docs/current/runtime-config-client.html#GUC-LOCK-TIMEOUT">Lock Timeout</a>
   */
  void startTransactionWithLockTimeout();

  /**
   * @return the number of updated rows (see {@link Statement#executeBatch()}).
   */
  int[] updateActiveMtMessages(Collection<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters);

  /**
   * Update if a channel is available or not, to route the mt messages.
   * For example wifi is enabled or not.
   */
  void updateAssetCapabilityState(AssetCapabilityState assetCapabilityState);
}
