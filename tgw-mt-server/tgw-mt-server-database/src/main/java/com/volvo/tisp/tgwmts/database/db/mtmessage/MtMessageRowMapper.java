package com.volvo.tisp.tgwmts.database.db.mtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;
import java.util.Optional;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationVersion;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpPayload;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

public final class MtMessageRowMapper implements RowMapper<PersistedMtMessage> {
  public static final RowMapper<PersistedMtMessage> INSTANCE = new MtMessageRowMapper();

  private MtMessageRowMapper() {
    // do nothing
  }

  private static Instant getMtMessageCreated(ResultSet resultSet) throws SQLException {
    return resultSet.getTimestamp("mt_message_created").toInstant();
  }

  private static MtMessageId getMtMessageId(ResultSet resultSet) throws SQLException {
    return MtMessageId.ofLong(resultSet.getLong("mt_message_id"));
  }

  private static QueueId getQueueId(ResultSet resultSet) throws SQLException {
    return QueueId.ofString(resultSet.getString("queue_id"));
  }

  private static Optional<ReplyOption> getReplyOption(ResultSet resultSet) throws SQLException {
    String correlationIdString = resultSet.getString("correlation_id");

    if (correlationIdString == null) {
      return Optional.empty();
    }

    ReplyOptionBuilder replyOptionBuilder = new ReplyOptionBuilder()
        .setCorrelationId(CorrelationId.ofString(correlationIdString))
        .setReplyTo(ReplyTo.ofString(resultSet.getString("reply_to")));

    return Optional.of(replyOptionBuilder.build());
  }

  private static SendSchemaName getSendSchemaName(ResultSet resultSet) throws SQLException {
    return new SendSchemaName(resultSet.getString("send_schema_name"));
  }

  private static SrpDestinationService getSrpDestinationService(ResultSet resultSet) throws SQLException {
    return SrpDestinationService.ofInt(resultSet.getInt("srp_destination_service"));
  }

  private static SrpDestinationVersion getSrpDestinationVersion(ResultSet resultSet) throws SQLException {
    return SrpDestinationVersion.ofShort(resultSet.getShort("srp_destination_version"));
  }

  private static SrpPayload getSrpPayload(ResultSet resultSet) throws SQLException {
    return SrpPayload.ofImmutableByteArray(ImmutableByteArray.of(resultSet.getBytes("srp_payload")));
  }

  private static Tid getTid(ResultSet resultSet) throws SQLException {
    return Tid.ofString(resultSet.getString("tid"));
  }

  private static VehicleLockId getVehicleLockId(ResultSet resultSet) throws SQLException {
    return VehicleLockId.ofLong(resultSet.getLong("vehicle_lock_id"));
  }

  private static MtMessage mapMtMessage(ResultSet resultSet) throws SQLException {
    return new MtMessageBuilder()
        .setQueueId(getQueueId(resultSet))
        .setReplyOption(getReplyOption(resultSet))
        .setSendSchemaName(getSendSchemaName(resultSet))
        .setSrpOption(mapSrpOption(resultSet))
        .setTid(getTid(resultSet))
        .setVehicleLockId(getVehicleLockId(resultSet))
        .build();
  }

  private static SrpOption mapSrpOption(ResultSet resultSet) throws SQLException {
    return new SrpOptionBuilder()
        .setSrpDestinationService(getSrpDestinationService(resultSet))
        .setSrpDestinationVersion(getSrpDestinationVersion(resultSet))
        .setSrpLevel12(resultSet.getBoolean("srp_level_12"))
        .setSrpPayload(getSrpPayload(resultSet))
        .build();
  }

  @Override
  public PersistedMtMessage map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new PersistedMtMessageBuilder()
        .setCreated(getMtMessageCreated(resultSet))
        .setMtMessage(mapMtMessage(resultSet))
        .setMtMessageId(getMtMessageId(resultSet))
        .build();
  }
}
