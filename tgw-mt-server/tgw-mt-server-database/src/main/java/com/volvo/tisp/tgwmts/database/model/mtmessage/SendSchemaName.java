package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.util.Collection;
import java.util.Locale;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public record SendSchemaName(String value) {
  public static final SendSchemaName COMMON_HIGH = new SendSchemaName("COMMON_HIGH");
  public static final SendSchemaName COMMON_LONG = new SendSchemaName("COMMON_LONG");
  public static final SendSchemaName COMMON_LOW = new SendSchemaName("COMMON_LOW");
  public static final SendSchemaName COMMON_MID = new SendSchemaName("COMMON_MID");
  public static final SendSchemaName COMMON_NORMAL = new SendSchemaName("COMMON_NORMAL");
  public static final SendSchemaName COMMON_RSWDL = new SendSchemaName("COMMON_RSWDL");
  public static final SendSchemaName COMMON_SETUP = new SendSchemaName("COMMON_SETUP");
  public static final SendSchemaName COMMON_SHORT = new SendSchemaName("COMMON_SHORT");
  public static final SendSchemaName COMMON_VERY_HIGH = new SendSchemaName("COMMON_VERY_HIGH");
  public static final SendSchemaName DARF_CONFIG = new SendSchemaName("DARF_CONFIG");
  public static final SendSchemaName DFOL_HIGH = new SendSchemaName("DFOL_HIGH");
  public static final SendSchemaName DFOL_LOW = new SendSchemaName("DFOL_LOW");
  public static final SendSchemaName DFOL_MID = new SendSchemaName("DFOL_MID");
  public static final SendSchemaName DFOL_MID_PLUS = new SendSchemaName("DFOL_MID_PLUS");
  public static final SendSchemaName DFOL_SETUP = new SendSchemaName("DFOL_SETUP");
  public static final SendSchemaName DRUT_VERY_HIGH = new SendSchemaName("DRUT_VERY_HIGH");
  public static final SendSchemaName RDNS_MID = new SendSchemaName("RDNS_MID");
  public static final SendSchemaName RSWDL_LOW = new SendSchemaName("RSWDL_LOW");
  public static final SendSchemaName RSWDL_MID = new SendSchemaName("RSWDL_MID");
  public static final SendSchemaName SAT_ONLY = new SendSchemaName("SAT_ONLY");
  public static final SendSchemaName SMS_ONLY = new SendSchemaName("SMS_ONLY");
  public static final SendSchemaName UPTIME_VERY_HIGH = new SendSchemaName("UPTIME_VERY_HIGH");
  public static final SendSchemaName VLINK_HIGH = new SendSchemaName("VLINK_HIGH");
  public static final SendSchemaName VLINK_LOW = new SendSchemaName("VLINK_LOW");

  private static final Map<String, SendSchemaName> LEGACY_STRING_TO_NAME_MAP = legacyValues()
      .stream()
      .collect(Collectors.toMap(Object::toString, Function.identity()));

  public SendSchemaName {
    Validate.lengthBetween(value, 1, 64, "value");
  }

  public static Optional<SendSchemaName> fromLegacyString(String string) {
    Validate.notEmpty(string, "string");

    return Optional.ofNullable(LEGACY_STRING_TO_NAME_MAP.get(string.toUpperCase(Locale.ENGLISH)));
  }

  public static Collection<SendSchemaName> legacyValues() {
    return Set.of(
        COMMON_HIGH, COMMON_LONG, COMMON_LOW, COMMON_MID, COMMON_NORMAL, COMMON_RSWDL, COMMON_SETUP, COMMON_SHORT, COMMON_VERY_HIGH,
        DARF_CONFIG,
        DFOL_HIGH, DFOL_LOW, DFOL_MID, DFOL_MID_PLUS, DFOL_SETUP,
        DRUT_VERY_HIGH,
        RSWDL_LOW, RSWDL_MID,
        SAT_ONLY, SMS_ONLY,
        UPTIME_VERY_HIGH,
        VLINK_HIGH, VLINK_LOW);
  }

  @Override
  public String toString() {
    return value;
  }
}
