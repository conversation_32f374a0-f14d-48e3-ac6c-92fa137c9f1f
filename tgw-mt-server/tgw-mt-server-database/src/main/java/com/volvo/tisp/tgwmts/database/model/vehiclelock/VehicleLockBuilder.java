package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class VehicleLockBuilder {
  private Vpi vpi;

  /**
   * Convenience method to simplify the creation of a new {@link VehicleLock} instance with a given {@link Vpi}.
   */
  public static VehicleLock ofVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    return new VehicleLockBuilder().setVpi(vpi).build();
  }

  public VehicleLock build() {
    Validate.notNull(vpi, "vpi");

    return new VehicleLock(this);
  }

  public Vpi getVpi() {
    return vpi;
  }

  public VehicleLockBuilder setVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    this.vpi = vpi;
    return this;
  }
}
