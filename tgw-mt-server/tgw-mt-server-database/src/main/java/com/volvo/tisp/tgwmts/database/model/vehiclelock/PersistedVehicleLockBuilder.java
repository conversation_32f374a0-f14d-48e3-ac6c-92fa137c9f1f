package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class PersistedVehicleLockBuilder {
  private Instant created;
  private VehicleLock vehicleLock;
  private VehicleLockId vehicleLockId;

  public PersistedVehicleLock build() {
    Validate.notNull(created, "created");
    Validate.notNull(vehicleLock, "vehicleLock");
    Validate.notNull(vehicleLockId, "vehicleLockId");

    return new PersistedVehicleLock(this);
  }

  public Instant getCreated() {
    return created;
  }

  public VehicleLock getVehicleLock() {
    return vehicleLock;
  }

  public VehicleLockId getVehicleLockId() {
    return vehicleLockId;
  }

  public PersistedVehicleLockBuilder setCreated(Instant created) {
    Validate.notNull(created, "created");

    this.created = created;
    return this;
  }

  public PersistedVehicleLockBuilder setVehicleLock(VehicleLock vehicleLock) {
    Validate.notNull(vehicleLock, "vehicleLock");

    this.vehicleLock = vehicleLock;
    return this;
  }

  public PersistedVehicleLockBuilder setVehicleLockId(VehicleLockId vehicleLockId) {
    Validate.notNull(vehicleLockId, "vehicleLockId");

    this.vehicleLockId = vehicleLockId;
    return this;
  }
}
