package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class SrpOptionBuilder {
  private SrpDestinationService srpDestinationService;
  private SrpDestinationVersion srpDestinationVersion;
  private boolean srpLevel12;
  private SrpPayload srpPayload;

  public SrpOption build() {
    Validate.notNull(srpDestinationService, "srpDestinationService");
    Validate.notNull(srpDestinationVersion, "srpDestinationVersion");
    Validate.notNull(srpPayload, "srpPayload");

    return new SrpOption(this);
  }

  public SrpDestinationService getSrpDestinationService() {
    return srpDestinationService;
  }

  public SrpDestinationVersion getSrpDestinationVersion() {
    return srpDestinationVersion;
  }

  public SrpPayload getSrpPayload() {
    return srpPayload;
  }

  public boolean isSrpLevel12() {
    return srpLevel12;
  }

  public SrpOptionBuilder setSrpDestinationService(SrpDestinationService srpDestinationService) {
    Validate.notNull(srpDestinationService, "srpDestinationService");

    this.srpDestinationService = srpDestinationService;
    return this;
  }

  public SrpOptionBuilder setSrpDestinationVersion(SrpDestinationVersion srpDestinationVersion) {
    Validate.notNull(srpDestinationVersion, "srpDestinationVersion");

    this.srpDestinationVersion = srpDestinationVersion;
    return this;
  }

  public SrpOptionBuilder setSrpLevel12(boolean srpLevel12) {
    this.srpLevel12 = srpLevel12;
    return this;
  }

  public SrpOptionBuilder setSrpPayload(SrpPayload srpPayload) {
    Validate.notNull(srpPayload, "srpPayload");

    this.srpPayload = srpPayload;
    return this;
  }
}
