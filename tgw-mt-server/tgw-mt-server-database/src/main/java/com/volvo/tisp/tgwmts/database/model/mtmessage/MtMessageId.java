package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.tgwmts.database.model.PrimaryKey;

public final class MtMessageId extends PrimaryKey {
  private MtMessageId(long id) {
    super(id);
  }

  public static MtMessageId ofLong(long id) {
    return new MtMessageId(id);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    MtMessageId other = (MtMessageId) object;
    return id == other.id;
  }

  @Override
  public int hashCode() {
    return Long.hashCode(id);
  }
}
