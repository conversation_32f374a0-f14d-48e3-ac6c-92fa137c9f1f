package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import com.volvo.tisp.tgwmts.database.model.PrimaryKey;

public final class VehicleLockId extends PrimaryKey {
  private VehicleLockId(long id) {
    super(id);
  }

  public static VehicleLockId ofLong(long id) {
    return new VehicleLockId(id);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    VehicleLockId other = (VehicleLockId) object;
    return id == other.id;
  }

  @Override
  public int hashCode() {
    return Long.hashCode(id);
  }
}
