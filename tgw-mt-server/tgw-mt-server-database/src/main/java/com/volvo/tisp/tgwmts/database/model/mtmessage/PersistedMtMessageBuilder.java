package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class PersistedMtMessageBuilder {
  private Instant created;
  private MtMessage mtMessage;
  private MtMessageId mtMessageId;

  public PersistedMtMessage build() {
    Validate.notNull(created, "created");
    Validate.notNull(mtMessage, "mtMessage");
    Validate.notNull(mtMessageId, "mtMessageId");

    return new PersistedMtMessage(this);
  }

  public Instant getCreated() {
    return created;
  }

  public MtMessage getMtMessage() {
    return mtMessage;
  }

  public MtMessageId getMtMessageId() {
    return mtMessageId;
  }

  public PersistedMtMessageBuilder setCreated(Instant created) {
    Validate.notNull(created, "created");

    this.created = created;
    return this;
  }

  public PersistedMtMessageBuilder setMtMessage(MtMessage mtMessage) {
    Validate.notNull(mtMessage, "mtMessage");

    this.mtMessage = mtMessage;
    return this;
  }

  public PersistedMtMessageBuilder setMtMessageId(MtMessageId mtMessageId) {
    Validate.notNull(mtMessageId, "mtMessageId");

    this.mtMessageId = mtMessageId;
    return this;
  }
}
