package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class UpdateActiveMtMessageParameter {
  private final ActiveMtMessageId activeMtMessageId;
  private final RetryAttempt retryAttempt;
  private final SendSchemaStepId sendSchemaStepId;
  private final Instant timeout;

  UpdateActiveMtMessageParameter(UpdateActiveMtMessageParameterBuilder updateActiveMtMessageParameterBuilder) {
    Validate.notNull(updateActiveMtMessageParameterBuilder, "updateActiveMtMessageParameterBuilder");

    this.activeMtMessageId = updateActiveMtMessageParameterBuilder.getActiveMtMessageId();
    this.retryAttempt = updateActiveMtMessageParameterBuilder.getRetryAttempt();
    this.sendSchemaStepId = updateActiveMtMessageParameterBuilder.getSendSchemaStepId();
    this.timeout = updateActiveMtMessageParameterBuilder.getTimeout();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    UpdateActiveMtMessageParameter other = (UpdateActiveMtMessageParameter) object;
    if (!activeMtMessageId.equals(other.activeMtMessageId)) {
      return false;
    } else if (!retryAttempt.equals(other.retryAttempt)) {
      return false;
    } else if (!timeout.equals(other.timeout)) {
      return false;
    } else if (!sendSchemaStepId.equals(other.sendSchemaStepId)) {
      return false;
    }
    return true;
  }

  public ActiveMtMessageId getActiveMtMessageId() {
    return activeMtMessageId;
  }

  public RetryAttempt getRetryAttempt() {
    return retryAttempt;
  }

  public SendSchemaStepId getSendSchemaStepId() {
    return sendSchemaStepId;
  }

  public Instant getTimeout() {
    return timeout;
  }

  @Override
  public int hashCode() {
    int result = activeMtMessageId.hashCode();
    result = 31 * result + retryAttempt.hashCode();
    result = 31 * result + timeout.hashCode();
    result = 31 * result + sendSchemaStepId.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("activeMtMessageId=")
        .append(activeMtMessageId)
        .append(", retryAttempt=")
        .append(retryAttempt)
        .append(", sendSchemaStepId=")
        .append(sendSchemaStepId)
        .append(", timeout=")
        .append(timeout)
        .toString();
  }
}
