package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SrpOption {
  private final SrpDestinationService srpDestinationService;
  private final SrpDestinationVersion srpDestinationVersion;
  private final boolean srpLevel12;
  private final SrpPayload srpPayload;

  SrpOption(SrpOptionBuilder srpOptionBuilder) {
    Validate.notNull(srpOptionBuilder, "srpOptionBuilder");

    srpDestinationService = srpOptionBuilder.getSrpDestinationService();
    srpDestinationVersion = srpOptionBuilder.getSrpDestinationVersion();
    srpLevel12 = srpOptionBuilder.isSrpLevel12();
    srpPayload = srpOptionBuilder.getSrpPayload();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    SrpOption other = (SrpOption) object;
    if (!srpDestinationService.equals(other.srpDestinationService)) {
      return false;
    } else if (!srpDestinationVersion.equals(other.srpDestinationVersion)) {
      return false;
    } else if (srpLevel12 != other.srpLevel12) {
      return false;
    } else if (!srpPayload.equals(other.srpPayload)) {
      return false;
    }
    return true;
  }

  public SrpDestinationService getSrpDestinationService() {
    return srpDestinationService;
  }

  public SrpDestinationVersion getSrpDestinationVersion() {
    return srpDestinationVersion;
  }

  public SrpPayload getSrpPayload() {
    return srpPayload;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + srpDestinationService.hashCode();
    result = prime * result + srpDestinationVersion.hashCode();
    result = prime * result + Boolean.hashCode(srpLevel12);
    result = prime * result + srpPayload.hashCode();
    return result;
  }

  public boolean isSrpLevel12() {
    return srpLevel12;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("srpDestinationService=")
        .append(srpDestinationService)
        .append(", srpDestinationVersion=")
        .append(srpDestinationVersion)
        .append(", srpLevel12=")
        .append(srpLevel12)
        .append(", srpPayload=")
        .append(srpPayload)
        .toString();
  }
}
