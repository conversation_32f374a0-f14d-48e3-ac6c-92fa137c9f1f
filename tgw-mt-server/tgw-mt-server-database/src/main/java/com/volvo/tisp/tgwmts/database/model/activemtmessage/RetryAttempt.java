package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class RetryAttempt {
  public static final short MAX_VALUE = Short.MAX_VALUE;

  private final short retryAttemptValue;

  private RetryAttempt(short retryAttemptValue) {
    this.retryAttemptValue = retryAttemptValue;
  }

  /**
   * @throws IllegalArgumentException if {@code retryAttemptValue} is less than zero or greater than {@value #MAX_VALUE}.
   */
  public static RetryAttempt ofShort(short retryAttemptValue) {
    Validate.notNegativeAndNotGreaterThan(retryAttemptValue, MAX_VALUE, "retryAttemptValue");

    return new RetryAttempt(retryAttemptValue);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    RetryAttempt other = (RetryAttempt) object;
    return retryAttemptValue == other.retryAttemptValue;
  }

  @Override
  public int hashCode() {
    return Short.hashCode(retryAttemptValue);
  }

  public short toShort() {
    return retryAttemptValue;
  }

  @Override
  public String toString() {
    return Short.toString(retryAttemptValue);
  }
}
