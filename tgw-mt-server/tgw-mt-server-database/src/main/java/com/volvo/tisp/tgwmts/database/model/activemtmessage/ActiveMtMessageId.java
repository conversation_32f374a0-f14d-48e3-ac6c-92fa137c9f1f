package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import com.volvo.tisp.tgwmts.database.model.PrimaryKey;

public final class ActiveMtMessageId extends PrimaryKey {
  private ActiveMtMessageId(long id) {
    super(id);
  }

  public static ActiveMtMessageId ofLong(long id) {
    return new ActiveMtMessageId(id);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    ActiveMtMessageId other = (ActiveMtMessageId) object;
    return id == other.id;
  }

  @Override
  public int hashCode() {
    return Long.hashCode(id);
  }
}
