package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class QueueId {
  public static final int MAX_LENGTH = 64;

  private final String value;

  private QueueId(String value) {
    this.value = value;
  }

  public static QueueId ofString(String value) {
    Validate.lengthBetween(value, 1, MAX_LENGTH, "value");

    return new QueueId(value);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    QueueId other = (QueueId) object;
    return value.equals(other.value);
  }

  @Override
  public int hashCode() {
    return value.hashCode();
  }

  @Override
  public String toString() {
    return value;
  }
}
