package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class ReplyOptionBuilder {
  private CorrelationId correlationId;
  private ReplyTo replyTo;

  public ReplyOption build() {
    Validate.notNull(correlationId, "correlationId");
    Validate.notNull(replyTo, "replyTo");

    return new ReplyOption(this);
  }

  public CorrelationId getCorrelationId() {
    return correlationId;
  }

  public ReplyTo getReplyTo() {
    return replyTo;
  }

  public ReplyOptionBuilder setCorrelationId(CorrelationId correlationId) {
    Validate.notNull(correlationId, "correlationId");

    this.correlationId = correlationId;
    return this;
  }

  public ReplyOptionBuilder setReplyTo(ReplyTo replyTo) {
    Validate.notNull(replyTo, "replyTo");

    this.replyTo = replyTo;
    return this;
  }
}
