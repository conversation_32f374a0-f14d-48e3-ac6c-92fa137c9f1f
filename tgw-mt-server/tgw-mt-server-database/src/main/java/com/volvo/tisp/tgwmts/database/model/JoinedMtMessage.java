package com.volvo.tisp.tgwmts.database.model;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import java.util.Optional;

public record JoinedMtMessage(Optional<PersistedActiveMtMessage> persistedActiveMtMessage,
                              PersistedMtMessage persistedMtMessage,
                              PersistedVehicleLock persistedVehicleLock) {
    public JoinedMtMessage {
        Validate.notNull(persistedActiveMtMessage, "persistedActiveMtMessage");
        Validate.notNull(persistedMtMessage, "persistedMtMessage");
        Validate.notNull(persistedVehicleLock, "persistedVehicleLock");
    }

    @Override
    public String toString() {
        return new StringBuilder(50)
                .append("persistedActiveMtMessage=")
                .append(persistedActiveMtMessage)
                .append("persistedMtMessage=")
                .append(persistedMtMessage)
                .append("persistedVehicleLock=")
                .append(persistedVehicleLock)
                .toString();
    }
}


