package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class JoinedActiveMtMessageRowMapper implements RowMapper<JoinedActiveMtMessage> {
  private final RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper;
  private final RowMapper<PersistedMtMessage> mtMessageRowMapper;
  private final RowMapper<PersistedVehicleLock> vehicleLockRowMapper;

  private JoinedActiveMtMessageRowMapper(RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper, RowMapper<PersistedMtMessage> mtMessageRowMapper,
      RowMapper<PersistedVehicleLock> vehicleLockRowMapper) {
    this.activeMtMessageRowMapper = activeMtMessageRowMapper;
    this.mtMessageRowMapper = mtMessageRowMapper;
    this.vehicleLockRowMapper = vehicleLockRowMapper;
  }

  public static RowMapper<JoinedActiveMtMessage> create(RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper,
      RowMapper<PersistedMtMessage> mtMessageRowMapper,
      RowMapper<PersistedVehicleLock> vehicleLockRowMapper) {
    Validate.notNull(activeMtMessageRowMapper, "activeMtMessageRowMapper");
    Validate.notNull(mtMessageRowMapper, "mtMessageRowMapper");
    Validate.notNull(vehicleLockRowMapper, "vehicleLockRowMapper");

    return new JoinedActiveMtMessageRowMapper(activeMtMessageRowMapper, mtMessageRowMapper, vehicleLockRowMapper);
  }

  @Override
  public JoinedActiveMtMessage map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    PersistedActiveMtMessage persistedActiveMtMessage = activeMtMessageRowMapper.map(resultSet, statementContext);
    PersistedMtMessage persistedMtMessage = mtMessageRowMapper.map(resultSet, statementContext);
    PersistedVehicleLock persistedVehicleLock = vehicleLockRowMapper.map(resultSet, statementContext);

    return new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);
  }
}
