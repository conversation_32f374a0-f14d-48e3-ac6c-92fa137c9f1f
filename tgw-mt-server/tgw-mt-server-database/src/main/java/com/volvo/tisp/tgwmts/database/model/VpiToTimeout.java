package com.volvo.tisp.tgwmts.database.model;

import java.time.Instant;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record VpiToTimeout(Vpi vpi, Instant timeout) {
  public VpiToTimeout {
    Validate.notNull(vpi, "vpi");
    Validate.notNull(timeout, "timeout");
  }

  @Override
  public String toString() {
    return new StringBuilder(50)
        .append("vpi=")
        .append(vpi)
        .append("timeout=")
        .append(timeout)
        .toString();
  }
}


