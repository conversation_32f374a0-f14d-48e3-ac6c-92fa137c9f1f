package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtMessageBuilder {
  private QueueId queueId;
  private Optional<ReplyOption> replyOption = Optional.empty();
  private SendSchemaName sendSchemaName;
  private SrpOption srpOption;
  private Tid tid;
  private VehicleLockId vehicleLockId;

  public MtMessage build() {
    Validate.notNull(queueId, "queueId");
    Validate.notNull(sendSchemaName, "sendSchemaName");
    Validate.notNull(srpOption, "srpOption");
    Validate.notNull(tid, "tid");
    Validate.notNull(vehicleLockId, "vehicleLockId");

    return new MtMessage(this);
  }

  public QueueId getQueueId() {
    return queueId;
  }

  public Optional<ReplyOption> getReplyOption() {
    return replyOption;
  }

  public SendSchemaName getSendSchemaName() {
    return sendSchemaName;
  }

  public SrpOption getSrpOption() {
    return srpOption;
  }

  public Tid getTid() {
    return tid;
  }

  public VehicleLockId getVehicleLockId() {
    return vehicleLockId;
  }

  public MtMessageBuilder setQueueId(QueueId queueId) {
    Validate.notNull(queueId, "queueId");

    this.queueId = queueId;
    return this;
  }

  public MtMessageBuilder setReplyOption(Optional<ReplyOption> replyOption) {
    Validate.notNull(replyOption, "replyOption");

    this.replyOption = replyOption;
    return this;
  }

  public MtMessageBuilder setSendSchemaName(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    this.sendSchemaName = sendSchemaName;
    return this;
  }

  public MtMessageBuilder setSrpOption(SrpOption srpOption) {
    Validate.notNull(srpOption, "srpOption");

    this.srpOption = srpOption;
    return this;
  }

  public MtMessageBuilder setTid(Tid tid) {
    Validate.notNull(tid, "tid");

    this.tid = tid;
    return this;
  }

  public MtMessageBuilder setVehicleLockId(VehicleLockId vehicleLockId) {
    Validate.notNull(vehicleLockId, "vehicleLockId");

    this.vehicleLockId = vehicleLockId;
    return this;
  }
}
