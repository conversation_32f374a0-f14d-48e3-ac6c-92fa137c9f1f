package com.volvo.tisp.tgwmts.database.db;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.VpiToTimeout;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class VpiToTimeoutRowMapper implements RowMapper<VpiToTimeout> {
  public static final RowMapper<VpiToTimeout> INSTANCE = new VpiToTimeoutRowMapper();

  private VpiToTimeoutRowMapper() {
    // do nothing
  }

  private static Instant getInstant(ResultSet resultSet, String columnName) throws SQLException {
    return resultSet.getTimestamp(columnName).toInstant();
  }

  @Override
  public VpiToTimeout map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new VpiToTimeout(Vpi.ofString(resultSet.getString("vpi")), getInstant(resultSet, "timeout"));
  }
}
