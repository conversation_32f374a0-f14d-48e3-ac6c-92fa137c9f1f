package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class PersistedVehicleLock {
  private final Instant created;
  private final VehicleLock vehicleLock;
  private final VehicleLockId vehicleLockId;

  PersistedVehicleLock(PersistedVehicleLockBuilder persistedVehicleLockBuilder) {
    Validate.notNull(persistedVehicleLockBuilder, "persistedVehicleLockBuilder");

    this.created = persistedVehicleLockBuilder.getCreated();
    this.vehicleLock = persistedVehicleLockBuilder.getVehicleLock();
    this.vehicleLockId = persistedVehicleLockBuilder.getVehicleLockId();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    PersistedVehicleLock other = (PersistedVehicleLock) object;
    if (!created.equals(other.created)) {
      return false;
    } else if (!vehicleLock.equals(other.vehicleLock)) {
      return false;
    }
    return vehicleLockId.equals(other.vehicleLockId);
  }

  public Instant getCreated() {
    return created;
  }

  public VehicleLock getVehicleLock() {
    return vehicleLock;
  }

  public VehicleLockId getVehicleLockId() {
    return vehicleLockId;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + created.hashCode();
    result = prime * result + vehicleLock.hashCode();
    result = prime * result + vehicleLockId.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("created=")
        .append(created)
        .append(", vehicleLockId=")
        .append(vehicleLockId)
        .append(", vehicleLock={")
        .append(vehicleLock)
        .append("}")
        .toString();
  }
}
