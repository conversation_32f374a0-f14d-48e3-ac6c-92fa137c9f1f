package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class ActiveMtMessageBuilder {
  private MtMessageId mtMessageId;
  private RetryAttempt retryAttempt;
  private SendSchemaStepId sendSchemaStepId;
  private Instant timeout;

  public ActiveMtMessage build() {
    Validate.notNull(mtMessageId, "mtMessageId");
    Validate.notNull(retryAttempt, "retryAttempt");
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");
    Validate.notNull(timeout, "timeout");

    return new ActiveMtMessage(this);
  }

  public MtMessageId getMtMessageId() {
    return mtMessageId;
  }

  public RetryAttempt getRetryAttempt() {
    return retryAttempt;
  }

  public SendSchemaStepId getSendSchemaStepId() {
    return sendSchemaStepId;
  }

  public Instant getTimeout() {
    return timeout;
  }

  public ActiveMtMessageBuilder setMtMessageId(MtMessageId mtMessageId) {
    Validate.notNull(mtMessageId, "mtMessageId");

    this.mtMessageId = mtMessageId;
    return this;
  }

  public ActiveMtMessageBuilder setRetryAttempt(RetryAttempt retryAttempt) {
    Validate.notNull(retryAttempt, "retryAttempt");

    this.retryAttempt = retryAttempt;
    return this;
  }

  public ActiveMtMessageBuilder setSendSchemaStepId(SendSchemaStepId sendSchemaStepId) {
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");

    this.sendSchemaStepId = sendSchemaStepId;
    return this;
  }

  public ActiveMtMessageBuilder setTimeout(Instant timeout) {
    Validate.notNull(timeout, "timeout");

    this.timeout = timeout;
    return this;
  }
}
