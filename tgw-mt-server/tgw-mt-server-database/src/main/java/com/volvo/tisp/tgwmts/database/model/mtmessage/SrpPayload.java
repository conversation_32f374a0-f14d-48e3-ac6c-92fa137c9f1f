package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ByteUnit;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

public final class SrpPayload {
  private static final long MAX_PAYLOAD_SIZE = ByteUnit.MEGA_BYTES.toBytes(1);

  private final ImmutableByteArray immutableByteArray;

  private SrpPayload(ImmutableByteArray immutableByteArray) {
    this.immutableByteArray = immutableByteArray;
  }

  public static SrpPayload ofImmutableByteArray(ImmutableByteArray immutableByteArray) {
    Validate.notNull(immutableByteArray, "immutableByteArray");
    Validate.isPositiveAndNotGreaterThan(immutableByteArray.toByteArray().length, MAX_PAYLOAD_SIZE, "immutableByteArray");

    return new SrpPayload(immutableByteArray);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    SrpPayload other = (SrpPayload) object;
    return immutableByteArray.equals(other.immutableByteArray);
  }

  public ImmutableByteArray getImmutableByteArray() {
    return immutableByteArray;
  }

  @Override
  public int hashCode() {
    return immutableByteArray.hashCode();
  }

  @Override
  public String toString() {
    return immutableByteArray.toString();
  }
}
