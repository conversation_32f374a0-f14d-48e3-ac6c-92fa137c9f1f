package com.volvo.tisp.tgwmts.database.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public record InsertionFailure(InsertionFailureReason insertionFailureReason, RuntimeException runtimeException) {
  public InsertionFailure {
    Validate.notNull(insertionFailureReason, "insertionFailureReason");
    Validate.notNull(runtimeException, "runtimeException");

  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("insertionFailureReason=")
        .append(insertionFailureReason)
        .append(", runtimeException=")
        .append(runtimeException)
        .toString();
  }
}
