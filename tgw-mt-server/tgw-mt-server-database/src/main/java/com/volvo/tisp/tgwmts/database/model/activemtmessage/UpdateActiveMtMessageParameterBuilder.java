package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class UpdateActiveMtMessageParameterBuilder {
  private ActiveMtMessageId activeMtMessageId;
  private RetryAttempt retryAttempt;
  private SendSchemaStepId sendSchemaStepId;
  private Instant timeout;

  public UpdateActiveMtMessageParameter build() {
    Validate.notNull(activeMtMessageId, "activeMtMessageId");
    Validate.notNull(retryAttempt, "retryAttempt");
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");
    Validate.notNull(timeout, "timeout");

    return new UpdateActiveMtMessageParameter(this);
  }

  public ActiveMtMessageId getActiveMtMessageId() {
    return activeMtMessageId;
  }

  public RetryAttempt getRetryAttempt() {
    return retryAttempt;
  }

  public SendSchemaStepId getSendSchemaStepId() {
    return sendSchemaStepId;
  }

  public Instant getTimeout() {
    return timeout;
  }

  public UpdateActiveMtMessageParameterBuilder setActiveMtMessageId(ActiveMtMessageId activeMtMessageId) {
    Validate.notNull(activeMtMessageId, "activeMtMessageId");

    this.activeMtMessageId = activeMtMessageId;
    return this;
  }

  public UpdateActiveMtMessageParameterBuilder setRetryAttempt(RetryAttempt retryAttempt) {
    Validate.notNull(retryAttempt, "retryAttempt");

    this.retryAttempt = retryAttempt;
    return this;
  }

  public UpdateActiveMtMessageParameterBuilder setSendSchemaStepId(SendSchemaStepId sendSchemaStepId) {
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");

    this.sendSchemaStepId = sendSchemaStepId;
    return this;
  }

  public UpdateActiveMtMessageParameterBuilder setTimeout(Instant timeout) {
    Validate.notNull(timeout, "timeout");

    this.timeout = timeout;
    return this;
  }
}
