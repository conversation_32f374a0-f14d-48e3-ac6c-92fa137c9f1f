package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.time.Clock;
import java.time.Duration;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ActiveMtMessageReaderFactoryImpl implements ActiveMtMessageReaderFactory {
  private final Clock clock;
  private final Jdbi jdbi;
  private final RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper;
  private final RowMapper<JoinedMtMessage> joinedMtMessageRowMapper;
  private final Duration lockTimeout;

  private ActiveMtMessageReaderFactoryImpl(Clock clock, Jdbi jdbi, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration lockTimeout) {
    this.clock = clock;
    this.jdbi = jdbi;
    this.joinedActiveMtMessageRowMapper = joinedActiveMtMessageRowMapper;
    this.joinedMtMessageRowMapper = joinedMtMessageRowMapper;
    this.lockTimeout = lockTimeout;
  }

  public static ActiveMtMessageReaderFactory create(Clock clock, Jdbi jdbi, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration lockTimeout) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbi, "jdbi");
    Validate.notNull(joinedActiveMtMessageRowMapper, "joinedActiveMtMessageRowMapper");
    Validate.notNull(joinedMtMessageRowMapper, "joinedMtMessageRowMapper");
    Validate.isPositive(lockTimeout, "lockTimeout");

    return new ActiveMtMessageReaderFactoryImpl(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, lockTimeout);
  }

  @Override
  public ActiveMtMessageReader create() {
    Handle handle = jdbi.open();
    return ActiveMtMessageWriterImpl.create(clock, handle, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, lockTimeout);
  }
}
