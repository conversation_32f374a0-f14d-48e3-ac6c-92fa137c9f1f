package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class VehicleLock {
  private final Vpi vpi;

  VehicleLock(VehicleLockBuilder vehicleLockBuilder) {
    Validate.notNull(vehicleLockBuilder, "vehicleLockBuilder");

    vpi = vehicleLockBuilder.getVpi();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    VehicleLock other = (VehicleLock) object;
    return vpi.equals(other.vpi);
  }

  public Vpi getVpi() {
    return vpi;
  }

  @Override
  public int hashCode() {
    return vpi.hashCode();
  }

  @Override
  public String toString() {
    return new StringBuilder(50)
        .append("vpi=")
        .append(vpi)
        .toString();
  }
}
