package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class PersistedMtMessage {
  private final Instant created;
  private final MtMessage mtMessage;
  private final MtMessageId mtMessageId;

  PersistedMtMessage(PersistedMtMessageBuilder persistedMtMessageBuilder) {
    Validate.notNull(persistedMtMessageBuilder, "persistedMtMessageBuilder");

    this.created = persistedMtMessageBuilder.getCreated();
    this.mtMessage = persistedMtMessageBuilder.getMtMessage();
    this.mtMessageId = persistedMtMessageBuilder.getMtMessageId();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    PersistedMtMessage other = (PersistedMtMessage) object;
    if (!created.equals(other.created)) {
      return false;
    } else if (!mtMessage.equals(other.mtMessage)) {
      return false;
    }
    return mtMessageId.equals(other.mtMessageId);
  }

  public Instant getCreated() {
    return created;
  }

  public MtMessage getMtMessage() {
    return mtMessage;
  }

  public MtMessageId getMtMessageId() {
    return mtMessageId;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + created.hashCode();
    result = prime * result + mtMessage.hashCode();
    result = prime * result + mtMessageId.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("created=")
        .append(created)
        .append(", mtMessage={")
        .append(mtMessage)
        .append("}, mtMessageId=")
        .append(mtMessageId)
        .toString();
  }
}
