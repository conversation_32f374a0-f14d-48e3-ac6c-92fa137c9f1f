package com.volvo.tisp.tgwmts.database.db.stat;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.stat.PgStatTuple;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class PgStatTupleRowMapper implements RowMapper<PgStatTuple> {
  public static final RowMapper<PgStatTuple> INSTANCE = new PgStatTupleRowMapper();

  private PgStatTupleRowMapper() {
    // do nothing
  }

  @Override
  public PgStatTuple map(ResultSet resultSet, StatementContext ctx) throws SQLException {
    Validate.notNull(resultSet, "resultSet");
    Validate.notNull(ctx, "ctx");

    return new PgStatTuple(
        resultSet.getInt("dead_tuple_len"),
        resultSet.getInt("free_space"),
        resultSet.getInt("table_len"),
        resultSet.getInt("tuple_len"));
  }
}
