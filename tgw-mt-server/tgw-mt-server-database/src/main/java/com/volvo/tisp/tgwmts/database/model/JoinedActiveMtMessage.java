package com.volvo.tisp.tgwmts.database.model;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record JoinedActiveMtMessage(PersistedActiveMtMessage persistedActiveMtMessage, PersistedMtMessage persistedMtMessage,
                                    PersistedVehicleLock persistedVehicleLock) {
  public JoinedActiveMtMessage {
    Validate.notNull(persistedActiveMtMessage, "persistedActiveMtMessage");
    Validate.notNull(persistedMtMessage, "persistedMtMessage");
    Validate.notNull(persistedVehicleLock, "persistedVehicleLock");

  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    JoinedActiveMtMessage other = (JoinedActiveMtMessage) object;
    if (!persistedActiveMtMessage.equals(other.persistedActiveMtMessage)) {
      return false;
    } else if (!persistedMtMessage.equals(other.persistedMtMessage)) {
      return false;
    } else if (!persistedVehicleLock.equals(other.persistedVehicleLock)) {
      return false;
    }
    return true;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("persistedActiveMtMessage={")
        .append(persistedActiveMtMessage)
        .append("}, persistedMtMessage={")
        .append(persistedMtMessage)
        .append("}, persistedVehicleLock={")
        .append(persistedVehicleLock)
        .append("}")
        .toString();
  }
}
