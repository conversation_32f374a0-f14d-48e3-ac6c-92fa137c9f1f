package com.volvo.tisp.tgwmts.database.db.stat;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.stat.PgStatIndex;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class PgStatIndexRowMapper implements RowMapper<PgStatIndex> {
  public static final RowMapper<PgStatIndex> INSTANCE = new PgStatIndexRowMapper();

  @Override
  public PgStatIndex map(ResultSet resultSet, StatementContext ctx) throws SQLException {
    Validate.notNull(resultSet, "resultSet");
    Validate.notNull(ctx, "ctx");

    return new PgStatIndex(
        resultSet.getInt("deleted_pages"),
        resultSet.getInt("empty_pages"),
        resultSet.getInt("internal_pages"),
        resultSet.getInt("leaf_pages"));
  }
}
