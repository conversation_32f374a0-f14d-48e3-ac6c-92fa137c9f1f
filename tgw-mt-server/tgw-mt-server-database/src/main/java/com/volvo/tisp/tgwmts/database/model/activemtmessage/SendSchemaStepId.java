package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SendSchemaStepId {
  private static final SendSchemaStepId[] SINGLETONS = new SendSchemaStepId[] {
      new SendSchemaStepId((short) 0),
      new SendSchemaStepId((short) 1),
      new SendSchemaStepId((short) 2),
      new SendSchemaStepId((short) 3),
      new SendSchemaStepId((short) 4),
      new SendSchemaStepId((short) 5),
      new SendSchemaStepId((short) 6),
      new SendSchemaStepId((short) 7)};

  private final short value;

  private SendSchemaStepId(short value) {
    this.value = value;
  }

  public static SendSchemaStepId ofInt(int value) {
    Validate.notNegative(value, "value");

    if (value < SINGLETONS.length) {
      return SINGLETONS[value];
    }

    return new SendSchemaStepId((short) value);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    SendSchemaStepId other = (SendSchemaStepId) object;
    return value == other.value;
  }

  @Override
  public int hashCode() {
    return Integer.hashCode(value);
  }

  public SendSchemaStepId plusOne() {
    return SendSchemaStepId.ofInt(value + 1);
  }

  public int toInt() {
    return value;
  }

  @Override
  public String toString() {
    return Integer.toString(value);
  }
}
