package com.volvo.tisp.tgwmts.database.db;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class AssetCapabilityStateRowMapper implements RowMapper<AssetCapabilityState> {
  public static final RowMapper<AssetCapabilityState> INSTANCE = new AssetCapabilityStateRowMapper();

  private AssetCapabilityStateRowMapper() {
    // do nothing
  }

  @Override
  public AssetCapabilityState map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new AssetCapabilityState(Vpi.ofString(resultSet.getString("vpi")),
        AssetCapabilityState.AssetCapability.valueOf(resultSet.getString("channel")),
        resultSet.getBoolean("is_enabled"));
  }
}
