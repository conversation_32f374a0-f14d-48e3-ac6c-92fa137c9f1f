package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ActiveMtMessage {
  private final MtMessageId mtMessageId;
  private final RetryAttempt retryAttempt;
  private final SendSchemaStepId sendSchemaStepId;
  private final Instant timeout;

  ActiveMtMessage(ActiveMtMessageBuilder activeMtMessageBuilder) {
    Validate.notNull(activeMtMessageBuilder, "activeMtMessageBuilder");

    this.mtMessageId = activeMtMessageBuilder.getMtMessageId();
    this.retryAttempt = activeMtMessageBuilder.getRetryAttempt();
    this.sendSchemaStepId = activeMtMessageBuilder.getSendSchemaStepId();
    this.timeout = activeMtMessageBuilder.getTimeout();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    ActiveMtMessage other = (ActiveMtMessage) object;
    if (!mtMessageId.equals(other.mtMessageId)) {
      return false;
    } else if (!retryAttempt.equals(other.retryAttempt)) {
      return false;
    } else if (!sendSchemaStepId.equals(other.sendSchemaStepId)) {
      return false;
    } else if (!timeout.equals(other.timeout)) {
      return false;
    }
    return true;
  }

  public MtMessageId getMtMessageId() {
    return mtMessageId;
  }

  public RetryAttempt getRetryAttempt() {
    return retryAttempt;
  }

  public SendSchemaStepId getSendSchemaStepId() {
    return sendSchemaStepId;
  }

  public Instant getTimeout() {
    return timeout;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + mtMessageId.hashCode();
    result = prime * result + retryAttempt.hashCode();
    result = prime * result + sendSchemaStepId.hashCode();
    result = prime * result + timeout.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(150)
        .append("mtMessageId=")
        .append(mtMessageId)
        .append(", retryAttempt=")
        .append(retryAttempt)
        .append(", sendSchemaStepId=")
        .append(sendSchemaStepId)
        .append(", timeout=")
        .append(timeout)
        .toString();
  }
}
