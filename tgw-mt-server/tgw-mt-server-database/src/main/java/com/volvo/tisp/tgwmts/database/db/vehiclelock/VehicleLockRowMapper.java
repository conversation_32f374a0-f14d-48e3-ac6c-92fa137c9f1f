package com.volvo.tisp.tgwmts.database.db.vehiclelock;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class VehicleLockRowMapper implements RowMapper<PersistedVehicleLock> {
  public static final RowMapper<PersistedVehicleLock> INSTANCE = new VehicleLockRowMapper();

  private VehicleLockRowMapper() {
    // do nothing
  }

  private static Instant getInstant(ResultSet resultSet, String columnName) throws SQLException {
    return resultSet.getTimestamp(columnName).toInstant();
  }

  private static VehicleLockId getVehicleLockId(ResultSet resultSet, String columnName) throws SQLException {
    return VehicleLockId.ofLong(resultSet.getLong(columnName));
  }

  private static VehicleLock mapVehicleLock(ResultSet resultSet) throws SQLException {
    return VehicleLockBuilder.ofVpi(Vpi.ofString(resultSet.getString("vpi")));
  }

  @Override
  public PersistedVehicleLock map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new PersistedVehicleLockBuilder()
        .setCreated(getInstant(resultSet, "vehicle_lock_created"))
        .setVehicleLock(mapVehicleLock(resultSet))
        .setVehicleLockId(getVehicleLockId(resultSet, "vehicle_lock_id"))
        .build();
  }
}
