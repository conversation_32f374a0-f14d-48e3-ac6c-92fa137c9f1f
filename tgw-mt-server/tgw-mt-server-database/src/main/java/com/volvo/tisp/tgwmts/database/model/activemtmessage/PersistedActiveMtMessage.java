package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class PersistedActiveMtMessage {
  private final ActiveMtMessage activeMtMessage;
  private final ActiveMtMessageId activeMtMessageId;
  private final Instant created;
  private final Instant updated;

  PersistedActiveMtMessage(PersistedActiveMtMessageBuilder persistedActiveMtMessageBuilder) {
    Validate.notNull(persistedActiveMtMessageBuilder, "persistedActiveMtMessageBuilder");

    this.activeMtMessage = persistedActiveMtMessageBuilder.getActiveMtMessage();
    this.activeMtMessageId = persistedActiveMtMessageBuilder.getActiveMtMessageId();
    this.created = persistedActiveMtMessageBuilder.getCreated();
    this.updated = persistedActiveMtMessageBuilder.getUpdated();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    PersistedActiveMtMessage other = (PersistedActiveMtMessage) object;
    if (!activeMtMessage.equals(other.activeMtMessage)) {
      return false;
    } else if (!activeMtMessageId.equals(other.activeMtMessageId)) {
      return false;
    } else if (!created.equals(other.created)) {
      return false;
    }
    return updated.equals(other.updated);
  }

  public ActiveMtMessage getActiveMtMessage() {
    return activeMtMessage;
  }

  public ActiveMtMessageId getActiveMtMessageId() {
    return activeMtMessageId;
  }

  public Instant getCreated() {
    return created;
  }

  public Instant getUpdated() {
    return updated;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + activeMtMessage.hashCode();
    result = prime * result + activeMtMessageId.hashCode();
    result = prime * result + created.hashCode();
    result = prime * result + updated.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("activeMtMessageId=")
        .append(activeMtMessageId)
        .append(", activeMtMessage={")
        .append(activeMtMessage)
        .append(", created=")
        .append(created)
        .append("}, updated=")
        .append(updated)
        .toString();
  }
}
