package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.result.ResultBearing;
import org.jdbi.v3.core.statement.PreparedBatch;
import org.jdbi.v3.core.statement.Query;
import org.jdbi.v3.core.statement.Update;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.db.AssetCapabilityStateRowMapper;
import com.volvo.tisp.tgwmts.database.db.VpiToTimeoutRowMapper;
import com.volvo.tisp.tgwmts.database.db.mtmessage.MtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.stat.PgStatIndexRowMapper;
import com.volvo.tisp.tgwmts.database.db.stat.PgStatTupleRowMapper;
import com.volvo.tisp.tgwmts.database.db.vehiclelock.VehicleLockRowMapper;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.VpiToTimeout;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.database.model.stat.IndexName;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatIndex;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatTuple;
import com.volvo.tisp.tgwmts.database.model.stat.TableName;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

public final class ActiveMtMessageWriterImpl implements ActiveMtMessageWriter {
  private static final Logger logger = LoggerFactory.getLogger(ActiveMtMessageWriterImpl.class);
  private final Clock clock;
  private final Handle jdbiHandle;
  private final RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper;
  private final RowMapper<JoinedMtMessage> joinedMtMessageRowMapper;
  private final Duration lockTimeout;

  private ActiveMtMessageWriterImpl(Clock clock, Handle jdbiHandle, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration lockTimeout) {
    this.clock = clock;
    this.jdbiHandle = jdbiHandle;
    this.joinedActiveMtMessageRowMapper = joinedActiveMtMessageRowMapper;
    this.joinedMtMessageRowMapper = joinedMtMessageRowMapper;
    this.lockTimeout = lockTimeout;
  }

  public static ActiveMtMessageWriter create(Clock clock, Handle jdbiHandle, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration lockTimeout) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbiHandle, "jdbiHandle");
    Validate.notNull(joinedActiveMtMessageRowMapper, "joinedActiveMtMessageRowMapper");
    Validate.notNull(joinedMtMessageRowMapper, "joinedMtMessageRowMapper");
    Validate.isPositive(lockTimeout, "lockTimeout");

    return new ActiveMtMessageWriterImpl(clock, jdbiHandle, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, lockTimeout);
  }

  private static <T> Either<InsertionFailure, T> logAndCreateInsertionFailure(RuntimeException e) {
    logger.debug("", e);

    if (e.getCause() != null) {
      String message = e.getCause().getMessage();

      if (message != null && message.contains("duplicate key value violates unique constraint")) {
        return Either.left(new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, e));
      }

      if (message != null && message.contains("violates foreign key constraint")) {
        return Either.left(new InsertionFailure(InsertionFailureReason.FOREIGN_KEY_VIOLATION, e));
      }
    }

    return Either.left(new InsertionFailure(InsertionFailureReason.UNKNOWN, e));
  }

  @Override
  public void close() {
    try (jdbiHandle) {
      if (!jdbiHandle.isClosed() && jdbiHandle.isInTransaction()) {
        logger.warn("closing database connection before the transaction has been committed, the transaction will be rolled back");
        jdbiHandle.rollback();
      }
    }
  }

  @Override
  public void commitTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not commit a transaction on a closed handle");
    }

    if (!jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction not started");
    }

    jdbiHandle.commit();
  }

  @Override
  public int countActiveMtMessages() {
    try (Query query = jdbiHandle.createQuery("SELECT count(*) FROM active_mt_message")) {
      return query.mapTo(Integer.class).one();
    }
  }

  @Override
  public int countActiveMtMessagesByVehicleLockId(VehicleLockId vehicleLockId) {
    Validate.notNull(vehicleLockId, "vehicleLockId");

    try (Query query = jdbiHandle.createQuery(
        "SELECT count(*) FROM  active_mt_message JOIN mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id WHERE mt_message.vehicle_lock_id = :vehicle_lock_id")) {
      return query
          .bind("vehicle_lock_id", vehicleLockId.toLong())
          .mapTo(Integer.class)
          .one();
    }
  }

  @Override
  public int countMtMessages() {
    try (Query query = jdbiHandle.createQuery("SELECT count(*) FROM mt_message")) {
      return query.mapTo(Integer.class).one();
    }
  }

  @Override
  public int countMtMessagesByVehicleLockId(VehicleLockId vehicleLockId) {
    Validate.notNull(vehicleLockId, "vehicleLockId");

    try (Query query = jdbiHandle.createQuery("SELECT count(*) FROM mt_message WHERE vehicle_lock_id = :vehicle_lock_id")) {
      return query.bind("vehicle_lock_id", vehicleLockId.toLong()).mapTo(Integer.class).one();
    }
  }

  @Override
  public int countMtMessagesByVehicleLockIdAndQueueId(VehicleLockId vehicleLockId, QueueId queueId) {
    Validate.notNull(vehicleLockId, "vehicleLockId");
    Validate.notNull(queueId, "queueId");

    try (Query query = jdbiHandle.createQuery("SELECT count(*) FROM mt_message WHERE vehicle_lock_id = :vehicle_lock_id AND queue_id = :queue_id")) {
      return query
          .bind("vehicle_lock_id", vehicleLockId.toLong())
          .bind("queue_id", queueId.toString())
          .mapTo(Integer.class)
          .one();
    }
  }

  @Override
  public int deleteMtMessageById(MtMessageId mtMessageId) {
    Validate.notNull(mtMessageId, "mtMessageId");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM mt_message WHERE mt_message_id = :mt_message_id")) {
      return update.bind("mt_message_id", mtMessageId.toLong()).execute();
    }
  }

  @Override
  public int[] deleteMtMessageByIds(List<MtMessageId> mtMessageIds) {
    Validate.notEmpty(mtMessageIds, "mtMessageIds");

    try (PreparedBatch preparedBatch = jdbiHandle.prepareBatch("DELETE FROM mt_message WHERE mt_message_id = :mt_message_id")) {
      for (MtMessageId mtMessageId : mtMessageIds) {
        preparedBatch.bind("mt_message_id", mtMessageId.toLong()).add();
      }
      return preparedBatch.execute();
    }
  }

  @Override
  public int deleteMtMessagesByVpiAndQueueId(Vpi vpi, QueueId queueId) {
    Validate.notNull(vpi, "vpi");
    Validate.notNull(queueId, "queueId");

    try (Update update = jdbiHandle.createUpdate(
        "DELETE FROM mt_message USING vehicle_lock WHERE mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id AND queue_id = :queueId AND vpi = :vpi")) {
      return update
          .bind("queueId", queueId.toString())
          .bind("vpi", vpi.toString())
          .execute();
    }
  }

  @Override
  public int deleteVehicleLockByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Update update = jdbiHandle.createUpdate("DELETE FROM vehicle_lock WHERE vpi = :vpi ")) {
      return update.bind("vpi", vpi.toString()).execute();
    }
  }

  @Override
  public Optional<JoinedActiveMtMessage> findActiveMtMessageWithVpiLock(ActiveMtMessageId activeMtMessageId) {
    Validate.notNull(activeMtMessageId, "activeMtMessageId");

    try (Query query = jdbiHandle.createQuery(
        "SELECT * FROM vehicle_lock JOIN mt_message ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id JOIN active_mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id WHERE active_mt_message_id = :active_mt_message_id FOR UPDATE")) {
      return query
          .bind("active_mt_message_id", activeMtMessageId.toLong())
          .map(joinedActiveMtMessageRowMapper)
          .findFirst();
    }
  }

  @Override
  public List<JoinedActiveMtMessage> findActiveMtMessagesByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery(
        "SELECT * FROM vehicle_lock JOIN mt_message ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id JOIN active_mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id WHERE vpi = :vpi")) {
      return query
          .bind("vpi", vpi.toString())
          .map(joinedActiveMtMessageRowMapper)
          .list();
    }
  }

  @Override
  public List<JoinedActiveMtMessage> findActiveMtMessagesByVpiWithVpiLock(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery(
        "SELECT * FROM vehicle_lock JOIN mt_message ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id JOIN active_mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id WHERE vpi = :vpi FOR UPDATE")) {
      return query
          .bind("vpi", vpi.toString())
          .map(joinedActiveMtMessageRowMapper)
          .list();
    }
  }

  @Override
  public List<PersistedVehicleLock> findAndLockByVpis(Set<Vpi> vpis) {
    Validate.notEmpty(vpis, "vpis");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM vehicle_lock WHERE vpi IN (<vpi>) FOR UPDATE")) {
      return query
          .bindList("vpi", vpis.stream().map(Vpi::toString).toList())
          .map(VehicleLockRowMapper.INSTANCE)
          .list();
    }
  }

  @Override
  public Optional<AssetCapabilityState> findAssetCapabilityState(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery(
        "SELECT * FROM vehicle_lock JOIN channel_capability ON channel_capability.vehicle_lock_id = vehicle_lock.vehicle_lock_id WHERE vpi = :vpi")) {
      return query
          .bind("vpi", vpi.toString())
          .map(AssetCapabilityStateRowMapper.INSTANCE)
          .findFirst();
    }
  }

  @Override
  public List<VpiToTimeout> findGreatestActiveMtMessageTimeoutGroupByVpi() {
    try (Query query = jdbiHandle.createQuery(
        "SELECT vehicle_lock.vpi, MAX(active_mt_message.timeout) as timeout FROM active_mt_message JOIN mt_message on mt_message.mt_message_id = active_mt_message.mt_message_id JOIN vehicle_lock on vehicle_lock.vehicle_lock_id = mt_message.vehicle_lock_id GROUP BY vehicle_lock.vpi")) {
      return query
          .setFetchSize(500)
          .map(VpiToTimeoutRowMapper.INSTANCE)
          .list();
    }
  }

  @Override
  public List<JoinedMtMessage> findMtMessageAndActiveMtMessages(Vpi vpi) {
    try (Query query = jdbiHandle.createQuery(
        "SELECT * FROM vehicle_lock JOIN mt_message ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id LEFT JOIN active_mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id WHERE vpi = :vpi")) {
      return query
          .setFetchSize(100)
          .bind("vpi", vpi.toString())
          .map(joinedMtMessageRowMapper)
          .list();
    }
  }

  @Override
  public Optional<PersistedMtMessage> findMtMessageById(MtMessageId mtMessageId) {
    Validate.notNull(mtMessageId, "mtMessageId");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM mt_message WHERE mt_message_id = :mt_message_id")) {
      return query
          .bind("mt_message_id", mtMessageId.toLong())
          .map(MtMessageRowMapper.INSTANCE)
          .findFirst();
    }
  }

  @Override
  public List<PersistedMtMessage> findMtMessagesByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery(
        "SELECT mt_message.* FROM mt_message JOIN vehicle_lock ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id WHERE vpi = :vpi")) {
      return query
          .bind("vpi", vpi.toString())
          .map(MtMessageRowMapper.INSTANCE)
          .list();
    }
  }

  @Override
  public List<PersistedMtMessage> findMtMessagesByVpiAndQueueId(Vpi vpi, QueueId queueId) {
    Validate.notNull(vpi, "vpi");
    Validate.notNull(queueId, "queueId");

    try (Query query = jdbiHandle.createQuery(
        "SELECT mt_message.* FROM mt_message JOIN vehicle_lock ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id WHERE queue_id = :queueId AND vpi = :vpi")) {
      return query
          .bind("queueId", queueId.toString())
          .bind("vpi", vpi.toString())
          .map(MtMessageRowMapper.INSTANCE)
          .list();
    }
  }

  @Override
  public List<PersistedMtMessage> findOldestNonActiveMtMessages(Vpi vpi, int limit) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery(
        "SELECT mt_message.* FROM mt_message JOIN vehicle_lock ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id LEFT JOIN active_mt_message ON mt_message.mt_message_id = active_mt_message.mt_message_id WHERE vpi = :vpi AND active_mt_message_id IS NULL ORDER BY mt_message_created ASC, mt_message.mt_message_id ASC LIMIT :limit")) {
      return query
          .bind("vpi", vpi.toString())
          .bind("limit", limit)
          .map(MtMessageRowMapper.INSTANCE)
          .list();
    }
  }

  @Override
  public Optional<PgStatIndex> findPgStatIndex(IndexName indexName) {
    Validate.notNull(indexName, "indexName");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM pgstatindex(:indexName)")) {
      return query
          .bind("indexName", indexName.toString())
          .map(PgStatIndexRowMapper.INSTANCE)
          .findFirst();
    }
  }

  @Override
  public Optional<PgStatTuple> findPgStatTuple(TableName tableName) {
    Validate.notNull(tableName, "tableName");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM pgstattuple(:tableName)")) {
      return query
          .bind("tableName", tableName.toString())
          .map(PgStatTupleRowMapper.INSTANCE)
          .findFirst();
    }
  }

  @Override
  public List<JoinedActiveMtMessage> findTimedOutActiveMtMessagesWithVpiLock(int limit) {
    Validate.isPositive(limit, "limit");

    try (Query query = jdbiHandle.createQuery(
        "SELECT * FROM vehicle_lock JOIN mt_message ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id JOIN active_mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id WHERE timeout <= :now FOR UPDATE SKIP LOCKED LIMIT :limit")) {
      return query
          .bind("limit", limit)
          .bind("now", clock.instant())
          .map(joinedActiveMtMessageRowMapper)
          .list();
    }
  }

  @Override
  public Optional<PersistedVehicleLock> findVehicleLockByVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (Query query = jdbiHandle.createQuery("SELECT * FROM vehicle_lock WHERE vpi = :vpi")) {
      return query
          .bind("vpi", vpi.toString())
          .map(VehicleLockRowMapper.INSTANCE)
          .findFirst();
    }
  }

  @Override
  public Either<InsertionFailure, ActiveMtMessageId> insertActiveMtMessage(ActiveMtMessage activeMtMessage) {
    Validate.notNull(activeMtMessage, "activeMtMessage");

    try (Update update = jdbiHandle.createUpdate(
        "INSERT INTO active_mt_message (mt_message_id, active_mt_message_created, active_mt_message_updated, send_schema_step_id, retry_attempt, timeout) VALUES (:mt_message_id, :active_mt_message_created, :active_mt_message_updated, :send_schema_step_id, :retry_attempt, :timeout)")) {
      Instant now = clock.instant();
      ResultBearing resultBearing = update
          .bind("mt_message_id", activeMtMessage.getMtMessageId().toLong())
          .bind("active_mt_message_created", now)
          .bind("active_mt_message_updated", now)
          .bind("send_schema_step_id", activeMtMessage.getSendSchemaStepId().toInt())
          .bind("retry_attempt", activeMtMessage.getRetryAttempt().toShort())
          .bind("timeout", activeMtMessage.getTimeout())
          .executeAndReturnGeneratedKeys("active_mt_message_id");

      return Either.right(ActiveMtMessageId.ofLong(resultBearing.mapTo(Long.class).one()));
    } catch (RuntimeException e) {
      return logAndCreateInsertionFailure(e);
    }
  }

  @Override
  public Either<InsertionFailure, MtMessageId> insertMtMessage(MtMessage mtMessage) {
    Validate.notNull(mtMessage, "mtMessage");

    try (Update update = jdbiHandle.createUpdate(
        "INSERT INTO mt_message (vehicle_lock_id, mt_message_created, tid, send_schema_name, queue_id, srp_destination_service, srp_destination_version, srp_level_12, srp_payload, correlation_id, reply_to) VALUES (:vehicle_lock_id, :mt_message_created, :tid, :send_schema_name, :queue_id, :srp_destination_service, :srp_destination_version, :srp_level_12, :srp_payload, :correlation_id, :reply_to)")) {
      Optional<ReplyOption> replyOption = mtMessage.getReplyOption();
      SrpOption srpOption = mtMessage.getSrpOption();

      ResultBearing resultBearing = update
          .bind("vehicle_lock_id", mtMessage.getVehicleLockId().toLong())
          .bind("mt_message_created", clock.instant())
          .bind("tid", mtMessage.getTid().toString())
          .bind("send_schema_name", mtMessage.getSendSchemaName().toString())
          .bind("queue_id", mtMessage.getQueueId().toString())
          .bind("srp_destination_service", srpOption.getSrpDestinationService().toInt())
          .bind("srp_destination_version", srpOption.getSrpDestinationVersion().toShort())
          .bind("srp_level_12", srpOption.isSrpLevel12())
          .bind("srp_payload", srpOption.getSrpPayload().getImmutableByteArray().toByteArray())
          .bind("correlation_id", replyOption.map(value -> value.getCorrelationId().toString()).orElse(null))
          .bind("reply_to", replyOption.map(option -> option.getReplyTo().toString()).orElse(null))
          .executeAndReturnGeneratedKeys("mt_message_id");

      return Either.right(MtMessageId.ofLong(resultBearing.mapTo(Long.class).one()));
    } catch (RuntimeException e) {
      return logAndCreateInsertionFailure(e);
    }
  }

  @Override
  public Either<InsertionFailure, VehicleLockId> insertVehicleLock(VehicleLock vehicleLock) {
    Validate.notNull(vehicleLock, "vehicleLock");

    try (Update update = jdbiHandle.createUpdate("INSERT INTO vehicle_lock (vehicle_lock_created, vpi) VALUES (:vehicle_lock_created, :vpi)")) {
      ResultBearing resultBearing = update
          .bind("vehicle_lock_created", clock.instant())
          .bind("vpi", vehicleLock.getVpi().toString())
          .executeAndReturnGeneratedKeys("vehicle_lock_id");
      return Either.right(VehicleLockId.ofLong(resultBearing.mapTo(Long.class).one()));
    } catch (RuntimeException e) {
      return logAndCreateInsertionFailure(e);
    }
  }

  @Override
  public void rollbackTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not rollback a transaction on a closed handle");
    }

    if (!jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction not started");
    }
    jdbiHandle.rollback();
  }

  @Override
  public void startTransaction() {
    if (jdbiHandle.isClosed()) {
      throw new IllegalStateException("could not begin a transaction on a closed handle");
    }

    if (jdbiHandle.isInTransaction()) {
      throw new IllegalStateException("transaction already started");
    }
    jdbiHandle.begin();
  }

  @Override
  public void startTransactionWithLockTimeout() {
    startTransaction();
    jdbiHandle.execute("SELECT set_config('lock_timeout', ?, TRUE)", String.valueOf(lockTimeout.toMillis()));
  }

  @Override
  public int[] updateActiveMtMessages(Collection<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters) {
    Validate.notEmpty(updateActiveMtMessageParameters, "updateActiveMtMessageParameters");

    try (PreparedBatch preparedBatch = jdbiHandle.prepareBatch(
        "UPDATE active_mt_message SET active_mt_message_updated = :active_mt_message_updated, retry_attempt = :retry_attempt, send_schema_step_id = :send_schema_step_id, timeout = :timeout WHERE active_mt_message_id = :active_mt_message_id")) {
      Instant now = clock.instant();

      for (UpdateActiveMtMessageParameter updateActiveMtMessageParameter : updateActiveMtMessageParameters) {
        preparedBatch
            .bind("active_mt_message_id", updateActiveMtMessageParameter.getActiveMtMessageId().toLong())
            .bind("active_mt_message_updated", now)
            .bind("retry_attempt", updateActiveMtMessageParameter.getRetryAttempt().toShort())
            .bind("send_schema_step_id", updateActiveMtMessageParameter.getSendSchemaStepId().toInt())
            .bind("timeout", updateActiveMtMessageParameter.getTimeout())
            .add();
      }

      return preparedBatch.execute();
    }
  }

  @Override
  public void updateAssetCapabilityState(AssetCapabilityState assetCapabilityState) {
    Validate.notNull(assetCapabilityState, "assetCapabilityState");

    String sql = """
        WITH locked_vehicle AS (SELECT vehicle_lock_id FROM vehicle_lock WHERE vpi = :vpi FOR UPDATE)
        INSERT INTO channel_capability (vehicle_lock_id, channel, is_enabled, created_at, updated_at)
        VALUES ((SELECT vehicle_lock_id FROM locked_vehicle), :channel, :isEnabled, :now, :now)
        ON CONFLICT (vehicle_lock_id, channel) DO UPDATE SET is_enabled = :isEnabled, updated_at = :now
        """;

    try (Update update = jdbiHandle.createUpdate(sql)) {
      update.bind("vpi", assetCapabilityState.vpi().toString())
          .bind("channel", assetCapabilityState.assetCapability().name())
          .bind("isEnabled", assetCapabilityState.isEnabled())
          .bind("now", clock.instant())
          .execute();
    }
  }
}
