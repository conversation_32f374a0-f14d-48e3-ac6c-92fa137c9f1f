package com.volvo.tisp.tgwmts.database.model.mtmessage;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SrpDestinationVersion {
  public static final short MAX_VALUE = 255;

  private final short srpDestinationVersionValue;

  private SrpDestinationVersion(short srpDestinationVersionValue) {
    this.srpDestinationVersionValue = srpDestinationVersionValue;
  }

  /**
   * @throws IllegalArgumentException if {@code srpDestinationVersionValue} is less than zero or greater than {@value #MAX_VALUE}.
   */
  public static SrpDestinationVersion ofShort(short srpDestinationVersionValue) {
    Validate.notNegativeAndNotGreaterThan(srpDestinationVersionValue, MAX_VALUE, "srpDestinationVersionValue");

    return new SrpDestinationVersion(srpDestinationVersionValue);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    SrpDestinationVersion other = (SrpDestinationVersion) object;
    return srpDestinationVersionValue == other.srpDestinationVersionValue;
  }

  @Override
  public int hashCode() {
    return Short.hashCode(srpDestinationVersionValue);
  }

  public short toShort() {
    return srpDestinationVersionValue;
  }

  @Override
  public String toString() {
    return Short.toString(srpDestinationVersionValue);
  }
}
