package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.util.Objects;

import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ReplyOption {
  private final CorrelationId correlationId;
  private final ReplyTo replyTo;

  ReplyOption(ReplyOptionBuilder replyOptionBuilder) {
    Validate.notNull(replyOptionBuilder, "replyOptionBuilder");

    correlationId = replyOptionBuilder.getCorrelationId();
    replyTo = replyOptionBuilder.getReplyTo();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    }
    if (object == null || getClass() != object.getClass()) {
      return false;
    }

    ReplyOption other = (ReplyOption) object;
    return correlationId.equals(other.correlationId) && replyTo.equals(other.replyTo);
  }

  public CorrelationId getCorrelationId() {
    return correlationId;
  }

  public ReplyTo getReplyTo() {
    return replyTo;
  }

  @Override
  public int hashCode() {
    return Objects.hash(correlationId, replyTo);
  }

  @Override
  public String toString() {
    return "correlationId=" + correlationId + ", replyTo=" + replyTo;
  }
}
