package com.volvo.tisp.tgwmts.database.api;

import java.io.Closeable;
import java.util.List;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.VpiToTimeout;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.stat.IndexName;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatIndex;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatTuple;
import com.volvo.tisp.tgwmts.database.model.stat.TableName;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

public interface ActiveMtMessageReader extends Closeable {
  @Override
  void close();

  int countActiveMtMessages();

  int countActiveMtMessagesByVehicleLockId(VehicleLockId vehicleLockId);

  int countMtMessages();

  int countMtMessagesByVehicleLockId(VehicleLockId vehicleLockId);

  int countMtMessagesByVehicleLockIdAndQueueId(VehicleLockId vehicleLockId, QueueId queueId);

  List<JoinedActiveMtMessage> findActiveMtMessagesByVpi(Vpi vpi);

  Optional<AssetCapabilityState> findAssetCapabilityState(Vpi vpi);

  List<VpiToTimeout> findGreatestActiveMtMessageTimeoutGroupByVpi();

  List<JoinedMtMessage> findMtMessageAndActiveMtMessages(Vpi vpi);

  Optional<PersistedMtMessage> findMtMessageById(MtMessageId mtMessageId);

  List<PersistedMtMessage> findMtMessagesByVpi(Vpi vpi);

  /**
   * Finds a list of the oldest {@link MtMessage} which does not have a corresponding row in the active_mt_message table yet
   */
  List<PersistedMtMessage> findOldestNonActiveMtMessages(Vpi vpi, int limit);

  Optional<PgStatIndex> findPgStatIndex(IndexName indexName);

  Optional<PgStatTuple> findPgStatTuple(TableName tableName);

  Optional<PersistedVehicleLock> findVehicleLockByVpi(Vpi vpi);
}
