package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class MtMessage {
  private final QueueId queueId;
  private final Optional<ReplyOption> replyOption;
  private final SendSchemaName sendSchemaName;
  private final SrpOption srpOption;
  private final Tid tid;
  private final VehicleLockId vehicleLockId;

  MtMessage(MtMessageBuilder mtMessageBuilder) {
    Validate.notNull(mtMessageBuilder, "mtMessageBuilder");

    this.queueId = mtMessageBuilder.getQueueId();
    this.replyOption = mtMessageBuilder.getReplyOption();
    this.sendSchemaName = mtMessageBuilder.getSendSchemaName();
    this.srpOption = mtMessageBuilder.getSrpOption();
    this.tid = mtMessageBuilder.getTid();
    this.vehicleLockId = mtMessageBuilder.getVehicleLockId();
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }
    MtMessage other = (MtMessage) object;
    if (!queueId.equals(other.queueId)) {
      return false;
    } else if (!replyOption.equals(other.replyOption)) {
      return false;
    } else if (!sendSchemaName.equals(other.sendSchemaName)) {
      return false;
    } else if (!srpOption.equals(other.srpOption)) {
      return false;
    } else if (!tid.equals(other.tid)) {
      return false;
    } else if (!vehicleLockId.equals(other.vehicleLockId)) {
      return false;
    }
    return true;
  }

  public QueueId getQueueId() {
    return queueId;
  }

  public Optional<ReplyOption> getReplyOption() {
    return replyOption;
  }

  public SendSchemaName getSendSchemaName() {
    return sendSchemaName;
  }

  public SrpOption getSrpOption() {
    return srpOption;
  }

  public Tid getTid() {
    return tid;
  }

  public VehicleLockId getVehicleLockId() {
    return vehicleLockId;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + queueId.hashCode();
    result = prime * result + replyOption.hashCode();
    result = prime * result + sendSchemaName.hashCode();
    result = prime * result + srpOption.hashCode();
    result = prime * result + tid.hashCode();
    result = prime * result + vehicleLockId.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("queueId=")
        .append(queueId)
        .append(", replyOption={")
        .append(replyOption)
        .append("}, sendSchemaName=")
        .append(sendSchemaName)
        .append(", srpOption={")
        .append(srpOption)
        .append("}, tid=")
        .append(tid)
        .append(", vehicleLockId=")
        .append(vehicleLockId)
        .toString();
  }
}
