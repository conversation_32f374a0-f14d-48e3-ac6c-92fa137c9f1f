package com.volvo.tisp.tgwmts.database.db.mtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class JoinedMtMessageRowMapper implements RowMapper<JoinedMtMessage> {
  private final RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper;
  private final RowMapper<PersistedMtMessage> mtMessageRowMapper;
  private final RowMapper<PersistedVehicleLock> vehicleLockRowMapper;

  private JoinedMtMessageRowMapper(RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper,
                                   RowMapper<PersistedMtMessage> mtMessageRowMapper,
                                   RowMapper<PersistedVehicleLock> vehicleLockRowMapper) {
    this.activeMtMessageRowMapper = activeMtMessageRowMapper;
    this.mtMessageRowMapper = mtMessageRowMapper;
    this.vehicleLockRowMapper = vehicleLockRowMapper;
  }

  public static RowMapper<JoinedMtMessage> create(RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper,
                                                  RowMapper<PersistedMtMessage> mtMessageRowMapper,
                                                  RowMapper<PersistedVehicleLock> vehicleLockRowMapper) {
    Validate.notNull(activeMtMessageRowMapper, "activeMtMessageRowMapper");
    Validate.notNull(mtMessageRowMapper, "mtMessageRowMapper");
    Validate.notNull(vehicleLockRowMapper, "vehicleLockRowMapper");

    return new JoinedMtMessageRowMapper(activeMtMessageRowMapper, mtMessageRowMapper, vehicleLockRowMapper);
  }

  @Override
  public JoinedMtMessage map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    Optional<PersistedActiveMtMessage> persistedActiveMtMessage = getPersistedActiveMtMessage(resultSet, statementContext);
    PersistedMtMessage persistedMtMessage = mtMessageRowMapper.map(resultSet, statementContext);
    PersistedVehicleLock persistedVehicleLock = vehicleLockRowMapper.map(resultSet, statementContext);


    return new JoinedMtMessage(persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);
  }

  private Optional<PersistedActiveMtMessage> getPersistedActiveMtMessage(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    if (resultSet.getString("active_mt_message_id") == null)
      return Optional.empty();
    else
      return Optional.ofNullable(activeMtMessageRowMapper.map(resultSet, statementContext));
  }
}
