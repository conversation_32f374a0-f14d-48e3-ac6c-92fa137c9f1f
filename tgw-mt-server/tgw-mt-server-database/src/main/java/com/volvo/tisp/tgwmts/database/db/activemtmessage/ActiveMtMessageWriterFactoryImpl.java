package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.time.Clock;
import java.time.Duration;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.transaction.TransactionIsolationLevel;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ActiveMtMessageWriterFactoryImpl implements ActiveMtMessageWriterFactory {
  private final Clock clock;
  private final Jdbi jdbi;
  private final RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper;
  private final RowMapper<JoinedMtMessage> joinedMtMessageRowMapper;
  private final Duration lockTimeout;

  private ActiveMtMessageWriterFactoryImpl(Clock clock, Jdbi jdbi, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration lockTimeout) {
    this.clock = clock;
    this.jdbi = jdbi;
    this.joinedActiveMtMessageRowMapper = joinedActiveMtMessageRowMapper;
    this.joinedMtMessageRowMapper = joinedMtMessageRowMapper;
    this.lockTimeout = lockTimeout;
  }

  public static ActiveMtMessageWriterFactory create(Clock clock, Jdbi jdbi, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration lockTimeout) {
    Validate.notNull(clock, "clock");
    Validate.notNull(jdbi, "jdbi");
    Validate.notNull(joinedActiveMtMessageRowMapper, "joinedActiveMtMessageRowMapper");
    Validate.notNull(joinedMtMessageRowMapper, "joinedMtMessageRowMapper");
    Validate.isPositive(lockTimeout, "lockTimeout");

    return new ActiveMtMessageWriterFactoryImpl(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, lockTimeout);
  }

  @Override
  public ActiveMtMessageWriter createReadCommitted() {
    return create(TransactionIsolationLevel.READ_COMMITTED);
  }

  @Override
  public ActiveMtMessageWriter createReadUncommitted() {
    return create(TransactionIsolationLevel.READ_UNCOMMITTED);
  }

  @Override
  public ActiveMtMessageWriter createRepeatableRead() {
    return create(TransactionIsolationLevel.REPEATABLE_READ);
  }

  @Override
  public ActiveMtMessageWriter createSerializable() {
    return create(TransactionIsolationLevel.SERIALIZABLE);
  }

  private ActiveMtMessageWriter create(TransactionIsolationLevel transactionIsolationLevel) {
    Handle handle = jdbi.open();
    handle.setTransactionIsolation(transactionIsolationLevel);
    return ActiveMtMessageWriterImpl.create(clock, handle, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, lockTimeout);
  }
}
