package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.time.Instant;

import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.StatementContext;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ActiveMtMessageRowMapper implements RowMapper<PersistedActiveMtMessage> {
  public static final RowMapper<PersistedActiveMtMessage> INSTANCE = new ActiveMtMessageRowMapper();

  private ActiveMtMessageRowMapper() {
    // do nothing
  }

  private static ActiveMtMessageId getActiveMtMessageId(ResultSet resultSet, String columnName) throws SQLException {
    return ActiveMtMessageId.ofLong(resultSet.getLong(columnName));
  }

  private static Instant getInstant(ResultSet resultSet, String columnName) throws SQLException {
    return resultSet.getTimestamp(columnName).toInstant();
  }

  private static MtMessageId getMtMessageId(ResultSet resultSet, String columnName) throws SQLException {
    return MtMessageId.ofLong(resultSet.getLong(columnName));
  }

  private static RetryAttempt getRetryAttempt(ResultSet resultSet, String columnName) throws SQLException {
    return RetryAttempt.ofShort(resultSet.getShort(columnName));
  }

  private static SendSchemaStepId getSendSchemaStepId(ResultSet resultSet, String columnName) throws SQLException {
    return SendSchemaStepId.ofInt(resultSet.getInt(columnName));
  }

  private static ActiveMtMessage mapActiveMtMessage(ResultSet resultSet) throws SQLException {
    return new ActiveMtMessageBuilder()
        .setMtMessageId(getMtMessageId(resultSet, "mt_message_id"))
        .setRetryAttempt(getRetryAttempt(resultSet, "retry_attempt"))
        .setSendSchemaStepId(getSendSchemaStepId(resultSet, "send_schema_step_id"))
        .setTimeout(getInstant(resultSet, "timeout"))
        .build();
  }

  @Override
  public PersistedActiveMtMessage map(ResultSet resultSet, StatementContext statementContext) throws SQLException {
    Validate.notNull(resultSet, "resultSet");

    return new PersistedActiveMtMessageBuilder()
        .setActiveMtMessage(mapActiveMtMessage(resultSet))
        .setActiveMtMessageId(getActiveMtMessageId(resultSet, "active_mt_message_id"))
        .setCreated(getInstant(resultSet, "active_mt_message_created"))
        .setUpdated(getInstant(resultSet, "active_mt_message_updated"))
        .build();
  }
}
