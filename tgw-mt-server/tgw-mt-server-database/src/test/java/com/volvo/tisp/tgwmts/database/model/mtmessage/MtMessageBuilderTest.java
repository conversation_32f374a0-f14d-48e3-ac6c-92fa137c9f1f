package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageBuilderTest {
  private static void compare(MtMessageBuilder mtMessageBuilder, MtMessage mtMessage) {
    Assertions.assertSame(mtMessageBuilder.getQueueId(), mtMessage.getQueueId());
    Assertions.assertSame(mtMessageBuilder.getReplyOption(), mtMessage.getReplyOption());
    Assertions.assertSame(mtMessageBuilder.getSendSchemaName(), mtMessage.getSendSchemaName());
    Assertions.assertSame(mtMessageBuilder.getSrpOption(), mtMessage.getSrpOption());
    Assertions.assertSame(mtMessageBuilder.getTid(), mtMessage.getTid());
    Assertions.assertSame(mtMessageBuilder.getVehicleLockId(), mtMessage.getVehicleLockId());
  }

  @Test
  void buildTest() {
    MtMessageBuilder mtMessageBuilder = new MtMessageBuilder();
    AssertThrows.illegalArgumentException(mtMessageBuilder::build, "queueId must not be null");

    mtMessageBuilder.setQueueId(TestUtils.QUEUE_ID);
    AssertThrows.illegalArgumentException(mtMessageBuilder::build, "sendSchemaName must not be null");

    mtMessageBuilder.setSendSchemaName(TestUtils.SEND_SCHEMA_NAME);
    AssertThrows.illegalArgumentException(mtMessageBuilder::build, "srpOption must not be null");

    mtMessageBuilder.setSrpOption(TestUtils.createSrpOption());
    AssertThrows.illegalArgumentException(mtMessageBuilder::build, "tid must not be null");

    mtMessageBuilder.setTid(TestUtils.TID);
    AssertThrows.illegalArgumentException(mtMessageBuilder::build, "vehicleLockId must not be null");

    mtMessageBuilder.setVehicleLockId(TestUtils.VEHICLE_LOCK_ID);
    compare(mtMessageBuilder, mtMessageBuilder.build());
  }

  @Test
  void queueIdTest() {
    MtMessageBuilder mtMessageBuilder = new MtMessageBuilder();
    Assertions.assertNull(mtMessageBuilder.getQueueId());

    mtMessageBuilder.setQueueId(TestUtils.QUEUE_ID);
    Assertions.assertSame(TestUtils.QUEUE_ID, mtMessageBuilder.getQueueId());

    AssertThrows.illegalArgumentException(() -> mtMessageBuilder.setQueueId(null), "queueId must not be null");
    Assertions.assertSame(TestUtils.QUEUE_ID, mtMessageBuilder.getQueueId());
  }

  @Test
  void replyOptionTest() {
    MtMessageBuilder mtMessageBuilder = new MtMessageBuilder();
    Assertions.assertTrue(mtMessageBuilder.getReplyOption().isEmpty());

    final Optional<ReplyOption> replyOption = Optional.of(TestUtils.createReplyOption());
    mtMessageBuilder.setReplyOption(replyOption);
    Assertions.assertSame(replyOption, mtMessageBuilder.getReplyOption());

    AssertThrows.illegalArgumentException(() -> mtMessageBuilder.setReplyOption(null), "replyOption must not be null");
    Assertions.assertSame(replyOption, mtMessageBuilder.getReplyOption());
  }

  @Test
  void sendSchemaNameTest() {
    MtMessageBuilder mtMessageBuilder = new MtMessageBuilder();
    Assertions.assertNull(mtMessageBuilder.getSendSchemaName());

    mtMessageBuilder.setSendSchemaName(TestUtils.SEND_SCHEMA_NAME);
    Assertions.assertSame(TestUtils.SEND_SCHEMA_NAME, mtMessageBuilder.getSendSchemaName());

    AssertThrows.illegalArgumentException(() -> mtMessageBuilder.setSendSchemaName(null), "sendSchemaName must not be null");
    Assertions.assertSame(TestUtils.SEND_SCHEMA_NAME, mtMessageBuilder.getSendSchemaName());
  }

  @Test
  void srpOptionTest() {
    MtMessageBuilder mtMessageBuilder = new MtMessageBuilder();
    Assertions.assertNull(mtMessageBuilder.getSrpOption());

    final SrpOption srpOption = TestUtils.createSrpOption();
    mtMessageBuilder.setSrpOption(srpOption);
    Assertions.assertSame(srpOption, mtMessageBuilder.getSrpOption());

    AssertThrows.illegalArgumentException(() -> mtMessageBuilder.setSrpOption(null), "srpOption must not be null");
    Assertions.assertSame(srpOption, mtMessageBuilder.getSrpOption());
  }

  @Test
  void tidTest() {
    MtMessageBuilder mtMessageBuilder = new MtMessageBuilder();
    Assertions.assertNull(mtMessageBuilder.getTid());

    mtMessageBuilder.setTid(TestUtils.TID);
    Assertions.assertSame(TestUtils.TID, mtMessageBuilder.getTid());

    AssertThrows.illegalArgumentException(() -> mtMessageBuilder.setTid(null), "tid must not be null");
    Assertions.assertSame(TestUtils.TID, mtMessageBuilder.getTid());
  }

  @Test
  void vehicleLockIdTest() {
    MtMessageBuilder mtMessageBuilder = new MtMessageBuilder();
    Assertions.assertNull(mtMessageBuilder.getVehicleLockId());

    mtMessageBuilder.setVehicleLockId(TestUtils.VEHICLE_LOCK_ID);
    Assertions.assertSame(TestUtils.VEHICLE_LOCK_ID, mtMessageBuilder.getVehicleLockId());

    AssertThrows.illegalArgumentException(() -> mtMessageBuilder.setVehicleLockId(null), "vehicleLockId must not be null");
    Assertions.assertSame(TestUtils.VEHICLE_LOCK_ID, mtMessageBuilder.getVehicleLockId());
  }
}
