package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.time.Clock;
import java.time.Duration;
import java.util.function.Function;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.transaction.TransactionIsolationLevel;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ActiveMtMessageWriterFactoryImplTest {
  private static final Duration LOCK_TIMEOUT = Duration.ofSeconds(42);

  private static void verifyCreate(Function<ActiveMtMessageWriterFactory, ActiveMtMessageWriter> function,
      TransactionIsolationLevel transactionIsolationLevel) {
    final Clock clock = Mockito.mock(Clock.class);
    final Handle handle = Mockito.mock(Handle.class);

    final Jdbi jdbi = Mockito.mock(Jdbi.class);
    Mockito.when(jdbi.open()).thenReturn(handle);

    ActiveMtMessageWriterFactory mtMessageWriterFactory = ActiveMtMessageWriterFactoryImpl.create(clock, jdbi, TestUtils.mockRowMapper(),
        TestUtils.mockRowMapper(), LOCK_TIMEOUT);
    Assertions.assertNotSame(function.apply(mtMessageWriterFactory), function.apply(mtMessageWriterFactory));

    Mockito.verify(handle, Mockito.times(2)).setTransactionIsolation(transactionIsolationLevel);
    Mockito.verifyNoMoreInteractions(handle);
  }

  @Test
  void createInvalidTest() {
    final Clock clock = Mockito.mock(Clock.class);
    final Jdbi jdbi = Mockito.mock(Jdbi.class);
    final RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper = TestUtils.mockRowMapper();
    final RowMapper<JoinedMtMessage> joinedMtMessageRowMapper = TestUtils.mockRowMapper();

    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterFactoryImpl.create(null, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "clock must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterFactoryImpl.create(clock, null, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "jdbi must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterFactoryImpl.create(clock, jdbi, null, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "joinedActiveMtMessageRowMapper must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, null, LOCK_TIMEOUT),
        "joinedMtMessageRowMapper must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, null),
        "lockTimeout must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, Duration.ZERO),
        "lockTimeout must be positive: PT0S");
  }

  @Test
  void createReadCommittedTest() {
    verifyCreate(ActiveMtMessageWriterFactory -> ActiveMtMessageWriterFactory.createReadCommitted(), TransactionIsolationLevel.READ_COMMITTED);
  }

  @Test
  void createReadUncommittedTest() {
    verifyCreate(ActiveMtMessageWriterFactory -> ActiveMtMessageWriterFactory.createReadUncommitted(), TransactionIsolationLevel.READ_UNCOMMITTED);
  }

  @Test
  void createRepeatableReadTest() {
    verifyCreate(ActiveMtMessageWriterFactory -> ActiveMtMessageWriterFactory.createRepeatableRead(), TransactionIsolationLevel.REPEATABLE_READ);
  }

  @Test
  void createSerializableTest() {
    verifyCreate(ActiveMtMessageWriterFactory -> ActiveMtMessageWriterFactory.createSerializable(), TransactionIsolationLevel.SERIALIZABLE);
  }
}
