package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SrpOptionTest {
  @Test
  void equalsAndHashcodeTest() {
    SrpOption srpOption = TestUtils.createSrpOption();
    AssertUtils.assertEqualsAndHashCode(srpOption, TestUtils.createSrpOption());

    Assertions.assertNotEquals(srpOption, TestUtils.createSrpOptionBuilder().setSrpDestinationService(SrpDestinationService.ofInt(51)).build());
    Assertions.assertNotEquals(srpOption, TestUtils.createSrpOptionBuilder().setSrpDestinationVersion(SrpDestinationVersion.ofShort((short) 2)).build());
    Assertions.assertNotEquals(srpOption,
        TestUtils.createSrpOptionBuilder().setSrpPayload(SrpPayload.ofImmutableByteArray(ImmutableByteArray.of(new byte[] {1, 2, 3}))).build());
  }

  @Test
  void getSrpDestinationServiceTest() {
    Assertions.assertEquals(TestUtils.SRP_DESTINATION_SERVICE, TestUtils.createSrpOption().getSrpDestinationService());
  }

  @Test
  void getSrpDestinationVersionTest() {
    Assertions.assertEquals(TestUtils.SRP_DESTINATION_VERSION, TestUtils.createSrpOption().getSrpDestinationVersion());
  }

  @Test
  void getSrpPayloadTest() {
    Assertions.assertEquals(TestUtils.SRP_PAYLOAD, TestUtils.createSrpOption().getSrpPayload());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new SrpOption(null), "srpOptionBuilder must not be null");
  }

  @Test
  void isSrpLevel12Test() {
    Assertions.assertEquals(TestUtils.SRP_LEVEL_12, TestUtils.createSrpOption().isSrpLevel12());
  }

  @Test
  void toStringTest() {
    String expectedString = "srpDestinationService=50, srpDestinationVersion=1, srpLevel12=false, srpPayload=[42]";
    Assertions.assertEquals(expectedString, TestUtils.createSrpOption().toString());
  }
}
