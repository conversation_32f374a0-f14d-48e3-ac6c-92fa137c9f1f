package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.time.Clock;
import java.time.Duration;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ActiveMtMessageReaderFactoryImplTest {
  private static final Duration LOCK_TIMEOUT = Duration.ofSeconds(42);

  @Test
  void createInvalidTest() {
    final Clock clock = Mockito.mock(Clock.class);
    final Jdbi jdbi = Mockito.mock(Jdbi.class);
    final RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper = TestUtils.mockRowMapper();
    final RowMapper<JoinedMtMessage> joinedMtMessageRowMapper = TestUtils.mockRowMapper();

    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageReaderFactoryImpl.create(null, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "clock must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageReaderFactoryImpl.create(clock, null, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "jdbi must not be null");
    AssertThrows.illegalArgumentException(() -> ActiveMtMessageReaderFactoryImpl.create(clock, jdbi, null, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "joinedActiveMtMessageRowMapper must not be null");
    AssertThrows.illegalArgumentException(() -> ActiveMtMessageReaderFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, null, LOCK_TIMEOUT),
        "joinedMtMessageRowMapper must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageReaderFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, null),
        "lockTimeout must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageReaderFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, Duration.ZERO),
        "lockTimeout must be positive: PT0S");
  }

  @Test
  void createTest() {
    final Clock clock = Mockito.mock(Clock.class);

    final Jdbi jdbi = Mockito.mock(Jdbi.class);
    Mockito.when(jdbi.open()).thenReturn(Mockito.mock(Handle.class));

    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = ActiveMtMessageReaderFactoryImpl.create(clock, jdbi, TestUtils.mockRowMapper(),
        TestUtils.mockRowMapper(), LOCK_TIMEOUT);
    Assertions.assertNotSame(activeMtMessageReaderFactory.create(), activeMtMessageReaderFactory.create());
  }
}
