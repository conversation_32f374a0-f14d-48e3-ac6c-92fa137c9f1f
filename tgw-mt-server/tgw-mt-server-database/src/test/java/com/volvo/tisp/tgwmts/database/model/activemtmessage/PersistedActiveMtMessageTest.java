package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedActiveMtMessageTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new PersistedActiveMtMessage(null), "persistedActiveMtMessageBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "activeMtMessageId=3, activeMtMessage={mtMessageId=2, retryAttempt=10, sendSchemaStepId=3, timeout=1970-01-01T00:01:00Z, created=1970-01-01T00:00:00Z}, updated=1970-01-01T00:00:00Z";
    Assertions.assertEquals(expectedString, TestUtils.createPersistedActiveMtMessage().toString());
  }
}
