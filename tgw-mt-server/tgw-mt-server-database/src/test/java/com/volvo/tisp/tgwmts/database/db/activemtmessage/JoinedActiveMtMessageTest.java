package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class JoinedActiveMtMessageTest {
  private static JoinedActiveMtMessage createJoinedActiveMtMessage() {
    return new JoinedActiveMtMessage(TestUtils.createPersistedActiveMtMessage(), TestUtils.createPersistedMtMessage(), TestUtils.createPersistedVehicleLock());
  }

  @Test
  void constructorTest() {
    final PersistedActiveMtMessage persistedActiveMtMessage = TestUtils.createPersistedActiveMtMessage();
    final PersistedMtMessage persistedMtMessage = TestUtils.createPersistedMtMessage();
    final PersistedVehicleLock persistedVehicleLock = TestUtils.createPersistedVehicleLock();

    JoinedActiveMtMessage joinedActiveMtMessage = new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);
    Assertions.assertSame(persistedActiveMtMessage, joinedActiveMtMessage.persistedActiveMtMessage());
    Assertions.assertSame(persistedMtMessage, joinedActiveMtMessage.persistedMtMessage());
    Assertions.assertSame(persistedVehicleLock, joinedActiveMtMessage.persistedVehicleLock());
  }

  @Test
  void invalidConstructorTest() {
    final PersistedActiveMtMessage persistedActiveMtMessage = TestUtils.createPersistedActiveMtMessage();
    final PersistedMtMessage persistedMtMessage = TestUtils.createPersistedMtMessage();
    final PersistedVehicleLock persistedVehicleLock = TestUtils.createPersistedVehicleLock();

    AssertThrows.illegalArgumentException(() -> new JoinedActiveMtMessage(null, persistedMtMessage, persistedVehicleLock),
        "persistedActiveMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> new JoinedActiveMtMessage(persistedActiveMtMessage, null, persistedVehicleLock),
        "persistedMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, null),
        "persistedVehicleLock must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "persistedActiveMtMessage={activeMtMessageId=3, activeMtMessage={mtMessageId=2, retryAttempt=10, sendSchemaStepId=3, timeout=1970-01-01T00:01:00Z, created=1970-01-01T00:00:00Z}, updated=1970-01-01T00:00:00Z}, persistedMtMessage={created=1970-01-01T00:00:00Z, mtMessage={queueId=vps, replyOption={Optional[correlationId=b4d4b26f-142a-4556-9af5-46d3e10068d8, replyTo=reply-to-queue-name]}, sendSchemaName=COMMON_HIGH, srpOption={srpDestinationService=50, srpDestinationVersion=1, srpLevel12=false, srpPayload=[42]}, tid=123456789012345678901234567890FF, vehicleLockId=1}, mtMessageId=2}, persistedVehicleLock={created=1970-01-01T00:00:00Z, vehicleLockId=1, vehicleLock={vpi=12345678901234567890ABCDEFABCDEF}}";
    Assertions.assertEquals(expectedString, createJoinedActiveMtMessage().toString());
  }
}
