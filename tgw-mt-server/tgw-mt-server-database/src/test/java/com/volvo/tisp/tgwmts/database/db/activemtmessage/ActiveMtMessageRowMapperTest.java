package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ActiveMtMessageRowMapperTest {
  private static void checkActiveMtMessage(ActiveMtMessage activeMtMessage) {
    Assertions.assertEquals(TestUtils.MT_MESSAGE_ID, activeMtMessage.getMtMessageId());
    Assertions.assertEquals(TestUtils.RETRY_ATTEMPT, activeMtMessage.getRetryAttempt());
    Assertions.assertEquals(TestUtils.SEND_SCHEMA_STEP_ID, activeMtMessage.getSendSchemaStepId());
    Assertions.assertEquals(TestUtils.TIMEOUT, activeMtMessage.getTimeout());
  }

  private static void checkPersistedActiveMtMessage(PersistedActiveMtMessage persistedActiveMtMessage) {
    Assertions.assertEquals(TestUtils.ACTIVE_MT_MESSAGE_ID, persistedActiveMtMessage.getActiveMtMessageId());
    Assertions.assertEquals(TestUtils.CREATED, persistedActiveMtMessage.getCreated());
    Assertions.assertEquals(TestUtils.UPDATED, persistedActiveMtMessage.getUpdated());

    checkActiveMtMessage(persistedActiveMtMessage.getActiveMtMessage());
  }

  private static void mockGetBoolean(ResultSet resultSet, String columnLabel, boolean returnValue) throws SQLException {
    Mockito.when((resultSet.getBoolean(columnLabel))).thenReturn(returnValue);
  }

  private static void mockGetInt(ResultSet resultSet, String columnLabel, int returnValue) throws SQLException {
    Mockito.when(Integer.valueOf(resultSet.getInt(columnLabel))).thenReturn(Integer.valueOf(returnValue));
  }

  private static void mockGetLong(ResultSet resultSet, String columnLabel, long returnValue) throws SQLException {
    Mockito.when(Long.valueOf(resultSet.getLong(columnLabel))).thenReturn(Long.valueOf(returnValue));
  }

  private static void mockGetShort(ResultSet resultSet, String columnLabel, short returnValue) throws SQLException {
    Mockito.when(Short.valueOf(resultSet.getShort(columnLabel))).thenReturn(Short.valueOf(returnValue));
  }

  private static ResultSet mockResultSet() throws SQLException {
    ResultSet resultSet = Mockito.mock(ResultSet.class);

    Mockito.when(resultSet.getTimestamp("active_mt_message_created")).thenReturn(Timestamp.from(TestUtils.CREATED));
    mockGetLong(resultSet, "active_mt_message_id", TestUtils.ACTIVE_MT_MESSAGE_ID.toLong());
    mockGetInt(resultSet, "send_schema_step_id", TestUtils.SEND_SCHEMA_STEP_ID.toInt());
    mockGetLong(resultSet, "mt_message_id", TestUtils.MT_MESSAGE_ID.toLong());
    mockGetShort(resultSet, "retry_attempt", TestUtils.RETRY_ATTEMPT.toShort());
    Mockito.when(resultSet.getTimestamp("timeout")).thenReturn(Timestamp.from(TestUtils.TIMEOUT));
    Mockito.when(resultSet.getTimestamp("active_mt_message_updated")).thenReturn(Timestamp.from(TestUtils.UPDATED));

    return resultSet;
  }

  @Test
  void invalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> ActiveMtMessageRowMapper.INSTANCE.map(null, null), "resultSet must not be null");
  }

  @Test
  void mapTest() throws SQLException {
    PersistedActiveMtMessage persistedActiveMtMessage = ActiveMtMessageRowMapper.INSTANCE.map(mockResultSet(), null);
    checkPersistedActiveMtMessage(persistedActiveMtMessage);
  }
}
