package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class VehicleLockIdTest {
  @Test
  void equalsAndHashcodeTest() {
    VehicleLockId vehicleLockId = VehicleLockId.ofLong(1);
    AssertUtils.assertEqualsAndHashCode(vehicleLockId, VehicleLockId.ofLong(1));

    Assertions.assertNotEquals(vehicleLockId, VehicleLockId.ofLong(2));
  }

  @Test
  void ofLongInvalidTest() {
    AssertThrows.illegalArgumentException(() -> VehicleLockId.ofLong(-1), "id must be positive: -1");
    AssertThrows.illegalArgumentException(() -> VehicleLockId.ofLong(0), "id must be positive: 0");
  }

  @Test
  void toLongTest() {
    Assertions.assertEquals(1, VehicleLockId.ofLong(1).toLong());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("1", VehicleLockId.ofLong(1).toString());
  }
}
