package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class ActiveMtMessageIdTest {
  @Test
  void equalsAndHashcodeTest() {
    AssertUtils.assertEqualsAndHashCode(TestUtils.ACTIVE_MT_MESSAGE_ID, ActiveMtMessageId.ofLong(3));

    Assertions.assertNotEquals(TestUtils.ACTIVE_MT_MESSAGE_ID, ActiveMtMessageId.ofLong(1));
  }

  @Test
  void ofLongInvalidTest() {
    AssertThrows.illegalArgumentException(() -> ActiveMtMessageId.ofLong(-1), "id must be positive: -1");
    AssertThrows.illegalArgumentException(() -> ActiveMtMessageId.ofLong(0), "id must be positive: 0");
  }

  @Test
  void toLongTest() {
    Assertions.assertEquals(3, TestUtils.ACTIVE_MT_MESSAGE_ID.toLong());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("1", ActiveMtMessageId.ofLong(1).toString());
  }
}
