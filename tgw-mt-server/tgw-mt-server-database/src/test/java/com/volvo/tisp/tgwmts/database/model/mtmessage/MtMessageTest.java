package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class MtMessageTest {
  private static SrpOption createSrpOption() {
    return TestUtils.createSrpOptionBuilder().setSrpDestinationService(SrpDestinationService.ofInt(51)).build();
  }

  @Test
  void equalsAndHashcodeTest() {
    MtMessage mtMessage = TestUtils.createMtMessage();
    AssertUtils.assertEqualsAndHashCode(mtMessage, TestUtils.createMtMessage());

    Assertions.assertNotEquals(mtMessage, TestUtils.createMtMessageBuilder().setQueueId(QueueId.ofString("123")).build());
    Assertions.assertNotEquals(mtMessage, TestUtils.createMtMessageBuilder().setReplyOption(Optional.empty()).build());
    Assertions.assertNotEquals(mtMessage, TestUtils.createMtMessageBuilder().setSendSchemaName(SendSchemaName.COMMON_MID).build());
    Assertions.assertNotEquals(mtMessage, TestUtils.createMtMessageBuilder().setSrpOption(createSrpOption()).build());
    Assertions.assertNotEquals(mtMessage, TestUtils.createMtMessageBuilder().setTid(Tid.ofString("00000000000000000000000000000000")).build());
    Assertions.assertNotEquals(mtMessage, TestUtils.createMtMessageBuilder().setVehicleLockId(VehicleLockId.ofLong(2)).build());
  }

  @Test
  void getQueueIdTest() {
    Assertions.assertEquals(TestUtils.QUEUE_ID, TestUtils.createMtMessage().getQueueId());
  }

  @Test
  void getReplyOptionTest() {
    Assertions.assertEquals(Optional.of(TestUtils.createReplyOption()), TestUtils.createMtMessage().getReplyOption());
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertEquals(TestUtils.SEND_SCHEMA_NAME, TestUtils.createMtMessage().getSendSchemaName());
  }

  @Test
  void getSrpOptionTest() {
    Assertions.assertEquals(TestUtils.createSrpOption(), TestUtils.createMtMessage().getSrpOption());
  }

  @Test
  void getTidTest() {
    Assertions.assertEquals(TestUtils.TID, TestUtils.createMtMessage().getTid());
  }

  @Test
  void getVehicleLockIdTest() {
    Assertions.assertEquals(TestUtils.VEHICLE_LOCK_ID, TestUtils.createMtMessage().getVehicleLockId());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new MtMessage(null), "mtMessageBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "queueId=vps, replyOption={Optional[correlationId=b4d4b26f-142a-4556-9af5-46d3e10068d8, replyTo=reply-to-queue-name]}, sendSchemaName=COMMON_HIGH, srpOption={srpDestinationService=50, srpDestinationVersion=1, srpLevel12=false, srpPayload=[42]}, tid=123456789012345678901234567890FF, vehicleLockId=1";
    Assertions.assertEquals(expectedString, TestUtils.createMtMessage().toString());
  }
}
