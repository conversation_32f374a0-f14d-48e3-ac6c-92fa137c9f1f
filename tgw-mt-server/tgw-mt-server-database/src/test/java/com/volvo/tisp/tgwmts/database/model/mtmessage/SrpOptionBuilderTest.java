package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SrpOptionBuilderTest {
  private static void compare(SrpOptionBuilder srpOptionBuilder, SrpOption srpOption) {
    Assertions.assertSame(srpOptionBuilder.getSrpDestinationService(), srpOption.getSrpDestinationService());
    Assertions.assertSame(srpOptionBuilder.getSrpDestinationVersion(), srpOption.getSrpDestinationVersion());
    Assertions.assertSame(srpOptionBuilder.getSrpPayload(), srpOption.getSrpPayload());
    Assertions.assertEquals(srpOptionBuilder.isSrpLevel12(), srpOption.isSrpLevel12());
  }

  @Test
  void buildTest() {
    SrpOptionBuilder srpOptionBuilder = new SrpOptionBuilder();
    AssertThrows.illegalArgumentException(srpOptionBuilder::build, "srpDestinationService must not be null");

    srpOptionBuilder.setSrpDestinationService(TestUtils.SRP_DESTINATION_SERVICE);
    AssertThrows.illegalArgumentException(srpOptionBuilder::build, "srpDestinationVersion must not be null");

    srpOptionBuilder.setSrpDestinationVersion(TestUtils.SRP_DESTINATION_VERSION);
    AssertThrows.illegalArgumentException(srpOptionBuilder::build, "srpPayload must not be null");

    srpOptionBuilder.setSrpPayload(TestUtils.SRP_PAYLOAD);

    compare(srpOptionBuilder, srpOptionBuilder.build());
  }

  @Test
  void srpDestinationServiceTest() {
    SrpOptionBuilder srpOptionBuilder = new SrpOptionBuilder();
    Assertions.assertNull(srpOptionBuilder.getSrpDestinationService());

    srpOptionBuilder.setSrpDestinationService(TestUtils.SRP_DESTINATION_SERVICE);
    Assertions.assertSame(TestUtils.SRP_DESTINATION_SERVICE, srpOptionBuilder.getSrpDestinationService());

    AssertThrows.illegalArgumentException(() -> srpOptionBuilder.setSrpDestinationService(null), "srpDestinationService must not be null");
    Assertions.assertSame(TestUtils.SRP_DESTINATION_SERVICE, srpOptionBuilder.getSrpDestinationService());
  }

  @Test
  void srpDestinationVersionTest() {
    SrpOptionBuilder srpOptionBuilder = new SrpOptionBuilder();
    Assertions.assertNull(srpOptionBuilder.getSrpDestinationVersion());

    srpOptionBuilder.setSrpDestinationVersion(TestUtils.SRP_DESTINATION_VERSION);
    Assertions.assertSame(TestUtils.SRP_DESTINATION_VERSION, srpOptionBuilder.getSrpDestinationVersion());

    AssertThrows.illegalArgumentException(() -> srpOptionBuilder.setSrpDestinationVersion(null), "srpDestinationVersion must not be null");
    Assertions.assertSame(TestUtils.SRP_DESTINATION_VERSION, srpOptionBuilder.getSrpDestinationVersion());
  }

  @Test
  void srpLevel12Test() {
    SrpOptionBuilder srpOptionBuilder = new SrpOptionBuilder();
    Assertions.assertFalse(srpOptionBuilder.isSrpLevel12());

    srpOptionBuilder.setSrpLevel12(true);
    Assertions.assertTrue(srpOptionBuilder.isSrpLevel12());
  }

  @Test
  void srpPayloadTest() {
    SrpOptionBuilder srpOptionBuilder = new SrpOptionBuilder();
    Assertions.assertNull(srpOptionBuilder.getSrpPayload());

    srpOptionBuilder.setSrpPayload(TestUtils.SRP_PAYLOAD);
    Assertions.assertSame(TestUtils.SRP_PAYLOAD, srpOptionBuilder.getSrpPayload());

    AssertThrows.illegalArgumentException(() -> srpOptionBuilder.setSrpPayload(null), "srpPayload must not be null");
    Assertions.assertSame(TestUtils.SRP_PAYLOAD, srpOptionBuilder.getSrpPayload());
  }
}
