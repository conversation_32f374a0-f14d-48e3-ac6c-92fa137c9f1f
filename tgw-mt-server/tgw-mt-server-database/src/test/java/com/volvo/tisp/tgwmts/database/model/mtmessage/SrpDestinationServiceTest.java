package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SrpDestinationServiceTest {
  @Test
  void equalsAndHashcodeTest() {
    final SrpDestinationService srpDestinationService = SrpDestinationService.ofInt(42);
    AssertUtils.assertEqualsAndHashCode(srpDestinationService, SrpDestinationService.ofInt(42));

    Assertions.assertNotEquals(srpDestinationService, SrpDestinationService.ofInt(43));
  }

  @Test
  void ofIntTest() {
    Assertions.assertEquals(42, SrpDestinationService.ofInt(42).toInt());

    AssertThrows.illegalArgumentException(() -> SrpDestinationService.ofInt(-1), "srpDestinationServiceValue must not be negative: -1");
    AssertThrows.illegalArgumentException(() -> SrpDestinationService.ofInt(65_536), "srpDestinationServiceValue must not be greater than 65535: 65536");
  }

  @Test
  void toIntTest() {
    Assertions.assertEquals(SrpDestinationService.MAX_VALUE, SrpDestinationService.ofInt(65_535).toInt());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("42", SrpDestinationService.ofInt(42).toString());
  }
}
