package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.io.IOException;
import java.lang.Thread.State;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import javax.sql.DataSource;

import org.awaitility.Awaitility;
import org.awaitility.core.ConditionTimeoutException;
import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.mapper.RowMapper;
import org.jdbi.v3.core.statement.UnableToExecuteStatementException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.testcontainers.utility.DockerImageName;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.db.DbUtils;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameterBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ActiveMtMessageWriterImplTest {
  private static final Duration LOCK_TIMEOUT = Duration.ofSeconds(42);
  private static final Vpi VPI_2 = Vpi.ofString("FFFFFFFFFFFFFFFFFFFFFFFFFFFFFFFF");

  private static void awaitThreadState(Thread thread, State state) {
    try {
      Awaitility.await().atMost(Duration.ofSeconds(2)).until(() -> thread.getState() == state);
    } catch (ConditionTimeoutException e) {
      Assertions.fail(e.getMessage() + ", thread state: " + thread.getState());
    }
  }

  private static void checkInsertionFailure(InsertionFailure insertionFailure, InsertionFailureReason expectedInsertionFailureReason, String expectedMessage) {
    Assertions.assertEquals(expectedInsertionFailureReason, insertionFailure.insertionFailureReason());
    Assertions.assertEquals(expectedMessage, insertionFailure.runtimeException().getCause().getMessage());
  }

  private static ActiveMtMessageWriter createActiveMtMessageWriter(DataSource dataSource) {
    return DbUtils.createActiveMtMessageWriterFactory(dataSource).createReadCommitted();
  }

  private static ActiveMtMessageId createAndInsertActiveMtMessage(ActiveMtMessageWriter activeMtMessageWriter, MtMessageId mtMessageId) {
    return insertActiveMtMessage(activeMtMessageWriter, TestUtils.createActiveMtMessage(mtMessageId));
  }

  private static void createAndInsertActiveMtMessageWithFutureTimeout(ActiveMtMessageWriter activeMtMessageWriter, VehicleLockId vehicleLockId) {
    MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
    insertActiveMtMessage(activeMtMessageWriter, TestUtils.createActiveMtMessage(mtMessageId, Instant.now().plusSeconds(3600)));
  }

  private static void createAndInsertActiveMtMessages(ActiveMtMessageWriter activeMtMessageWriter, VehicleLockId vehicleLockId, int numberOfActiveMtMessages) {
    for (int i = 0; i < numberOfActiveMtMessages; ++i) {
      createAndInsertActiveMtMessage(activeMtMessageWriter, createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId));
    }
  }

  private static MtMessageId createAndInsertMtMessage(ActiveMtMessageWriter activeMtMessageWriter, VehicleLockId vehicleLockId) {
    return insertMtMessage(activeMtMessageWriter, TestUtils.createMtMessage(vehicleLockId));
  }

  private static MtMessageId createAndInsertMtMessage(ActiveMtMessageWriter activeMtMessageWriter, VehicleLockId vehicleLockId, QueueId queueId) {
    return insertMtMessage(activeMtMessageWriter, TestUtils.createMtMessage(vehicleLockId, queueId));
  }

  private static VehicleLockId createAndInsertVehicleLock(ActiveMtMessageWriter activeMtMessageWriter) {
    return insertVehicleLock(activeMtMessageWriter, TestUtils.createVehicleLock());
  }

  private static MtMessageId createAndInsertVehicleLockAndMtMessage(ActiveMtMessageWriter activeMtMessageWriter) {
    return createAndInsertMtMessage(activeMtMessageWriter, createAndInsertVehicleLock(activeMtMessageWriter));
  }

  private static void createAndInsertVehicleLockAndMtMessages(ActiveMtMessageWriter activeMtMessageWriter, int numberOfMtMessages) {
    VehicleLockId vehicleLockId = createAndInsertVehicleLock(activeMtMessageWriter);

    for (int i = 0; i < numberOfMtMessages; ++i) {
      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
    }
  }

  private static ActiveMtMessageId createAndInsertVehicleLockMtMessageAndActiveMtMessage(ActiveMtMessageWriter activeMtMessageWriter) {
    MtMessageId mtMessageId = createAndInsertVehicleLockAndMtMessage(activeMtMessageWriter);
    return createAndInsertActiveMtMessage(activeMtMessageWriter, mtMessageId);
  }

  private static void createLockByVpiConsumer(ActiveMtMessageWriter activeMtMessageWriter) {
    List<JoinedActiveMtMessage> joinedActiveMtMessages = activeMtMessageWriter.findActiveMtMessagesByVpiWithVpiLock(TestUtils.VPI);

    int size = joinedActiveMtMessages.size();
    if (size != 1) {
      throw new IllegalStateException("Test failed, joinedActiveMtMessages did not contain exactly one entry, was " + size);
    }
  }

  private static Collection<UpdateActiveMtMessageParameter> createUpdateActiveMtMessageParameters(SendSchemaStepId sendSchemaStepId, RetryAttempt retryAttempt,
      int count) {
    Collection<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters = new ArrayList<>();

    for (int i = 1; i <= count; ++i) {
      updateActiveMtMessageParameters.add(
          new UpdateActiveMtMessageParameterBuilder()
              .setActiveMtMessageId(ActiveMtMessageId.ofLong(i))
              .setRetryAttempt(retryAttempt)
              .setSendSchemaStepId(sendSchemaStepId)
              .setTimeout(TestUtils.TIMEOUT)
              .build());
    }

    return updateActiveMtMessageParameters;
  }

  private static ActiveMtMessageId insertActiveMtMessage(ActiveMtMessageWriter activeMtMessageWriter, ActiveMtMessage activeMtMessage) {
    return activeMtMessageWriter.insertActiveMtMessage(activeMtMessage).getRight();
  }

  private static MtMessageId insertMtMessage(ActiveMtMessageWriter activeMtMessageWriter, MtMessage mtMessage) {
    return activeMtMessageWriter.insertMtMessage(mtMessage).getRight();
  }

  private static VehicleLockId insertVehicleLock(ActiveMtMessageWriter activeMtMessageWriter, VehicleLock vehicleLock) {
    return activeMtMessageWriter.insertVehicleLock(vehicleLock).getRight();
  }

  private static void runVerifyLockTimeout(ActiveMtMessageWriterFactory activeMtMessageWriterFactory, Consumer<ActiveMtMessageWriter> lockConsumer) {
    AtomicReference<Optional<RuntimeException>> atomicReference = new AtomicReference<>(Optional.empty());

    Thread thread = new Thread(() -> verifyLockTimeout(activeMtMessageWriterFactory, atomicReference, lockConsumer));

    try {
      thread.start();
    } finally {
      awaitThreadState(thread, State.TERMINATED);
    }

    Optional<RuntimeException> optionalRuntimeException = atomicReference.get();
    Assertions.assertTrue(optionalRuntimeException.isEmpty(), optionalRuntimeException.toString());
  }

  private static EmbeddedPostgres startEmbeddedPostgres() throws IOException {
    return EmbeddedPostgres.builder()
        .setImage(DockerImageName.parse("artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/postgres:latest"))
        .setLocaleConfig("locale", "C")
        .start();
  }

  private static void verifyFindAndLockByVpis(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi) {
    verifyPersistedVehicleLocks(activeMtMessageWriter.findAndLockByVpis(Set.of(vpi)), vpi);
  }

  private static void verifyForUpdateRowLocking(AtomicReference<Thread> atomicReference, CountDownLatch countDownLatch1, CountDownLatch countDownLatch2,
      Thread firstThread, Thread secondThread) {
    firstThread.start();
    awaitThreadState(firstThread, State.WAITING);
    awaitThreadState(secondThread, State.NEW);
    Assertions.assertSame(firstThread, atomicReference.get());

    secondThread.start();
    awaitThreadState(secondThread, State.RUNNABLE);
    awaitThreadState(firstThread, State.WAITING);
    Assertions.assertEquals(1, countDownLatch1.getCount());
    Assertions.assertEquals(1, countDownLatch2.getCount());
    Assertions.assertSame(firstThread, atomicReference.get());

    countDownLatch1.countDown();

    awaitThreadState(firstThread, State.TERMINATED);
    awaitThreadState(secondThread, State.WAITING);
    Assertions.assertEquals(0, countDownLatch1.getCount());
    Assertions.assertEquals(1, countDownLatch2.getCount());
    Assertions.assertSame(secondThread, atomicReference.get());

    countDownLatch2.countDown();

    awaitThreadState(secondThread, State.TERMINATED);
    awaitThreadState(firstThread, State.TERMINATED);
    Assertions.assertEquals(0, countDownLatch1.getCount());
    Assertions.assertEquals(0, countDownLatch2.getCount());
    Assertions.assertNull(atomicReference.get());
  }

  private static void verifyJoinedActiveMtMessage(VehicleLock vehicleLock, VehicleLockId vehicleLockId, MtMessage mtMessage, MtMessageId mtMessageId,
      ActiveMtMessage activeMtMessage, ActiveMtMessageId activeMtMessageId, JoinedActiveMtMessage joinedActiveMtMessage) {
    PersistedVehicleLock persistedVehicleLock = joinedActiveMtMessage.persistedVehicleLock();
    Assertions.assertEquals(vehicleLock.getVpi(), persistedVehicleLock.getVehicleLock().getVpi());
    Assertions.assertEquals(vehicleLockId, persistedVehicleLock.getVehicleLockId());

    PersistedMtMessage persistedMtMessage = joinedActiveMtMessage.persistedMtMessage();
    Assertions.assertEquals(mtMessageId, persistedMtMessage.getMtMessageId());
    Assertions.assertEquals(mtMessage, persistedMtMessage.getMtMessage());

    PersistedActiveMtMessage persistedActiveMtMessage = joinedActiveMtMessage.persistedActiveMtMessage();
    Assertions.assertEquals(activeMtMessageId, persistedActiveMtMessage.getActiveMtMessageId());
    Assertions.assertEquals(activeMtMessage, persistedActiveMtMessage.getActiveMtMessage());
  }

  private static void verifyLockTimeout(ActiveMtMessageWriterFactory activeMtMessageWriterFactory, AtomicReference<Optional<RuntimeException>> atomicReference,
      Consumer<ActiveMtMessageWriter> lockConsumer) {
    try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
      activeMtMessageWriter.startTransactionWithLockTimeout();
      Assertions.assertThrows(UnableToExecuteStatementException.class, () -> lockConsumer.accept(activeMtMessageWriter));
    } catch (RuntimeException e) {
      atomicReference.set(Optional.of(e));
    }
  }

  private static void verifyMtMessageAndActiveMtMessage(MtMessage mtMessage, MtMessageId mtMessageId,
      ActiveMtMessage activeMtMessage, ActiveMtMessageId activeMtMessageId, JoinedMtMessage joinedMtMessage) {

    PersistedMtMessage persistedMtMessage = joinedMtMessage.persistedMtMessage();
    Assertions.assertEquals(mtMessageId, persistedMtMessage.getMtMessageId());
    Assertions.assertEquals(mtMessage, persistedMtMessage.getMtMessage());

    PersistedActiveMtMessage persistedActiveMtMessage = joinedMtMessage.persistedActiveMtMessage().get();
    Assertions.assertEquals(activeMtMessageId, persistedActiveMtMessage.getActiveMtMessageId());
    Assertions.assertEquals(activeMtMessage, persistedActiveMtMessage.getActiveMtMessage());
  }

  private static void verifyMtMessageAndEmptyActiveMtMessage(MtMessage mtMessage, MtMessageId mtMessageId, JoinedMtMessage joinedMtMessage) {

    PersistedMtMessage persistedMtMessage = joinedMtMessage.persistedMtMessage();
    Assertions.assertEquals(mtMessageId, persistedMtMessage.getMtMessageId());
    Assertions.assertEquals(mtMessage, persistedMtMessage.getMtMessage());
    Assertions.assertTrue(joinedMtMessage.persistedActiveMtMessage().isEmpty());
  }

  private static void verifyPersistedVehicleLocks(List<PersistedVehicleLock> persistedVehicleLocks, Vpi expectedVpi) {
    Assertions.assertEquals(1, persistedVehicleLocks.size());
    Assertions.assertEquals(expectedVpi, persistedVehicleLocks.get(0).getVehicleLock().getVpi());
  }

  private static void verifySingleMtMessageExistsWithQueueId(ActiveMtMessageWriter activeMtMessageWriter, QueueId queueId) {
    List<PersistedMtMessage> persistedMtMessages = activeMtMessageWriter.findMtMessagesByVpi(TestUtils.VPI);
    Assertions.assertEquals(1, persistedMtMessages.size());
    Assertions.assertEquals(queueId, persistedMtMessages.get(0).getMtMessage().getQueueId());
    Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());
  }

  @Test
  void closeIsClosedExceptionTest() {
    Handle jdbiHandle = Mockito.mock(Handle.class);
    Mockito.when(jdbiHandle.isClosed()).thenThrow(new IllegalStateException("foo"));

    ActiveMtMessageWriter activeMtMessageWriter = ActiveMtMessageWriterImpl.create(Mockito.mock(Clock.class), jdbiHandle, TestUtils.mockRowMapper(),
        TestUtils.mockRowMapper(), LOCK_TIMEOUT);
    IllegalStateException illegalStateException = AssertThrows.illegalStateException(activeMtMessageWriter::close, "foo");
    Assertions.assertEquals(0, illegalStateException.getSuppressed().length);

    Mockito.verify(jdbiHandle).close();
  }

  @Test
  void closeThrowsExceptionTest() {
    Handle jdbiHandle = Mockito.mock(Handle.class);
    Mockito.when((jdbiHandle.isClosed())).thenThrow(new IllegalStateException("foo"));
    Mockito.doThrow(new IllegalArgumentException("bar")).when(jdbiHandle).close();

    ActiveMtMessageWriter activeMtMessageWriter = ActiveMtMessageWriterImpl.create(Mockito.mock(Clock.class), jdbiHandle, TestUtils.mockRowMapper(),
        TestUtils.mockRowMapper(), LOCK_TIMEOUT);
    IllegalStateException illegalStateException = AssertThrows.illegalStateException(activeMtMessageWriter::close, "foo");

    Assertions.assertEquals(1, illegalStateException.getSuppressed().length);

    Throwable throwable = illegalStateException.getSuppressed()[0];
    Assertions.assertEquals(IllegalArgumentException.class, throwable.getClass());
    Assertions.assertEquals("bar", throwable.getMessage());

    Mockito.verify(jdbiHandle).close();
  }

  @Test
  void commitTransactionTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalStateException(activeMtMessageWriter::commitTransaction, "transaction not started");
      activeMtMessageWriter.startTransaction();
      activeMtMessageWriter.rollbackTransaction();
      AssertThrows.illegalStateException(activeMtMessageWriter::commitTransaction, "transaction not started");
      activeMtMessageWriter.startTransaction();
      activeMtMessageWriter.commitTransaction();
      AssertThrows.illegalStateException(activeMtMessageWriter::commitTransaction, "transaction not started");
      activeMtMessageWriter.startTransaction();
      activeMtMessageWriter.close();
      AssertThrows.illegalStateException(activeMtMessageWriter::commitTransaction, "could not commit a transaction on a closed handle");
    }
  }

  @Test
  void countActiveMtMessagesByVehicleLockIdNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.countActiveMtMessagesByVehicleLockId(null), "vehicleLockId must not be null");
    }
  }

  @Test
  void countActiveMtMessagesByVehicleLockIdTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, TestUtils.createVehicleLock());

      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessagesByVehicleLockId(vehicleLockId));

      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);

      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessagesByVehicleLockId(vehicleLockId));

      insertActiveMtMessage(activeMtMessageWriter, TestUtils.createActiveMtMessage(mtMessageId));

      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessagesByVehicleLockId(vehicleLockId));

      createAndInsertActiveMtMessages(activeMtMessageWriter, vehicleLockId, 3);

      Assertions.assertEquals(4, activeMtMessageWriter.countActiveMtMessagesByVehicleLockId(vehicleLockId));
    }
  }

  @Test
  void countActiveMtMessagesTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());

      createAndInsertVehicleLockMtMessageAndActiveMtMessage(activeMtMessageWriter);

      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessages());
    }
  }

  @Test
  void countMtMessagesByVehicleLockIdAndQueueIdNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(null, TestUtils.QUEUE_ID),
          "vehicleLockId must not be null");

      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(TestUtils.VEHICLE_LOCK_ID, null),
          "queueId must not be null");
    }
  }

  @Test
  void countMtMessagesByVehicleLockIdAndQueueIdTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, TestUtils.createVehicleLock());
      QueueId anotherQueueId = QueueId.ofString("anotherQueueId");

      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, TestUtils.QUEUE_ID));
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, anotherQueueId));

      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      Assertions.assertEquals(1, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, TestUtils.QUEUE_ID));
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, anotherQueueId));
      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      Assertions.assertEquals(2, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, TestUtils.QUEUE_ID));
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, anotherQueueId));

      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId, anotherQueueId);
      Assertions.assertEquals(2, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, TestUtils.QUEUE_ID));
      Assertions.assertEquals(1, activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, anotherQueueId));
    }
  }

  @Test
  void countMtMessagesByVehicleLockIdTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.countMtMessagesByVehicleLockId(null), "vehicleLockId must not be null");

      VehicleLockId vehicleLockId = createAndInsertVehicleLock(activeMtMessageWriter);
      VehicleLockId notExistingVehicleLockId = VehicleLockId.ofLong(42);
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockId(vehicleLockId));
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockId(notExistingVehicleLockId));

      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);

      Assertions.assertEquals(1, activeMtMessageWriter.countMtMessagesByVehicleLockId(vehicleLockId));
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockId(notExistingVehicleLockId));

      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);

      Assertions.assertEquals(2, activeMtMessageWriter.countMtMessagesByVehicleLockId(vehicleLockId));
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessagesByVehicleLockId(notExistingVehicleLockId));
    }
  }

  @Test
  void countMtMessagesTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessages());

      createAndInsertVehicleLockAndMtMessages(activeMtMessageWriter, 3);

      Assertions.assertEquals(3, activeMtMessageWriter.countMtMessages());
    }
  }

  @Test
  void createInvalidTest() {
    final Clock clock = Mockito.mock(Clock.class);
    final Handle handle = Mockito.mock(Handle.class);
    final RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper = TestUtils.mockRowMapper();
    final RowMapper<JoinedMtMessage> joinedMtMessageRowMapper = TestUtils.mockRowMapper();

    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterImpl.create(null, handle, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "clock must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterImpl.create(clock, null, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "jdbiHandle must not be null");
    AssertThrows.illegalArgumentException(() -> ActiveMtMessageWriterImpl.create(clock, handle, null, joinedMtMessageRowMapper, LOCK_TIMEOUT),
        "joinedActiveMtMessageRowMapper must not be null");
    AssertThrows.illegalArgumentException(() -> ActiveMtMessageWriterImpl.create(clock, handle, joinedActiveMtMessageRowMapper, null, LOCK_TIMEOUT),
        "joinedMtMessageRowMapper must not be null");
    AssertThrows.illegalArgumentException(
        () -> ActiveMtMessageWriterImpl.create(clock, handle, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, null),
        "lockTimeout must not be null");
  }

  @Test
  void deleteMtMessageByIdNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.deleteMtMessageById(null), "mtMessageId must not be null");
    }
  }

  @Test
  void deleteMtMessageByIdOnDeleteCascadeTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.deleteMtMessageById(TestUtils.MT_MESSAGE_ID));

      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, createAndInsertVehicleLock(activeMtMessageWriter));
      createAndInsertActiveMtMessage(activeMtMessageWriter, mtMessageId);

      Assertions.assertEquals(1, activeMtMessageWriter.countMtMessages());
      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessages());

      Assertions.assertEquals(1, activeMtMessageWriter.deleteMtMessageById(mtMessageId));

      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessages());
      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());
    }
  }

  @Test
  void deleteMtMessageByIdsTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      VehicleLockId vehicleLockId = createAndInsertVehicleLock(activeMtMessageWriter);
      MtMessageId messageId1 = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      MtMessageId messageId2 = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      MtMessageId messageId3 = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      createAndInsertActiveMtMessage(activeMtMessageWriter, messageId1);

      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessages());
      Assertions.assertEquals(3, activeMtMessageWriter.countMtMessages());

      int[] ints1 = activeMtMessageWriter.deleteMtMessageByIds(List.of(messageId3));

      Assertions.assertEquals(1, ints1.length);
      Assertions.assertTrue(Arrays.stream(ints1).allMatch(i -> i == 1));
      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessages());
      Assertions.assertEquals(2, activeMtMessageWriter.countMtMessages());

      int[] ints2 = activeMtMessageWriter.deleteMtMessageByIds(List.of(messageId1, messageId2));

      Assertions.assertEquals(2, ints2.length);
      Assertions.assertTrue(Arrays.stream(ints2).allMatch(i -> i == 1));
      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessages());
    }
  }

  @Test
  void deleteMtMessagesByVpiAndQueueIdNullParameterTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.deleteMtMessagesByVpiAndQueueId(null, TestUtils.QUEUE_ID), "vpi must not be null");
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.deleteMtMessagesByVpiAndQueueId(TestUtils.VPI, null), "queueId must not be null");
    }
  }

  @Test
  void deleteMtMessagesByVpiAndQueueIdTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.deleteMtMessagesByVpiAndQueueId(TestUtils.VPI, TestUtils.QUEUE_ID));

      VehicleLockId vehicleLockId = createAndInsertVehicleLock(activeMtMessageWriter);
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      createAndInsertActiveMtMessage(activeMtMessageWriter, mtMessageId);

      QueueId queueId = QueueId.ofString("xyz");
      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId, queueId);

      Assertions.assertEquals(2, activeMtMessageWriter.countMtMessages());
      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessages());

      Assertions.assertEquals(0, activeMtMessageWriter.deleteMtMessagesByVpiAndQueueId(VPI_2, TestUtils.QUEUE_ID));
      Assertions.assertEquals(2, activeMtMessageWriter.countMtMessages());

      Assertions.assertEquals(0, activeMtMessageWriter.deleteMtMessagesByVpiAndQueueId(TestUtils.VPI, QueueId.ofString("abc")));
      Assertions.assertEquals(2, activeMtMessageWriter.countMtMessages());

      Assertions.assertEquals(1, activeMtMessageWriter.deleteMtMessagesByVpiAndQueueId(TestUtils.VPI, TestUtils.QUEUE_ID));
      verifySingleMtMessageExistsWithQueueId(activeMtMessageWriter, queueId);
    }
  }

  @Test
  void deleteVehicleLockByVpiNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.deleteVehicleLockByVpi(null), "vpi must not be null");
    }
  }

  @Test
  void deleteVehicleLockByVpiTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.deleteVehicleLockByVpi(TestUtils.VPI));

      createAndInsertVehicleLockMtMessageAndActiveMtMessage(activeMtMessageWriter);
      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessages());
      Assertions.assertEquals(1, activeMtMessageWriter.countMtMessages());

      Assertions.assertEquals(1, activeMtMessageWriter.deleteVehicleLockByVpi(TestUtils.VPI));

      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());
      Assertions.assertEquals(0, activeMtMessageWriter.countMtMessages());
      Assertions.assertTrue(activeMtMessageWriter.findVehicleLockByVpi(TestUtils.VPI).isEmpty());
    }
  }

  @Test
  void findActiveMtMessageWithVpiLockForUpdateTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres()) {
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory = DbUtils.createActiveMtMessageWriterFactory(embeddedPostgres.getPostgresDatabase());

      try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
        createAndInsertVehicleLockMtMessageAndActiveMtMessage(activeMtMessageWriter);
      }

      final AtomicReference<Thread> atomicReference = new AtomicReference<>();
      final CountDownLatch countDownLatch1 = new CountDownLatch(1);
      final CountDownLatch countDownLatch2 = new CountDownLatch(1);

      final Consumer<ActiveMtMessageWriter> consumer = activeMtMessageWriter -> {
        Optional<JoinedActiveMtMessage> optional = activeMtMessageWriter.findActiveMtMessageWithVpiLock(ActiveMtMessageId.ofLong(1));

        if (optional.isEmpty()) {
          throw new IllegalStateException("Test failed, optional is empty");
        }
      };

      final Thread firstThread = new Thread(new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, consumer, countDownLatch1),
          "TestThread-1");
      final Thread secondThread = new Thread(new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, consumer, countDownLatch2),
          "TestThread-2");

      verifyForUpdateRowLocking(atomicReference, countDownLatch1, countDownLatch2, firstThread, secondThread);
    }
  }

  @Test
  void findActiveMtMessageWithVpiLockTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findActiveMtMessageWithVpiLock(null), "activeMtMessageId must not be null");
      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());

      VehicleLock vehicleLock = TestUtils.createVehicleLock();
      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, vehicleLock);
      MtMessage mtMessage = TestUtils.createMtMessage();
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      ActiveMtMessage activeMtMessage = TestUtils.createActiveMtMessage(mtMessageId);
      ActiveMtMessageId activeMtMessageId = insertActiveMtMessage(activeMtMessageWriter, activeMtMessage);

      JoinedActiveMtMessage joinedActiveMtMessage = activeMtMessageWriter.findActiveMtMessageWithVpiLock(activeMtMessageId).orElseThrow();
      verifyJoinedActiveMtMessage(vehicleLock, vehicleLockId, mtMessage, mtMessageId, activeMtMessage, activeMtMessageId, joinedActiveMtMessage);
    }
  }

  @Test
  void findActiveMtMessagesByVpiNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findActiveMtMessagesByVpi(null), "vpi must not be null");
    }
  }

  @Test
  void findActiveMtMessagesByVpiTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      VehicleLock vehicleLock = TestUtils.createVehicleLock();
      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, vehicleLock);
      MtMessage mtMessage = TestUtils.createMtMessage();
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      ActiveMtMessage activeMtMessage = TestUtils.createActiveMtMessage(mtMessageId);
      ActiveMtMessageId activeMtMessageId = insertActiveMtMessage(activeMtMessageWriter, activeMtMessage);

      List<JoinedActiveMtMessage> joinedActiveMtMessages = activeMtMessageWriter.findActiveMtMessagesByVpi(TestUtils.VPI);
      Assertions.assertEquals(1, joinedActiveMtMessages.size());
      verifyJoinedActiveMtMessage(vehicleLock, vehicleLockId, mtMessage, mtMessageId, activeMtMessage, activeMtMessageId, joinedActiveMtMessages.get(0));

      createAndInsertActiveMtMessages(activeMtMessageWriter, vehicleLockId, 3);
      List<JoinedActiveMtMessage> activeMtMessagesByVpi = activeMtMessageWriter.findActiveMtMessagesByVpi(TestUtils.VPI);
      Assertions.assertEquals(4, activeMtMessagesByVpi.size());
    }
  }

  @Test
  void findActiveMtMessagesByVpiWithVpiLockForUpdateTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres()) {
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory = DbUtils.createActiveMtMessageWriterFactory(embeddedPostgres.getPostgresDatabase());

      try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
        createAndInsertVehicleLockMtMessageAndActiveMtMessage(activeMtMessageWriter);
      }

      final AtomicReference<Thread> atomicReference = new AtomicReference<>();
      final CountDownLatch countDownLatch1 = new CountDownLatch(1);
      final CountDownLatch countDownLatch2 = new CountDownLatch(1);

      final Consumer<ActiveMtMessageWriter> consumer = ActiveMtMessageWriterImplTest::createLockByVpiConsumer;

      final Thread firstThread = new Thread(new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, consumer, countDownLatch1),
          "TestThread-1");
      final Thread secondThread = new Thread(new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, consumer, countDownLatch2),
          "TestThread-2");

      verifyForUpdateRowLocking(atomicReference, countDownLatch1, countDownLatch2, firstThread, secondThread);
    }
  }

  @Test
  void findActiveMtMessagesByVpiWithVpiLockTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findActiveMtMessagesByVpiWithVpiLock(null), "vpi must not be null");
      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());

      VehicleLock vehicleLock = TestUtils.createVehicleLock();
      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, vehicleLock);
      MtMessage mtMessage = TestUtils.createMtMessage();
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      ActiveMtMessage activeMtMessage = TestUtils.createActiveMtMessage(mtMessageId);
      ActiveMtMessageId activeMtMessageId = insertActiveMtMessage(activeMtMessageWriter, activeMtMessage);

      List<JoinedActiveMtMessage> joinedActiveMtMessages = activeMtMessageWriter.findActiveMtMessagesByVpiWithVpiLock(TestUtils.VPI);

      Assertions.assertEquals(1, joinedActiveMtMessages.size());
      verifyJoinedActiveMtMessage(vehicleLock, vehicleLockId, mtMessage, mtMessageId, activeMtMessage, activeMtMessageId, joinedActiveMtMessages.get(0));
    }
  }

  @Test
  void findAndLockByVpisForUpdateTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres()) {
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory = DbUtils.createActiveMtMessageWriterFactory(embeddedPostgres.getPostgresDatabase());

      try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
        createAndInsertVehicleLock(activeMtMessageWriter);
      }

      final AtomicReference<Thread> atomicReference = new AtomicReference<>();
      final CountDownLatch countDownLatch1 = new CountDownLatch(1);
      final CountDownLatch countDownLatch2 = new CountDownLatch(1);

      final Consumer<ActiveMtMessageWriter> consumer = activeMtMessageWriter -> {
        List<PersistedVehicleLock> persistedVehicleLocks = activeMtMessageWriter.findAndLockByVpis(Set.of(TestUtils.VPI));

        if (persistedVehicleLocks.size() != 1) {
          throw new IllegalStateException("Test failed, persistedVehicleLocks did not contain exactly one entry");
        }
      };

      final Thread firstThread = new Thread(new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, consumer, countDownLatch1),
          "TestThread-1");
      final Thread secondThread = new Thread(new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, consumer, countDownLatch2),
          "TestThread-2");

      verifyForUpdateRowLocking(atomicReference, countDownLatch1, countDownLatch2, firstThread, secondThread);
    }
  }

  @Test
  void findAndLockByVpisNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findAndLockByVpis(null), "vpis must not be null");
    }
  }

  @Test
  void findAndLockByVpisTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.findAndLockByVpis(Set.of(TestUtils.VPI)).size());

      createAndInsertVehicleLock(activeMtMessageWriter);
      verifyFindAndLockByVpis(activeMtMessageWriter, TestUtils.VPI);

      verifyPersistedVehicleLocks(activeMtMessageWriter.findAndLockByVpis(Set.of(TestUtils.VPI, VPI_2)), TestUtils.VPI);

      insertVehicleLock(activeMtMessageWriter, VehicleLockBuilder.ofVpi(VPI_2));
      verifyFindAndLockByVpis(activeMtMessageWriter, TestUtils.VPI);
      verifyFindAndLockByVpis(activeMtMessageWriter, VPI_2);
    }
  }

  @Test
  void findGreatestActiveMtMessageTimeoutGroupByVpiTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());

      createAndInsertVehicleLockMtMessageAndActiveMtMessage(activeMtMessageWriter);

      Assertions.assertEquals(1, activeMtMessageWriter.findGreatestActiveMtMessageTimeoutGroupByVpi().size());
    }
  }

  @Test
  void findMtMessageAndActiveMtMessages() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      VehicleLock vehicleLock = TestUtils.createVehicleLock();
      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, vehicleLock);
      MtMessage mtMessage = TestUtils.createMtMessage();
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      ActiveMtMessage activeMtMessage = TestUtils.createActiveMtMessage(mtMessageId);
      ActiveMtMessageId activeMtMessageId = insertActiveMtMessage(activeMtMessageWriter, activeMtMessage);

      List<JoinedMtMessage> mtMessageAndActiveMtMessage = activeMtMessageWriter.findMtMessageAndActiveMtMessages(TestUtils.VPI);
      Assertions.assertEquals(1, mtMessageAndActiveMtMessage.size());
      verifyMtMessageAndActiveMtMessage(mtMessage, mtMessageId, activeMtMessage, activeMtMessageId, mtMessageAndActiveMtMessage.get(0));

      createAndInsertActiveMtMessages(activeMtMessageWriter, vehicleLockId, 3);
      List<JoinedMtMessage> activeMtMessagesByVpi = activeMtMessageWriter.findMtMessageAndActiveMtMessages(TestUtils.VPI);
      Assertions.assertEquals(4, activeMtMessagesByVpi.size());
    }
  }

  @Test
  void findMtMessageAndNoExistingActiveMtMessage() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      VehicleLock vehicleLock = TestUtils.createVehicleLock();
      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, vehicleLock);
      MtMessage mtMessage = TestUtils.createMtMessage();
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);

      List<JoinedMtMessage> mtMessageAndActiveMtMessage = activeMtMessageWriter.findMtMessageAndActiveMtMessages(TestUtils.VPI);
      Assertions.assertEquals(1, mtMessageAndActiveMtMessage.size());
      verifyMtMessageAndEmptyActiveMtMessage(mtMessage, mtMessageId, mtMessageAndActiveMtMessage.get(0));
    }
  }

  @Test
  void findMtMessageByIdNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findMtMessageById(null), "mtMessageId must not be null");
    }
  }

  @Test
  void findMtMessageByIdTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      VehicleLockId vehicleLockId = createAndInsertVehicleLock(activeMtMessageWriter);
      MtMessage mtMessage = TestUtils.createMtMessage(vehicleLockId);
      MtMessageId mtMessageId = insertMtMessage(activeMtMessageWriter, mtMessage);
      Optional<PersistedMtMessage> optionalPersistedMtMessage = activeMtMessageWriter.findMtMessageById(mtMessageId);

      PersistedMtMessage persistedMtMessage = optionalPersistedMtMessage.get();
      Assertions.assertEquals(mtMessageId, persistedMtMessage.getMtMessageId());
      Assertions.assertEquals(mtMessage, persistedMtMessage.getMtMessage());
    }
  }

  @Test
  void findMtMessagesByVpiAndQueueIdTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertTrue(activeMtMessageWriter.findMtMessagesByVpiAndQueueId(TestUtils.VPI, TestUtils.QUEUE_ID).isEmpty());

      VehicleLockId vehicleLockId = createAndInsertVehicleLock(activeMtMessageWriter);
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      createAndInsertActiveMtMessage(activeMtMessageWriter, mtMessageId);

      QueueId queueId = QueueId.ofString("xyz");
      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId, queueId);
      createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId, queueId);

      Assertions.assertEquals(3, activeMtMessageWriter.countMtMessages());
      Assertions.assertEquals(1, activeMtMessageWriter.countActiveMtMessages());

      Assertions.assertEquals(0, activeMtMessageWriter.findMtMessagesByVpiAndQueueId(VPI_2, TestUtils.QUEUE_ID).size());
      Assertions.assertEquals(0, activeMtMessageWriter.findMtMessagesByVpiAndQueueId(TestUtils.VPI, QueueId.ofString("abc")).size());
      Assertions.assertEquals(1, activeMtMessageWriter.findMtMessagesByVpiAndQueueId(TestUtils.VPI, TestUtils.QUEUE_ID).size());
      Assertions.assertEquals(2, activeMtMessageWriter.findMtMessagesByVpiAndQueueId(TestUtils.VPI, queueId).size());
    }
  }

  @Test
  void findMtMessagesByVpiNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findMtMessagesByVpi(null), "vpi must not be null");
    }
  }

  @Test
  void findMtMessagesByVpiTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.findMtMessagesByVpi(TestUtils.VPI).size());

      createAndInsertVehicleLockAndMtMessages(activeMtMessageWriter, 3);
      Assertions.assertEquals(0, activeMtMessageWriter.countActiveMtMessages());

      Assertions.assertEquals(activeMtMessageWriter.countMtMessages(), activeMtMessageWriter.findMtMessagesByVpi(TestUtils.VPI).size());
    }
  }

  @Test
  void findOldestNonActiveMtMessageTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findOldestNonActiveMtMessages(null, 1), "vpi must not be null");
      Assertions.assertTrue(activeMtMessageWriter.findOldestNonActiveMtMessages(TestUtils.VPI, 1).isEmpty());

      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, TestUtils.createVehicleLock());
      createAndInsertActiveMtMessages(activeMtMessageWriter, vehicleLockId, 2);
      MtMessageId mtMessageId1 = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);
      MtMessageId mtMessageId2 = createAndInsertMtMessage(activeMtMessageWriter, vehicleLockId);

      Assertions.assertEquals(mtMessageId1, activeMtMessageWriter.findOldestNonActiveMtMessages(TestUtils.VPI, 1).get(0).getMtMessageId());
      Assertions.assertEquals(2, activeMtMessageWriter.findOldestNonActiveMtMessages(TestUtils.VPI, 2).size());

      activeMtMessageWriter.deleteMtMessageById(mtMessageId1);
      Assertions.assertEquals(mtMessageId2, activeMtMessageWriter.findOldestNonActiveMtMessages(TestUtils.VPI, 1).get(0).getMtMessageId());

      activeMtMessageWriter.deleteMtMessageById(mtMessageId2);
      Assertions.assertTrue(activeMtMessageWriter.findOldestNonActiveMtMessages(TestUtils.VPI, 1).isEmpty());
    }
  }

  @Test
  void findTimedOutActiveMtMessagesWithVpiLockForUpdateSkipLockedTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres()) {
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory = DbUtils.createActiveMtMessageWriterFactory(embeddedPostgres.getPostgresDatabase());
      try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
        createAndInsertVehicleLockMtMessageAndActiveMtMessage(activeMtMessageWriter);
      }

      final AtomicReference<Thread> atomicReference = new AtomicReference<>();
      final CountDownLatch countDownLatch = new CountDownLatch(1);

      final Consumer<ActiveMtMessageWriter> consumer = activeMtMessageWriter -> {
        List<JoinedActiveMtMessage> joinedActiveMtMessages = activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(10);

        if (joinedActiveMtMessages.size() != 1) {
          throw new IllegalStateException("Test failed, joinedActiveMtMessages did not contain exactly one entry");
        }
      };

      final Thread thread = new Thread(new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, consumer, countDownLatch), "TestThread-1");

      thread.start();
      Assertions.assertEquals(1, countDownLatch.getCount());
      awaitThreadState(thread, State.WAITING);
      Assertions.assertSame(thread, atomicReference.get());

      try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
        Assertions.assertEquals(0, activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(10).size());
      }

      countDownLatch.countDown();
      Assertions.assertEquals(0, countDownLatch.getCount());
      awaitThreadState(thread, State.TERMINATED);
      Assertions.assertNull(atomicReference.get());
    }
  }

  @Test
  void findTimedOutActiveMtMessagesWithVpiLockInvalidParameterTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(0), "limit must be positive: 0");
    }
  }

  @Test
  void findTimedOutActiveMtMessagesWithVpiLockTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertEquals(0, activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(10).size());

      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, TestUtils.createVehicleLock());
      createAndInsertActiveMtMessages(activeMtMessageWriter, vehicleLockId, 5);
      createAndInsertActiveMtMessageWithFutureTimeout(activeMtMessageWriter, vehicleLockId);

      Assertions.assertEquals(5, activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(10).size());
      Assertions.assertEquals(2, activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(2).size());
    }
  }

  @Test
  void findVehicleLockByVpiNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.findVehicleLockByVpi(null), "vpi must not be null");
    }
  }

  @Test
  void findVehicleLockByVpiTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      Assertions.assertFalse(activeMtMessageWriter.findVehicleLockByVpi(TestUtils.VPI).isPresent());

      createAndInsertVehicleLock(activeMtMessageWriter);

      Optional<PersistedVehicleLock> vehicleLockByVpi = activeMtMessageWriter.findVehicleLockByVpi(TestUtils.VPI);
      Assertions.assertEquals(TestUtils.VPI, vehicleLockByVpi.get().getVehicleLock().getVpi());
    }
  }

  @Test
  void insertActiveMtMessageDuplicateKeyFailureTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, createAndInsertVehicleLock(activeMtMessageWriter));
      createAndInsertActiveMtMessage(activeMtMessageWriter, mtMessageId);

      InsertionFailure insertionFailure = activeMtMessageWriter.insertActiveMtMessage(TestUtils.createActiveMtMessage(mtMessageId)).getLeft();

      checkInsertionFailure(insertionFailure, InsertionFailureReason.DUPLICATE_KEY,
          "ERROR: duplicate key value violates unique constraint \"active_mt_message_mt_message_id_key\"\n"
              + "  Detail: Key (mt_message_id)=(1) already exists.");
    }
  }

  @Test
  void insertActiveMtMessageForeignKeyViolationFailureTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      InsertionFailure insertionFailure = activeMtMessageWriter.insertActiveMtMessage(TestUtils.createActiveMtMessage()).getLeft();

      checkInsertionFailure(insertionFailure, InsertionFailureReason.FOREIGN_KEY_VIOLATION,
          "ERROR: insert or update on table \"active_mt_message\" violates foreign key constraint \"fk_active_mt_message_mt_message_id\"\n"
              + "  Detail: Key (mt_message_id)=(2) is not present in table \"mt_message\".");
    }
  }

  @Test
  void insertActiveMtMessageNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.insertActiveMtMessage(null), "activeMtMessage must not be null");
    }
  }

  @Test
  void insertActiveMtMessageTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      MtMessageId mtMessageId = createAndInsertMtMessage(activeMtMessageWriter, createAndInsertVehicleLock(activeMtMessageWriter));
      ActiveMtMessage activeMtMessage = TestUtils.createActiveMtMessage(mtMessageId);
      ActiveMtMessageId activeMtMessageId = insertActiveMtMessage(activeMtMessageWriter, activeMtMessage);

      PersistedActiveMtMessage persistedActiveMtMessage = activeMtMessageWriter.findActiveMtMessagesByVpi(TestUtils.VPI)
          .get(0)
          .persistedActiveMtMessage();
      Assertions.assertEquals(activeMtMessage, persistedActiveMtMessage.getActiveMtMessage());
      Assertions.assertEquals(activeMtMessageId, persistedActiveMtMessage.getActiveMtMessageId());
    }
  }

  @Test
  void insertMtMessageForeignKeyViolationTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      InsertionFailure insertionFailure = activeMtMessageWriter.insertMtMessage(TestUtils.createMtMessage(VehicleLockId.ofLong(42))).getLeft();

      checkInsertionFailure(insertionFailure, InsertionFailureReason.FOREIGN_KEY_VIOLATION,
          "ERROR: insert or update on table \"mt_message\" violates foreign key constraint \"fk_mt_message_vehicle_lock_id\"\n" +
              "  Detail: Key (vehicle_lock_id)=(42) is not present in table \"vehicle_lock\".");
    }
  }

  @Test
  void insertMtMessageNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.insertMtMessage(null), "mtMessage must not be null");
    }
  }

  @Test
  void insertMtMessageTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      MtMessage mtMessage = TestUtils.createMtMessageWithoutReplyOption(createAndInsertVehicleLock(activeMtMessageWriter));
      MtMessageId mtMessageId = insertMtMessage(activeMtMessageWriter, mtMessage);
      Assertions.assertEquals(1, mtMessageId.toLong());

      PersistedMtMessage persistedMtMessage = activeMtMessageWriter.findMtMessagesByVpi(TestUtils.VPI).get(0);
      Assertions.assertEquals(DbUtils.INSTANT_2, persistedMtMessage.getCreated());
      Assertions.assertEquals(mtMessage, persistedMtMessage.getMtMessage());
      Assertions.assertEquals(mtMessageId, persistedMtMessage.getMtMessageId());
    }
  }

  @Test
  void insertVehicleLockDuplicateKeyFailureTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      createAndInsertVehicleLock(activeMtMessageWriter);
      InsertionFailure insertionFailure = activeMtMessageWriter.insertVehicleLock(TestUtils.createVehicleLock()).getLeft();

      checkInsertionFailure(insertionFailure, InsertionFailureReason.DUPLICATE_KEY,
          "ERROR: duplicate key value violates unique constraint \"vehicle_lock_vpi_key\"\n" +
              "  Detail: Key (vpi)=(12345678901234567890ABCDEFABCDEF) already exists.");
    }
  }

  @Test
  void insertVehicleLockNullTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.insertVehicleLock(null), "vehicleLock must not be null");
    }
  }

  @Test
  void insertVehicleLockTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      createAndInsertVehicleLock(activeMtMessageWriter);

      Assertions.assertTrue(activeMtMessageWriter.findVehicleLockByVpi(TestUtils.VPI).isPresent());
    }
  }

  @Test
  void rollbackTransactionTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalStateException(activeMtMessageWriter::rollbackTransaction, "transaction not started");
      activeMtMessageWriter.startTransaction();
      activeMtMessageWriter.commitTransaction();
      AssertThrows.illegalStateException(activeMtMessageWriter::rollbackTransaction, "transaction not started");
      activeMtMessageWriter.startTransaction();
      activeMtMessageWriter.rollbackTransaction();
      AssertThrows.illegalStateException(activeMtMessageWriter::rollbackTransaction, "transaction not started");
      activeMtMessageWriter.startTransaction();
      activeMtMessageWriter.close();
      AssertThrows.illegalStateException(activeMtMessageWriter::rollbackTransaction, "could not rollback a transaction on a closed handle");
    }
  }

  @Test
  void startTransactionTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      activeMtMessageWriter.startTransaction();
      AssertThrows.illegalStateException(activeMtMessageWriter::startTransaction, "transaction already started");
      activeMtMessageWriter.rollbackTransaction();
      activeMtMessageWriter.startTransaction();
      AssertThrows.illegalStateException(activeMtMessageWriter::startTransaction, "transaction already started");
      activeMtMessageWriter.commitTransaction();
      activeMtMessageWriter.startTransaction();
      AssertThrows.illegalStateException(activeMtMessageWriter::startTransaction, "transaction already started");
      activeMtMessageWriter.close();
      AssertThrows.illegalStateException(activeMtMessageWriter::startTransaction, "could not begin a transaction on a closed handle");
    }
  }

  @Test
  void startTransactionWithLockTimeoutFailsInTransactionTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      activeMtMessageWriter.startTransaction();

      AssertThrows.illegalStateException(activeMtMessageWriter::startTransactionWithLockTimeout, "transaction already started");
    }
  }

  @Test
  void startTransactionWithLockTimeoutTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres()) {
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory = DbUtils.createActiveMtMessageWriterFactory(embeddedPostgres.getPostgresDatabase());

      try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
        createAndInsertVehicleLockMtMessageAndActiveMtMessage(activeMtMessageWriter);
      }

      AtomicReference<Thread> atomicReference = new AtomicReference<>();
      CountDownLatch countDownLatch = new CountDownLatch(1);

      Thread testThread = new Thread(
          new ForUpdateTestRunnable(activeMtMessageWriterFactory, atomicReference, ActiveMtMessageWriterImplTest::createLockByVpiConsumer, countDownLatch));

      testThread.start();
      awaitThreadState(testThread, State.WAITING);
      Assertions.assertEquals(1, countDownLatch.getCount());

      Consumer<ActiveMtMessageWriter> lockByActiveMtMessageIdConsumer =
          activeMtMessageWriter -> activeMtMessageWriter.findActiveMtMessageWithVpiLock(ActiveMtMessageId.ofLong(1));

      Consumer<ActiveMtMessageWriter> lockByVpiConsumer =
          activeMtMessageWriter -> activeMtMessageWriter.findActiveMtMessagesByVpiWithVpiLock(TestUtils.VPI);

      Consumer<ActiveMtMessageWriter> lockByVpisConsumer = activeMtMessageWriter -> activeMtMessageWriter.findAndLockByVpis(Set.of(TestUtils.VPI));

      runVerifyLockTimeout(activeMtMessageWriterFactory, lockByActiveMtMessageIdConsumer);
      runVerifyLockTimeout(activeMtMessageWriterFactory, lockByVpiConsumer);
      runVerifyLockTimeout(activeMtMessageWriterFactory, lockByVpisConsumer);

      countDownLatch.countDown();
      awaitThreadState(testThread, State.TERMINATED);
    }
  }

  @Test
  void updateActiveMtMessagesTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.updateActiveMtMessages(null), "updateActiveMtMessageParameters must not be null");
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.updateActiveMtMessages(List.of()), "updateActiveMtMessageParameters must not be empty");

      VehicleLockId vehicleLockId = insertVehicleLock(activeMtMessageWriter, TestUtils.createVehicleLock());
      createAndInsertActiveMtMessages(activeMtMessageWriter, vehicleLockId, 5);

      SendSchemaStepId sendSchemaStepId = SendSchemaStepId.ofInt(2);
      RetryAttempt retryAttempt = RetryAttempt.ofShort((short) 20);

      Collection<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters = createUpdateActiveMtMessageParameters(sendSchemaStepId, retryAttempt, 5);

      int[] updatedRowArray = activeMtMessageWriter.updateActiveMtMessages(updateActiveMtMessageParameters);

      Assertions.assertArrayEquals(new int[] {1, 1, 1, 1, 1}, updatedRowArray);
      List<JoinedActiveMtMessage> joinedActiveMtMessages = activeMtMessageWriter.findActiveMtMessagesByVpi(TestUtils.VPI);
      Assertions.assertEquals(5, joinedActiveMtMessages
          .stream()
          .map(JoinedActiveMtMessage::persistedActiveMtMessage)
          .map(PersistedActiveMtMessage::getActiveMtMessage)
          .filter(activeMtMessage -> activeMtMessage.getSendSchemaStepId().equals(sendSchemaStepId))
          .filter(activeMtMessage -> activeMtMessage.getRetryAttempt().equals(retryAttempt))
          .count());
    }
  }

  @Test
  void updateAssetCapabilityStateTest() throws IOException {
    try (EmbeddedPostgres embeddedPostgres = startEmbeddedPostgres();
        ActiveMtMessageWriter activeMtMessageWriter = createActiveMtMessageWriter(embeddedPostgres.getPostgresDatabase())) {
      AssertThrows.illegalArgumentException(() -> activeMtMessageWriter.updateAssetCapabilityState(null), "assetCapabilityState must not be null");

      AssetCapabilityState assetCapabilityState = new AssetCapabilityState(TestUtils.VPI, AssetCapabilityState.AssetCapability.WIFI, true);
      Assertions.assertThrowsExactly(UnableToExecuteStatementException.class, () -> activeMtMessageWriter.updateAssetCapabilityState(assetCapabilityState));

      insertVehicleLock(activeMtMessageWriter, TestUtils.createVehicleLock());

      AssetCapabilityState wifiEnable = new AssetCapabilityState(TestUtils.VPI, AssetCapabilityState.AssetCapability.WIFI, true);
      activeMtMessageWriter.updateAssetCapabilityState(wifiEnable);

      AssetCapabilityState persistedAssetCapabilityState = activeMtMessageWriter.findAssetCapabilityState(TestUtils.VPI).get();
      Assertions.assertTrue(persistedAssetCapabilityState.isEnabled());

      AssetCapabilityState wifiDisable = new AssetCapabilityState(TestUtils.VPI, AssetCapabilityState.AssetCapability.WIFI, false);
      activeMtMessageWriter.updateAssetCapabilityState(wifiDisable);

      AssetCapabilityState updatedAssetCapabilityState = activeMtMessageWriter.findAssetCapabilityState(TestUtils.VPI).get();
      Assertions.assertFalse(updatedAssetCapabilityState.isEnabled());
    }
  }
}
