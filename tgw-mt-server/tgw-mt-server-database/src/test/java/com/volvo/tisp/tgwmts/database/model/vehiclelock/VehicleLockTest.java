package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class VehicleLockTest {
  @Test
  void equalsAndHashcodeTest() {
    VehicleLock vehicleLock = TestUtils.createVehicleLock();
    AssertUtils.assertEqualsAndHashCode(vehicleLock, TestUtils.createVehicleLock());

    Assertions.assertNotEquals(vehicleLock, VehicleLockBuilder.ofVpi(Vpi.ofString("00000000000000000000000000000000")));
  }

  @Test
  void getVpiTest() {
    Assertions.assertEquals(TestUtils.VPI, TestUtils.createVehicleLock().getVpi());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new VehicleLock(null), "vehicleLockBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "vpi=12345678901234567890ABCDEFABCDEF";
    Assertions.assertEquals(expectedString, TestUtils.createVehicleLock().toString());
  }
}
