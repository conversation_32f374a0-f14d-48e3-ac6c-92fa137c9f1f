package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedVehicleLockBuilderTest {
  private static void compare(PersistedVehicleLockBuilder persistedVehicleLockBuilder, PersistedVehicleLock persistedVehicleLock) {
    Assertions.assertSame(persistedVehicleLockBuilder.getCreated(), persistedVehicleLock.getCreated());
    Assertions.assertSame(persistedVehicleLockBuilder.getVehicleLock(), persistedVehicleLock.getVehicleLock());
    Assertions.assertSame(persistedVehicleLockBuilder.getVehicleLockId(), persistedVehicleLock.getVehicleLockId());
  }

  @Test
  void buildTest() {
    PersistedVehicleLockBuilder persistedVehicleLockBuilder = new PersistedVehicleLockBuilder();
    AssertThrows.illegalArgumentException(persistedVehicleLockBuilder::build, "created must not be null");

    persistedVehicleLockBuilder.setCreated(TestUtils.CREATED);
    AssertThrows.illegalArgumentException(persistedVehicleLockBuilder::build, "vehicleLock must not be null");

    persistedVehicleLockBuilder.setVehicleLock(TestUtils.createVehicleLock());
    AssertThrows.illegalArgumentException(persistedVehicleLockBuilder::build, "vehicleLockId must not be null");

    persistedVehicleLockBuilder.setVehicleLockId(TestUtils.VEHICLE_LOCK_ID);
    compare(persistedVehicleLockBuilder, persistedVehicleLockBuilder.build());
  }

  @Test
  void createdTest() {
    PersistedVehicleLockBuilder persistedVehicleLockBuilder = new PersistedVehicleLockBuilder();
    Assertions.assertNull(persistedVehicleLockBuilder.getCreated());

    persistedVehicleLockBuilder.setCreated(TestUtils.CREATED);
    Assertions.assertSame(TestUtils.CREATED, persistedVehicleLockBuilder.getCreated());

    AssertThrows.illegalArgumentException(() -> persistedVehicleLockBuilder.setCreated(null), "created must not be null");
    Assertions.assertSame(TestUtils.CREATED, persistedVehicleLockBuilder.getCreated());
  }

  @Test
  void vehicleLockIdTest() {
    PersistedVehicleLockBuilder persistedVehicleLockBuilder = new PersistedVehicleLockBuilder();
    Assertions.assertNull(persistedVehicleLockBuilder.getVehicleLockId());

    persistedVehicleLockBuilder.setVehicleLockId(TestUtils.VEHICLE_LOCK_ID);
    Assertions.assertSame(TestUtils.VEHICLE_LOCK_ID, persistedVehicleLockBuilder.getVehicleLockId());

    AssertThrows.illegalArgumentException(() -> persistedVehicleLockBuilder.setVehicleLockId(null), "vehicleLockId must not be null");
    Assertions.assertSame(TestUtils.VEHICLE_LOCK_ID, persistedVehicleLockBuilder.getVehicleLockId());
  }

  @Test
  void vehicleLockTest() {
    PersistedVehicleLockBuilder persistedVehicleLockBuilder = new PersistedVehicleLockBuilder();
    Assertions.assertNull(persistedVehicleLockBuilder.getVehicleLock());

    final VehicleLock vehicleLock = TestUtils.createVehicleLock();
    persistedVehicleLockBuilder.setVehicleLock(vehicleLock);
    Assertions.assertSame(vehicleLock, persistedVehicleLockBuilder.getVehicleLock());

    AssertThrows.illegalArgumentException(() -> persistedVehicleLockBuilder.setVehicleLock(null), "vehicleLock must not be null");
    Assertions.assertSame(vehicleLock, persistedVehicleLockBuilder.getVehicleLock());
  }
}
