package com.volvo.tisp.tgwmts.database.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class InsertionFailureTest {
  private static final InsertionFailureReason INSERTION_FAILURE_REASON = InsertionFailureReason.DUPLICATE_KEY;
  private static final RuntimeException RUNTIME_EXCEPTION = new RuntimeException("test");

  @Test
  void getInsertionFailureReasonTest() {
    Assertions.assertSame(INSERTION_FAILURE_REASON, new InsertionFailure(INSERTION_FAILURE_REASON, RUNTIME_EXCEPTION).insertionFailureReason());
  }

  @Test
  void getRuntimeExceptionTest() {
    Assertions.assertSame(RUNTIME_EXCEPTION, new InsertionFailure(INSERTION_FAILURE_REASON, RUNTIME_EXCEPTION).runtimeException());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new InsertionFailure(null, RUNTIME_EXCEPTION), "insertionFailureReason must not be null");
    AssertThrows.illegalArgumentException(() -> new InsertionFailure(INSERTION_FAILURE_REASON, null), "runtimeException must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "insertionFailureReason=DUPLICATE_KEY, runtimeException=java.lang.RuntimeException: test";
    Assertions.assertEquals(expectedString, new InsertionFailure(INSERTION_FAILURE_REASON, RUNTIME_EXCEPTION).toString());
  }
}
