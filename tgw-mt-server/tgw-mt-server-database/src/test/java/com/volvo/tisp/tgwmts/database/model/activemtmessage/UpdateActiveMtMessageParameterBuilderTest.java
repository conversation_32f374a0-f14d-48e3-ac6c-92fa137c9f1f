package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class UpdateActiveMtMessageParameterBuilderTest {
  private static void compare(UpdateActiveMtMessageParameterBuilder updateActiveMtMessageParameterBuilder,
      UpdateActiveMtMessageParameter updateActiveMtMessageParameter) {
    Assertions.assertSame(updateActiveMtMessageParameterBuilder.getActiveMtMessageId(), updateActiveMtMessageParameter.getActiveMtMessageId());
    Assertions.assertSame(updateActiveMtMessageParameterBuilder.getRetryAttempt(), updateActiveMtMessageParameter.getRetryAttempt());
    Assertions.assertSame(updateActiveMtMessageParameterBuilder.getSendSchemaStepId(), updateActiveMtMessageParameter.getSendSchemaStepId());
    Assertions.assertSame(updateActiveMtMessageParameterBuilder.getTimeout(), updateActiveMtMessageParameter.getTimeout());
  }

  @Test
  void activeMtMessageIdTest() {
    UpdateActiveMtMessageParameterBuilder updateActiveMtMessageParameterBuilder = new UpdateActiveMtMessageParameterBuilder();
    Assertions.assertNull(updateActiveMtMessageParameterBuilder.getActiveMtMessageId());

    updateActiveMtMessageParameterBuilder.setActiveMtMessageId(TestUtils.ACTIVE_MT_MESSAGE_ID);
    Assertions.assertSame(TestUtils.ACTIVE_MT_MESSAGE_ID, updateActiveMtMessageParameterBuilder.getActiveMtMessageId());

    AssertThrows.illegalArgumentException(() -> updateActiveMtMessageParameterBuilder.setActiveMtMessageId(null), "activeMtMessageId must not be null");
    Assertions.assertSame(TestUtils.ACTIVE_MT_MESSAGE_ID, updateActiveMtMessageParameterBuilder.getActiveMtMessageId());
  }

  @Test
  void buildTest() {
    UpdateActiveMtMessageParameterBuilder updateActiveMtMessageParameterBuilder = new UpdateActiveMtMessageParameterBuilder();
    AssertThrows.illegalArgumentException(updateActiveMtMessageParameterBuilder::build, "activeMtMessageId must not be null");

    updateActiveMtMessageParameterBuilder.setActiveMtMessageId(TestUtils.ACTIVE_MT_MESSAGE_ID);
    AssertThrows.illegalArgumentException(updateActiveMtMessageParameterBuilder::build, "retryAttempt must not be null");

    updateActiveMtMessageParameterBuilder.setRetryAttempt(TestUtils.RETRY_ATTEMPT);
    AssertThrows.illegalArgumentException(updateActiveMtMessageParameterBuilder::build, "sendSchemaStepId must not be null");

    updateActiveMtMessageParameterBuilder.setSendSchemaStepId(TestUtils.SEND_SCHEMA_STEP_ID);
    AssertThrows.illegalArgumentException(updateActiveMtMessageParameterBuilder::build, "timeout must not be null");

    updateActiveMtMessageParameterBuilder.setTimeout(TestUtils.TIMEOUT);
    compare(updateActiveMtMessageParameterBuilder, updateActiveMtMessageParameterBuilder.build());
  }

  @Test
  void retryAttemptTest() {
    UpdateActiveMtMessageParameterBuilder updateActiveMtMessageParameterBuilder = new UpdateActiveMtMessageParameterBuilder();
    Assertions.assertNull(updateActiveMtMessageParameterBuilder.getRetryAttempt());

    updateActiveMtMessageParameterBuilder.setRetryAttempt(TestUtils.RETRY_ATTEMPT);
    Assertions.assertSame(TestUtils.RETRY_ATTEMPT, updateActiveMtMessageParameterBuilder.getRetryAttempt());

    AssertThrows.illegalArgumentException(() -> updateActiveMtMessageParameterBuilder.setRetryAttempt(null), "retryAttempt must not be null");
    Assertions.assertSame(TestUtils.RETRY_ATTEMPT, updateActiveMtMessageParameterBuilder.getRetryAttempt());
  }

  @Test
  void sendSchemaStepIdTest() {
    UpdateActiveMtMessageParameterBuilder updateActiveMtMessageParameterBuilder = new UpdateActiveMtMessageParameterBuilder();
    Assertions.assertNull(updateActiveMtMessageParameterBuilder.getSendSchemaStepId());

    updateActiveMtMessageParameterBuilder.setSendSchemaStepId(TestUtils.SEND_SCHEMA_STEP_ID);
    Assertions.assertSame(TestUtils.SEND_SCHEMA_STEP_ID, updateActiveMtMessageParameterBuilder.getSendSchemaStepId());

    AssertThrows.illegalArgumentException(() -> updateActiveMtMessageParameterBuilder.setSendSchemaStepId(null), "sendSchemaStepId must not be null");
    Assertions.assertSame(TestUtils.SEND_SCHEMA_STEP_ID, updateActiveMtMessageParameterBuilder.getSendSchemaStepId());
  }

  @Test
  void timeoutTest() {
    UpdateActiveMtMessageParameterBuilder updateActiveMtMessageParameterBuilder = new UpdateActiveMtMessageParameterBuilder();
    Assertions.assertNull(updateActiveMtMessageParameterBuilder.getTimeout());

    updateActiveMtMessageParameterBuilder.setTimeout(TestUtils.TIMEOUT);
    Assertions.assertSame(TestUtils.TIMEOUT, updateActiveMtMessageParameterBuilder.getTimeout());

    AssertThrows.illegalArgumentException(() -> updateActiveMtMessageParameterBuilder.setTimeout(null), "timeout must not be null");
    Assertions.assertSame(TestUtils.TIMEOUT, updateActiveMtMessageParameterBuilder.getTimeout());
  }
}
