package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SrpPayloadTest {
  private static final ImmutableByteArray IMMUTABLE_BYTE_ARRAY = ImmutableByteArray.of(new byte[] {42});

  @Test
  void equalsAndHashCodeTest() {
    SrpPayload srpPayload = SrpPayload.ofImmutableByteArray(IMMUTABLE_BYTE_ARRAY);
    AssertUtils.assertEqualsAndHashCode(srpPayload, SrpPayload.ofImmutableByteArray(IMMUTABLE_BYTE_ARRAY));

    Assertions.assertNotEquals(srpPayload, SrpPayload.ofImmutableByteArray(ImmutableByteArray.of(new byte[2])));
  }

  @Test
  void getImmutableByteArrayTest() {
    Assertions.assertSame(IMMUTABLE_BYTE_ARRAY, SrpPayload.ofImmutableByteArray(IMMUTABLE_BYTE_ARRAY).getImmutableByteArray());
  }

  @Test
  void ofImmutableByteArrayInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SrpPayload.ofImmutableByteArray(null), "immutableByteArray must not be null");
    AssertThrows.illegalArgumentException(() -> SrpPayload.ofImmutableByteArray(ImmutableByteArray.of(new byte[1_000_001])),
        "immutableByteArray must not be greater than 1000000: 1000001");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("[42]", SrpPayload.ofImmutableByteArray(IMMUTABLE_BYTE_ARRAY).toString());
  }
}
