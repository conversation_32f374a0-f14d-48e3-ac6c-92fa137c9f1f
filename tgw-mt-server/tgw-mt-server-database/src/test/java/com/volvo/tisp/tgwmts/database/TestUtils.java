package com.volvo.tisp.tgwmts.database;

import java.time.Instant;
import java.util.Optional;

import org.jdbi.v3.core.mapper.RowMapper;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameterBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationVersion;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpPayload;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;

public final class TestUtils {
  public static final ActiveMtMessageId ACTIVE_MT_MESSAGE_ID = ActiveMtMessageId.ofLong(3);
  public static final CorrelationId CORRELATION_ID = CorrelationId.ofString("b4d4b26f-142a-4556-9af5-46d3e10068d8");
  public static final Instant CREATED = Instant.ofEpochSecond(0);
  public static final MtMessageId MT_MESSAGE_ID = MtMessageId.ofLong(2);
  public static final QueueId QUEUE_ID = QueueId.ofString("vps");
  public static final ReplyTo REPLY_TO = ReplyTo.ofString("reply-to-queue-name");
  public static final RetryAttempt RETRY_ATTEMPT = RetryAttempt.ofShort((short) 10);
  public static final SendSchemaName SEND_SCHEMA_NAME = SendSchemaName.COMMON_HIGH;
  public static final SendSchemaStepId SEND_SCHEMA_STEP_ID = SendSchemaStepId.ofInt(3);
  public static final SrpDestinationService SRP_DESTINATION_SERVICE = SrpDestinationService.ofInt(50);
  public static final SrpDestinationVersion SRP_DESTINATION_VERSION = SrpDestinationVersion.ofShort((short) 1);
  public static final boolean SRP_LEVEL_12 = false;
  public static final SrpPayload SRP_PAYLOAD = SrpPayload.ofImmutableByteArray(ImmutableByteArray.of(new byte[] {42}));
  public static final Tid TID = Tid.ofString("123456789012345678901234567890FF");
  public static final Instant TIMEOUT = Instant.ofEpochSecond(60);
  public static final Instant UPDATED = Instant.ofEpochSecond(0);
  public static final VehicleLockId VEHICLE_LOCK_ID = VehicleLockId.ofLong(1);
  public static final Vpi VPI = Vpi.ofString("12345678901234567890ABCDEFABCDEF");

  private TestUtils() {
    throw new IllegalStateException();
  }

  public static ActiveMtMessage createActiveMtMessage() {
    return createActiveMtMessageBuilder().build();
  }

  public static ActiveMtMessage createActiveMtMessage(Instant timeout) {
    return createActiveMtMessage(MT_MESSAGE_ID, timeout);
  }

  public static ActiveMtMessage createActiveMtMessage(MtMessageId mtMessageId) {
    return createActiveMtMessage(mtMessageId, TIMEOUT);
  }

  public static ActiveMtMessage createActiveMtMessage(MtMessageId mtMessageId, Instant timeout) {
    return createActiveMtMessageBuilder(mtMessageId, timeout).build();
  }

  public static ActiveMtMessageBuilder createActiveMtMessageBuilder() {
    return createActiveMtMessageBuilder(MT_MESSAGE_ID, TIMEOUT);
  }

  public static MtMessage createMtMessage() {
    return createMtMessageBuilder().build();
  }

  public static MtMessage createMtMessage(VehicleLockId vehicleLockId) {
    return createMtMessageBuilder(vehicleLockId).build();
  }

  public static MtMessage createMtMessage(VehicleLockId vehicleLockId, QueueId queueId) {
    return createMtMessageBuilder(vehicleLockId)
        .setQueueId(queueId)
        .build();
  }

  public static MtMessageBuilder createMtMessageBuilder() {
    return createMtMessageBuilder(VEHICLE_LOCK_ID);
  }

  public static MtMessageBuilder createMtMessageBuilder(VehicleLockId vehicleLockId) {
    return new MtMessageBuilder()
        .setQueueId(QUEUE_ID)
        .setReplyOption(Optional.of(createReplyOption()))
        .setSendSchemaName(SEND_SCHEMA_NAME)
        .setSrpOption(createSrpOption())
        .setTid(TID)
        .setVehicleLockId(vehicleLockId);
  }

  public static MtMessage createMtMessageWithoutReplyOption(VehicleLockId vehicleLockId) {
    return createMtMessageBuilder(vehicleLockId)
        .setReplyOption(Optional.empty())
        .build();
  }

  public static PersistedActiveMtMessage createPersistedActiveMtMessage() {
    return new PersistedActiveMtMessageBuilder()
        .setActiveMtMessage(createActiveMtMessage(TIMEOUT))
        .setActiveMtMessageId(ACTIVE_MT_MESSAGE_ID)
        .setCreated(CREATED)
        .setUpdated(UPDATED)
        .build();
  }

  public static PersistedMtMessage createPersistedMtMessage() {
    return new PersistedMtMessageBuilder()
        .setCreated(CREATED)
        .setMtMessage(createMtMessage())
        .setMtMessageId(MT_MESSAGE_ID)
        .build();
  }

  public static PersistedVehicleLock createPersistedVehicleLock() {
    return new PersistedVehicleLockBuilder()
        .setCreated(CREATED)
        .setVehicleLock(createVehicleLock())
        .setVehicleLockId(VEHICLE_LOCK_ID)
        .build();
  }

  public static ReplyOption createReplyOption() {
    return createReplyOptionBuilder().build();
  }

  public static ReplyOptionBuilder createReplyOptionBuilder() {
    return new ReplyOptionBuilder()
        .setCorrelationId(CORRELATION_ID)
        .setReplyTo(REPLY_TO);
  }

  public static SrpOption createSrpOption() {
    return createSrpOptionBuilder().build();
  }

  public static SrpOptionBuilder createSrpOptionBuilder() {
    return new SrpOptionBuilder()
        .setSrpDestinationService(SRP_DESTINATION_SERVICE)
        .setSrpDestinationVersion(SRP_DESTINATION_VERSION)
        .setSrpLevel12(SRP_LEVEL_12)
        .setSrpPayload(SRP_PAYLOAD);
  }

  public static UpdateActiveMtMessageParameter createUpdateActiveMtMessageParameter() {
    return createUpdateActiveMtMessageParameterBuilder().build();
  }

  public static UpdateActiveMtMessageParameterBuilder createUpdateActiveMtMessageParameterBuilder() {
    return new UpdateActiveMtMessageParameterBuilder()
        .setActiveMtMessageId(ACTIVE_MT_MESSAGE_ID)
        .setRetryAttempt(RETRY_ATTEMPT)
        .setSendSchemaStepId(SEND_SCHEMA_STEP_ID)
        .setTimeout(TIMEOUT);
  }

  public static VehicleLock createVehicleLock() {
    return createVehicleLockBuilder().build();
  }

  public static VehicleLockBuilder createVehicleLockBuilder() {
    return new VehicleLockBuilder()
        .setVpi(VPI);
  }

  public static <T> RowMapper<T> mockRowMapper() {
    return Mockito.mock(RowMapper.class);
  }

  private static ActiveMtMessageBuilder createActiveMtMessageBuilder(MtMessageId mtMessageId, Instant timeout) {
    return new ActiveMtMessageBuilder()
        .setMtMessageId(mtMessageId)
        .setRetryAttempt(RETRY_ATTEMPT)
        .setSendSchemaStepId(SEND_SCHEMA_STEP_ID)
        .setTimeout(timeout);
  }
}
