package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class ActiveMtMessageTest {
  @Test
  void equalsAndHashcodeTest() {
    ActiveMtMessage activeMtMessage = TestUtils.createActiveMtMessage();
    AssertUtils.assertEqualsAndHashCode(activeMtMessage, TestUtils.createActiveMtMessage());

    Assertions.assertNotEquals(activeMtMessage, TestUtils.createActiveMtMessageBuilder().setMtMessageId(MtMessageId.ofLong(5)).build());
    Assertions.assertNotEquals(activeMtMessage, TestUtils.createActiveMtMessageBuilder().setRetryAttempt(RetryAttempt.ofShort((short) 20)).build());
    Assertions.assertNotEquals(activeMtMessage, TestUtils.createActiveMtMessageBuilder().setSendSchemaStepId(SendSchemaStepId.ofInt(2)).build());
    Assertions.assertNotEquals(activeMtMessage, TestUtils.createActiveMtMessageBuilder().setTimeout(Instant.now()).build());
  }

  @Test
  void getTest() {
    Assertions.assertEquals(TestUtils.MT_MESSAGE_ID, TestUtils.createActiveMtMessage().getMtMessageId());
    Assertions.assertEquals(TestUtils.RETRY_ATTEMPT, TestUtils.createActiveMtMessage().getRetryAttempt());
    Assertions.assertEquals(TestUtils.SEND_SCHEMA_STEP_ID, TestUtils.createActiveMtMessage().getSendSchemaStepId());
    Assertions.assertEquals(TestUtils.TIMEOUT, TestUtils.createActiveMtMessage().getTimeout());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new ActiveMtMessage(null), "activeMtMessageBuilder must not be null");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("mtMessageId=2, retryAttempt=10, sendSchemaStepId=3, timeout=1970-01-01T00:01:00Z",
        TestUtils.createActiveMtMessage().toString());
  }
}
