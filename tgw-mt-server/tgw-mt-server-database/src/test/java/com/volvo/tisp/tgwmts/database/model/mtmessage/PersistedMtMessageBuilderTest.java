package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedMtMessageBuilderTest {
  private static void compare(PersistedMtMessageBuilder persistedMtMessageBuilder, PersistedMtMessage persistedMtMessage) {
    Assertions.assertSame(persistedMtMessageBuilder.getCreated(), persistedMtMessage.getCreated());
    Assertions.assertSame(persistedMtMessageBuilder.getMtMessage(), persistedMtMessage.getMtMessage());
    Assertions.assertSame(persistedMtMessageBuilder.getMtMessageId(), persistedMtMessage.getMtMessageId());
  }

  @Test
  void buildTest() {
    PersistedMtMessageBuilder persistedMtMessageBuilder = new PersistedMtMessageBuilder();
    AssertThrows.illegalArgumentException(persistedMtMessageBuilder::build, "created must not be null");

    persistedMtMessageBuilder.setCreated(TestUtils.CREATED);
    AssertThrows.illegalArgumentException(persistedMtMessageBuilder::build, "mtMessage must not be null");

    persistedMtMessageBuilder.setMtMessageId(TestUtils.MT_MESSAGE_ID);
    AssertThrows.illegalArgumentException(persistedMtMessageBuilder::build, "mtMessage must not be null");

    persistedMtMessageBuilder.setMtMessage(TestUtils.createMtMessage());
    compare(persistedMtMessageBuilder, persistedMtMessageBuilder.build());
  }

  @Test
  void createdTest() {
    PersistedMtMessageBuilder persistedMtMessageBuilder = new PersistedMtMessageBuilder();
    Assertions.assertNull(persistedMtMessageBuilder.getCreated());

    persistedMtMessageBuilder.setCreated(TestUtils.CREATED);
    Assertions.assertSame(TestUtils.CREATED, persistedMtMessageBuilder.getCreated());

    AssertThrows.illegalArgumentException(() -> persistedMtMessageBuilder.setCreated(null), "created must not be null");
    Assertions.assertSame(TestUtils.CREATED, persistedMtMessageBuilder.getCreated());
  }

  @Test
  void mtMessageIdTest() {
    PersistedMtMessageBuilder persistedMtMessageBuilder = new PersistedMtMessageBuilder();
    Assertions.assertNull(persistedMtMessageBuilder.getMtMessageId());

    persistedMtMessageBuilder.setMtMessageId(TestUtils.MT_MESSAGE_ID);
    Assertions.assertSame(TestUtils.MT_MESSAGE_ID, persistedMtMessageBuilder.getMtMessageId());

    AssertThrows.illegalArgumentException(() -> persistedMtMessageBuilder.setMtMessageId(null), "mtMessageId must not be null");
    Assertions.assertSame(TestUtils.MT_MESSAGE_ID, persistedMtMessageBuilder.getMtMessageId());
  }

  @Test
  void mtMessageTest() {
    PersistedMtMessageBuilder persistedMtMessageBuilder = new PersistedMtMessageBuilder();

    final MtMessage mtMessage = TestUtils.createMtMessage();
    persistedMtMessageBuilder.setMtMessage(mtMessage);
    Assertions.assertSame(mtMessage, persistedMtMessageBuilder.getMtMessage());

    AssertThrows.illegalArgumentException(() -> persistedMtMessageBuilder.setMtMessage(null), "mtMessage must not be null");
    Assertions.assertSame(mtMessage, persistedMtMessageBuilder.getMtMessage());
  }
}
