package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ActiveMtMessageBuilderTest {
  private static void compare(ActiveMtMessageBuilder activeMtMessageBuilder, ActiveMtMessage activeMtMessage) {
    Assertions.assertSame(activeMtMessageBuilder.getMtMessageId(), activeMtMessage.getMtMessageId());
    Assertions.assertSame(activeMtMessageBuilder.getRetryAttempt(), activeMtMessage.getRetryAttempt());
    Assertions.assertSame(activeMtMessageBuilder.getSendSchemaStepId(), activeMtMessage.getSendSchemaStepId());
    Assertions.assertSame(activeMtMessageBuilder.getTimeout(), activeMtMessage.getTimeout());
  }

  @Test
  void buildTest() {
    ActiveMtMessageBuilder activeMtMessageBuilder = new ActiveMtMessageBuilder();
    AssertThrows.illegalArgumentException(activeMtMessageBuilder::build, "mtMessageId must not be null");

    activeMtMessageBuilder.setMtMessageId(TestUtils.MT_MESSAGE_ID);
    AssertThrows.illegalArgumentException(activeMtMessageBuilder::build, "retryAttempt must not be null");

    activeMtMessageBuilder.setRetryAttempt(TestUtils.RETRY_ATTEMPT);
    AssertThrows.illegalArgumentException(activeMtMessageBuilder::build, "sendSchemaStepId must not be null");

    activeMtMessageBuilder.setSendSchemaStepId(TestUtils.SEND_SCHEMA_STEP_ID);
    AssertThrows.illegalArgumentException(activeMtMessageBuilder::build, "timeout must not be null");

    activeMtMessageBuilder.setTimeout(TestUtils.TIMEOUT);
    compare(activeMtMessageBuilder, activeMtMessageBuilder.build());
  }

  @Test
  void mtMessageIdTest() {
    ActiveMtMessageBuilder activeMtMessageBuilder = new ActiveMtMessageBuilder();
    Assertions.assertNull(activeMtMessageBuilder.getMtMessageId());

    activeMtMessageBuilder.setMtMessageId(TestUtils.MT_MESSAGE_ID);
    Assertions.assertSame(TestUtils.MT_MESSAGE_ID, activeMtMessageBuilder.getMtMessageId());

    AssertThrows.illegalArgumentException(() -> activeMtMessageBuilder.setMtMessageId(null), "mtMessageId must not be null");
    Assertions.assertSame(TestUtils.MT_MESSAGE_ID, activeMtMessageBuilder.getMtMessageId());
  }

  @Test
  void retryAttemptTest() {
    ActiveMtMessageBuilder activeMtMessageBuilder = new ActiveMtMessageBuilder();
    Assertions.assertNull(activeMtMessageBuilder.getRetryAttempt());

    activeMtMessageBuilder.setRetryAttempt(TestUtils.RETRY_ATTEMPT);
    Assertions.assertSame(TestUtils.RETRY_ATTEMPT, activeMtMessageBuilder.getRetryAttempt());

    AssertThrows.illegalArgumentException(() -> activeMtMessageBuilder.setRetryAttempt(null), "retryAttempt must not be null");
    Assertions.assertSame(TestUtils.RETRY_ATTEMPT, activeMtMessageBuilder.getRetryAttempt());
  }

  @Test
  void sendSchemaStepIdTest() {
    ActiveMtMessageBuilder activeMtMessageBuilder = new ActiveMtMessageBuilder();
    Assertions.assertNull(activeMtMessageBuilder.getSendSchemaStepId());

    activeMtMessageBuilder.setSendSchemaStepId(TestUtils.SEND_SCHEMA_STEP_ID);
    Assertions.assertSame(TestUtils.SEND_SCHEMA_STEP_ID, activeMtMessageBuilder.getSendSchemaStepId());

    AssertThrows.illegalArgumentException(() -> activeMtMessageBuilder.setSendSchemaStepId(null), "sendSchemaStepId must not be null");
    Assertions.assertSame(TestUtils.SEND_SCHEMA_STEP_ID, activeMtMessageBuilder.getSendSchemaStepId());
  }

  @Test
  void timeoutTest() {
    ActiveMtMessageBuilder activeMtMessageBuilder = new ActiveMtMessageBuilder();
    Assertions.assertNull(activeMtMessageBuilder.getTimeout());

    activeMtMessageBuilder.setTimeout(TestUtils.TIMEOUT);
    Assertions.assertSame(TestUtils.TIMEOUT, activeMtMessageBuilder.getTimeout());

    AssertThrows.illegalArgumentException(() -> activeMtMessageBuilder.setTimeout(null), "timeout must not be null");
    Assertions.assertSame(TestUtils.TIMEOUT, activeMtMessageBuilder.getTimeout());
  }
}
