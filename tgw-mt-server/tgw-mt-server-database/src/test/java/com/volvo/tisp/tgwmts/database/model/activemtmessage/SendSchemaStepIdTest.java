package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SendSchemaStepIdTest {
  @Test
  void equalsAndHashcodeTest() {
    SendSchemaStepId sendSchemaStepId = SendSchemaStepId.ofInt(8);
    AssertUtils.assertEqualsAndHashCode(sendSchemaStepId, SendSchemaStepId.ofInt(8));

    Assertions.assertNotEquals(sendSchemaStepId, SendSchemaStepId.ofInt(1));
  }

  @Test
  void ofIntInvalidTest() {
    AssertThrows.illegalArgumentException(() -> SendSchemaStepId.ofInt(-1), "value must not be negative: -1");
  }

  @Test
  void plusOneTest() {
    Assertions.assertEquals(SendSchemaStepId.ofInt(2), SendSchemaStepId.ofInt(1).plusOne());
  }

  @Test
  void singletonTest() {
    Assertions.assertSame(SendSchemaStepId.ofInt(0), SendSchemaStepId.ofInt(0));
    Assertions.assertSame(SendSchemaStepId.ofInt(1), SendSchemaStepId.ofInt(1));
    Assertions.assertSame(SendSchemaStepId.ofInt(2), SendSchemaStepId.ofInt(2));
    Assertions.assertSame(SendSchemaStepId.ofInt(3), SendSchemaStepId.ofInt(3));
    Assertions.assertSame(SendSchemaStepId.ofInt(4), SendSchemaStepId.ofInt(4));
    Assertions.assertSame(SendSchemaStepId.ofInt(5), SendSchemaStepId.ofInt(5));
    Assertions.assertSame(SendSchemaStepId.ofInt(6), SendSchemaStepId.ofInt(6));
    Assertions.assertSame(SendSchemaStepId.ofInt(7), SendSchemaStepId.ofInt(7));

    Assertions.assertNotSame(SendSchemaStepId.ofInt(8), SendSchemaStepId.ofInt(8));
  }

  @Test
  void toIntTest() {
    Assertions.assertEquals(42, SendSchemaStepId.ofInt(42).toInt());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("42", SendSchemaStepId.ofInt(42).toString());
  }
}
