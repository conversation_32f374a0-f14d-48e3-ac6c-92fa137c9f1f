package com.volvo.tisp.tgwmts.database.db.vehiclelock;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VehicleLockRowMapperTest {
  private static void checkVehicleLock(VehicleLock vehicleLock) {
    Assertions.assertEquals(TestUtils.VPI, vehicleLock.getVpi());
  }

  private static void checkVehicleLockRowMapper(PersistedVehicleLock persistedVehicleLock) {
    Assertions.assertEquals(TestUtils.CREATED, persistedVehicleLock.getCreated());
    Assertions.assertEquals(TestUtils.VEHICLE_LOCK_ID, persistedVehicleLock.getVehicleLockId());

    checkVehicleLock(persistedVehicleLock.getVehicleLock());
  }

  private static void mockGetLong(ResultSet resultSet, String columnLabel, long returnValue) throws SQLException {
    Mockito.when(Long.valueOf(resultSet.getLong(columnLabel))).thenReturn(Long.valueOf(returnValue));
  }

  private static ResultSet mockResultSet() throws SQLException {
    ResultSet resultSet = Mockito.mock(ResultSet.class);

    mockGetLong(resultSet, "vehicle_lock_id", TestUtils.VEHICLE_LOCK_ID.toLong());
    Mockito.when(resultSet.getTimestamp("vehicle_lock_created")).thenReturn(Timestamp.from(TestUtils.CREATED));
    Mockito.when(resultSet.getString("vpi")).thenReturn(TestUtils.VPI.toString());

    return resultSet;
  }

  @Test
  void invalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> VehicleLockRowMapper.INSTANCE.map(null, null), "resultSet must not be null");
  }

  @Test
  void mapTest() throws SQLException {
    PersistedVehicleLock persistedVehicleLock = VehicleLockRowMapper.INSTANCE.map(mockResultSet(), null);

    checkVehicleLockRowMapper(persistedVehicleLock);
  }
}
