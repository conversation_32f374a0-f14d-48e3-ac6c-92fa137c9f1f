package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SrpDestinationVersionTest {
  @Test
  void equalsAndHashcodeTest() {
    final SrpDestinationVersion srpDestinationVersion = SrpDestinationVersion.ofShort((short) 42);
    AssertUtils.assertEqualsAndHashCode(srpDestinationVersion, SrpDestinationVersion.ofShort((short) 42));

    Assertions.assertNotEquals(srpDestinationVersion, SrpDestinationVersion.ofShort((short) 43));
  }

  @Test
  void ofShortTest() {
    Assertions.assertEquals(42, SrpDestinationVersion.ofShort((short) 42).toShort());

    AssertThrows.illegalArgumentException(() -> SrpDestinationVersion.ofShort((short) -1), "srpDestinationVersionValue must not be negative: -1");
    AssertThrows.illegalArgumentException(() -> SrpDestinationVersion.ofShort((short) 256), "srpDestinationVersionValue must not be greater than 255: 256");
  }

  @Test
  void toShortTest() {
    Assertions.assertEquals(SrpDestinationVersion.MAX_VALUE, SrpDestinationVersion.ofShort((short) 255).toShort());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("42", SrpDestinationVersion.ofShort((short) 42).toString());
  }
}
