package com.volvo.tisp.tgwmts.database.db.mtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Timestamp;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageRowMapperTest {
  private static void checkMtMessage(MtMessage mtMessage) {
    Assertions.assertEquals(TestUtils.QUEUE_ID, mtMessage.getQueueId());
    Assertions.assertEquals(TestUtils.SEND_SCHEMA_NAME, mtMessage.getSendSchemaName());
    Assertions.assertEquals(TestUtils.TID, mtMessage.getTid());
    Assertions.assertEquals(TestUtils.VEHICLE_LOCK_ID, mtMessage.getVehicleLockId());

    checkSrpOption(mtMessage.getSrpOption());
  }

  private static void checkPersistedMtMessage(PersistedMtMessage persistedMtMessage) {
    Assertions.assertEquals(TestUtils.MT_MESSAGE_ID, persistedMtMessage.getMtMessageId());
    Assertions.assertEquals(TestUtils.CREATED, persistedMtMessage.getCreated());

    checkMtMessage(persistedMtMessage.getMtMessage());
  }

  private static void checkReplyOption(ReplyOption replyOption) {
    Assertions.assertEquals(TestUtils.CORRELATION_ID, replyOption.getCorrelationId());
    Assertions.assertEquals(TestUtils.REPLY_TO, replyOption.getReplyTo());
  }

  private static void checkSrpOption(SrpOption srpOption) {
    Assertions.assertEquals(TestUtils.SRP_DESTINATION_SERVICE, srpOption.getSrpDestinationService());
    Assertions.assertEquals(TestUtils.SRP_DESTINATION_VERSION, srpOption.getSrpDestinationVersion());
    Assertions.assertEquals(TestUtils.SRP_PAYLOAD, srpOption.getSrpPayload());
  }

  private static void mockGetLong(ResultSet resultSet, String columnLabel, long returnValue) throws SQLException {
    Mockito.when(resultSet.getLong(columnLabel)).thenReturn(returnValue);
  }

  private static void mockGetSrpDestinationService(ResultSet resultSet, int returnValue) throws SQLException {
    Mockito.when(resultSet.getInt("srp_destination_service")).thenReturn(returnValue);
  }

  private static void mockGetSrpDestinationVersion(ResultSet resultSet, short returnValue) throws SQLException {
    Mockito.when(resultSet.getShort("srp_destination_version")).thenReturn(returnValue);
  }

  private static ResultSet mockNotNullColumns(ResultSet resultSet) throws SQLException {
    Mockito.when(resultSet.getTimestamp("mt_message_created")).thenReturn(Timestamp.from(TestUtils.CREATED));
    mockGetLong(resultSet, "mt_message_id", TestUtils.MT_MESSAGE_ID.toLong());
    Mockito.when(resultSet.getString("send_schema_name")).thenReturn(TestUtils.SEND_SCHEMA_NAME.toString());
    Mockito.when(resultSet.getString("queue_id")).thenReturn(TestUtils.QUEUE_ID.toString());
    mockGetSrpDestinationService(resultSet, TestUtils.SRP_DESTINATION_SERVICE.toInt());
    mockGetSrpDestinationVersion(resultSet, TestUtils.SRP_DESTINATION_VERSION.toShort());
    Mockito.when(resultSet.getBytes("srp_payload")).thenReturn(TestUtils.SRP_PAYLOAD.getImmutableByteArray().toByteArray());
    Mockito.when(resultSet.getString("tid")).thenReturn(TestUtils.TID.toString());
    mockGetLong(resultSet, "vehicle_lock_id", TestUtils.VEHICLE_LOCK_ID.toLong());

    return resultSet;
  }

  private static ResultSet mockNullableColumns(ResultSet resultSet) throws SQLException {
    Mockito.when(resultSet.getString("correlation_id")).thenReturn(TestUtils.CORRELATION_ID.toString());
    Mockito.when(resultSet.getString("reply_to")).thenReturn(TestUtils.REPLY_TO.toString());
    return resultSet;
  }

  private static ResultSet mockResultSet() throws SQLException {
    ResultSet resultSet = Mockito.mock(ResultSet.class);
    mockNotNullColumns(resultSet);
    return mockNullableColumns(resultSet);
  }

  @Test
  void invalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> MtMessageRowMapper.INSTANCE.map(null, null), "resultSet must not be null");
  }

  @Test
  void mapTest() throws SQLException {
    PersistedMtMessage persistedMtMessage = MtMessageRowMapper.INSTANCE.map(mockResultSet(), null);

    checkPersistedMtMessage(persistedMtMessage);
    checkReplyOption(persistedMtMessage.getMtMessage().getReplyOption().orElseThrow());
  }

  @Test
  void mapWithEmptyValuesTest() throws SQLException {
    PersistedMtMessage persistedMtMessage = MtMessageRowMapper.INSTANCE.map(mockNotNullColumns(Mockito.mock(ResultSet.class)), null);

    checkPersistedMtMessage(persistedMtMessage);
    Assertions.assertTrue(persistedMtMessage.getMtMessage().getReplyOption().isEmpty());
  }
}
