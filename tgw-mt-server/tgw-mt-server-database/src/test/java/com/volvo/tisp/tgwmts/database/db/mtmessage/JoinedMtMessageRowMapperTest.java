package com.volvo.tisp.tgwmts.database.db.mtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class JoinedMtMessageRowMapperTest {
  private static void checkMtMessageToActiveMessage(JoinedMtMessage joinedMtMessage, Optional<PersistedActiveMtMessage> persistedActiveMtMessage,
                                                    PersistedMtMessage persistedMtMessage) {
    Assertions.assertEquals(persistedActiveMtMessage, joinedMtMessage.persistedActiveMtMessage());
    Assertions.assertEquals(persistedMtMessage, joinedMtMessage.persistedMtMessage());
  }

  @Test
  void invalidParameterTest() {
    RowMapper<JoinedMtMessage> rowMapper = JoinedMtMessageRowMapper.create(TestUtils.mockRowMapper(), TestUtils.mockRowMapper(), TestUtils.mockRowMapper());
    AssertThrows.illegalArgumentException(() -> rowMapper.map(null, null), "resultSet must not be null");
  }

  @Test
  void mapTest() throws SQLException {
    final ResultSet resultSet = Mockito.mock(ResultSet.class);
    final Optional<PersistedActiveMtMessage> persistedActiveMtMessage = Optional.of(TestUtils.createPersistedActiveMtMessage());
    final PersistedMtMessage persistedMtMessage = TestUtils.createPersistedMtMessage();
    final PersistedVehicleLock persistedVehicleLock = TestUtils.createPersistedVehicleLock();

    RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper = TestUtils.mockRowMapper();
    Mockito.when(activeMtMessageRowMapper.map(resultSet, null)).thenReturn(persistedActiveMtMessage.get());

    RowMapper<PersistedMtMessage> mtMessageRowMapper = TestUtils.mockRowMapper();
    Mockito.when(mtMessageRowMapper.map(resultSet, null)).thenReturn(persistedMtMessage);

    RowMapper<PersistedVehicleLock> vehicleLockRowMapper = TestUtils.mockRowMapper();
    Mockito.when(vehicleLockRowMapper.map(resultSet, null)).thenReturn(persistedVehicleLock);

    RowMapper<JoinedMtMessage> rowMapper = JoinedMtMessageRowMapper.create(activeMtMessageRowMapper, mtMessageRowMapper, vehicleLockRowMapper);
    Mockito.when(resultSet.getString("active_mt_message_id")).thenReturn("12345678");
    JoinedMtMessage joinedMtMessage = rowMapper.map(resultSet, null);

    checkMtMessageToActiveMessage(joinedMtMessage, persistedActiveMtMessage, persistedMtMessage);

    Mockito.verify(activeMtMessageRowMapper).map(resultSet, null);
    Mockito.verify(mtMessageRowMapper).map(resultSet, null);
    Mockito.verifyNoMoreInteractions(activeMtMessageRowMapper, mtMessageRowMapper);
  }

  @Test
  void mapEptyActiveMessageTest() throws SQLException {
    final ResultSet resultSet = Mockito.mock(ResultSet.class);
    final Optional<PersistedActiveMtMessage> persistedActiveMtMessage = Optional.empty();
    final PersistedMtMessage persistedMtMessage = TestUtils.createPersistedMtMessage();
    final PersistedVehicleLock persistedVehicleLock = TestUtils.createPersistedVehicleLock();

    RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper = TestUtils.mockRowMapper();

    RowMapper<PersistedMtMessage> mtMessageRowMapper = TestUtils.mockRowMapper();
    Mockito.when(mtMessageRowMapper.map(resultSet, null)).thenReturn(persistedMtMessage);

    RowMapper<PersistedVehicleLock> vehicleLockRowMapper = TestUtils.mockRowMapper();
    Mockito.when(vehicleLockRowMapper.map(resultSet, null)).thenReturn(persistedVehicleLock);

    RowMapper<JoinedMtMessage> rowMapper = JoinedMtMessageRowMapper.create(activeMtMessageRowMapper, mtMessageRowMapper, vehicleLockRowMapper);
    Mockito.when(resultSet.getString("active_mt_message_id")).thenReturn(null);
    JoinedMtMessage joinedMtMessage = rowMapper.map(resultSet, null);

    checkMtMessageToActiveMessage(joinedMtMessage, persistedActiveMtMessage, persistedMtMessage);

    Mockito.verifyNoInteractions(activeMtMessageRowMapper);
    Mockito.verify(mtMessageRowMapper).map(resultSet, null);
    Mockito.verifyNoMoreInteractions(activeMtMessageRowMapper, mtMessageRowMapper);
  }
}