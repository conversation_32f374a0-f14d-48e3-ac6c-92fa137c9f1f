package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedMtMessageTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new PersistedMtMessage(null), "persistedMtMessageBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "created=1970-01-01T00:00:00Z, mtMessage={queueId=vps, replyOption={Optional[correlationId=b4d4b26f-142a-4556-9af5-46d3e10068d8, replyTo=reply-to-queue-name]}, sendSchemaName=COMMON_HIGH, srpOption={srpDestinationService=50, srpDestinationVersion=1, srpLevel12=false, srpPayload=[42]}, tid=123456789012345678901234567890FF, vehicleLockId=1}, mtMessageId=2";
    Assertions.assertEquals(expectedString, TestUtils.createPersistedMtMessage().toString());
  }
}
