package com.volvo.tisp.tgwmts.database.db;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.ActiveMtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.ActiveMtMessageWriterFactoryImpl;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.JoinedActiveMtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.mtmessage.JoinedMtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.mtmessage.MtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.vehiclelock.VehicleLockRowMapper;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;

public final class DbUtils {
  public static final Instant INSTANT_1 = Instant.ofEpochSecond(41);
  public static final Instant INSTANT_2 = Instant.ofEpochSecond(42);
  private static final Duration LOCK_TIMEOUT = Duration.ofSeconds(1);

  private DbUtils() {
    throw new IllegalStateException();
  }

  public static ActiveMtMessageWriterFactory createActiveMtMessageWriterFactory(DataSource dataSource) {
    Jdbi jdbi = runFlywayAndCreateJdbi(dataSource);
    return ActiveMtMessageWriterFactoryImpl.create(mockClock(), jdbi, createJoinedActiveMtMessageRowMapper(), createMtessageToActiveMessageRowMapper(),
        LOCK_TIMEOUT);
  }

  private static RowMapper<JoinedActiveMtMessage> createJoinedActiveMtMessageRowMapper() {
    return JoinedActiveMtMessageRowMapper.create(ActiveMtMessageRowMapper.INSTANCE, MtMessageRowMapper.INSTANCE, VehicleLockRowMapper.INSTANCE);
  }

  private static RowMapper<JoinedMtMessage> createMtessageToActiveMessageRowMapper() {
    return JoinedMtMessageRowMapper.create(ActiveMtMessageRowMapper.INSTANCE, MtMessageRowMapper.INSTANCE, VehicleLockRowMapper.INSTANCE);
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(INSTANT_1, INSTANT_2).thenAnswer(invocationOnMock -> Instant.now());
    return clock;
  }

  private static void runFlyway(DataSource dataSource) {
    MtDatabaseFlywayExecutor.performDatabaseMigration(dataSource);
  }

  private static Jdbi runFlywayAndCreateJdbi(DataSource dataSource) {
    runFlyway(dataSource);
    return Jdbi.create(dataSource);
  }
}
