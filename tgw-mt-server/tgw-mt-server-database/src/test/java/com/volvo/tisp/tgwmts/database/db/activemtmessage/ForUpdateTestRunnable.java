package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Consumer;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;

class ForUpdateTestRunnable implements Runnable {
  private final ActiveMtMessageWriterFactory activeMtMessageWriterFactory;
  private final AtomicReference<Thread> atomicReference;
  private final Consumer<ActiveMtMessageWriter> consumer;
  private final CountDownLatch countDownLatch;

  ForUpdateTestRunnable(ActiveMtMessageWriterFactory activeMtMessageWriterFactory, AtomicReference<Thread> atomicReference,
      Consumer<ActiveMtMessageWriter> consumer, CountDownLatch countDownLatch) {
    this.activeMtMessageWriterFactory = activeMtMessageWriterFactory;
    this.atomicReference = atomicReference;
    this.consumer = consumer;
    this.countDownLatch = countDownLatch;
  }

  @Override
  public void run() {
    try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
      activeMtMessageWriter.startTransaction();

      consumer.accept(activeMtMessageWriter);

      atomicReference.set(Thread.currentThread());

      countDownLatch.await();

      atomicReference.set(null);
      activeMtMessageWriter.commitTransaction();
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new IllegalStateException(e);
    }
  }
}
