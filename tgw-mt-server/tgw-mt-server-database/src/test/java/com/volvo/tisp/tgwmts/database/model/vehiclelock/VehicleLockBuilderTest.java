package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VehicleLockBuilderTest {
  private static void verifyEquals(VehicleLockBuilder vehicleLockBuilder, VehicleLock vehicleLock) {
    Assertions.assertSame(vehicleLockBuilder.getVpi(), vehicleLock.getVpi());
  }

  @Test
  void buildTest() {
    VehicleLockBuilder vehicleLockBuilder = new VehicleLockBuilder();
    AssertThrows.illegalArgumentException(vehicleLockBuilder::build, "vpi must not be null");

    vehicleLockBuilder.setVpi(TestUtils.VPI);
    verifyEquals(vehicleLockBuilder, vehicleLockBuilder.build());
  }

  @Test
  void ofVpiTest() {
    AssertThrows.illegalArgumentException(() -> VehicleLockBuilder.ofVpi(null), "vpi must not be null");

    Assertions.assertSame(TestUtils.VPI, VehicleLockBuilder.ofVpi(TestUtils.VPI).getVpi());
  }

  @Test
  void vpiTest() {
    VehicleLockBuilder vehicleLockBuilder = new VehicleLockBuilder();
    Assertions.assertNull(vehicleLockBuilder.getVpi());

    vehicleLockBuilder.setVpi(TestUtils.VPI);
    Assertions.assertSame(TestUtils.VPI, vehicleLockBuilder.getVpi());

    AssertThrows.illegalArgumentException(() -> vehicleLockBuilder.setVpi(null), "vpi must not be null");
    Assertions.assertSame(TestUtils.VPI, vehicleLockBuilder.getVpi());
  }
}
