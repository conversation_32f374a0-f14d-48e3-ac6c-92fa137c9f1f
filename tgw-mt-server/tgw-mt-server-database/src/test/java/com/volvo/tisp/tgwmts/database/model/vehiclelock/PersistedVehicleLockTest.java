package com.volvo.tisp.tgwmts.database.model.vehiclelock;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedVehicleLockTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new PersistedVehicleLock(null), "persistedVehicleLockBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "created=1970-01-01T00:00:00Z, vehicleLockId=1, vehicleLock={vpi=12345678901234567890ABCDEFABCDEF}";
    Assertions.assertEquals(expectedString, TestUtils.createPersistedVehicleLock().toString());
  }
}
