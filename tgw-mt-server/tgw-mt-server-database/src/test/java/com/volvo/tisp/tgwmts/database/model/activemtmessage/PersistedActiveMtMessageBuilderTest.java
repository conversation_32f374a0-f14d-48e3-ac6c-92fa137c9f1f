package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class PersistedActiveMtMessageBuilderTest {
  private static void compare(PersistedActiveMtMessageBuilder persistedActiveMtMessageBuilder, PersistedActiveMtMessage persistedActiveMtMessage) {
    Assertions.assertSame(persistedActiveMtMessageBuilder.getActiveMtMessage(), persistedActiveMtMessage.getActiveMtMessage());
    Assertions.assertSame(persistedActiveMtMessageBuilder.getActiveMtMessageId(), persistedActiveMtMessage.getActiveMtMessageId());
    Assertions.assertSame(persistedActiveMtMessageBuilder.getCreated(), persistedActiveMtMessage.getCreated());
    Assertions.assertSame(persistedActiveMtMessageBuilder.getUpdated(), persistedActiveMtMessage.getUpdated());
  }

  @Test
  void activeMtMessageIdTest() {
    PersistedActiveMtMessageBuilder persistedActiveMtMessageBuilder = new PersistedActiveMtMessageBuilder();

    persistedActiveMtMessageBuilder.setActiveMtMessageId(TestUtils.ACTIVE_MT_MESSAGE_ID);
    Assertions.assertSame(TestUtils.ACTIVE_MT_MESSAGE_ID, persistedActiveMtMessageBuilder.getActiveMtMessageId());

    AssertThrows.illegalArgumentException(() -> persistedActiveMtMessageBuilder.setActiveMtMessageId(null), "activeMtMessageId must not be null");
    Assertions.assertSame(TestUtils.ACTIVE_MT_MESSAGE_ID, persistedActiveMtMessageBuilder.getActiveMtMessageId());
  }

  @Test
  void activeMtMessageTest() {
    PersistedActiveMtMessageBuilder persistedActiveMtMessageBuilder = new PersistedActiveMtMessageBuilder();

    final ActiveMtMessage activeMtMessage = TestUtils.createActiveMtMessage();
    persistedActiveMtMessageBuilder.setActiveMtMessage(activeMtMessage);
    Assertions.assertSame(activeMtMessage, persistedActiveMtMessageBuilder.getActiveMtMessage());

    AssertThrows.illegalArgumentException(() -> persistedActiveMtMessageBuilder.setActiveMtMessage(null), "activeMtMessage must not be null");
    Assertions.assertSame(activeMtMessage, persistedActiveMtMessageBuilder.getActiveMtMessage());
  }

  @Test
  void buildTest() {
    PersistedActiveMtMessageBuilder persistedActiveMtMessageBuilder = new PersistedActiveMtMessageBuilder();
    AssertThrows.illegalArgumentException(persistedActiveMtMessageBuilder::build, "activeMtMessage must not be null");

    persistedActiveMtMessageBuilder.setActiveMtMessage(TestUtils.createActiveMtMessage());
    AssertThrows.illegalArgumentException(persistedActiveMtMessageBuilder::build, "activeMtMessageId must not be null");

    persistedActiveMtMessageBuilder.setActiveMtMessageId(TestUtils.ACTIVE_MT_MESSAGE_ID);
    AssertThrows.illegalArgumentException(persistedActiveMtMessageBuilder::build, "created must not be null");

    persistedActiveMtMessageBuilder.setCreated(TestUtils.CREATED);
    AssertThrows.illegalArgumentException(persistedActiveMtMessageBuilder::build, "updated must not be null");

    persistedActiveMtMessageBuilder.setUpdated(TestUtils.UPDATED);
    compare(persistedActiveMtMessageBuilder, persistedActiveMtMessageBuilder.build());
  }

  @Test
  void createdTest() {
    PersistedActiveMtMessageBuilder persistedActiveMtMessageBuilder = new PersistedActiveMtMessageBuilder();
    Assertions.assertNull(persistedActiveMtMessageBuilder.getCreated());

    persistedActiveMtMessageBuilder.setCreated(TestUtils.CREATED);
    Assertions.assertSame(TestUtils.CREATED, persistedActiveMtMessageBuilder.getCreated());

    AssertThrows.illegalArgumentException(() -> persistedActiveMtMessageBuilder.setCreated(null), "created must not be null");
    Assertions.assertSame(TestUtils.CREATED, persistedActiveMtMessageBuilder.getCreated());
  }

  @Test
  void updatedTest() {
    PersistedActiveMtMessageBuilder persistedActiveMtMessageBuilder = new PersistedActiveMtMessageBuilder();
    Assertions.assertNull(persistedActiveMtMessageBuilder.getUpdated());

    persistedActiveMtMessageBuilder.setUpdated(TestUtils.UPDATED);
    Assertions.assertSame(TestUtils.UPDATED, persistedActiveMtMessageBuilder.getUpdated());

    AssertThrows.illegalArgumentException(() -> persistedActiveMtMessageBuilder.setUpdated(null), "updated must not be null");
    Assertions.assertSame(TestUtils.UPDATED, persistedActiveMtMessageBuilder.getUpdated());
  }
}
