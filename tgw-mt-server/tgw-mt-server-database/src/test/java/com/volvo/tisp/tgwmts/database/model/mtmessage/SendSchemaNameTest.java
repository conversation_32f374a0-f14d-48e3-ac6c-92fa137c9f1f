package com.volvo.tisp.tgwmts.database.model.mtmessage;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SendSchemaNameTest {
  @Test
  void fromStringTest() {
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_HIGH), SendSchemaName.fromLegacyString("common_high"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_LONG), SendSchemaName.fromLegacyString("common_long"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_LOW), SendSchemaName.fromLegacyString("common_low"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_MID), SendSchemaName.fromLegacyString("common_mid"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_NORMAL), SendSchemaName.fromLegacyString("common_normal"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_RSWDL), SendSchemaName.fromLegacyString("common_rswdl"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_SETUP), SendSchemaName.fromLegacyString("common_setup"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_SHORT), SendSchemaName.fromLegacyString("common_short"));
    Assertions.assertEquals(Optional.of(SendSchemaName.COMMON_VERY_HIGH), SendSchemaName.fromLegacyString("common_very_high"));
    Assertions.assertEquals(Optional.of(SendSchemaName.DFOL_HIGH), SendSchemaName.fromLegacyString("dfol_high"));
    Assertions.assertEquals(Optional.of(SendSchemaName.DFOL_LOW), SendSchemaName.fromLegacyString("dfol_low"));
    Assertions.assertEquals(Optional.of(SendSchemaName.DFOL_MID), SendSchemaName.fromLegacyString("dfol_mid"));
    Assertions.assertEquals(Optional.of(SendSchemaName.DFOL_MID_PLUS), SendSchemaName.fromLegacyString("dfol_mid_plus"));
    Assertions.assertEquals(Optional.of(SendSchemaName.DFOL_SETUP), SendSchemaName.fromLegacyString("dfol_setup"));
    Assertions.assertEquals(Optional.of(SendSchemaName.DRUT_VERY_HIGH), SendSchemaName.fromLegacyString("drut_very_high"));
    Assertions.assertEquals(Optional.of(SendSchemaName.RSWDL_LOW), SendSchemaName.fromLegacyString("rswdl_low"));
    Assertions.assertEquals(Optional.of(SendSchemaName.RSWDL_MID), SendSchemaName.fromLegacyString("rswdl_mid"));
    Assertions.assertEquals(Optional.of(SendSchemaName.UPTIME_VERY_HIGH), SendSchemaName.fromLegacyString("uptime_very_high"));
    Assertions.assertEquals(Optional.of(SendSchemaName.VLINK_HIGH), SendSchemaName.fromLegacyString("vlink_high"));
    Assertions.assertEquals(Optional.of(SendSchemaName.VLINK_LOW), SendSchemaName.fromLegacyString("vlink_low"));

    Assertions.assertEquals(Optional.empty(), SendSchemaName.fromLegacyString("uptime-short"));
    Assertions.assertEquals(Optional.empty(), SendSchemaName.fromLegacyString(" "));
  }

  @Test
  void invalidParamTest() {
    AssertThrows.illegalArgumentException(() -> SendSchemaName.fromLegacyString(""), "string must not be empty");
    AssertThrows.illegalArgumentException(() -> SendSchemaName.fromLegacyString(null), "string must not be null");
  }
}