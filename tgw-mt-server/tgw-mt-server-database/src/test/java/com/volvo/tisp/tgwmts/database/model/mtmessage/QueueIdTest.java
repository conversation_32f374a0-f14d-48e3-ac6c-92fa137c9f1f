package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class QueueIdTest {
  @Test
  void equalsAndHashcodeTest() {
    String value = "foo";
    QueueId queueId = QueueId.ofString(value);
    AssertUtils.assertEqualsAndHashCode(queueId, QueueId.ofString(value));

    Assertions.assertNotEquals(queueId, QueueId.ofString("bar"));
  }

  @Test
  void ofStringInvalidTest() {
    AssertThrows.illegalArgumentException(() -> QueueId.ofString(null), "value must not be null");
    AssertThrows.illegalArgumentException(() -> QueueId.ofString(""), "value must have a minimum length of 1: ");
    String invalidQueueIdString = "x".repeat(QueueId.MAX_LENGTH + 1);
    AssertThrows.illegalArgumentException(() -> QueueId.ofString(invalidQueueIdString),
        "value must have a maximum length of " + QueueId.MAX_LENGTH + ": " + invalidQueueIdString);
  }

  @Test
  void ofStringTest() {
    QueueId.ofString("x".repeat(QueueId.MAX_LENGTH));
    QueueId.ofString("x");
  }

  @Test
  void toStringTest() {
    String expectedString = "foo";
    Assertions.assertSame(expectedString, QueueId.ofString(expectedString).toString());
  }
}
