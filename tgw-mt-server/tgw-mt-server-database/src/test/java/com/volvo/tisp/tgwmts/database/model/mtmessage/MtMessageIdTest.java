package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class MtMessageIdTest {
  @Test
  void equalsAndHashcodeTest() {
    MtMessageId mtMessageId = MtMessageId.ofLong(1);
    AssertUtils.assertEqualsAndHashCode(mtMessageId, MtMessageId.ofLong(1));

    Assertions.assertNotEquals(mtMessageId, MtMessageId.ofLong(2));
  }

  @Test
  void ofLongInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MtMessageId.ofLong(-1), "id must be positive: -1");
    AssertThrows.illegalArgumentException(() -> MtMessageId.ofLong(0), "id must be positive: 0");
  }

  @Test
  void toLongTest() {
    Assertions.assertEquals(1, MtMessageId.ofLong(1).toLong());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("1", MtMessageId.ofLong(1L).toString());
  }
}
