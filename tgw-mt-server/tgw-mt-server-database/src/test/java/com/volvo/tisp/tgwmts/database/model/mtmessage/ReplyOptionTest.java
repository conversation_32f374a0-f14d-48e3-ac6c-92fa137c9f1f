package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class ReplyOptionTest {
  @Test
  void equalsAndHashcodeTest() {
    ReplyOption replyOption = TestUtils.createReplyOption();
    AssertUtils.assertEqualsAndHashCode(replyOption, TestUtils.createReplyOption());

    Assertions.assertNotEquals(replyOption,
        TestUtils.createReplyOptionBuilder().setCorrelationId(CorrelationId.ofString("00000000000000000000000000000000")).build());
    Assertions.assertNotEquals(replyOption, TestUtils.createReplyOptionBuilder().setReplyTo(ReplyTo.ofString("00000000000000000000000000000000")).build());
  }

  @Test
  void getCorrelationIdTest() {
    Assertions.assertEquals(TestUtils.CORRELATION_ID, TestUtils.createReplyOption().getCorrelationId());
  }

  @Test
  void getReplyToTest() {
    Assertions.assertEquals(TestUtils.REPLY_TO, TestUtils.createReplyOption().getReplyTo());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new ReplyOption(null), "replyOptionBuilder must not be null");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("correlationId=b4d4b26f-142a-4556-9af5-46d3e10068d8, replyTo=reply-to-queue-name", TestUtils.createReplyOption().toString());
  }
}
