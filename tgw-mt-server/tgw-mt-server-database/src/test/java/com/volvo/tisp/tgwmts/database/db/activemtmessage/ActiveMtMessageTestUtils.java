package com.volvo.tisp.tgwmts.database.db.activemtmessage;

final class ActiveMtMessageTestUtils {
  private ActiveMtMessageTestUtils() {
    throw new IllegalStateException();
  }

  static String getByActiveMtMessageIdLockTimeoutErrorMessage() {
    return "org.postgresql.util.PSQLException: ERROR: canceling statement due to lock timeout\n"
        + "  Where: while locking tuple (0,1) in relation \"vehicle_lock\" [statement:"
        + "\"SELECT * FROM vehicle_lock JOIN mt_message ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id "
        + "JOIN active_mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id "
        + "WHERE active_mt_message_id = :active_mt_message_id FOR UPDATE\", "
        + "arguments:{named:{active_mt_message_id:1}}]";
  }

  static String getByVpiLockTimeoutErrorMessage() {
    return "org.postgresql.util.PSQLException: ERROR: canceling statement due to lock timeout\n"
        + "  Where: while locking tuple (0,1) in relation \"vehicle_lock\" [statement:"
        + "\"SELECT * FROM vehicle_lock JOIN mt_message ON mt_message.vehicle_lock_id = vehicle_lock.vehicle_lock_id "
        + "JOIN active_mt_message ON active_mt_message.mt_message_id = mt_message.mt_message_id WHERE vpi = :vpi "
        + "FOR UPDATE\", arguments:{positional:{}, named:{vpi:12345678901234567890ABCDEFABCDEF}, finder:[]}]";
  }

  static String getByVpisLockTimeoutErrorMessage() {
    return "org.postgresql.util.PSQLException: ERROR: canceling statement due to lock timeout\n"
        + "  Where: while locking tuple (0,1) in relation \"vehicle_lock\" [statement:"
        + "\"SELECT * FROM vehicle_lock WHERE vpi IN (:__vpi_0) FOR UPDATE\", "
        + "arguments:{positional:{}, named:{__vpi_0:12345678901234567890ABCDEFABCDEF}, finder:[]}]";
  }
}
