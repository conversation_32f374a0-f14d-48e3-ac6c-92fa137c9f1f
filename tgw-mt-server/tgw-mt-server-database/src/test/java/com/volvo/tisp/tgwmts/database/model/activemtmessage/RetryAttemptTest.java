package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class RetryAttemptTest {
  @Test
  void equalsAndHashcodeTest() {
    final RetryAttempt retryAttempt = RetryAttempt.ofShort((short) 42);
    AssertUtils.assertEqualsAndHashCode(retryAttempt, RetryAttempt.ofShort((short) 42));

    Assertions.assertNotEquals(retryAttempt, RetryAttempt.ofShort((short) 43));
  }

  @Test
  void ofShortTest() {
    Assertions.assertEquals(42, RetryAttempt.ofShort((short) 42).toShort());

    AssertThrows.illegalArgumentException(() -> RetryAttempt.ofShort((short) -1), "retryAttemptValue must not be negative: -1");
  }

  @Test
  void toShortTest() {
    Assertions.assertEquals(RetryAttempt.MAX_VALUE, RetryAttempt.ofShort((short) 32767).toShort());
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("42", RetryAttempt.ofShort((short) 42).toString());
  }
}
