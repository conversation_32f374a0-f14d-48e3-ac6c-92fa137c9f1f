package com.volvo.tisp.tgwmts.database.model.mtmessage;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ReplyOptionBuilderTest {
  private static void compare(ReplyOptionBuilder replyOptionBuilder, ReplyOption replyOption) {
    Assertions.assertSame(replyOptionBuilder.getCorrelationId(), replyOption.getCorrelationId());
    Assertions.assertSame(replyOptionBuilder.getReplyTo(), replyOption.getReplyTo());
  }

  @Test
  void buildTest() {
    ReplyOptionBuilder persistedMtMessageBuilder = new ReplyOptionBuilder();
    AssertThrows.illegalArgumentException(persistedMtMessageBuilder::build, "correlationId must not be null");

    persistedMtMessageBuilder.setCorrelationId(TestUtils.CORRELATION_ID);
    AssertThrows.illegalArgumentException(persistedMtMessageBuilder::build, "replyTo must not be null");

    persistedMtMessageBuilder.setReplyTo(TestUtils.REPLY_TO);
    compare(persistedMtMessageBuilder, persistedMtMessageBuilder.build());
  }

  @Test
  void correlationIdTest() {
    ReplyOptionBuilder replyOptionBuilder = new ReplyOptionBuilder();
    Assertions.assertNull(replyOptionBuilder.getCorrelationId());

    replyOptionBuilder.setCorrelationId(TestUtils.CORRELATION_ID);
    Assertions.assertSame(TestUtils.CORRELATION_ID, replyOptionBuilder.getCorrelationId());

    AssertThrows.illegalArgumentException(() -> replyOptionBuilder.setCorrelationId(null), "correlationId must not be null");
    Assertions.assertSame(TestUtils.CORRELATION_ID, replyOptionBuilder.getCorrelationId());
  }

  @Test
  void replyToTest() {
    ReplyOptionBuilder replyOptionBuilder = new ReplyOptionBuilder();
    Assertions.assertNull(replyOptionBuilder.getReplyTo());

    replyOptionBuilder.setReplyTo(TestUtils.REPLY_TO);
    Assertions.assertSame(TestUtils.REPLY_TO, replyOptionBuilder.getReplyTo());

    AssertThrows.illegalArgumentException(() -> replyOptionBuilder.setReplyTo(null), "replyTo must not be null");
    Assertions.assertSame(TestUtils.REPLY_TO, replyOptionBuilder.getReplyTo());
  }
}
