package com.volvo.tisp.tgwmts.database.model.activemtmessage;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class UpdateActiveMtMessageParameterTest {
  @Test
  void equalsAndHashcodeTest() {
    UpdateActiveMtMessageParameter updateActiveMtMessageParameter = TestUtils.createUpdateActiveMtMessageParameter();
    AssertUtils.assertEqualsAndHashCode(updateActiveMtMessageParameter, TestUtils.createUpdateActiveMtMessageParameter());

    Assertions.assertNotEquals(updateActiveMtMessageParameter,
        TestUtils.createUpdateActiveMtMessageParameterBuilder().setActiveMtMessageId(ActiveMtMessageId.ofLong(5)).build());
    Assertions.assertNotEquals(updateActiveMtMessageParameter,
        TestUtils.createUpdateActiveMtMessageParameterBuilder().setRetryAttempt(RetryAttempt.ofShort((short) 20)).build());
    Assertions.assertNotEquals(updateActiveMtMessageParameter,
        TestUtils.createUpdateActiveMtMessageParameterBuilder().setSendSchemaStepId(SendSchemaStepId.ofInt(2)).build());
    Assertions.assertNotEquals(updateActiveMtMessageParameter, TestUtils.createUpdateActiveMtMessageParameterBuilder().setTimeout(Instant.now()).build());
  }

  @Test
  void getActiveMtMessageIdTest() {
    Assertions.assertSame(TestUtils.ACTIVE_MT_MESSAGE_ID, TestUtils.createUpdateActiveMtMessageParameter().getActiveMtMessageId());
  }

  @Test
  void getRetryAttemptTest() {
    Assertions.assertSame(TestUtils.RETRY_ATTEMPT, TestUtils.createUpdateActiveMtMessageParameter().getRetryAttempt());
  }

  @Test
  void getSendSchemaStepIdTest() {
    Assertions.assertSame(TestUtils.SEND_SCHEMA_STEP_ID, TestUtils.createUpdateActiveMtMessageParameter().getSendSchemaStepId());
  }

  @Test
  void getTimeoutTest() {
    Assertions.assertSame(TestUtils.TIMEOUT, TestUtils.createUpdateActiveMtMessageParameter().getTimeout());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new UpdateActiveMtMessageParameter(null), "updateActiveMtMessageParameterBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "activeMtMessageId=3, retryAttempt=10, sendSchemaStepId=3, timeout=1970-01-01T00:01:00Z";
    Assertions.assertEquals(expectedString, TestUtils.createUpdateActiveMtMessageParameter().toString());
  }
}
