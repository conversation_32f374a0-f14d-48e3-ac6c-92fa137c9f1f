package com.volvo.tisp.tgwmts.database.db.activemtmessage;

import java.sql.ResultSet;
import java.sql.SQLException;

import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class JoinedActiveMtMessageRowMapperTest {
  private static void checkJoinedActiveMtMessage(JoinedActiveMtMessage joinedActiveMtMessage, PersistedActiveMtMessage persistedActiveMtMessage,
      PersistedMtMessage persistedMtMessage, PersistedVehicleLock persistedVehicleLock) {
    Assertions.assertEquals(persistedActiveMtMessage, joinedActiveMtMessage.persistedActiveMtMessage());
    Assertions.assertEquals(persistedMtMessage, joinedActiveMtMessage.persistedMtMessage());
    Assertions.assertEquals(persistedVehicleLock, joinedActiveMtMessage.persistedVehicleLock());
  }

  @Test
  void invalidParameterTest() {
    RowMapper<JoinedActiveMtMessage> rowMapper = JoinedActiveMtMessageRowMapper.create(TestUtils.mockRowMapper(), TestUtils.mockRowMapper(),
        TestUtils.mockRowMapper());
    AssertThrows.illegalArgumentException(() -> rowMapper.map(null, null), "resultSet must not be null");
  }

  @Test
  void mapTest() throws SQLException {
    final ResultSet resultSet = Mockito.mock(ResultSet.class);
    final PersistedActiveMtMessage persistedActiveMtMessage = TestUtils.createPersistedActiveMtMessage();
    final PersistedMtMessage persistedMtMessage = TestUtils.createPersistedMtMessage();
    final PersistedVehicleLock persistedVehicleLock = TestUtils.createPersistedVehicleLock();

    RowMapper<PersistedActiveMtMessage> activeMtMessageRowMapper = TestUtils.mockRowMapper();
    Mockito.when(activeMtMessageRowMapper.map(resultSet, null)).thenReturn(persistedActiveMtMessage);

    RowMapper<PersistedMtMessage> mtMessageRowMapper = TestUtils.mockRowMapper();
    Mockito.when(mtMessageRowMapper.map(resultSet, null)).thenReturn(persistedMtMessage);

    RowMapper<PersistedVehicleLock> vehicleLockRowMapper = TestUtils.mockRowMapper();
    Mockito.when(vehicleLockRowMapper.map(resultSet, null)).thenReturn(persistedVehicleLock);

    RowMapper<JoinedActiveMtMessage> rowMapper = JoinedActiveMtMessageRowMapper.create(activeMtMessageRowMapper, mtMessageRowMapper, vehicleLockRowMapper);
    JoinedActiveMtMessage joinedActiveMtMessage = rowMapper.map(resultSet, null);

    checkJoinedActiveMtMessage(joinedActiveMtMessage, persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);

    Mockito.verify(activeMtMessageRowMapper).map(resultSet, null);
    Mockito.verify(mtMessageRowMapper).map(resultSet, null);
    Mockito.verify(vehicleLockRowMapper).map(resultSet, null);
    Mockito.verifyNoMoreInteractions(activeMtMessageRowMapper, mtMessageRowMapper, vehicleLockRowMapper);
  }
}
