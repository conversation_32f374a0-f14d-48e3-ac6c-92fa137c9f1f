package com.volvo.tisp.tgwmts.database.model;

import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.TestUtils;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class JoinedMtMessageTest {
  private static JoinedMtMessage createJoinedMtMessage() {
    return new JoinedMtMessage(Optional.of(TestUtils.createPersistedActiveMtMessage()), TestUtils.createPersistedMtMessage(),
            TestUtils.createPersistedVehicleLock());
  }

  @Test
  void constructorTest() {
    final PersistedMtMessage mtMessage = TestUtils.createPersistedMtMessage();
    final Optional<PersistedActiveMtMessage> activeMtMessage = Optional.of(TestUtils.createPersistedActiveMtMessage());
    final PersistedVehicleLock persistedVehicleLock = TestUtils.createPersistedVehicleLock();

    JoinedMtMessage joinedMtMessage = new JoinedMtMessage(activeMtMessage, mtMessage, persistedVehicleLock);
    Assertions.assertSame(activeMtMessage, joinedMtMessage.persistedActiveMtMessage());
    Assertions.assertSame(mtMessage, joinedMtMessage.persistedMtMessage());
  }

  @Test
  void invalidConstructorTest() {
    final PersistedMtMessage mtMessage = TestUtils.createPersistedMtMessage();
    final Optional<PersistedActiveMtMessage> activeMtMessage = Optional.of(TestUtils.createPersistedActiveMtMessage());
    final PersistedVehicleLock persistedVehicleLock = TestUtils.createPersistedVehicleLock();

    AssertThrows.illegalArgumentException(() -> new JoinedMtMessage(activeMtMessage, null, persistedVehicleLock),
        "persistedMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> new JoinedMtMessage(null, mtMessage, persistedVehicleLock),
        "persistedActiveMtMessage must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "persistedActiveMtMessage=Optional[activeMtMessageId=3, activeMtMessage={mtMessageId=2, retryAttempt=10, sendSchemaStepId=3, timeout=1970-01-01T00:01:00Z, created=1970-01-01T00:00:00Z}, updated=1970-01-01T00:00:00Z]persistedMtMessage=created=1970-01-01T00:00:00Z, mtMessage={queueId=vps, replyOption={Optional[correlationId=b4d4b26f-142a-4556-9af5-46d3e10068d8, replyTo=reply-to-queue-name]}, sendSchemaName=COMMON_HIGH, srpOption={srpDestinationService=50, srpDestinationVersion=1, srpLevel12=false, srpPayload=[42]}, tid=123456789012345678901234567890FF, vehicleLockId=1}, mtMessageId=2persistedVehicleLock=created=1970-01-01T00:00:00Z, vehicleLockId=1, vehicleLock={vpi=12345678901234567890ABCDEFABCDEF}";
    Assertions.assertEquals(expectedString, createJoinedMtMessage().toString());
  }
}