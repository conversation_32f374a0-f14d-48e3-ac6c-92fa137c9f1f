<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xmlns="http://maven.apache.org/POM/4.0.0"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tgw-mt-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <groupId>com.wirelesscar.ngtp.cd.assemblies</groupId>
  <artifactId>tgw-mt-server-deployable</artifactId>
  <packaging>pom</packaging>

  <properties>
    <deploy.engine.jar.version>199</deploy.engine.jar.version>
    <deploy.engine.maven.plugin.version>42</deploy.engine.maven.plugin.version>
    <deploy.engine.version>35</deploy.engine.version>
    <supervisor3x.version>51</supervisor3x.version>
    <jdk21.version>5</jdk21.version>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tgw-mt-server-app</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.framework.deploy-engine</groupId>
      <artifactId>deploy-engine-jar-deployer</artifactId>
      <version>${deploy.engine.jar.version}</version>
      <classifier>bundle</classifier>
      <type>zip</type>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.framework.deploy-engine</groupId>
      <artifactId>deploy-engine</artifactId>
      <version>${deploy.engine.version}</version>
      <type>pom</type>
      <exclusions>
        <exclusion>
          <groupId>org.supervisord</groupId>
          <artifactId>supervisor3x</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>net.java.jdk</groupId>
      <artifactId>jdk21-aarch64</artifactId>
      <version>${jdk21.version}</version>
      <type>npm</type>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>com.wirelesscar.framework.deploy-engine</groupId>
        <artifactId>deploy-engine-maven-plugin</artifactId>
        <version>${deploy.engine.maven.plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>package</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>