<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>159</version>
  </parent>

  <artifactId>tgw-mt-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <properties>
    <!-- override surfire plugin version until https://issues.apache.org/jira/browse/SUREFIRE-1815 is fixed -->
    <surefire.plugin>3.5.3</surefire.plugin>
    <component.long-name>tgw-mt-server</component.long-name>
    <component.short-name>tgwmts</component.short-name>
    <component.debug.port>8787</component.debug.port>
    <component.debug.suspend>n</component.debug.suspend>

    <maven.compiler.release>21</maven.compiler.release>

    <common-dto-lib.version>180</common-dto-lib.version>
    <componentbase-lib.version>2055</componentbase-lib.version>
    <connectivity-api.version>39</connectivity-api.version>
    <connectivity-management-service-models.version>0.2.0</connectivity-management-service-models.version>
    <jdbi.version>3.49.5</jdbi.version>
    <otj-pg-embedded.version>1.1.1</otj-pg-embedded.version>
    <protobuf.version>4.31.1</protobuf.version>
    <reactor-netty-http.version>1.2.7</reactor-netty-http.version>
    <service-routing-protocol-lib.version>53</service-routing-protocol-lib.version>
    <shedlock.version>6.9.0</shedlock.version>
    <subscriptionrepository-client.version>675</subscriptionrepository-client.version>
    <service-monitoring-lib.version>32</service-monitoring-lib.version>
    <tce-common.version>353</tce-common.version>

    <tce-opus-client.version>179</tce-opus-client.version>

    <test-utils-lib.version>70</test-utils-lib.version>
    <tgw-device-info-lib.version>112</tgw-device-info-lib.version>
    <tisp-dependencies.version>159</tisp-dependencies.version>
    <vc-device-bulk-sync-lib.version>28</vc-device-bulk-sync-lib.version>
    <vc-uncaught-exception-handler-lib.version>28</vc-uncaught-exception-handler-lib.version>
    <vehicle-communication-statistics-client.version>120</vehicle-communication-statistics-client.version>
    <maven.compiler.source>21</maven.compiler.source>
    <maven.compiler.target>21</maven.compiler.target>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.connectivity</groupId>
        <artifactId>connectivity-management-service-models</artifactId>
        <version>${connectivity-management-service-models.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-device-bulk-sync-lib</artifactId>
        <version>${vc-device-bulk-sync-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>common-dto-lib</artifactId>
        <version>${common-dto-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-uncaught-exception-handler-lib</artifactId>
        <version>${vc-uncaught-exception-handler-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>service-routing-protocol-lib</artifactId>
        <version>${service-routing-protocol-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>net.javacrumbs.shedlock</groupId>
        <artifactId>shedlock-spring</artifactId>
        <version>${shedlock.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp.servicemonitoring</groupId>
        <artifactId>service-monitoring-lib</artifactId>
        <version>${service-monitoring-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>net.javacrumbs.shedlock</groupId>
        <artifactId>shedlock-provider-jdbc-template</artifactId>
        <version>${shedlock.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jdbi</groupId>
        <artifactId>jdbi3-core</artifactId>
        <version>${jdbi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-opus-client-api</artifactId>
        <version>${tce-opus-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tgw-device-info-activation-notify</artifactId>
        <version>${tgw-device-info-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tgw-device-info-cache-notify</artifactId>
        <version>${tgw-device-info-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-common-proto-api</artifactId>
        <version>${tce-common.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.volvo.connectivity</groupId>
        <artifactId>connectivity-api</artifactId>
        <version>${connectivity-api.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java</artifactId>
        <version>${protobuf.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.protobuf</groupId>
        <artifactId>protobuf-java-util</artifactId>
        <version>${protobuf.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vehicle-communication-statistics-client-protobuf</artifactId>
        <version>${vehicle-communication-statistics-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-impl</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.netty</groupId>
        <artifactId>reactor-netty-http</artifactId>
        <version>${reactor-netty-http.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.componentbase-lib</groupId>
        <artifactId>componentbase-common</artifactId>
        <version>${componentbase-lib.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>test-utils-lib</artifactId>
        <version>${test-utils-lib.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.opentable.components</groupId>
        <artifactId>otj-pg-embedded</artifactId>
        <version>${otj-pg-embedded.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-test-util</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <configuration>
          <skip>true</skip>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>tgwmts-default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>tgw-mt-server-app</module>
        <module>tgw-mt-server-database</module>
        <module>tgw-mt-server-impl</module>
        <module>tgw-mt-server-integration-tests</module>
      </modules>
    </profile>
    <profile>
      <id>deployable-assembly</id>
      <activation>
        <property>
          <name>deployable-assembly</name>
        </property>
      </activation>
      <modules>
        <module>deployable-assembly</module>
      </modules>
    </profile>
    <profile>
      <id>component-tests</id>
    </profile>
  </profiles>
</project>
