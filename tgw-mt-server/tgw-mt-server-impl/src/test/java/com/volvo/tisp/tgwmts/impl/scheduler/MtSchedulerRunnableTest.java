package com.volvo.tisp.tgwmts.impl.scheduler;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.impl.utils.Throttler;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtSchedulerRunnableTest {
  @Test
  void interruptedExceptionTest() throws InterruptedException {
    MtSchedulerWorker mtSchedulerWorker = Mockito.mock(MtSchedulerWorker.class);
    Mockito.when(mtSchedulerWorker.doWorkOnce()).thenReturn(0);

    Throttler throttler = Mockito.mock(Throttler.class);
    Mockito.doThrow(InterruptedException.class).when(throttler).acquire();

    MtSchedulerRunnable mtSchedulerRunnable = new MtSchedulerRunnable(mtSchedulerWorker, throttler);
    mtSchedulerRunnable.run();

    Mockito.verify(mtSchedulerWorker).doWorkOnce();
    Mockito.verify(throttler).acquire();
    Mockito.verifyNoMoreInteractions(mtSchedulerWorker, throttler);
  }

  @Test
  void invalidConstructorTest() {
    MtSchedulerWorker mtSchedulerWorker = Mockito.mock(MtSchedulerWorker.class);
    Throttler throttler = Mockito.mock(Throttler.class);

    AssertThrows.illegalArgumentException(
        () -> new MtSchedulerRunnable(null, throttler), "mtSchedulerWorker must not be null");
    AssertThrows.illegalArgumentException(
        () -> new MtSchedulerRunnable(mtSchedulerWorker, null), "throttler must not be null");
  }

  @Test
  void runTest() throws InterruptedException {
    MtSchedulerWorker mtSchedulerWorker = Mockito.mock(MtSchedulerWorker.class);
    Mockito.when(mtSchedulerWorker.doWorkOnce()).thenReturn(1);

    Throttler throttler = Mockito.mock(Throttler.class);

    MtSchedulerRunnable mtSchedulerRunnable = new MtSchedulerRunnable(mtSchedulerWorker, throttler);
    Thread thread = new Thread(mtSchedulerRunnable);

    try {
      thread.start();
      Mockito.verify(mtSchedulerWorker, Mockito.timeout(500).atLeast(10)).doWorkOnce();
      Mockito.verifyNoMoreInteractions(throttler);
    } finally {
      thread.interrupt();
      thread.join();
    }
  }

  @Test
  void runtimeExceptionTest() throws InterruptedException {
    MtSchedulerWorker mtSchedulerWorker = Mockito.mock(MtSchedulerWorker.class);
    Mockito.when(mtSchedulerWorker.doWorkOnce())
        .thenReturn(1)
        .thenThrow(RuntimeException.class)
        .thenReturn(0);

    Throttler throttler = Mockito.mock(Throttler.class);
    Mockito.doThrow(InterruptedException.class).when(throttler).acquire();

    MtSchedulerRunnable mtSchedulerRunnable = new MtSchedulerRunnable(mtSchedulerWorker, throttler);
    mtSchedulerRunnable.run();

    Mockito.verify(mtSchedulerWorker, Mockito.times(3)).doWorkOnce();
    Mockito.verify(throttler).acquire();
    Mockito.verifyNoMoreInteractions(mtSchedulerWorker, throttler);
  }
}
