package com.volvo.tisp.tgwmts.impl.jms;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Tags;

class MtMessageMetricReporterTest {
  @Test
  void onActiveMtInsertFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onActiveMtInsertFailure(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onActiveMtInsertFailure(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      mtMessageMetricReporter.onActiveMtInsertFailure(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "active-mt-message", "status", "failure"), Duration.ofMillis(100), 1);

      mtMessageMetricReporter.onActiveMtInsertFailure(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "active-mt-message", "status", "failure"), Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onActiveMtInsertSuccessTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onActiveMtInsertSuccess(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onActiveMtInsertSuccess(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      mtMessageMetricReporter.onActiveMtInsertSuccess(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "active-mt-message", "status", "success"), Duration.ofMillis(100), 1);

      mtMessageMetricReporter.onActiveMtInsertSuccess(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "active-mt-message", "status", "success"), Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onDiscardedMtMessageWithReplyTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onDiscardedMtMessageWithReply();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "discarded", "reply", "true"), 1);

      mtMessageMetricReporter.onDiscardedMtMessageWithReply();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "discarded", "reply", "true"), 2);
    });
  }

  @Test
  void onDiscardedMtMessageWithoutReplyTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onDiscardedMtMessageWithoutReply();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "discarded", "reply", "false"), 1);

      mtMessageMetricReporter.onDiscardedMtMessageWithoutReply();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "discarded", "reply", "false"), 2);
    });
  }

  @Test
  void onEnqueueingTypeTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onEnqueueingType(EnqueueingType.NORMAL, TestUtil.SRP_DESTINATION_SERVICE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "enqueueing-type", Tags.of("type", "NORMAL"), 1);

      mtMessageMetricReporter.onEnqueueingType(EnqueueingType.NORMAL, TestUtil.SRP_DESTINATION_SERVICE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "enqueueing-type", Tags.of("type", "NORMAL"), 2);

      mtMessageMetricReporter.onEnqueueingType(EnqueueingType.IGNORE, TestUtil.SRP_DESTINATION_SERVICE);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "enqueueing-type", Tags.of("type", "IGNORE"), 1);
    });
  }

  @Test
  void onIgnoredMtMessageTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onIgnoredMtMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "ignored"), 1);

      mtMessageMetricReporter.onIgnoredMtMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "ignored"), 2);
    });
  }

  @Test
  void onMtInsertFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onMtInsertFailure(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onMtInsertFailure(Duration.ofMillis(-1)), "duration must not be negative: PT-0.001S");

      mtMessageMetricReporter.onMtInsertFailure(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "mt-message", "status", "failure"), Duration.ofMillis(100), 1);

      mtMessageMetricReporter.onMtInsertFailure(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "mt-message", "status", "failure"), Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onMtInsertSuccessTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onMtInsertSuccess(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onMtInsertSuccess(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      mtMessageMetricReporter.onMtInsertSuccess(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "mt-message", "status", "success"), Duration.ofMillis(100), 1);

      mtMessageMetricReporter.onMtInsertSuccess(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.persist", Tags.of("type", "mt-message", "status", "success"), Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onMtStatusPublishDurationTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onMtStatusPublishDuration(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onMtStatusPublishDuration(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      mtMessageMetricReporter.onMtStatusPublishDuration(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.status.publish", Duration.ofMillis(100), 1);

      mtMessageMetricReporter.onMtStatusPublishDuration(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.status.publish", Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onMtStatusPublishErrorTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onMtStatusPublishError();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status.publish.error", 1);

      mtMessageMetricReporter.onMtStatusPublishError();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status.publish.error", 2);
    });
  }

  @Test
  void onMtStatusTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onMtStatus(null), "mtStatus must not be null");

      mtMessageMetricReporter.onMtStatus(MtStatus.DELIVERED);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status", Tags.of("type", "DELIVERED"), 1);

      mtMessageMetricReporter.onMtStatus(MtStatus.DELIVERED);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt.status", Tags.of("type", "DELIVERED"), 2);
    });
  }

  @Test
  void onMtV2InvalidTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onMtV2Invalid();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "api.jms.message.received", Tags.of("version", "v2", "status", "invalid"), 1);

      mtMessageMetricReporter.onMtV2Invalid();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "api.jms.message.received", Tags.of("version", "v2", "status", "invalid"), 2);
    });
  }

  @Test
  void onSuccessTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onSuccess(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtMessageMetricReporter.onSuccess(Duration.ofMillis(-1)), "duration must not be negative: PT-0.001S");

      mtMessageMetricReporter.onSuccess(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt", Tags.of("status", "success"), Duration.ofMillis(100), 1);

      mtMessageMetricReporter.onSuccess(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt", Tags.of("status", "success"), Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onUnidentifiedMtMessageTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageMetricReporter::new, (meterRegistry, mtMessageMetricReporter) -> {
      mtMessageMetricReporter.onUnidentifiedMtMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "unidentified"), 1);

      mtMessageMetricReporter.onUnidentifiedMtMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt", Tags.of("status", "unidentified"), 2);
    });
  }
}
