package com.volvo.tisp.tgwmts.impl.cache;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.MeterRegistry;

class ConnectionEstablishedRegistrationCacheMetricReporterTest {
  @Test
  void onCacheSizeChangedTest() {
    AssertThrows.illegalArgumentException(
        () -> new ConnectionEstablishedRegistrationCacheMetricReporter(Mockito.mock(MeterRegistry.class)).onCacheSizeChanged(-2),
        "size must not be negative: -2");

    MetricsReporterTestUtils.initReporterAndTest(ConnectionEstablishedRegistrationCacheMetricReporter::new,
        (meterRegistry, connectionEstablishedRegistrationCacheMetricReporter) -> {
          connectionEstablishedRegistrationCacheMetricReporter.onCacheSizeChanged(10);
          MetricsReporterTestUtils.checkGauge(meterRegistry, "conn-est-reg-cache-size", 10);
        });
  }

  @Test
  void onInvalidExpiryTimeTest() {
    String invalidExpiryCounterName = "conn-est-invalid-expiry-time";
    MetricsReporterTestUtils.initReporterAndTest(ConnectionEstablishedRegistrationCacheMetricReporter::new,
        (meterRegistry, connectionEstablishedRegistrationCacheMetricReporter) -> {
          connectionEstablishedRegistrationCacheMetricReporter.onInvalidExpiryTime();
          MetricsReporterTestUtils.checkCounter(meterRegistry, invalidExpiryCounterName, 1);

          connectionEstablishedRegistrationCacheMetricReporter.onInvalidExpiryTime();
          MetricsReporterTestUtils.checkCounter(meterRegistry, invalidExpiryCounterName, 2);
        });
  }
}
