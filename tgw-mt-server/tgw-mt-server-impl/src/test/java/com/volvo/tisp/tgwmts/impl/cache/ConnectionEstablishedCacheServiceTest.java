package com.volvo.tisp.tgwmts.impl.cache;

import java.time.Clock;
import java.time.Instant;

import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Scheduler;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ConnectionEstablishedCacheServiceTest {
  private static final Vpi VPI_1 = Vpi.ofString("12345678901234567890ABCDEFAAAAAA");
  private static final Vpi VPI_2 = Vpi.ofString("12345678901234567890ABCDEFBBBBBB");
  private static final Vpi VPI_3 = Vpi.ofString("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA");
  private static final Vpi VPI_4 = Vpi.ofString("BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB");
  private static final Vpi VPI_5 = Vpi.ofString("CCCCCCCCCCCCCCCCCCCCCCCCCCCCCCCC");

  private static Cache<Vpi, Instant> createCache() {
    return Caffeine.newBuilder().scheduler(Scheduler.systemScheduler()).build();
  }

  @NotNull
  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(TestUtil.INSTANT);
    return clock;
  }

  @Test
  void addExistingVpiTest() {
    Instant longExpiryInstant = TestUtil.INSTANT.plusMillis(2000);
    Instant shortExpiryInstant = TestUtil.INSTANT.plusMillis(1000);

    Cache<Vpi, Instant> cache = createCache();
    Clock clock = mockClock();
    ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter = Mockito.mock(
        ConnectionEstablishedRegistrationCacheMetricReporter.class);
    ConnectionEstablishedCacheService connectionEstablishedCacheService = new ConnectionEstablishedCacheService(clock, cache,
        connectionEstablishedRegistrationCacheMetricReporter);

    connectionEstablishedCacheService.add(TestUtil.VPI, longExpiryInstant);
    connectionEstablishedCacheService.add(TestUtil.VPI, shortExpiryInstant);

    Assertions.assertEquals(longExpiryInstant, connectionEstablishedCacheService.get(TestUtil.VPI).orElseThrow());

    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock, connectionEstablishedRegistrationCacheMetricReporter);
  }

  @Test
  void addInvalidExpiryTimeTest() {
    Cache<Vpi, Instant> cache = createCache();
    ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter = Mockito.mock(
        ConnectionEstablishedRegistrationCacheMetricReporter.class);
    Clock clock = mockClock();
    ConnectionEstablishedCacheService connectionEstablishedCacheService = new ConnectionEstablishedCacheService(clock, cache,
        connectionEstablishedRegistrationCacheMetricReporter);

    Instant expiryInstant = TestUtil.INSTANT.minusMillis(1000);

    connectionEstablishedCacheService.add(TestUtil.VPI, expiryInstant);
    connectionEstablishedCacheService.add(TestUtil.VPI, TestUtil.INSTANT);

    Mockito.verify(connectionEstablishedRegistrationCacheMetricReporter, Mockito.times(2)).onInvalidExpiryTime();
    Mockito.verify(clock, Mockito.times(2)).instant();

    Mockito.verifyNoMoreInteractions(clock, connectionEstablishedRegistrationCacheMetricReporter);
  }

  @Test
  void addInvalidParameterTest() {
    Cache<Vpi, Instant> cache = createCache();
    ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter = Mockito.mock(
        ConnectionEstablishedRegistrationCacheMetricReporter.class);
    Clock clock = Mockito.mock(Clock.class);
    ConnectionEstablishedCacheService connectionEstablishedCacheService = new ConnectionEstablishedCacheService(clock, cache,
        connectionEstablishedRegistrationCacheMetricReporter);

    Instant expiryInstant = TestUtil.INSTANT.minusMillis(1000);

    AssertThrows.illegalArgumentException(() -> connectionEstablishedCacheService.add(null, expiryInstant), "vpi must not be null");
    AssertThrows.illegalArgumentException(() -> connectionEstablishedCacheService.add(TestUtil.VPI, null), "expiryInstant must not be null");

    Mockito.verifyNoInteractions(clock, connectionEstablishedRegistrationCacheMetricReporter);
  }

  @Test
  void addTest() {
    Cache<Vpi, Instant> cache = createCache();
    ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter = Mockito.mock(
        ConnectionEstablishedRegistrationCacheMetricReporter.class);
    Clock clock = mockClock();
    ConnectionEstablishedCacheService connectionEstablishedCacheService = new ConnectionEstablishedCacheService(clock, cache,
        connectionEstablishedRegistrationCacheMetricReporter);

    Instant expiryInstant = TestUtil.INSTANT.plusMillis(1000);
    connectionEstablishedCacheService.add(TestUtil.VPI, expiryInstant);
    Assertions.assertEquals(expiryInstant, cache.getIfPresent(TestUtil.VPI));

    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock, connectionEstablishedRegistrationCacheMetricReporter);
  }

  @Test
  void getTest() {
    Cache<Vpi, Instant> cache = createCache();
    Clock clock = mockClock();
    ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter = Mockito.mock(
        ConnectionEstablishedRegistrationCacheMetricReporter.class);

    ConnectionEstablishedCacheService connectionEstablishedCacheService = new ConnectionEstablishedCacheService(clock, cache,
        connectionEstablishedRegistrationCacheMetricReporter);
    AssertThrows.illegalArgumentException(() -> connectionEstablishedCacheService.getEntries(0), "limit must be positive: 0");

    Instant expiryInstant = TestUtil.INSTANT.plusMillis(1000);
    connectionEstablishedCacheService.add(VPI_1, expiryInstant);
    connectionEstablishedCacheService.add(VPI_2, expiryInstant);
    connectionEstablishedCacheService.add(VPI_3, expiryInstant);
    connectionEstablishedCacheService.add(VPI_4, expiryInstant);
    connectionEstablishedCacheService.add(VPI_5, expiryInstant);

    Assertions.assertEquals(2, connectionEstablishedCacheService.getEntries(2).size());
    Assertions.assertEquals(5, connectionEstablishedCacheService.getEntries(10).size());

    Mockito.verifyNoInteractions(connectionEstablishedRegistrationCacheMetricReporter);
  }

  @Test
  void removeTest() {
    Cache<Vpi, Instant> cache = createCache();
    Clock clock = mockClock();
    ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter = Mockito.mock(
        ConnectionEstablishedRegistrationCacheMetricReporter.class);
    ConnectionEstablishedCacheService connectionEstablishedCacheService = new ConnectionEstablishedCacheService(clock, cache,
        connectionEstablishedRegistrationCacheMetricReporter);
    Instant expiryInstant = TestUtil.INSTANT.plusMillis(1000);

    AssertThrows.illegalArgumentException(() -> connectionEstablishedCacheService.remove(null), "vpi must not be null");

    connectionEstablishedCacheService.add(TestUtil.VPI, expiryInstant);
    Assertions.assertEquals(1, connectionEstablishedCacheService.getEntries(10).size());

    connectionEstablishedCacheService.remove(TestUtil.VPI);
    Assertions.assertEquals(0, connectionEstablishedCacheService.getEntries(10).size());
    Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationCacheMetricReporter);
  }

  @Test
  void reportCacheSizeMetricTest() {
    Cache<Vpi, Instant> cache = createCache();
    Clock clock = mockClock();
    ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter = Mockito.mock(
        ConnectionEstablishedRegistrationCacheMetricReporter.class);
    ConnectionEstablishedCacheService connectionEstablishedCacheService = new ConnectionEstablishedCacheService(clock, cache,
        connectionEstablishedRegistrationCacheMetricReporter);

    Instant expiryInstant = TestUtil.INSTANT.plusMillis(1000);
    connectionEstablishedCacheService.add(VPI_1, expiryInstant);
    connectionEstablishedCacheService.add(VPI_2, expiryInstant);
    connectionEstablishedCacheService.add(VPI_3, expiryInstant);
    connectionEstablishedCacheService.add(VPI_4, expiryInstant);
    connectionEstablishedCacheService.add(VPI_5, expiryInstant);

    connectionEstablishedCacheService.reportCacheSizeMetric();

    Mockito.verify(connectionEstablishedRegistrationCacheMetricReporter).onCacheSizeChanged(5);
    Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationCacheMetricReporter);
  }
}
