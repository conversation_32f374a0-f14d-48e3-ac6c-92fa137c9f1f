package com.volvo.tisp.tgwmts.impl.rest;

import com.volvo.tisp.tgwmts.impl.model.MtMessageInformation;
import com.volvo.tisp.tgwmts.impl.services.MtMessageInformationService;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.util.Collections;
import java.util.Optional;

class MtMessageInformationRestControllerTest {
    private static final Vpi VPI_1 = Vpi.ofString("12345678901234567890ABCDEFABCDEF");

    static void verifyResponseEntity(ResponseEntity<MtMessageInformation> responseEntity, HttpStatus expectedHttpStatus, MtMessageInformation messagesInfo) {
        Assertions.assertEquals(expectedHttpStatus, responseEntity.getStatusCode());
        Assertions.assertEquals(messagesInfo, responseEntity.getBody());
    }

    @Test
    void getMessagesInformationByVpi() {
        MtMessageInformationService mtMessageInformationService = Mockito.mock(
                MtMessageInformationService.class);
        Optional<MtMessageInformation> messagesInfo = Optional.of(new MtMessageInformation(VPI_1.toString(), Collections.emptyList()));
        Mockito.when(mtMessageInformationService.getMessageInformationByVpi(VPI_1))
                .thenReturn(messagesInfo);
        MtMessageInformationRestController mtMessageInformationRestController = new MtMessageInformationRestController(
                mtMessageInformationService);

        ResponseEntity<MtMessageInformation> responseEntity = mtMessageInformationRestController.getMessageInformationByVpi(VPI_1.toString());

        verifyResponseEntity(responseEntity, HttpStatus.OK, messagesInfo.get());
        Mockito.verify(mtMessageInformationService).getMessageInformationByVpi(VPI_1);
        Mockito.verifyNoMoreInteractions(mtMessageInformationService);

    }

    @Test
    void getNonExistingMessagesInformationByVpi() {
        MtMessageInformationService mtMessageInformationService = Mockito.mock(
                MtMessageInformationService.class);
        Mockito.when(mtMessageInformationService.getMessageInformationByVpi(VPI_1))
                .thenReturn(Optional.empty());
        MtMessageInformationRestController mtMessageInformationRestController = new MtMessageInformationRestController(
                mtMessageInformationService);

        ResponseEntity<MtMessageInformation> responseEntity = mtMessageInformationRestController.getMessageInformationByVpi(VPI_1.toString());

        verifyResponseEntity(responseEntity, HttpStatus.NOT_FOUND, null);
        Mockito.verify(mtMessageInformationService).getMessageInformationByVpi(VPI_1);
        Mockito.verifyNoMoreInteractions(mtMessageInformationService);

    }
}