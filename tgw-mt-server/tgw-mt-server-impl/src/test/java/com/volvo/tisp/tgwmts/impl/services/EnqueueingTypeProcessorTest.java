package com.volvo.tisp.tgwmts.impl.services;

import java.time.Duration;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class EnqueueingTypeProcessorTest {
  private static void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideTest(EnqueueingType enqueueingType,
      int expectedActiveMtMessageWriterDeleteTimes) {
    List<PersistedMtMessage> mtMessages = List.of(TestUtil.createPersistedMtMessageBuilder().build());
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(activeMtMessageWriter.findMtMessagesByVpiAndQueueId(TestUtil.VPI, TestUtil.QUEUE_ID)).thenReturn(mtMessages);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    EnqueueingTypeProcessor enqueueingTypeProcessor = new EnqueueingTypeProcessor(5, mtMessageMetricReporter, mtStatusPublisher);

    enqueueingTypeProcessor.deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(TestUtil.createReceivedMtMessage(enqueueingType),
        activeMtMessageWriter);

    Mockito.verify(activeMtMessageWriter, Mockito.times(expectedActiveMtMessageWriterDeleteTimes))
        .findMtMessagesByVpiAndQueueId(TestUtil.VPI, TestUtil.QUEUE_ID);
    Mockito.verify(activeMtMessageWriter, Mockito.times(expectedActiveMtMessageWriterDeleteTimes))
        .deleteMtMessageByIds(mtMessages.stream().map(PersistedMtMessage::getMtMessageId).toList());
    Mockito.verify(mtStatusPublisher, Mockito.times(expectedActiveMtMessageWriterDeleteTimes))
        .publishMtStatus(
            ArgumentMatchers.eq(MtStatus.OVERRIDDEN),
            ArgumentMatchers.any(ReplyOption.class),
            ArgumentMatchers.eq(TestUtil.VPI));
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, mtStatusPublisher);
  }

  private static ActiveMtMessageWriter mockActiveMtMessageWriter(int mtMessageCount, int activeMtMessageCount) {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(TestUtil.VEHICLE_LOCK_ID, TestUtil.QUEUE_ID)).thenReturn(mtMessageCount);
    Mockito.when(activeMtMessageWriter.countActiveMtMessagesByVehicleLockId(TestUtil.VEHICLE_LOCK_ID)).thenReturn(activeMtMessageCount);
    return activeMtMessageWriter;
  }

  private static void shouldActiveMtMessageBeCreatedTest(EnqueueingType enqueueingType, int numberOfActiveMtMessage,
      boolean expectedShouldActiveMtMessageBeCreated, int expectedActiveMtMessageWriterQueryTimes) {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(10, numberOfActiveMtMessage);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    EnqueueingTypeProcessor enqueueingTypeProcessor = new EnqueueingTypeProcessor(5, mtMessageMetricReporter, mtStatusPublisher);

    Assertions.assertEquals(expectedShouldActiveMtMessageBeCreated,
        enqueueingTypeProcessor.shouldActiveMtMessageBeCreated(TestUtil.VEHICLE_LOCK_ID, TestUtil.createReceivedMtMessage(enqueueingType),
            activeMtMessageWriter));

    Mockito.verify(activeMtMessageWriter, Mockito.times(expectedActiveMtMessageWriterQueryTimes))
        .countActiveMtMessagesByVehicleLockId(TestUtil.VEHICLE_LOCK_ID);
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, mtStatusPublisher);
  }

  private static void shouldMessageBeIgnoredTest(EnqueueingType enqueueingType, int numberOfMtMessage,
      boolean expectedShouldIgnored, int expectedActiveMtMessageWriterQueryTimes, int expectedMtMessageMetricReporterTimes) {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(numberOfMtMessage, 0);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    EnqueueingTypeProcessor enqueueingTypeProcessor = new EnqueueingTypeProcessor(5, mtMessageMetricReporter, mtStatusPublisher);

    Assertions.assertEquals(expectedShouldIgnored,
        enqueueingTypeProcessor.shouldMessageBeIgnored(TestUtil.VEHICLE_LOCK_ID, TestUtil.createReceivedMtMessage(enqueueingType), activeMtMessageWriter));
    Mockito.verify(activeMtMessageWriter, Mockito.times(expectedActiveMtMessageWriterQueryTimes))
        .countMtMessagesByVehicleLockIdAndQueueId(TestUtil.VEHICLE_LOCK_ID, TestUtil.QUEUE_ID);
    Mockito.verify(mtMessageMetricReporter, Mockito.times(expectedMtMessageMetricReporterTimes)).onIgnoredMtMessage();
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, mtStatusPublisher);
  }

  @Test
  void activateQueuedOnOverrideTest() {
    List<PersistedMtMessage> mtMessages = List.of(TestUtil.createPersistedMtMessageBuilder().build());
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher publisher = Mockito.mock(MtStatusPublisher.class);
    ActiveMtMessageWriter writer = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(writer.findMtMessagesByVpiAndQueueId(TestUtil.VPI, TestUtil.QUEUE_ID)).thenReturn(mtMessages);
    Mockito.when(writer.countActiveMtMessages()).thenReturn(0);
    PersistedMtMessage queuedMtMessage = TestUtil.createPersistedMtMessageBuilder().setMtMessageId(MtMessageId.ofLong(2L)).build();
    Mockito.when(writer.findOldestNonActiveMtMessages(TestUtil.VPI, 1)).thenReturn(List.of(queuedMtMessage));
    Mockito.when(writer.insertActiveMtMessage(ArgumentMatchers.any(ActiveMtMessage.class))).thenReturn(Either.right(TestUtil.ACTIVE_MT_MESSAGE_ID));

    EnqueueingTypeProcessor enqueueingTypeProcessor = new EnqueueingTypeProcessor(1, mtMessageMetricReporter, publisher);
    enqueueingTypeProcessor.deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(TestUtil.createReceivedMtMessage(EnqueueingType.OVERRIDE), writer);

    Mockito.verify(writer).findMtMessagesByVpiAndQueueId(TestUtil.VPI, TestUtil.QUEUE_ID);
    Mockito.verify(writer).deleteMtMessageByIds(mtMessages.stream().map(PersistedMtMessage::getMtMessageId).toList());
    Mockito.verify(writer).countActiveMtMessages();
    Mockito.verify(writer).findOldestNonActiveMtMessages(TestUtil.VPI, 1);
    Mockito.verify(writer).insertActiveMtMessage(ArgumentMatchers.any(ActiveMtMessage.class));
    Mockito.verify(mtMessageMetricReporter).onActiveMtInsertSuccess(ArgumentMatchers.any(Duration.class));
    Mockito.verify(publisher).publishMtStatus(
        ArgumentMatchers.eq(MtStatus.OVERRIDDEN),
        ArgumentMatchers.any(ReplyOption.class),
        ArgumentMatchers.eq(TestUtil.VPI));
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, writer, publisher);
  }

  @Test
  void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideIgnoreTest() {
    deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideTest(EnqueueingType.IGNORE, 0);
  }

  @Test
  void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideImmediateTest() {
    deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideTest(EnqueueingType.IMMEDIATE, 0);
  }

  @Test
  void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideInvalidParameterTest() {
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(10, 5);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    EnqueueingTypeProcessor enqueueingTypeProcessor = new EnqueueingTypeProcessor(5, mtMessageMetricReporter, mtStatusPublisher);

    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(null, activeMtMessageWriter),
        "receivedMtMessage must not be null");
    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(TestUtil.createReceivedMtMessage(), null),
        "activeMtMessageWriter must not be null");
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, mtStatusPublisher);
  }

  @Test
  void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideNormalTest() {
    deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideTest(EnqueueingType.NORMAL, 0);
  }

  @Test
  void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideOverrideImmediateTest() {
    deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideTest(EnqueueingType.OVERRIDE_IMMEDIATE, 1);
  }

  @Test
  void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideOverrideTest() {
    deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverrideTest(EnqueueingType.OVERRIDE, 1);
  }

  @Test
  void invalidConstructorTest() {
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    AssertThrows.illegalArgumentException(() -> new EnqueueingTypeProcessor(0, null, mtStatusPublisher),
        "maxNumberOfInFlightMessagesPerVehicle must be positive: 0");
    AssertThrows.illegalArgumentException(() -> new EnqueueingTypeProcessor(5, null, mtStatusPublisher), "mtMessageMetricReporter must not be null");
    AssertThrows.illegalArgumentException(() -> new EnqueueingTypeProcessor(5, mtMessageMetricReporter, null), "mtStatusPublisher must not be null");
  }

  @Test
  void shouldActiveMtMessageBeCreatedImmediateTest() {
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.IMMEDIATE, 4, true, 0);
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.IMMEDIATE, 5, true, 0);
  }

  @Test
  void shouldActiveMtMessageBeCreatedInvalidParameterTest() {
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(10, 5);
    EnqueueingTypeProcessor enqueueingTypeProcessor = new EnqueueingTypeProcessor(5, mtMessageMetricReporter, mtStatusPublisher);
    VehicleLockId vehicleLockId = VehicleLockId.ofLong(1L);
    ReceivedMtMessage receivedMtMessage = TestUtil.createReceivedMtMessage();

    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.shouldActiveMtMessageBeCreated(null, receivedMtMessage, activeMtMessageWriter), "vehicleLockId must not be null");
    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.shouldActiveMtMessageBeCreated(vehicleLockId, null, activeMtMessageWriter), "receivedMtMessage must not be null");
    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.shouldActiveMtMessageBeCreated(vehicleLockId, receivedMtMessage, null), "activeMtMessageWriter must not be null");
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter);
  }

  @Test
  void shouldActiveMtMessageBeCreatedOverrideIgnoreTest() {
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.IGNORE, 4, true, 1);
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.IGNORE, 5, false, 1);
  }

  @Test
  void shouldActiveMtMessageBeCreatedOverrideImmediateTest() {
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.OVERRIDE_IMMEDIATE, 4, true, 0);
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.OVERRIDE_IMMEDIATE, 5, true, 0);
  }

  @Test
  void shouldActiveMtMessageBeCreatedOverrideNormalTest() {
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.NORMAL, 4, true, 1);
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.NORMAL, 5, false, 1);
  }

  @Test
  void shouldActiveMtMessageBeCreatedOverrideOverrideTest() {
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.OVERRIDE, 4, true, 1);
    shouldActiveMtMessageBeCreatedTest(EnqueueingType.OVERRIDE, 5, false, 1);
  }

  @Test
  void shouldMessageBeIgnoredIgnoreTest() {
    shouldMessageBeIgnoredTest(EnqueueingType.IGNORE, 10, true, 1, 1);
    shouldMessageBeIgnoredTest(EnqueueingType.IGNORE, 0, false, 1, 0);
  }

  @Test
  void shouldMessageBeIgnoredImmediateTest() {
    shouldMessageBeIgnoredTest(EnqueueingType.IMMEDIATE, 10, false, 0, 0);
  }

  @Test
  void shouldMessageBeIgnoredInvalidParameterTest() {
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(10, 5);
    EnqueueingTypeProcessor enqueueingTypeProcessor = new EnqueueingTypeProcessor(5, mtMessageMetricReporter, mtStatusPublisher);
    VehicleLockId vehicleLockId = VehicleLockId.ofLong(1L);
    ReceivedMtMessage receivedMtMessage = TestUtil.createReceivedMtMessage();

    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.shouldMessageBeIgnored(null, receivedMtMessage, activeMtMessageWriter),
        "vehicleLockId must not be null");
    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.shouldMessageBeIgnored(vehicleLockId, null, activeMtMessageWriter),
        "receivedMtMessage must not be null");
    AssertThrows.illegalArgumentException(
        () -> enqueueingTypeProcessor.shouldMessageBeIgnored(vehicleLockId, receivedMtMessage, null),
        "activeMtMessageWriter must not be null");
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, mtStatusPublisher);
  }

  @Test
  void shouldMessageBeIgnoredNormalTest() {
    shouldMessageBeIgnoredTest(EnqueueingType.NORMAL, 10, false, 0, 0);
  }

  @Test
  void shouldMessageBeIgnoredOverrideImmediateTest() {
    shouldMessageBeIgnoredTest(EnqueueingType.OVERRIDE_IMMEDIATE, 10, false, 0, 0);
  }

  @Test
  void shouldMessageBeIgnoredOverrideTest() {
    shouldMessageBeIgnoredTest(EnqueueingType.OVERRIDE, 10, false, 0, 0);
  }
}
