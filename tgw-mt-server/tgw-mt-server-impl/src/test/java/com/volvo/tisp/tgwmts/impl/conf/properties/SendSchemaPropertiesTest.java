package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SendSchemaPropertiesTest {
  @Test
  void getterTest() {
    Assertions.assertEquals(Duration.ofDays(1), new SendSchemaProperties(Duration.ofDays(1)).getMaxTimeout());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new SendSchemaProperties(null), "maxTimeout must not be null");
    AssertThrows.illegalArgumentException(() -> new SendSchemaProperties(Duration.ZERO), "maxTimeout cannot be less than 1 min, was: PT0S");
  }
}