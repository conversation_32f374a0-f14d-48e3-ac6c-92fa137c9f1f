package com.volvo.tisp.tgwmts.impl.schema;

import java.time.Duration;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.converters.SendSchemaNameMappingFunction;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolSetupSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DynamicSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.SatOnlySendSchema;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;

class FilterableSendSchemaFetcherTest {
  private static SendSchema createDynamicSendSchema() {
    return DynamicSendSchema.create(
        Duration.ofDays(2),
        new SendSchemaName("test"),
        List.of(
            SendSchemaStep.forUdp(SendSchemaStepId.ofInt(1)),
            SendSchemaStep.forSms(SendSchemaStepId.ofInt(2))),
        (short) 15);
  }

  private static SendSchemaNameMappingFunction mockMappingFunction(SendSchema sendSchema) {
    SendSchemaNameMappingFunction mockingFunction = Mockito.mock(SendSchemaNameMappingFunction.class);
    Mockito.when(mockingFunction.apply(ArgumentMatchers.any(SendSchemaName.class))).thenReturn(sendSchema);
    return mockingFunction;
  }

  private static void verifyDfolSetupDefaults(SendSchema sendSchema) {
    Assertions.assertEquals(SendSchemaName.DFOL_SETUP, sendSchema.getSendSchemaName());
    Assertions.assertEquals(Duration.ofDays(14), sendSchema.getGlobalTimeout());
    Assertions.assertEquals(3, sendSchema.getMaxRetryAttempts());
  }

  @Test
  void fetchDynamicSendSchemaWithUdpFilterTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(createDynamicSendSchema());
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    Set<SendSchemaStepType> filter = Set.of(SendSchemaStepType.UDP);
    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.DFOL_SETUP, SendSchemaStepFilter.of(filter));

    Assertions.assertEquals("test", sendSchema.getSendSchemaName().toString());
    Assertions.assertEquals(15, sendSchema.getMaxRetryAttempts());
    Assertions.assertEquals(Duration.ofDays(2), sendSchema.getGlobalTimeout());
    VerificationUtil.verifySendSchemaStepUdp(sendSchema, 1, Duration.ofMinutes(3));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(2)).isEmpty());
  }

  @Test
  void fetchLegacySendSchemaDoubleFilterTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(DfolSetupSendSchema.INSTANCE);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    Set<SendSchemaStepType> filter1 = Set.of(SendSchemaStepType.UDP, SendSchemaStepType.SAT);
    Set<SendSchemaStepType> filter2 = Set.of(SendSchemaStepType.UDP, SendSchemaStepType.SMS);
    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.DFOL_SETUP, SendSchemaStepFilter.of(filter1), SendSchemaStepFilter.of(filter2));

    verifyDfolSetupDefaults(sendSchema);
    VerificationUtil.verifySendSchemaStepUdp(sendSchema, 1, Duration.ofMinutes(3));
    VerificationUtil.verifySendSchemaStepWait(sendSchema, 2, Duration.ofDays(14));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(3)).isEmpty());
  }

  @Test
  void fetchLegacySendSchemaNoFilterTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(DfolSetupSendSchema.INSTANCE);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.DFOL_SETUP);

    verifyDfolSetupDefaults(sendSchema);
    VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
    VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
    VerificationUtil.verifySendSchemaStepSms(sendSchema, 3);
    VerificationUtil.verifySendSchemaStepSat(sendSchema, 4);
    VerificationUtil.verifySendSchemaStepSms(sendSchema, 5);
    VerificationUtil.verifySendSchemaStepSms(sendSchema, 6);
    VerificationUtil.verifySendSchemaStepWait(sendSchema, 7, Duration.ofDays(14));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(8)).isEmpty());
  }

  @Test
  void fetchLegacySendSchemaOnlySmsTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(DfolSetupSendSchema.INSTANCE);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    Set<SendSchemaStepType> smsFilter = Set.of(SendSchemaStepType.SMS);
    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.DFOL_SETUP, SendSchemaStepFilter.of(smsFilter));

    verifyDfolSetupDefaults(sendSchema);
    VerificationUtil.verifySendSchemaStepSms(sendSchema, 1, Duration.ofMinutes(65));
    VerificationUtil.verifySendSchemaStepSms(sendSchema, 2, Duration.ofMinutes(65));
    VerificationUtil.verifySendSchemaStepSms(sendSchema, 3, Duration.ofMinutes(65));
    VerificationUtil.verifySendSchemaStepWait(sendSchema, 4, Duration.ofDays(14));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
  }

  @Test
  void fetchLegacySendSchemaOnlyUdpTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(DfolSetupSendSchema.INSTANCE);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    Set<SendSchemaStepType> udpFilter = Set.of(SendSchemaStepType.UDP);
    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.DFOL_SETUP, SendSchemaStepFilter.of(udpFilter));

    verifyDfolSetupDefaults(sendSchema);
    VerificationUtil.verifySendSchemaStepUdp(sendSchema, 1, Duration.ofMinutes(3));
    VerificationUtil.verifySendSchemaStepWait(sendSchema, 2, Duration.ofDays(14));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(3)).isEmpty());
  }

  @Test
  void fetchLegacySendSchemaOnlyWifiTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(DfolSetupSendSchema.INSTANCE);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    Set<SendSchemaStepType> wifiFilter = Set.of(SendSchemaStepType.WIFI);
    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.DFOL_SETUP, SendSchemaStepFilter.of(wifiFilter));

    verifyDfolSetupDefaults(sendSchema);
    VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
    VerificationUtil.verifySendSchemaStepWait(sendSchema, 2, Duration.ofDays(14));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(3)).isEmpty());
  }

  @Test
  void fetchLegacySendSchemaUdpAndSatTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(DfolSetupSendSchema.INSTANCE);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    Set<SendSchemaStepType> smsFilter = Set.of(SendSchemaStepType.UDP, SendSchemaStepType.SAT);
    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.DFOL_SETUP, SendSchemaStepFilter.of(smsFilter));

    verifyDfolSetupDefaults(sendSchema);
    VerificationUtil.verifySendSchemaStepUdp(sendSchema, 1, Duration.ofMinutes(3));
    VerificationUtil.verifySendSchemaStepSat(sendSchema, 2, Duration.ofMinutes(60));
    VerificationUtil.verifySendSchemaStepWait(sendSchema, 3, Duration.ofDays(14));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(4)).isEmpty());
  }

  @Test
  void noWaitStepTest() {
    SendSchemaNameMappingFunction mappingFunction = mockMappingFunction(SatOnlySendSchema.INSTANCE);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = new FilterableSendSchemaFetcher(mappingFunction);

    Set<SendSchemaStepType> satFilter = Set.of(SendSchemaStepType.UDP, SendSchemaStepType.SAT);
    SendSchema sendSchema = filterableSendSchemaFetcher.fetch(SendSchemaName.SAT_ONLY, SendSchemaStepFilter.of(satFilter));

    Assertions.assertEquals(SendSchemaName.SAT_ONLY, sendSchema.getSendSchemaName());
    Assertions.assertEquals(Duration.ofHours(1), sendSchema.getGlobalTimeout());
    Assertions.assertEquals(1, sendSchema.getMaxRetryAttempts());
    VerificationUtil.verifySendSchemaStepSat(sendSchema, 1, Duration.ofMinutes(60));
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(2)).isEmpty());
  }
}
