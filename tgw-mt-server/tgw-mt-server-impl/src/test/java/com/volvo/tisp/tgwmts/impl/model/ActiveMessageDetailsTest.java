package com.volvo.tisp.tgwmts.impl.model;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static com.volvo.tisp.tgwmts.impl.util.TestUtil.*;

class ActiveMessageDetailsTest {
    private static final ActiveMessageDetails ACTIVE_MESSAGE_INFO_ITEM = TestUtil.createActiveMessageDetails();


    @Test
    void activeMtMessageId() {
        Assertions.assertSame(ACTIVE_MT_MESSAGE_ID.toLong(), ACTIVE_MESSAGE_INFO_ITEM.activeMtMessageId());
    }

    @Test
    void created() {
        Assertions.assertSame(INSTANT, ACTIVE_MESSAGE_INFO_ITEM.created());
    }

    @Test
    void updated() {
        Assertions.assertSame(INSTANT, ACTIVE_MESSAGE_INFO_ITEM.updated());
    }

    @Test
    void timeout() {
        Assertions.assertSame(INSTANT, ACTIVE_MESSAGE_INFO_ITEM.timeout());
    }

    @Test
    void retryAttempt() {
        Assertions.assertEquals(RETRY_ATTEMPT.toShort(), ACTIVE_MESSAGE_INFO_ITEM.retryAttempt());
    }

    @Test
    void sendSchemaStep() {
        Assertions.assertSame(SEND_SCHEMA_STEP_TYPE, ACTIVE_MESSAGE_INFO_ITEM.sendSchemaStep());
    }

}