package com.volvo.tisp.tgwmts.impl.schema.dynamic;

import java.time.Duration;
import java.util.Optional;
import java.util.function.BiConsumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.conf.properties.SendSchemaProperties;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.metric.reporter.SendSchemaMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;

class SendSchemaCalculatorTest {
  private static final Duration HIGH_SUMMED = Duration.ofMinutes(131);
  private static final Duration NORMAL_SUMMED = Duration.ofMinutes(71);
  private static final Duration ONE_DAY = Duration.ofDays(1);
  private static final SendSchemaNameParameterExtractor PARAMETER_EXTRACTOR = new SendSchemaNameParameterExtractor();
  private static final SendSchemaProperties SEND_SCHEMA_PROPERTIES = new SendSchemaProperties(Duration.ofDays(14));
  private static final Duration TWO_HOURS = Duration.ofHours(2);
  private static final Duration TWO_WEEKS = Duration.ofDays(14);

  private static void verifySendSchema(String sendSchemaName, BiConsumer<SendSchema, SendSchemaMetricReporter> consumer) {
    SendSchemaMetricReporter sendSchemaMetricReporter = Mockito.mock(SendSchemaMetricReporter.class);
    SendSchemaCalculator sendSchemaCalculator = new SendSchemaCalculator(sendSchemaMetricReporter, PARAMETER_EXTRACTOR, SEND_SCHEMA_PROPERTIES);

    SendSchema sendSchema = sendSchemaCalculator.calculate(new SendSchemaName(sendSchemaName));
    consumer.accept(sendSchema, sendSchemaMetricReporter);
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "ONE_MINUTE-LOW",
      "ONE_MINUTE-NORMAL",
      "ONE_MINUTE-HIGH",
      "TWO_MINUTE-LOW",
      "TWO_MINUTE-NORMAL",
      "TWO_MINUTE-HIGH",
      "FIVE_MINUTE-LOW",
      "FIVE_MINUTE-NORMAL",
      "FIVE_MINUTE-HIGH"
  })
  void calculateLowTtlTest(String sendSchemaName) {
    verifySendSchema(sendSchemaName, (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(3)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "ONE_DAY-HIGH",
      "ONE_DAY-EXTREME"
  })
  void calculateOndDayHighTest(String sendSchemaName) {
    verifySendSchema(sendSchemaName, (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepSms(sendSchema, 3);
      VerificationUtil.verifySendSchemaStepSat(sendSchema, 4);
      VerificationUtil.verifySendSchemaStepWait(sendSchema, 5, ONE_DAY.minus(HIGH_SUMMED));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @Test
  void calculateOneDayMidTest() {
    verifySendSchema("ONE_DAY-NORMAL", (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepSms(sendSchema, 3);
      VerificationUtil.verifySendSchemaStepWait(sendSchema, 4, ONE_DAY.minus(NORMAL_SUMMED));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "TEN_MINUTES-HIGH",
      "TEN_MINUTES-EXTREME"
  })
  void calculateTenMinutesHighTest(String sendSchemaName) {
    verifySendSchema(sendSchemaName, (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepSms(sendSchema, 3, Duration.ofMinutes(4));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(4)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @Test
  void calculateTwoHoursLowTest() {
    verifySendSchema("TWO_HOURS-LOW", (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepWait(sendSchema, 3, TWO_HOURS.minus(Duration.ofMinutes(6)));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(4)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @Test
  void calculateTwoHoursMidTest() {
    verifySendSchema("TWO_HOURS-NORMAL", (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepSms(sendSchema, 3);
      VerificationUtil.verifySendSchemaStepWait(sendSchema, 4, TWO_HOURS.minus(NORMAL_SUMMED));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @ParameterizedTest
  @ValueSource(strings = {
      "TWO_WEEKS-HIGH",
      "TWO_WEEKS-EXTREME"
  })
  void calculateTwoWeeksHighTest(String sendSchemaName) {
    verifySendSchema(sendSchemaName, (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepSms(sendSchema, 3);
      VerificationUtil.verifySendSchemaStepSat(sendSchema, 4);
      VerificationUtil.verifySendSchemaStepWait(sendSchema, 5, TWO_WEEKS.minus(HIGH_SUMMED));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @Test
  void calculateTwoWeeksMidTest() {
    verifySendSchema("TWO_WEEKS-NORMAL", (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepSms(sendSchema, 3);
      VerificationUtil.verifySendSchemaStepWait(sendSchema, 4, TWO_WEEKS.minus(NORMAL_SUMMED));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
      Mockito.verifyNoInteractions(sendSchemaMetricReporter);
    });
  }

  @Test
  void invalidNameTest() {
    SendSchemaMetricReporter sendSchemaMetricReporter = Mockito.mock(SendSchemaMetricReporter.class);
    SendSchemaName sendSchemaName = new SendSchemaName("INVALID-NAME");
    SendSchemaNameParameterExtractor sendSchemaNameParameterExtractor = Mockito.mock(SendSchemaNameParameterExtractor.class);
    Mockito.when(sendSchemaNameParameterExtractor.extract(sendSchemaName)).thenReturn(Optional.empty());
    SendSchemaCalculator sendSchemaCalculator = new SendSchemaCalculator(sendSchemaMetricReporter, sendSchemaNameParameterExtractor, SEND_SCHEMA_PROPERTIES);

    SendSchema sendSchema = sendSchemaCalculator.calculate(sendSchemaName);

    Assertions.assertEquals("COMMON_NORMAL", sendSchema.getSendSchemaName().toString());
    Mockito.verify(sendSchemaMetricReporter).onInvalidName();
    Mockito.verifyNoMoreInteractions(sendSchemaMetricReporter);
  }

  @Test
  void timeoutTooHighTest() {
    verifySendSchema("FIVE_WEEKS-HIGH", (sendSchema, sendSchemaMetricReporter) -> {
      VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
      VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
      VerificationUtil.verifySendSchemaStepSms(sendSchema, 3);
      VerificationUtil.verifySendSchemaStepSat(sendSchema, 4);
      VerificationUtil.verifySendSchemaStepWait(sendSchema, 5, TWO_WEEKS.minus(HIGH_SUMMED));
      Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
      Mockito.verify(sendSchemaMetricReporter).onTimeoutTooHigh();
      Mockito.verifyNoMoreInteractions(sendSchemaMetricReporter);
    });
  }

  @Test
  void timeoutTooLowTest() {
    SendSchemaMetricReporter sendSchemaMetricReporter = Mockito.mock(SendSchemaMetricReporter.class);
    SendSchemaName sendSchemaName = new SendSchemaName("TOO-LOW");
    SendSchemaNameParameterExtractor sendSchemaNameParameterExtractor = Mockito.mock(SendSchemaNameParameterExtractor.class);
    Mockito.when(sendSchemaNameParameterExtractor.extract(sendSchemaName))
        .thenReturn(Optional.of(new TtlAcceptedCostHolder(Duration.ofSeconds(10), AcceptedCost.NORMAL)));
    SendSchemaCalculator sendSchemaCalculator = new SendSchemaCalculator(sendSchemaMetricReporter, sendSchemaNameParameterExtractor, SEND_SCHEMA_PROPERTIES);

    SendSchema sendSchema = sendSchemaCalculator.calculate(sendSchemaName);

    VerificationUtil.verifySendSchemaStepWifi(sendSchema, 1);
    VerificationUtil.verifySendSchemaStepUdp(sendSchema, 2);
    Assertions.assertTrue(sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(3)).isEmpty());
    Mockito.verify(sendSchemaMetricReporter).onTimeoutTooLow();
    Mockito.verifyNoMoreInteractions(sendSchemaMetricReporter);
  }
}