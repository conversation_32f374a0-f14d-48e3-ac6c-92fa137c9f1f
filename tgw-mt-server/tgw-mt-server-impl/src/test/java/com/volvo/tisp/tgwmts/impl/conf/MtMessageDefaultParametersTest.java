package com.volvo.tisp.tgwmts.impl.conf;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageDefaultParametersTest {
  private static MtMessageDefaultParameters createMtMessageDefaultParameters() {
    return new MtMessageDefaultParameters(TestUtil.ENQUEUEING_TYPE, TestUtil.QUEUE_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void getEnqueueingTypeTest() {
    Assertions.assertSame(TestUtil.ENQUEUEING_TYPE, createMtMessageDefaultParameters().enqueueingType());
  }

  @Test
  void getQueueIdTest() {
    Assertions.assertSame(TestUtil.QUEUE_ID, createMtMessageDefaultParameters().queueId());
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(TestUtil.SEND_SCHEMA_NAME, createMtMessageDefaultParameters().sendSchemaName());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(
        () -> new MtMessageDefaultParameters(null, TestUtil.QUEUE_ID, TestUtil.SEND_SCHEMA_NAME), "enqueueingType must not be null");
    AssertThrows.illegalArgumentException(
        () -> new MtMessageDefaultParameters(TestUtil.ENQUEUEING_TYPE, null, TestUtil.SEND_SCHEMA_NAME), "queueId must not be null");
    AssertThrows.illegalArgumentException(
        () -> new MtMessageDefaultParameters(TestUtil.ENQUEUEING_TYPE, TestUtil.QUEUE_ID, null), "sendSchemaName must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "enqueueingType=NORMAL, queueId=queueId, sendSchemaName=COMMON_LOW";
    Assertions.assertEquals(expectedString, TestUtil.createMtMessageDefaultParameters().toString());
  }
}
