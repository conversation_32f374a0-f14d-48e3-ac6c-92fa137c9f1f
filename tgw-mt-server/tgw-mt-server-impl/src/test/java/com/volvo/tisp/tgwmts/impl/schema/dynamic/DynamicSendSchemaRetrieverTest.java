package com.volvo.tisp.tgwmts.impl.schema.dynamic;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;

class DynamicSendSchemaRetrieverTest {
  private static Cache<SendSchemaName, SendSchema> createCache(int maxSize) {
    return Caffeine.newBuilder().maximumSize(maxSize).build();
  }

  private static SendSchemaCalculator mockSendSchemaCalculator(List<SendSchemaName> names, List<SendSchema> schemas) {
    SendSchemaCalculator sendSchemaCalculator = Mockito.mock(SendSchemaCalculator.class);

    for (int i = 0; i < names.size(); i++) {
      Mockito.when(sendSchemaCalculator.calculate(names.get(i))).thenReturn(schemas.get(i));
    }

    return sendSchemaCalculator;
  }

  @Test
  void getSendSchemaTest() {
    Cache<SendSchemaName, SendSchema> cache = createCache(5);

    SendSchemaName name1 = new SendSchemaName("name-1");
    SendSchemaName name2 = new SendSchemaName("name-2");
    SendSchemaName name3 = new SendSchemaName("name-3");

    SendSchema schema1 = Mockito.mock(SendSchema.class);
    SendSchema schema2 = Mockito.mock(SendSchema.class);
    SendSchema schema3 = Mockito.mock(SendSchema.class);

    SendSchemaCalculator sendSchemaCalculator = mockSendSchemaCalculator(List.of(name1, name2, name3), List.of(schema1, schema2, schema3));
    DynamicSendSchemaRetriever dynamicSendSchemaRetriever = new DynamicSendSchemaRetriever(cache, sendSchemaCalculator);

    /* Add three unique */
    Assertions.assertEquals(0, cache.estimatedSize());
    Assertions.assertEquals(schema1, dynamicSendSchemaRetriever.getSendSchema(name1));
    Assertions.assertEquals(1, cache.estimatedSize());
    Assertions.assertEquals(schema2, dynamicSendSchemaRetriever.getSendSchema(name2));
    Assertions.assertEquals(2, cache.estimatedSize());
    Assertions.assertEquals(schema3, dynamicSendSchemaRetriever.getSendSchema(name3));
    Assertions.assertEquals(3, cache.estimatedSize());

    /* Add first schema again - verify still 3 in cache */
    Assertions.assertEquals(schema1, dynamicSendSchemaRetriever.getSendSchema(name1));
    Assertions.assertEquals(3, cache.estimatedSize());

    /* Verify only three calculations were made, despite 4 calls */
    Mockito.verify(sendSchemaCalculator, Mockito.times(3)).calculate(ArgumentMatchers.any(SendSchemaName.class));
  }

  @Test
  void maxSizeTest() {
    Cache<SendSchemaName, SendSchema> cache = createCache(1);

    SendSchemaName name1 = new SendSchemaName("name-1");
    SendSchemaName name2 = new SendSchemaName("name-2");

    SendSchema schema1 = Mockito.mock(SendSchema.class);
    SendSchema schema2 = Mockito.mock(SendSchema.class);

    SendSchemaCalculator sendSchemaCalculator = mockSendSchemaCalculator(List.of(name1, name2), List.of(schema1, schema2));
    DynamicSendSchemaRetriever dynamicSendSchemaRetriever = new DynamicSendSchemaRetriever(cache, sendSchemaCalculator);

    Assertions.assertEquals(0, cache.estimatedSize());

    Assertions.assertEquals(schema1, dynamicSendSchemaRetriever.getSendSchema(name1));
    Assertions.assertEquals(1, cache.estimatedSize());

    Assertions.assertEquals(schema2, dynamicSendSchemaRetriever.getSendSchema(name2));
    cache.cleanUp(); // force maintenance
    Assertions.assertEquals(1, cache.estimatedSize());

    Mockito.verify(sendSchemaCalculator, Mockito.times(2)).calculate(ArgumentMatchers.any(SendSchemaName.class));
  }
}