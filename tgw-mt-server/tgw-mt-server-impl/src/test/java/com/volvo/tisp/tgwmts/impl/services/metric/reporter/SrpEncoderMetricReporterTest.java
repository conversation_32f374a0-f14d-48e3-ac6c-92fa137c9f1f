package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class SrpEncoderMetricReporterTest {
  @Test
  void onMtSrp10ReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SrpEncoderMetricReporter::new, (meterRegistry, srpEncoderMetricReporter) -> {
      srpEncoderMetricReporter.onMtSrp10Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encode", Tags.of("srp-level", "srp10"), 1);

      srpEncoderMetricReporter.onMtSrp10Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encode", Tags.of("srp-level", "srp10"), 2);
    });
  }

  @Test
  void onMtSrp11ReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SrpEncoderMetricReporter::new, (meterRegistry, srpEncoderMetricReporter) -> {
      srpEncoderMetricReporter.onMtSrp11Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encode", Tags.of("srp-level", "srp11"), 1);

      srpEncoderMetricReporter.onMtSrp11Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encode", Tags.of("srp-level", "srp11"), 2);
    });
  }

  @Test
  void onMtSrp12ReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SrpEncoderMetricReporter::new, (meterRegistry, srpEncoderMetricReporter) -> {
      srpEncoderMetricReporter.onMtSrp12Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encode", Tags.of("srp-level", "srp12"), 1);

      srpEncoderMetricReporter.onMtSrp12Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encode", Tags.of("srp-level", "srp12"), 2);
    });
  }
}
