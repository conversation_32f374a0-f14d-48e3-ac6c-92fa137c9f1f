package com.volvo.tisp.tgwmts.impl.influx;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class ApplicationEventReporterTest {
  private static final String APPLICATION_EVENT = "app-event";
  private static final String EVENT = "type";

  @Test
  void onApplicationReadyEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onApplicationReadyEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "STARTED"), 1);

      mtStatusMetricReporter.onApplicationReadyEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "STARTED"), 2);
    });
  }

  @Test
  void onContextClosedTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onContextClosed();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "STOPPED"), 1);

      mtStatusMetricReporter.onContextClosed();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "STOPPED"), 2);
    });
  }

  @Test
  void onServiceActivatedEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onServiceActivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "ACTIVATED"), 1);

      mtStatusMetricReporter.onServiceActivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "ACTIVATED"), 2);
    });
  }

  @Test
  void onServiceDeactivatedEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(ApplicationEventReporter::new, (meterRegistry, mtStatusMetricReporter) -> {
      mtStatusMetricReporter.onServiceDeactivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "DEACTIVATED"), 1);

      mtStatusMetricReporter.onServiceDeactivatedEvent();
      MetricsReporterTestUtils.checkCounter(meterRegistry, APPLICATION_EVENT, Tags.of(EVENT, "DEACTIVATED"), 2);
    });
  }
}
