package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatusMessage;
import com.volvo.tisp.tgwmts.impl.model.TransportType;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.ReceivedMtStatusMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.services.statistics.MtStatusStatisticsHandler;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.vcss.client.protobuf.common.TransportTypeProtobuf;
import com.volvo.tisp.vc.vcss.client.protobuf.MtStatusStatisticProtobuf.MtStatusStatistic;

class ReceivedMtStatusMessageProcessorTest {
  private static void initAndVerify(ReceivedMtStatusMessage receivedMtStatusMessage, FinishedMtMessageHandler finishedMtMessageHandler,
      MtStatusStatisticsHandler mtStatusStatisticsHandler,
      BiConsumer<ActiveMtMessageWriter, JoinedActiveMtMessage> test) {
    TispContext.runInContext(() -> {
      JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
      ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Optional.of(joinedActiveMtMessage));
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
      Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
      Clock clock = mockClock();
      ReceivedMtStatusMessageMetricReporter receivedMtStatusMessageMetricReporter = Mockito.mock(ReceivedMtStatusMessageMetricReporter.class);
      SendSchemaProcessor sendSchemaProcessor = Mockito.mock(SendSchemaProcessor.class);

      ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor = new ReceivedMtStatusMessageProcessor(activeMtMessageWriterFactory, clock,
          finishedMtMessageHandler, loggingHelper, receivedMtStatusMessageMetricReporter, sendSchemaProcessor, mtStatusStatisticsHandler);

      receivedMtStatusMessageProcessor.processMtStatusMessage(receivedMtStatusMessage);

      test.accept(activeMtMessageWriter, joinedActiveMtMessage);

      verifyDatabaseApiInteractions(activeMtMessageWriter, activeMtMessageWriterFactory);
      Mockito.verify(receivedMtStatusMessageMetricReporter).onFindActiveMtMessageWithVpiLock(Duration.ofSeconds(3));
      Mockito.verify(receivedMtStatusMessageMetricReporter).onReceivedMtStatus(receivedMtStatusMessage.receivedMtStatus(), Duration.ofSeconds(4));
      Mockito.verify(loggingHelper).accept(ArgumentMatchers.any(IntegrationLogParameter.class));
      Mockito.verify(clock, Mockito.times(4)).instant();
      Mockito.verifyNoMoreInteractions(activeMtMessageWriterFactory, activeMtMessageWriter, clock);
      Mockito.verify(receivedMtStatusMessageMetricReporter).onMtStatusStatisticTransportType(TransportTypeProtobuf.TransportType.UDP);
      Mockito.verifyNoInteractions(sendSchemaProcessor);
    });
  }

  private static ActiveMtMessageWriter mockActiveMtMessageWriter(Optional<JoinedActiveMtMessage> optional) {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(activeMtMessageWriter.findActiveMtMessageWithVpiLock(TestUtil.ACTIVE_MT_MESSAGE_ID)).thenReturn(optional);
    return activeMtMessageWriter;
  }

  private static ActiveMtMessageWriterFactory mockActiveMtMessageWriterFactory(ActiveMtMessageWriter activeMtMessageWriter) {
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = Mockito.mock(ActiveMtMessageWriterFactory.class);
    Mockito.when(activeMtMessageWriterFactory.createReadCommitted()).thenReturn(activeMtMessageWriter);
    return activeMtMessageWriterFactory;
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(2)).thenReturn(Instant.ofEpochSecond(5)).thenReturn(Instant.ofEpochSecond(6));
    return clock;
  }

  private static Consumer<IntegrationLogParameter> mockLoggingHelper() {
    return Mockito.mock(Consumer.class);
  }

  private static void verifyDatabaseApiInteractions(ActiveMtMessageWriter activeMtMessageWriter, ActiveMtMessageWriterFactory activeMtMessageWriterFactory) {
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).findActiveMtMessageWithVpiLock(TestUtil.ACTIVE_MT_MESSAGE_ID);
    Mockito.verify(activeMtMessageWriter).commitTransaction();
    Mockito.verify(activeMtMessageWriter).close();
  }

  private static void verifyStatisticsPublishing(TransportType transportType, Consumer<ReceivedMtStatusMessageMetricReporter> consumer) {
    TispContext.runInContext(() -> {
      ReceivedMtStatusMessage message = TestUtil.createReceivedMtStatusMessage(transportType);
      JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
      FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
      ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Optional.of(joinedActiveMtMessage));
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
      Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
      Clock clock = mockClock();
      ReceivedMtStatusMessageMetricReporter receivedMtStatusMessageMetricReporter = Mockito.mock(ReceivedMtStatusMessageMetricReporter.class);
      SendSchemaProcessor sendSchemaProcessor = Mockito.mock(SendSchemaProcessor.class);
      MtStatusStatisticsHandler mtStatusStatisticsHandler = Mockito.mock(MtStatusStatisticsHandler.class);

      ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor = new ReceivedMtStatusMessageProcessor(activeMtMessageWriterFactory, clock,
          finishedMtMessageHandler, loggingHelper, receivedMtStatusMessageMetricReporter, sendSchemaProcessor, mtStatusStatisticsHandler);

      receivedMtStatusMessageProcessor.processMtStatusMessage(message);
      Mockito.verify(receivedMtStatusMessageMetricReporter).onFindActiveMtMessageWithVpiLock(Duration.ofSeconds(3));
      Mockito.verify(receivedMtStatusMessageMetricReporter).onReceivedMtStatus(message.receivedMtStatus(), Duration.ofSeconds(4));
      consumer.accept(receivedMtStatusMessageMetricReporter);
    });
  }

  @Test
  void failedToProcessTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Optional.empty());
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = mockClock();
    Mockito.when(clock.instant()).thenThrow(new RuntimeException("test"));
    FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    ReceivedMtStatusMessageMetricReporter receivedMtStatusMessageMetricReporter = Mockito.mock(ReceivedMtStatusMessageMetricReporter.class);
    SendSchemaProcessor sendSchemaProcessor = Mockito.mock(SendSchemaProcessor.class);
    MtStatusStatisticsHandler mtStatusStatisticsHandler = Mockito.mock(MtStatusStatisticsHandler.class);

    ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor = new ReceivedMtStatusMessageProcessor(activeMtMessageWriterFactory, clock,
        finishedMtMessageHandler, loggingHelper, receivedMtStatusMessageMetricReporter, sendSchemaProcessor, mtStatusStatisticsHandler);

    receivedMtStatusMessageProcessor.processMtStatusMessage(TestUtil.createReceivedMtStatusMessage(ReceivedMtStatus.DELIVERED));

    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(clock).instant();
    Mockito.verify(receivedMtStatusMessageMetricReporter).onFailedToProcess();
    Mockito.verifyNoMoreInteractions(activeMtMessageWriterFactory, activeMtMessageWriter, receivedMtStatusMessageMetricReporter);
    Mockito.verifyNoInteractions(finishedMtMessageHandler, sendSchemaProcessor, mtStatusStatisticsHandler);
  }

  @Test
  void mtStatusStatisticsPublishingTest() {
    verifyStatisticsPublishing(TransportType.UDP,
        reporter -> Mockito.verify(reporter).onMtStatusStatisticTransportType(TransportTypeProtobuf.TransportType.UDP));
    verifyStatisticsPublishing(TransportType.SMS,
        reporter -> Mockito.verify(reporter).onMtStatusStatisticTransportType(TransportTypeProtobuf.TransportType.SMS));
    verifyStatisticsPublishing(TransportType.SAT,
        reporter -> Mockito.verify(reporter).onMtStatusStatisticTransportType(TransportTypeProtobuf.TransportType.SATELLITE));
    verifyStatisticsPublishing(TransportType.WIFI,
        reporter -> Mockito.verify(reporter).onMtStatusStatisticTransportType(TransportTypeProtobuf.TransportType.WIFI));
    verifyStatisticsPublishing(TransportType.SOFTCAR, Mockito::verifyNoMoreInteractions);
    verifyStatisticsPublishing(TransportType.UNKNOWN, reporter -> Mockito.verify(reporter).onUnknownTransportType());
  }

  @Test
  void notFoundTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Optional.empty());
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = mockClock();
    FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    ReceivedMtStatusMessageMetricReporter receivedMtStatusMessageMetricReporter = Mockito.mock(ReceivedMtStatusMessageMetricReporter.class);
    SendSchemaProcessor sendSchemaProcessor = Mockito.mock(SendSchemaProcessor.class);
    MtStatusStatisticsHandler mtStatusStatisticsHandler = Mockito.mock(MtStatusStatisticsHandler.class);

    ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor = new ReceivedMtStatusMessageProcessor(activeMtMessageWriterFactory, clock,
        finishedMtMessageHandler, loggingHelper, receivedMtStatusMessageMetricReporter, sendSchemaProcessor, mtStatusStatisticsHandler);

    receivedMtStatusMessageProcessor.processMtStatusMessage(TestUtil.createReceivedMtStatusMessage(ReceivedMtStatus.DELIVERED));

    verifyDatabaseApiInteractions(activeMtMessageWriter, activeMtMessageWriterFactory);
    Mockito.verify(receivedMtStatusMessageMetricReporter).onFindActiveMtMessageWithVpiLock(Duration.ofSeconds(3));
    Mockito.verify(receivedMtStatusMessageMetricReporter).onNotFound();
    Mockito.verifyNoMoreInteractions(activeMtMessageWriterFactory, activeMtMessageWriter, receivedMtStatusMessageMetricReporter);
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verifyNoInteractions(finishedMtMessageHandler, sendSchemaProcessor, mtStatusStatisticsHandler);
  }

  @Test
  void processMtStatusMessageCancelledTest() {
    FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Optional.of(joinedActiveMtMessage));
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    UpdateActiveMtMessageParameter updateActiveMtMessageParameter = TestUtil.createUpdateActiveMtMessageParameter();
    Clock clock = mockClock();
    ReceivedMtStatusMessageMetricReporter receivedMtStatusMessageMetricReporter = Mockito.mock(ReceivedMtStatusMessageMetricReporter.class);
    SendSchemaProcessor sendSchemaProcessor = Mockito.mock(SendSchemaProcessor.class);
    Mockito.when(sendSchemaProcessor.executeNextSendSchemaStep(activeMtMessageWriter, joinedActiveMtMessage))
        .thenReturn(Optional.of(updateActiveMtMessageParameter));
    MtStatusStatisticsHandler mtStatusStatisticsHandler = Mockito.mock(MtStatusStatisticsHandler.class);

    ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor = new ReceivedMtStatusMessageProcessor(activeMtMessageWriterFactory, clock,
        finishedMtMessageHandler, loggingHelper, receivedMtStatusMessageMetricReporter, sendSchemaProcessor, mtStatusStatisticsHandler);

    ReceivedMtStatusMessage receivedMtStatusMessage = TestUtil.createReceivedMtStatusMessage(ReceivedMtStatus.CANCELED);
    TispContext.runInContext(() -> receivedMtStatusMessageProcessor.processMtStatusMessage(receivedMtStatusMessage));

    Mockito.verify(sendSchemaProcessor).executeNextSendSchemaStep(activeMtMessageWriter, joinedActiveMtMessage);
    verifyDatabaseApiInteractions(activeMtMessageWriter, activeMtMessageWriterFactory);
    Mockito.verify(activeMtMessageWriter).updateActiveMtMessages(List.of(updateActiveMtMessageParameter));
    Mockito.verify(clock, Mockito.times(4)).instant();
    Mockito.verify(receivedMtStatusMessageMetricReporter).onFindActiveMtMessageWithVpiLock(Duration.ofSeconds(3));
    Mockito.verify(receivedMtStatusMessageMetricReporter).onReceivedMtStatus(receivedMtStatusMessage.receivedMtStatus(), Duration.ofSeconds(4));
    Mockito.verify(receivedMtStatusMessageMetricReporter).onCanceled();
    Mockito.verify(receivedMtStatusMessageMetricReporter).onMtStatusStatisticTransportType(TransportTypeProtobuf.TransportType.UDP);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriterFactory, activeMtMessageWriter, clock, receivedMtStatusMessageMetricReporter, sendSchemaProcessor);
  }

  @Test
  void processMtStatusMessageDeliveredTest() {
    FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
    MtStatusStatisticsHandler mtStatusStatisticsHandler = Mockito.mock(MtStatusStatisticsHandler.class);
    initAndVerify(TestUtil.createReceivedMtStatusMessage(ReceivedMtStatus.DELIVERED), finishedMtMessageHandler, mtStatusStatisticsHandler,
        (activeMtMessageWriter, joinedActiveMtMessage) -> {
          Mockito.verify(finishedMtMessageHandler).onDelivered(activeMtMessageWriter, joinedActiveMtMessage);
          Mockito.verify(mtStatusStatisticsHandler).handle(Mockito.any(MtStatusStatistic.class));
          Mockito.verifyNoMoreInteractions(finishedMtMessageHandler, mtStatusStatisticsHandler);
        });
  }

  @Test
  void processMtStatusMessageRejectedTest() {
    FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
    MtStatusStatisticsHandler mtStatusStatisticsHandler = Mockito.mock(MtStatusStatisticsHandler.class);
    initAndVerify(TestUtil.createReceivedMtStatusMessage(ReceivedMtStatus.REJECTED), finishedMtMessageHandler, mtStatusStatisticsHandler,
        (activeMtMessageWriter, joinedActiveMtMessage) -> {
          Mockito.verify(finishedMtMessageHandler).onRejected(activeMtMessageWriter, joinedActiveMtMessage);
          Mockito.verify(mtStatusStatisticsHandler).handle(Mockito.any(MtStatusStatistic.class));
          Mockito.verifyNoMoreInteractions(finishedMtMessageHandler, mtStatusStatisticsHandler);
        });
  }

  @Test
  void processMtStatusMessageThrottledTest() {
    FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
    MtStatusStatisticsHandler mtStatusStatisticsHandler = Mockito.mock(MtStatusStatisticsHandler.class);
    initAndVerify(TestUtil.createReceivedMtStatusMessage(ReceivedMtStatus.THROTTLED), finishedMtMessageHandler, mtStatusStatisticsHandler,
        (activeMtMessageWriter, joinedActiveMtMessage) -> {
          Mockito.verify(finishedMtMessageHandler).onThrottled(activeMtMessageWriter, joinedActiveMtMessage);
          Mockito.verify(mtStatusStatisticsHandler).handle(Mockito.any(MtStatusStatistic.class));
          Mockito.verifyNoMoreInteractions(finishedMtMessageHandler, mtStatusStatisticsHandler);
        });
  }
}
