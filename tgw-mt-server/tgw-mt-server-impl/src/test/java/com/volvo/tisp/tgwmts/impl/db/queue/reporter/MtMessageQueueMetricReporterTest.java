package com.volvo.tisp.tgwmts.impl.db.queue.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Tags;

class MtMessageQueueMetricReporterTest {
  @Test
  void activeMtDbQueueSizeTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageQueueMetricReporter::new, (meterRegistry, mtMessageQueueMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageQueueMetricReporter.activeMtDbQueueSize(-1), "queueSize must not be negative: -1");

      mtMessageQueueMetricReporter.activeMtDbQueueSize(1);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "db.queue.size", Tags.of("table", "ACTIVE-MT-MESSAGE"), 1);

      mtMessageQueueMetricReporter.activeMtDbQueueSize(20);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "db.queue.size", Tags.of("table", "ACTIVE-MT-MESSAGE"), 20);
    });
  }

  @Test
  void mtDbQueueSizeTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageQueueMetricReporter::new, (meterRegistry, mtMessageQueueMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageQueueMetricReporter.mtDbQueueSize(-1), "queueSize must not be negative: -1");

      mtMessageQueueMetricReporter.mtDbQueueSize(1);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "db.queue.size", Tags.of("table", "MT-MESSAGE"), 1);

      mtMessageQueueMetricReporter.mtDbQueueSize(20);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "db.queue.size", Tags.of("table", "MT-MESSAGE"), 20);
    });
  }
}
