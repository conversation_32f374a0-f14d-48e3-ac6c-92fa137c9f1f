package com.volvo.tisp.tgwmts.impl.model;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import static com.volvo.tisp.tgwmts.impl.util.TestUtil.*;

class MtMessageInformationTest {
    private static final MtMessageInformation MT_MESSAGES_INFO = TestUtil.createMtMessagesInfo();


    @Test
    void vpi() {
        Assertions.assertEquals(VPI.toString(), MT_MESSAGES_INFO.vpi());
    }

    @Test
    void mtMessages() {
        Assertions.assertEquals(createMtMessageDetails(), MT_MESSAGES_INFO.mtMessageDetails().get(0));
    }
}