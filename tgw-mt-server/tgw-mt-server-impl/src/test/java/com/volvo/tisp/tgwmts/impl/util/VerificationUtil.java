package com.volvo.tisp.tgwmts.impl.util;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;

public final class VerificationUtil {
  private VerificationUtil() {
    throw new IllegalStateException();
  }

  public static void verifySendSchemaStep(SendSchema sendSchema, int currentStep, SendSchemaStepType sendSchemaStepType, Duration expectedWaitDuration) {
    SendSchemaStepId expectedSendSchemaStepId = SendSchemaStepId.ofInt(currentStep);
    SendSchemaStep sendSchemaStep = sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(currentStep)).orElseThrow();

    Assertions.assertEquals(expectedSendSchemaStepId, sendSchemaStep.getSendSchemaStepId());
    Assertions.assertEquals(sendSchemaStepType, sendSchemaStep.getSendSchemaStepType());
    Assertions.assertEquals(expectedWaitDuration, sendSchemaStep.getWaitDuration());
  }

  public static void verifySendSchemaStepSat(SendSchema sendSchema, int currentStep, Duration expectedWaitDuration) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.SAT, expectedWaitDuration);
  }

  public static void verifySendSchemaStepSat(SendSchema sendSchema, int currentStep) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.SAT, SendSchemaStep.DEFAULT_SAT_TIMEOUT);
  }

  public static void verifySendSchemaStepSms(SendSchema sendSchema, int currentStep, Duration expectedWaitDuration) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.SMS, expectedWaitDuration);
  }

  public static void verifySendSchemaStepSms(SendSchema sendSchema, int currentStep) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.SMS, SendSchemaStep.DEFAULT_SMS_TIMEOUT);
  }

  public static void verifySendSchemaStepUdp(SendSchema sendSchema, int currentStep, Duration expectedWaitDuration) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.UDP, expectedWaitDuration);
  }

  public static void verifySendSchemaStepUdp(SendSchema sendSchema, int currentStep) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.UDP, SendSchemaStep.DEFAULT_UDP_TIMEOUT);
  }

  public static void verifySendSchemaStepWait(SendSchema sendSchema, int currentStep, Duration expectedWaitDuration) {
    SendSchemaStepId expectedSendSchemaStepId = SendSchemaStepId.ofInt(currentStep);
    SendSchemaStep sendSchemaStep = sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(currentStep)).orElseThrow();

    Assertions.assertEquals(expectedSendSchemaStepId, sendSchemaStep.getSendSchemaStepId());
    Assertions.assertEquals(SendSchemaStepType.WAIT, sendSchemaStep.getSendSchemaStepType());
    Assertions.assertEquals(expectedWaitDuration, sendSchemaStep.getWaitDuration());
  }

  public static void verifySendSchemaStepWifi(SendSchema sendSchema, int currentStep) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.WIFI, SendSchemaStep.DEFAULT_WIFI_TIMEOUT);
  }

  public static void verifySendSchemaStepWifi(SendSchema sendSchema, int currentStep, Duration expectedWaitDuration) {
    verifySendSchemaStep(sendSchema, currentStep, SendSchemaStepType.WIFI, expectedWaitDuration);
  }
}
