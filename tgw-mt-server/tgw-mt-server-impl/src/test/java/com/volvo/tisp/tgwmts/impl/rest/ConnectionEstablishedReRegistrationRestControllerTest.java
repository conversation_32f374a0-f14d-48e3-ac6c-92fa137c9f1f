package com.volvo.tisp.tgwmts.impl.rest;

import static com.volvo.tisp.tgwmts.impl.rest.ConnectionEstablishedEventRestControllerTest.verifyResponseEntity;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.volvo.tisp.tgwmts.impl.services.ConnectionEstablishedRegistrationRetryService;

class ConnectionEstablishedReRegistrationRestControllerTest {

  @Test
  void postConnectionEstablishedRegistrationResendFailedTest() {
    ConnectionEstablishedRegistrationRetryService connectionEstablishedRegistrationRetryService = Mockito.mock(
        ConnectionEstablishedRegistrationRetryService.class);
    ConnectionEstablishedReRegistrationRestController connectionEstablishedReRegistrationRestController = new ConnectionEstablishedReRegistrationRestController(
        connectionEstablishedRegistrationRetryService);

    Mockito.doThrow(new RuntimeException()).when(connectionEstablishedRegistrationRetryService).resendConnectionEstablishedRegistrations();

    ResponseEntity<String> responseEntity = connectionEstablishedReRegistrationRestController.attemptResend();

    verifyResponseEntity(responseEntity, HttpStatus.INTERNAL_SERVER_ERROR, "Failed to resend connection established registrations");
    Mockito.verify(connectionEstablishedRegistrationRetryService).resendConnectionEstablishedRegistrations();
    Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationRetryService);
  }

  @Test
  void postConnectionEstablishedRegistrationResendTest() {
    ConnectionEstablishedRegistrationRetryService connectionEstablishedRegistrationRetryService = Mockito.mock(
        ConnectionEstablishedRegistrationRetryService.class);
    ConnectionEstablishedReRegistrationRestController connectionEstablishedReRegistrationRestController = new ConnectionEstablishedReRegistrationRestController(
        connectionEstablishedRegistrationRetryService);

    ResponseEntity<String> responseEntity = connectionEstablishedReRegistrationRestController.attemptResend();

    verifyResponseEntity(responseEntity, HttpStatus.OK, "Attempting to resend connection established registrations");
    Mockito.verify(connectionEstablishedRegistrationRetryService).resendConnectionEstablishedRegistrations();
    Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationRetryService);
  }
}
