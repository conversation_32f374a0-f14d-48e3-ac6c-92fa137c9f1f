package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class FinishedMtMessageHandlerMetricReporterTest {
  private static final String TYPE = "TYPE";

  @Test
  void onFailedActiveMtMessageInsertionTest() {
    MetricsReporterTestUtils.initReporterAndTest(FinishedMtMessageHandlerMetricReporter::new, (meterRegistry, finishedMtMessageHandlerMetricReporter) -> {
      finishedMtMessageHandlerMetricReporter.onFailedActiveMtMessageInsertion();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "insertion.failure", Tags.of(TYPE, "ACTIVE_MT_MESSAGE"), 1);

      finishedMtMessageHandlerMetricReporter.onFailedActiveMtMessageInsertion();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "insertion.failure", Tags.of(TYPE, "ACTIVE_MT_MESSAGE"), 2);
    });
  }

  @Test
  void onFailedMtMessageDeletionTest() {
    MetricsReporterTestUtils.initReporterAndTest(FinishedMtMessageHandlerMetricReporter::new, (meterRegistry, finishedMtMessageHandlerMetricReporter) -> {
      finishedMtMessageHandlerMetricReporter.onFailedMtMessageDeletion();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "deletion.failure", Tags.of(TYPE, "MT_MESSAGE"), 1);

      finishedMtMessageHandlerMetricReporter.onFailedMtMessageDeletion();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "deletion.failure", Tags.of(TYPE, "MT_MESSAGE"), 2);
    });
  }

  @Test
  void onFailedVehicleLockDeletionTest() {
    MetricsReporterTestUtils.initReporterAndTest(FinishedMtMessageHandlerMetricReporter::new, (meterRegistry, finishedMtMessageHandlerMetricReporter) -> {
      finishedMtMessageHandlerMetricReporter.onFailedVehicleLockDeletion();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "deletion.failure", Tags.of(TYPE, "VEHICLE_LOCK"), 1);

      finishedMtMessageHandlerMetricReporter.onFailedVehicleLockDeletion();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "deletion.failure", Tags.of(TYPE, "VEHICLE_LOCK"), 2);
    });
  }
}
