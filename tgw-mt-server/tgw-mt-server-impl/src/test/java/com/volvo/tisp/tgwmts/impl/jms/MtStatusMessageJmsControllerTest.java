package com.volvo.tisp.tgwmts.impl.jms;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.connectivity.proto.MtStatus;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.tgwmts.impl.converters.MtStatusMessageInputConverterFunction;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatusMessage;
import com.volvo.tisp.tgwmts.impl.services.ReceivedMtStatusMessageProcessor;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtStatusMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtStatusMessageJmsControllerTest {
  @Test
  void failingReceiveMtStatusMessageTest() {
    ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor = Mockito.mock(ReceivedMtStatusMessageProcessor.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    MtStatusMessageJmsController mtStatusMessageJmsController = new MtStatusMessageJmsController(mtStatusMetricReporter, receivedMtStatusMessageProcessor,
        MtStatusMessageInputConverterFunction.INSTANCE);
    JmsMessage<MtStatus> jmsMessage = TestUtil.mockJmsMessage(TestUtil.createMtStatusBuilder().setMessageId("invalid").build());

    mtStatusMessageJmsController.receiveMtStatus(jmsMessage);

    Mockito.verify(mtStatusMetricReporter).onInvalidMtStatus();
    Mockito.verifyNoInteractions(receivedMtStatusMessageProcessor);
    Mockito.verifyNoMoreInteractions(mtStatusMetricReporter);
  }

  @Test
  void receiveTgwMtStatusMessageNullParameterTest() {
    MtStatusMessageJmsController mtStatusMessageJmsController = new MtStatusMessageJmsController(Mockito.mock(MtStatusMetricReporter.class),
        Mockito.mock(ReceivedMtStatusMessageProcessor.class), MtStatusMessageInputConverterFunction.INSTANCE);

    AssertThrows.illegalArgumentException(() -> mtStatusMessageJmsController.receiveMtStatus(null), "jmsMessage must not be null");
  }

  @Test
  void receiveTgwMtStatusMessageTest() {
    ReceivedMtStatusMessage receivedMtStatusMessage = TestUtil.createReceivedMtStatusMessage(ReceivedMtStatus.DELIVERED);
    ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor = Mockito.mock(ReceivedMtStatusMessageProcessor.class);
    MtStatusMetricReporter mtStatusMetricReporter = Mockito.mock(MtStatusMetricReporter.class);
    MtStatusMessageJmsController mtStatusMessageJmsController = new MtStatusMessageJmsController(mtStatusMetricReporter,
        receivedMtStatusMessageProcessor, MtStatusMessageInputConverterFunction.INSTANCE);
    JmsMessage<MtStatus> jmsMessage = TestUtil.mockJmsMessage(TestUtil.createMtStatusMessage());

    mtStatusMessageJmsController.receiveMtStatus(jmsMessage);

    Mockito.verify(receivedMtStatusMessageProcessor).processMtStatusMessage(receivedMtStatusMessage);
    Mockito.verifyNoInteractions(mtStatusMetricReporter);
    Mockito.verifyNoMoreInteractions(receivedMtStatusMessageProcessor);
  }
}
