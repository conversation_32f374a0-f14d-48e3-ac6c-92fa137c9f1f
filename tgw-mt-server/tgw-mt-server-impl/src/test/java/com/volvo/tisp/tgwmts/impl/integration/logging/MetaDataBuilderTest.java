package com.volvo.tisp.tgwmts.impl.integration.logging;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MetaDataBuilderTest {
  @Test
  void build() {
    MetaDataBuilder metaDataBuilder = TestUtil.createMetaDataBuilder();
    Assertions.assertEquals(metaDataBuilder.getMap(), metaDataBuilder.build().getMapAsOptional().orElseThrow());
  }

  @Test
  void setMobileDirection() {
    MetaDataBuilder metaDataBuilder = new MetaDataBuilder();
    AssertThrows.illegalArgumentException(() -> metaDataBuilder.setMobileDirection(null), "mobileDirection must not be null");

    metaDataBuilder.setMobileDirection("TestDir");
    Assertions.assertEquals("TestDir", metaDataBuilder.getMap().get(MetaDataKey.MOBILE_DIRECTION.toString()));
  }

  @Test
  void setSendSchemaName() {
    MetaDataBuilder metaDataBuilder = new MetaDataBuilder();
    AssertThrows.illegalArgumentException(() -> metaDataBuilder.setSendSchemaName(null), "sendSchemaName must not be null");

    metaDataBuilder.setSendSchemaName(SendSchemaName.COMMON_HIGH);
    Assertions.assertEquals("COMMON_HIGH", metaDataBuilder.getMap().get(MetaDataKey.SCHEDULE_NAME.toString()));
  }

  @Test
  void setSendSchemaStepType() {
    MetaDataBuilder metaDataBuilder = new MetaDataBuilder();
    AssertThrows.illegalArgumentException(() -> metaDataBuilder.setSendSchemaStepType(null), "sendSchemaStepType must not be null");

    metaDataBuilder.setSendSchemaStepType(SendSchemaStepType.WAIT);
    Assertions.assertEquals("WAIT", metaDataBuilder.getMap().get(MetaDataKey.STACK_NAME.toString()));
  }

  @Test
  void setSrpDestinationService() {
    MetaDataBuilder metaDataBuilder = new MetaDataBuilder();
    AssertThrows.illegalArgumentException(() -> metaDataBuilder.setSrpDestinationService(null), "srpDestinationService must not be null");

    metaDataBuilder.setSrpDestinationService(TestUtil.SRP_DESTINATION_SERVICE);
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_SERVICE.toString(), metaDataBuilder.getMap().get(MetaDataKey.SERVICE_ID.toString()));
  }

  @Test
  void setSrpDestinationVersion() {
    MetaDataBuilder metaDataBuilder = new MetaDataBuilder();
    AssertThrows.illegalArgumentException(() -> metaDataBuilder.setSrpDestinationVersion(null), "srpDestinationVersion must not be null");

    metaDataBuilder.setSrpDestinationVersion(TestUtil.SRP_DESTINATION_VERSION);
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_VERSION.toString(), metaDataBuilder.getMap().get(MetaDataKey.SERVICE_VERSION.toString()));
  }
}
