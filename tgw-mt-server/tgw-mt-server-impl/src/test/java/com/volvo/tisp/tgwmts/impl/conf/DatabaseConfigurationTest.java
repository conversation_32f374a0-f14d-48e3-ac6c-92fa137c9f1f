package com.volvo.tisp.tgwmts.impl.conf;

import java.time.Clock;
import java.time.Duration;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class DatabaseConfigurationTest {
  private static final Duration LOCK_TIMEOUT = Duration.ofSeconds(42);

  private static DatasourceConfigProperties mockDatasourceConfigProperties() {
    return new DatasourceConfigProperties("foo", "postgres", "bar", 2, 1, Duration.ofSeconds(2));
  }

  private static <T> RowMapper<T> mockRowMapper() {
    return Mockito.mock(RowMapper.class);
  }

  @Test
  void createActiveMtMessageReaderFactoryTest() {
    Assertions
        .assertNotNull(
            new DatabaseConfiguration().createActiveMtMessageReaderFactory(Mockito.mock(Clock.class), Mockito.mock(Jdbi.class), mockRowMapper(),
                mockRowMapper(), LOCK_TIMEOUT));
  }

  @Test
  void createActiveMtMessageWriterFactoryTest() {
    Assertions
        .assertNotNull(
            new DatabaseConfiguration().createActiveMtMessageWriterFactory(Mockito.mock(Clock.class), Mockito.mock(Jdbi.class), mockRowMapper(),
                mockRowMapper(), LOCK_TIMEOUT));
  }

  @Test
  void createDataSourceTest() {
    Assertions.assertNotNull(new DatabaseConfiguration().createDataSource(mockDatasourceConfigProperties()));
  }

  @Test
  void createDatabaseLockTimeoutTest() {
    Assertions.assertNotNull(new DatabaseConfiguration().createDatabaseLockTimeout(Duration.ofSeconds(2)));
  }

  @Test
  void createJdbiTest() {
    Assertions.assertNotNull(new DatabaseConfiguration().createJdbi(Mockito.mock(DataSource.class)));
  }

  @Test
  void createJoinedActiveMtMessageRowMapperTest() {
    Assertions.assertNotNull(new DatabaseConfiguration().createJoinedActiveMtMessageRowMapper());
  }
}
