package com.volvo.tisp.tgwmts.impl.schema.dynamic;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SendSchemaNameParameterExtractorTest {
  private static final Duration ONE_HOUR = Duration.ofHours(1);

  private static void verify(String string, Duration duration, AcceptedCost acceptedCost) {
    Assertions.assertEquals(new TtlAcceptedCostHolder(duration, acceptedCost),
        new SendSchemaNameParameterExtractor().extract(new SendSchemaName(string)).orElseThrow());
  }

  private static void verifyIsEmpty(String string) {
    Assertions.assertTrue(new SendSchemaNameParameterExtractor().extract(new SendSchemaName(string)).isEmpty());
  }

  @Test
  void convertTest() {
    verify("ONE_MINUTE-HIGH", Duration.ofMinutes(1), AcceptedCost.HIGH);
    verify("TWO_MINUTES-NORMAL", Duration.ofMinutes(2), AcceptedCost.NORMAL);
    verify("ONE_HOUR-NORMAL", ONE_HOUR, AcceptedCost.NORMAL);
    verify("TWO_HOURS-LOW", Duration.ofHours(2), AcceptedCost.LOW);
    verify("ONE_DAY-NORMAL", Duration.ofDays(1), AcceptedCost.NORMAL);
    verify("TWO_DAYS-HIGH", Duration.ofDays(2), AcceptedCost.HIGH);
    verify("ONE_WEEK-LOW", Duration.ofDays(7), AcceptedCost.LOW);
    verify("TWO_WEEKS-HIGH", Duration.ofDays(14), AcceptedCost.HIGH);
    verify("THREE_WEEKS-NORMAL", Duration.ofDays(21), AcceptedCost.NORMAL);

    verify("ONE_MINUTE-LOW", Duration.ofMinutes(1), AcceptedCost.LOW);
    verify("TWO_MINUTE-LOW", Duration.ofMinutes(2), AcceptedCost.LOW);
    verify("THREE_MINUTE-LOW", Duration.ofMinutes(3), AcceptedCost.LOW);
    verify("FOUR_MINUTE-LOW", Duration.ofMinutes(4), AcceptedCost.LOW);
    verify("FIVE_MINUTE-LOW", Duration.ofMinutes(5), AcceptedCost.LOW);
    verify("TEN_MINUTE-LOW", Duration.ofMinutes(10), AcceptedCost.LOW);
    verify("TEN_MINUTES-LOW", Duration.ofMinutes(10), AcceptedCost.LOW);
    verify("THIRTY_MINUTE-LOW", Duration.ofMinutes(30), AcceptedCost.LOW);

    verify("TWELVE_MINUTES-HIGH", Duration.ofMinutes(12), AcceptedCost.HIGH);
    verify("TWELVE_HOURS-HIGH", Duration.ofHours(12), AcceptedCost.HIGH);
    verify("ELEVEN_DAYS-HIGH", Duration.ofDays(11), AcceptedCost.HIGH);
    verify("SIX_DAYS-HIGH", Duration.ofDays(6), AcceptedCost.HIGH);

    verify("TWELVE_MINUTES-EXTREME", Duration.ofMinutes(12), AcceptedCost.EXTREME);
    verify("TWELVE_HOURS-EXTREME", Duration.ofHours(12), AcceptedCost.EXTREME);
    verify("ELEVEN_DAYS-EXTREME", Duration.ofDays(11), AcceptedCost.EXTREME);
    verify("SIX_DAYS-EXTREME", Duration.ofDays(6), AcceptedCost.EXTREME);
  }

  @Test
  void extractWithInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> new SendSchemaNameParameterExtractor().extract(null), "sendSchemaName must not be null");
  }

  @Test
  void isEmptyTest() {
    verifyIsEmpty(" ");
    verifyIsEmpty("random-string");
    verifyIsEmpty("ONES_DAY-HIGH");
    verifyIsEmpty("DONE_DAY-HIGH");
    verifyIsEmpty("ONE_DAY-HIGHS");
    verifyIsEmpty("ONE-DAY-HIGH");
    verifyIsEmpty("ONE_SECOND-HIGH");
  }
}