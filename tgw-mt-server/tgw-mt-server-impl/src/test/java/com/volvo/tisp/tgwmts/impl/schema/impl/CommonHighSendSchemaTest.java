package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CommonHighSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), CommonHighSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, CommonHighSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> CommonHighSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.COMMON_HIGH, CommonHighSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(CommonHighSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(CommonHighSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(CommonHighSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepSat(CommonHighSendSchema.INSTANCE, 4);
    VerificationUtil.verifySendSchemaStepWait(CommonHighSendSchema.INSTANCE, 5, Duration.ofDays(7));

    Assertions.assertTrue(CommonHighSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
  }
}
