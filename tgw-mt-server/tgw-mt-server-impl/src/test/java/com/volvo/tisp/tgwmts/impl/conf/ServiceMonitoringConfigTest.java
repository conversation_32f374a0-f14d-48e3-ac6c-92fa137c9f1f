package com.volvo.tisp.tgwmts.impl.conf;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.core.env.Environment;

import com.fasterxml.jackson.databind.ObjectMapper;

import software.amazon.awssdk.services.sqs.SqsClient;

class ServiceMonitoringConfigTest {
  @Test
  void createServiceMonitoring() {
    Assertions.assertNotNull(new ServiceMonitoringConfig().createServiceMonitoring(Mockito.mock(SqsClient.class), Mockito.mock(ObjectMapper.class),
        Mockito.mock(Environment.class)));
  }
}
