package com.volvo.tisp.tgwmts.impl.db.stat.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.stat.TableName;
import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class PgStatTupleMetricReporterTest {
  public static final String TABLE_NAME = "TABLE_NAME";

  @Test
  void onDeadTupleLenTest() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatTupleMetricReporter::new, (meterRegistry, pgStatTupleMetricReporter) -> {
      pgStatTupleMetricReporter.onDeadTupleLen(TableName.MT_MESSAGE, 5);
      pgStatTupleMetricReporter.onDeadTupleLen(TableName.ACTIVE_MT_MESSAGE, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "dead-tuple-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "dead-tuple-len", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), 20);

      pgStatTupleMetricReporter.onDeadTupleLen(TableName.MT_MESSAGE, 10);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "dead-tuple-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 10);
    });
  }

  @Test
  void onFreeSpaceTest() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatTupleMetricReporter::new, (meterRegistry, pgStatTupleMetricReporter) -> {
      pgStatTupleMetricReporter.onFreeSpace(TableName.MT_MESSAGE, 5);
      pgStatTupleMetricReporter.onFreeSpace(TableName.ACTIVE_MT_MESSAGE, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "free-space", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "free-space", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), 20);

      pgStatTupleMetricReporter.onFreeSpace(TableName.MT_MESSAGE, 3);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "free-space", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 3);
    });
  }

  @Test
  void onTableLenTest() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatTupleMetricReporter::new, (meterRegistry, pgStatTupleMetricReporter) -> {
      pgStatTupleMetricReporter.onTableLen(TableName.MT_MESSAGE, 5);
      pgStatTupleMetricReporter.onTableLen(TableName.ACTIVE_MT_MESSAGE, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "table-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "table-len", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), 20);

      pgStatTupleMetricReporter.onTableLen(TableName.MT_MESSAGE, 3);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "table-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 3);
    });
  }

  @Test
  void onTupleLenTest() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatTupleMetricReporter::new, (meterRegistry, pgStatTupleMetricReporter) -> {
      pgStatTupleMetricReporter.onTupleLen(TableName.MT_MESSAGE, 5);
      pgStatTupleMetricReporter.onTupleLen(TableName.ACTIVE_MT_MESSAGE, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "tuple-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "tuple-len", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), 20);

      pgStatTupleMetricReporter.onTupleLen(TableName.MT_MESSAGE, 3);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "tuple-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), 3);
    });
  }
}