package com.volvo.tisp.tgwmts.impl.jms;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.services.EnqueueingTypeProcessor;
import com.volvo.tisp.tgwmts.impl.services.MtPersister;
import com.volvo.tisp.tgwmts.impl.services.VehicleLockIdProvider;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeper;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

class MtMessagePersistStackTest {
  private static Consumer<IntegrationLogParameter> mockLoggingHelper() {
    return Mockito.mock(Consumer.class);
  }

  @Test
  void enqueueingTypeProcessorTest() {
    EnqueueingTypeProcessor enqueueingTypeProcessor = Mockito.mock(EnqueueingTypeProcessor.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();

    MtMessagePersistStack mtMessagePersistStack = new MtMessagePersistStack(enqueueingTypeProcessor, loggingHelper, Mockito.mock(MtDoorkeeper.class),
        Mockito.mock(MtPersister.class), TestUtil.mockFunction(), Mockito.mock(VehicleLockIdProvider.class));

    Assertions.assertSame(enqueueingTypeProcessor, mtMessagePersistStack.getEnqueueingTypeProcessor());
  }

  @Test
  void loggingHelperTest() {
    VehicleLockIdProvider vehicleLockIdProvider = Mockito.mock(VehicleLockIdProvider.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtMessagePersistStack mtMessagePersistStack = new MtMessagePersistStack(Mockito.mock(EnqueueingTypeProcessor.class), loggingHelper,
        Mockito.mock(MtDoorkeeper.class),
        Mockito.mock(MtPersister.class), TestUtil.mockFunction(), vehicleLockIdProvider);

    Assertions.assertSame(loggingHelper, mtMessagePersistStack.getLoggingHelper());
  }

  @Test
  void mtDoorkeeperTest() {
    MtDoorkeeper mtDoorkeeper = Mockito.mock(MtDoorkeeper.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtMessagePersistStack mtMessagePersistStack = new MtMessagePersistStack(Mockito.mock(EnqueueingTypeProcessor.class), loggingHelper, mtDoorkeeper,
        Mockito.mock(MtPersister.class), TestUtil.mockFunction(), Mockito.mock(VehicleLockIdProvider.class));

    Assertions.assertSame(mtDoorkeeper, mtMessagePersistStack.getMtDoorkeeper());
  }

  @Test
  void tgwIdentifierFunctionTest() {
    Function<Vpi, Optional<PersistedDeviceInfo>> tgwIdentifierFunction = TestUtil.mockFunction();
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtMessagePersistStack mtMessagePersistStack = new MtMessagePersistStack(Mockito.mock(EnqueueingTypeProcessor.class), loggingHelper,
        Mockito.mock(MtDoorkeeper.class),
        Mockito.mock(MtPersister.class), tgwIdentifierFunction, Mockito.mock(VehicleLockIdProvider.class));

    Assertions.assertSame(tgwIdentifierFunction, mtMessagePersistStack.getTgwIdentifierFunction());
  }

  @Test
  void vehicleLockIdProviderTest() {
    VehicleLockIdProvider vehicleLockIdProvider = Mockito.mock(VehicleLockIdProvider.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtMessagePersistStack mtMessagePersistStack = new MtMessagePersistStack(Mockito.mock(EnqueueingTypeProcessor.class), loggingHelper,
        Mockito.mock(MtDoorkeeper.class),
        Mockito.mock(MtPersister.class), TestUtil.mockFunction(), vehicleLockIdProvider);

    Assertions.assertSame(vehicleLockIdProvider, mtMessagePersistStack.getVehicleLockIdProvider());
  }
}
