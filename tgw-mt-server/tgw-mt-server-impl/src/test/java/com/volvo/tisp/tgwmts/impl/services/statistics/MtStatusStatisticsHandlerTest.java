package com.volvo.tisp.tgwmts.impl.services.statistics;

import java.time.Duration;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.impl.conf.properties.MtStatusStatisticsConfigProperties;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtStatusStatisticsMetricReporter;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtStatusStatisticsHandlerTest {
  private static MtStatusStatisticsHandler createMtStatusStatisticsHandler(MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter,
      MtStatusStatisticsPublisher mtStatusStatisticsPublisher, MtStatusStatisticsConfigProperties mtStatusStatisticsConfigProperties) {
    return new MtStatusStatisticsHandler(mtStatusStatisticsPublisher, mtStatusStatisticsConfigProperties, mtStatusStatisticsMetricReporter);
  }

  @Test
  void enrichedMoMessageNullTest() {
    MtStatusStatisticsHandler mtStatusStatisticsHandler = createMtStatusStatisticsHandler(Mockito.mock(MtStatusStatisticsMetricReporter.class),
        Mockito.mock(MtStatusStatisticsPublisher.class), new MtStatusStatisticsConfigProperties(Duration.ofMillis(10), 10, 10, 10));
    AssertThrows.illegalArgumentException(() -> mtStatusStatisticsHandler.handle(null), "mtStatusStatistic must not be null");
  }
}