package com.volvo.tisp.tgwmts.impl.converters;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.DynamicSendSchemaRetriever;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.SendSchemaCalculator;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonLongSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonLowSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonNormalSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonRswdlSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonSetupSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonShortSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonVeryHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DarfConfigSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolLowSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolMidPlusSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolSetupSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DrutVeryHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.RdnsMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.RswdlLowSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.RswdlMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.SatOnlySendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.SmsOnlySendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.UptimeVeryHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.VLinkHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.VLinkLowSendSchema;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SendSchemaNameMappingFunctionTest {
  private static void verifyApply(SendSchemaName sendSchemaName, SendSchema expectedSendSchema) {
    DynamicSendSchemaRetriever dynamicSendSchemaRetriever = Mockito.mock(DynamicSendSchemaRetriever.class);
    SendSchemaNameMappingFunction sendSchemaNameMappingFunction = new SendSchemaNameMappingFunction(dynamicSendSchemaRetriever);
    Assertions.assertSame(expectedSendSchema, sendSchemaNameMappingFunction.apply(sendSchemaName));
    Mockito.verifyNoInteractions(dynamicSendSchemaRetriever);
  }

  @Test
  void applyTest() {
    verifyApply(SendSchemaName.COMMON_HIGH, CommonHighSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_LONG, CommonLongSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_LOW, CommonLowSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_MID, CommonMidSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_NORMAL, CommonNormalSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_RSWDL, CommonRswdlSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_SETUP, CommonSetupSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_SHORT, CommonShortSendSchema.INSTANCE);
    verifyApply(SendSchemaName.COMMON_VERY_HIGH, CommonVeryHighSendSchema.INSTANCE);
    verifyApply(SendSchemaName.DARF_CONFIG, DarfConfigSendSchema.INSTANCE);
    verifyApply(SendSchemaName.DFOL_HIGH, DfolHighSendSchema.INSTANCE);
    verifyApply(SendSchemaName.DFOL_LOW, DfolLowSendSchema.INSTANCE);
    verifyApply(SendSchemaName.DFOL_MID, DfolMidSendSchema.INSTANCE);
    verifyApply(SendSchemaName.DFOL_MID_PLUS, DfolMidPlusSendSchema.INSTANCE);
    verifyApply(SendSchemaName.DFOL_SETUP, DfolSetupSendSchema.INSTANCE);
    verifyApply(SendSchemaName.DRUT_VERY_HIGH, DrutVeryHighSendSchema.INSTANCE);
    verifyApply(SendSchemaName.RDNS_MID, RdnsMidSendSchema.INSTANCE);
    verifyApply(SendSchemaName.RSWDL_LOW, RswdlLowSendSchema.INSTANCE);
    verifyApply(SendSchemaName.RSWDL_MID, RswdlMidSendSchema.INSTANCE);
    verifyApply(SendSchemaName.UPTIME_VERY_HIGH, UptimeVeryHighSendSchema.INSTANCE);
    verifyApply(SendSchemaName.VLINK_HIGH, VLinkHighSendSchema.INSTANCE);
    verifyApply(SendSchemaName.VLINK_LOW, VLinkLowSendSchema.INSTANCE);
    verifyApply(SendSchemaName.SAT_ONLY, SatOnlySendSchema.INSTANCE);
    verifyApply(SendSchemaName.SMS_ONLY, SmsOnlySendSchema.INSTANCE);
  }

  @Test
  void dynamicApplyTest() {
    SendSchema sendSchema = Mockito.mock(SendSchema.class);
    SendSchemaName sendSchemaName = new SendSchemaName("DYNAMIC-SCHEMA");
    SendSchemaCalculator sendSchemaCalculator = Mockito.mock(SendSchemaCalculator.class);
    Mockito.when(sendSchemaCalculator.calculate(sendSchemaName)).thenReturn(sendSchema);
    DynamicSendSchemaRetriever dynamicSendSchemaRetriever = new DynamicSendSchemaRetriever(Caffeine.newBuilder().build(), sendSchemaCalculator);
    SendSchemaNameMappingFunction sendSchemaNameMappingFunction = new SendSchemaNameMappingFunction(dynamicSendSchemaRetriever);

    Assertions.assertEquals(sendSchema, sendSchemaNameMappingFunction.apply(sendSchemaName));
    Mockito.verify(sendSchemaCalculator).calculate(sendSchemaName);
    Mockito.verifyNoMoreInteractions(sendSchemaCalculator);
  }

  @Test
  void invalidParameterTest() {
    DynamicSendSchemaRetriever dynamicSendSchemaRetriever = Mockito.mock(DynamicSendSchemaRetriever.class);
    AssertThrows.illegalArgumentException(() -> new SendSchemaNameMappingFunction(dynamicSendSchemaRetriever).apply(null), "sendSchemaName must not be null");
  }
}
