package com.volvo.tisp.tgwmts.impl.integration.logging;

import java.util.Map;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;

class MetaDataTest {
  @Test
  void getMapAsOptionalTest() {
    Map<String, String> map = TestUtil.createMetaDataBuilder().build().getMapAsOptional().orElseThrow();
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_SERVICE.toString(), map.get("serviceId"));
  }

  @Test
  void testToStringTest() {
    Assertions.assertEquals("{serviceId=5}", TestUtil.createMetaDataBuilder().build().toString());
  }
}
