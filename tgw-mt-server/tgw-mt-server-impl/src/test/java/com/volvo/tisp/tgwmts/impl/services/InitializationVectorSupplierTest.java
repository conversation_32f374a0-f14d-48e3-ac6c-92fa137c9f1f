package com.volvo.tisp.tgwmts.impl.services;

import java.security.SecureRandom;
import java.util.function.Supplier;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.vc.crypto.common.entity.InitializationVector;

class InitializationVectorSupplierTest {
  private static final byte[] BYTES = new byte[] {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12};

  @Test
  void getTest() {
    SecureRandom secureRandom = Mockito.mock(SecureRandom.class, Mockito.withSettings().withoutAnnotations());
    Mockito.doAnswer(invocation -> {
      byte[] argument = (byte[]) invocation.getArgument(0);
      System.arraycopy(BYTES, 0, argument, 0, InitializationVector.IV_LENGTH_IN_BYTES);
      return null;
    }).when(secureRandom).nextBytes(ArgumentMatchers.any());

    Supplier<InitializationVector> supplier = InitializationVectorSupplier.of(secureRandom);
    InitializationVector initializationVector = supplier.get();

    Assertions.assertArrayEquals(BYTES, initializationVector.getImmutableByteArray().toByteArray());
    Mockito.verify(secureRandom).nextBytes(ArgumentMatchers.any());
    Mockito.verifyNoMoreInteractions(secureRandom);
  }

  @Test
  void invalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> InitializationVectorSupplier.of(null), "secureRandom must not be null");
  }
}
