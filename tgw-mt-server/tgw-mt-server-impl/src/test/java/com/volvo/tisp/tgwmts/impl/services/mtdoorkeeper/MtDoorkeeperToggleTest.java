package com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtDoorkeeperToggleTest {
  @Test
  void createInvalidTest() {
    AssertThrows.illegalArgumentException(() -> MtDoorkeeperToggle.create(null, false), "booleanConsumer must not be null");
  }

  @Test
  void enabledTest() {
    final BooleanConsumer booleanConsumer = Mockito.mock(BooleanConsumer.class);

    MtDoorkeeperToggle mtDoorkeeperToggle = MtDoorkeeperToggle.create(booleanConsumer, true);
    Assertions.assertTrue(mtDoorkeeperToggle.isEnabled());
    Mockito.verify(booleanConsumer).accept(true);

    mtDoorkeeperToggle.setEnabled(false);
    Assertions.assertFalse(mtDoorkeeperToggle.isEnabled());
    Mockito.verify(booleanConsumer).accept(false);

    Mockito.verifyNoMoreInteractions(booleanConsumer);
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("true", MtDoorkeeperToggle.create(Mockito.mock(BooleanConsumer.class), true).toString());
  }
}
