package com.volvo.tisp.tgwmts.impl.schema.dynamic;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.conf.properties.SendSchemaProperties;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.metric.reporter.SendSchemaMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;

/**
 * Test to verify that SAT-enabled vehicles get reduced SMS timeout for faster SAT fallback.
 */
class SatAwareSmsTimeoutTest {

  private static final SendSchemaNameParameterExtractor PARAMETER_EXTRACTOR = new SendSchemaNameParameterExtractor();
  private static final SendSchemaProperties SEND_SCHEMA_PROPERTIES = new SendSchemaProperties(Duration.ofDays(1), Duration.ofMinutes(1));

  @Test
  void satEnabledVehicleUsesReducedSmsTimeoutTest() {
    SendSchemaMetricReporter sendSchemaMetricReporter = Mockito.mock(SendSchemaMetricReporter.class);
    SendSchemaCalculator sendSchemaCalculator = new SendSchemaCalculator(sendSchemaMetricReporter, PARAMETER_EXTRACTOR, SEND_SCHEMA_PROPERTIES);

    // Test with SAT enabled
    SendSchema satEnabledSchema = sendSchemaCalculator.calculate(new SendSchemaName("ONE_DAY-HIGH"), true);
    
    // Verify SMS step uses 5-minute timeout for SAT-enabled vehicles
    VerificationUtil.verifySendSchemaStepWifi(satEnabledSchema, 1);
    VerificationUtil.verifySendSchemaStepUdp(satEnabledSchema, 2);
    VerificationUtil.verifySendSchemaStepSms(satEnabledSchema, 3, SendSchemaStep.SAT_ENABLED_SMS_TIMEOUT);
    VerificationUtil.verifySendSchemaStepSat(satEnabledSchema, 4);
    
    Mockito.verifyNoInteractions(sendSchemaMetricReporter);
  }

  @Test
  void nonSatVehicleUsesDefaultSmsTimeoutTest() {
    SendSchemaMetricReporter sendSchemaMetricReporter = Mockito.mock(SendSchemaMetricReporter.class);
    SendSchemaCalculator sendSchemaCalculator = new SendSchemaCalculator(sendSchemaMetricReporter, PARAMETER_EXTRACTOR, SEND_SCHEMA_PROPERTIES);

    // Test with SAT disabled
    SendSchema nonSatSchema = sendSchemaCalculator.calculate(new SendSchemaName("ONE_DAY-HIGH"), false);
    
    // Verify SMS step uses default 65-minute timeout for non-SAT vehicles
    VerificationUtil.verifySendSchemaStepWifi(nonSatSchema, 1);
    VerificationUtil.verifySendSchemaStepUdp(nonSatSchema, 2);
    VerificationUtil.verifySendSchemaStepSms(nonSatSchema, 3, SendSchemaStep.DEFAULT_SMS_TIMEOUT);
    VerificationUtil.verifySendSchemaStepSat(nonSatSchema, 4);
    
    Mockito.verifyNoInteractions(sendSchemaMetricReporter);
  }

  @Test
  void backwardCompatibilityTest() {
    SendSchemaMetricReporter sendSchemaMetricReporter = Mockito.mock(SendSchemaMetricReporter.class);
    SendSchemaCalculator sendSchemaCalculator = new SendSchemaCalculator(sendSchemaMetricReporter, PARAMETER_EXTRACTOR, SEND_SCHEMA_PROPERTIES);

    // Test backward compatibility - original method should default to non-SAT behavior
    SendSchema backwardCompatSchema = sendSchemaCalculator.calculate(new SendSchemaName("ONE_DAY-HIGH"));
    
    // Verify SMS step uses default 65-minute timeout for backward compatibility
    VerificationUtil.verifySendSchemaStepWifi(backwardCompatSchema, 1);
    VerificationUtil.verifySendSchemaStepUdp(backwardCompatSchema, 2);
    VerificationUtil.verifySendSchemaStepSms(backwardCompatSchema, 3, SendSchemaStep.DEFAULT_SMS_TIMEOUT);
    VerificationUtil.verifySendSchemaStepSat(backwardCompatSchema, 4);
    
    Mockito.verifyNoInteractions(sendSchemaMetricReporter);
  }

  @Test
  void timeoutConstantsTest() {
    // Verify the timeout constants are correctly defined
    Assertions.assertEquals(Duration.ofMinutes(5), SendSchemaStep.SAT_ENABLED_SMS_TIMEOUT);
    Assertions.assertEquals(Duration.ofMinutes(65), SendSchemaStep.DEFAULT_SMS_TIMEOUT);
    
    // Verify SAT timeout is significantly shorter than default
    Assertions.assertTrue(SendSchemaStep.SAT_ENABLED_SMS_TIMEOUT.compareTo(SendSchemaStep.DEFAULT_SMS_TIMEOUT) < 0);
    
    // Verify the reduction is exactly 60 minutes (65 - 5 = 60)
    Duration reduction = SendSchemaStep.DEFAULT_SMS_TIMEOUT.minus(SendSchemaStep.SAT_ENABLED_SMS_TIMEOUT);
    Assertions.assertEquals(Duration.ofMinutes(60), reduction);
  }
}
