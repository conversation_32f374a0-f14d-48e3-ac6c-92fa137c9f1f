package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CommonRswdlSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), CommonRswdlSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(10, CommonRswdlSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> CommonRswdlSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.COMMON_RSWDL, CommonRswdlSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(CommonRswdlSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(CommonRswdlSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepWait(CommonRswdlSendSchema.INSTANCE, 3, Duration.ofDays(7));

    Assertions.assertTrue(CommonRswdlSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(4)).isEmpty());
  }
}
