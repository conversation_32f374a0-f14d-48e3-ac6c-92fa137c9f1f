package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MtRouterRestClientMetricReporterTest {
  private static final String CONVERSION_FAILURE = "CONVERSION-FAILURE";
  private static final String TYPE = "TYPE";

  @Test
  void onInvalidEncodedActiveMtMessageTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtRouterRestClientMetricReporter::new, (meterRegistry, mtRouterMessageMetricReporter) -> {
      mtRouterMessageMetricReporter.onInvalidEncodedActiveMtMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-router-client", Tags.of(TYPE, CONVERSION_FAILURE), 1);

      mtRouterMessageMetricReporter.onInvalidEncodedActiveMtMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-router-client", Tags.of(TYPE, CONVERSION_FAILURE), 2);
    });
  }
}