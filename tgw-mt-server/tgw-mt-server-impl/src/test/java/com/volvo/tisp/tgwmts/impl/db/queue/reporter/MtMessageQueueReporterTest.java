package com.volvo.tisp.tgwmts.impl.db.queue.reporter;

import java.time.Duration;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageQueueReporterTest {
  private static ActiveMtMessageReader mockActiveMtMessageReader(int mtMessageCount, int activeMtMessageCount) {
    ActiveMtMessageReader activeMtMessageReader = Mockito.mock(ActiveMtMessageReader.class);
    Mockito.when(activeMtMessageReader.countMtMessages()).thenReturn(mtMessageCount);
    Mockito.when(activeMtMessageReader.countActiveMtMessages()).thenReturn(activeMtMessageCount);
    return activeMtMessageReader;
  }

  private static ActiveMtMessageReaderFactory mockActiveMtMessageReaderFactory(ActiveMtMessageReader activeMtMessageReader) {
    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = Mockito.mock(ActiveMtMessageReaderFactory.class);
    Mockito.when(activeMtMessageReaderFactory.create()).thenReturn(activeMtMessageReader);
    return activeMtMessageReaderFactory;
  }

  @Test
  void invalidConstructorTest() {
    final MtMessageQueueReporterTask mtMessageQueueReporterTask = Mockito.mock(MtMessageQueueReporterTask.class);
    final Duration duration = Duration.ofSeconds(1);

    AssertThrows.illegalArgumentException(() -> new MtMessageQueueReporter(null, duration),
        "mtMessageQueueReporterTask must not be null");
    AssertThrows.illegalArgumentException(() -> new MtMessageQueueReporter(mtMessageQueueReporterTask, null),
        "reportDuration must not be null");
    AssertThrows.illegalArgumentException(() -> new MtMessageQueueReporter(mtMessageQueueReporterTask, Duration.ZERO),
        "reportDuration must be positive: PT0S");
  }

  @Test
  void startTest() {
    ActiveMtMessageReader activeMtMessageReader = mockActiveMtMessageReader(10, 2);
    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = mockActiveMtMessageReaderFactory(activeMtMessageReader);
    MtMessageQueueMetricReporter mtMessageQueueMetricReporter = Mockito.mock(MtMessageQueueMetricReporter.class);
    MtMessageQueueReporterTask mtMessageQueueReporterTask = new MtMessageQueueReporterTask(activeMtMessageReaderFactory, mtMessageQueueMetricReporter);

    try (MtMessageQueueReporter mtMessageQueueReporter = new MtMessageQueueReporter(mtMessageQueueReporterTask, Duration.ofMillis(100))) {
      mtMessageQueueReporter.start();
      Mockito.verify(mtMessageQueueMetricReporter, Mockito.timeout(500).atLeast(2)).mtDbQueueSize(10);
      Mockito.verify(mtMessageQueueMetricReporter, Mockito.atLeast(2)).activeMtDbQueueSize(2);
    }
  }
}
