package com.volvo.tisp.tgwmts.impl.scheduler;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtSchedulerMetricReporterTest {
  @Test
  void onTimedOutMessagesSelectedTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtSchedulerMetricReporter::new, (meterRegistry, mtSchedulerMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtSchedulerMetricReporter.onTimedOutMessagesSelected(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtSchedulerMetricReporter.onTimedOutMessagesSelected(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      mtSchedulerMetricReporter.onTimedOutMessagesSelected(Duration.ofSeconds(1));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "scheduler.timed-out-messages-selected", Duration.ofSeconds(1), 1);
    });
  }

  @Test
  void onTimedOutMessagesTotalTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtSchedulerMetricReporter::new, (meterRegistry, mtSchedulerMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtSchedulerMetricReporter.onTimedOutMessagesTotal(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtSchedulerMetricReporter.onTimedOutMessagesTotal(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      mtSchedulerMetricReporter.onTimedOutMessagesTotal(Duration.ofSeconds(1));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "scheduler.timed-out-messages-total", Duration.ofSeconds(1), 1);
    });
  }
}
