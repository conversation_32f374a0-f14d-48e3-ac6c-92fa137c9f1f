package com.volvo.tisp.tgwmts.impl.jms.model;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ReceivedMtMessageBuilderTest {
  private static void compare(ReceivedMtMessageBuilder receivedMtMessageBuilder, ReceivedMtMessage receivedMtMessage) {
    Assertions.assertSame(receivedMtMessageBuilder.getEnqueueingType(), receivedMtMessage.getEnqueueingType());
    Assertions.assertSame(receivedMtMessageBuilder.getQueueId(), receivedMtMessage.getQueueId());
    Assertions.assertSame(receivedMtMessageBuilder.getReplyOption(), receivedMtMessage.getReplyOption());
    Assertions.assertSame(receivedMtMessageBuilder.getSendSchemaName(), receivedMtMessage.getSendSchemaName());
    Assertions.assertSame(receivedMtMessageBuilder.getSrpOption(), receivedMtMessage.getSrpOption());
    Assertions.assertSame(receivedMtMessageBuilder.getTid(), receivedMtMessage.getTid());
    Assertions.assertSame(receivedMtMessageBuilder.getVpi(), receivedMtMessage.getVpi());
  }

  @Test
  void buildTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    AssertThrows.illegalArgumentException(receivedMtMessageBuilder::build, "enqueueingType must not be null");

    receivedMtMessageBuilder.setEnqueueingType(TestUtil.ENQUEUEING_TYPE);
    AssertThrows.illegalArgumentException(receivedMtMessageBuilder::build, "queueId must not be null");

    receivedMtMessageBuilder.setQueueId(TestUtil.QUEUE_ID);
    AssertThrows.illegalArgumentException(receivedMtMessageBuilder::build, "sendSchemaName must not be null");

    receivedMtMessageBuilder.setSendSchemaName(TestUtil.SEND_SCHEMA_NAME);
    AssertThrows.illegalArgumentException(receivedMtMessageBuilder::build, "srpOption must not be null");

    SrpOption srpOption = TestUtil.createSrpOption();
    receivedMtMessageBuilder.setSrpOption(srpOption);
    AssertThrows.illegalArgumentException(receivedMtMessageBuilder::build, "tid must not be null");

    receivedMtMessageBuilder.setTid(TestUtil.TID);
    AssertThrows.illegalArgumentException(receivedMtMessageBuilder::build, "vpi must not be null");

    receivedMtMessageBuilder.setVpi(TestUtil.VPI);
    compare(receivedMtMessageBuilder, receivedMtMessageBuilder.build());

    receivedMtMessageBuilder.setReplyOption(Optional.of(TestUtil.createReplyOption()));

    compare(receivedMtMessageBuilder, receivedMtMessageBuilder.build());
  }

  @Test
  void enqueueingTypeTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    Assertions.assertNull(receivedMtMessageBuilder.getEnqueueingType());

    receivedMtMessageBuilder.setEnqueueingType(EnqueueingType.IGNORE);
    Assertions.assertSame(EnqueueingType.IGNORE, receivedMtMessageBuilder.getEnqueueingType());

    AssertThrows.illegalArgumentException(() -> receivedMtMessageBuilder.setEnqueueingType(null), "enqueueingType must not be null");
    Assertions.assertSame(EnqueueingType.IGNORE, receivedMtMessageBuilder.getEnqueueingType());
  }

  @Test
  void queueIdTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    Assertions.assertNull(receivedMtMessageBuilder.getQueueId());

    receivedMtMessageBuilder.setQueueId(TestUtil.QUEUE_ID);
    Assertions.assertSame(TestUtil.QUEUE_ID, receivedMtMessageBuilder.getQueueId());

    AssertThrows.illegalArgumentException(() -> receivedMtMessageBuilder.setQueueId(null), "queueId must not be null");
    Assertions.assertSame(TestUtil.QUEUE_ID, receivedMtMessageBuilder.getQueueId());
  }

  @Test
  void replyOptionTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    Assertions.assertSame(ReceivedMtMessageBuilder.DEFAULT_REPLY_OPTION, receivedMtMessageBuilder.getReplyOption());

    AssertThrows.illegalArgumentException(() -> receivedMtMessageBuilder.setReplyOption(null), "replyOption must not be null");
    Assertions.assertSame(ReceivedMtMessageBuilder.DEFAULT_REPLY_OPTION, receivedMtMessageBuilder.getReplyOption());

    Optional<ReplyOption> expectedReplyOption = Optional.of(TestUtil.createReplyOption());
    receivedMtMessageBuilder.setReplyOption(expectedReplyOption);
    Assertions.assertSame(expectedReplyOption, receivedMtMessageBuilder.getReplyOption());
  }

  @Test
  void sendSchemaNameTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    Assertions.assertNull(receivedMtMessageBuilder.getSendSchemaName());

    receivedMtMessageBuilder.setSendSchemaName(TestUtil.SEND_SCHEMA_NAME);
    Assertions.assertSame(TestUtil.SEND_SCHEMA_NAME, receivedMtMessageBuilder.getSendSchemaName());

    AssertThrows.illegalArgumentException(() -> receivedMtMessageBuilder.setSendSchemaName(null), "sendSchemaName must not be null");
    Assertions.assertSame(TestUtil.SEND_SCHEMA_NAME, receivedMtMessageBuilder.getSendSchemaName());
  }

  @Test
  void srpOptionTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    Assertions.assertNull(receivedMtMessageBuilder.getSrpOption());

    SrpOption srpOption = TestUtil.createSrpOption();
    receivedMtMessageBuilder.setSrpOption(srpOption);
    Assertions.assertSame(srpOption, receivedMtMessageBuilder.getSrpOption());

    AssertThrows.illegalArgumentException(() -> receivedMtMessageBuilder.setSrpOption(null), "srpOption must not be null");
    Assertions.assertSame(srpOption, receivedMtMessageBuilder.getSrpOption());
  }

  @Test
  void tidTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    Assertions.assertNull(receivedMtMessageBuilder.getTid());

    receivedMtMessageBuilder.setTid(TestUtil.TID);
    Assertions.assertSame(TestUtil.TID, receivedMtMessageBuilder.getTid());

    AssertThrows.illegalArgumentException(() -> receivedMtMessageBuilder.setTid(null), "tid must not be null");
    Assertions.assertSame(TestUtil.TID, receivedMtMessageBuilder.getTid());
  }

  @Test
  void vpiTest() {
    ReceivedMtMessageBuilder receivedMtMessageBuilder = new ReceivedMtMessageBuilder();
    Assertions.assertNull(receivedMtMessageBuilder.getVpi());

    receivedMtMessageBuilder.setVpi(TestUtil.VPI);
    Assertions.assertSame(TestUtil.VPI, receivedMtMessageBuilder.getVpi());

    AssertThrows.illegalArgumentException(() -> receivedMtMessageBuilder.setVpi(null), "vpi must not be null");
    Assertions.assertSame(TestUtil.VPI, receivedMtMessageBuilder.getVpi());
  }
}
