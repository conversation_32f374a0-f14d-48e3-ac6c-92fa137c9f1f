package com.volvo.tisp.tgwmts.impl.integration.logging;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class LoggingHelperTest {
  @Test
  void accept() {
    TispContext.runInContext(() -> LoggingHelper.INSTANCE.accept(TestUtil.createIntegrationLogParameter()));
  }

  @Test
  void invalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> LoggingHelper.INSTANCE.accept(null), "integrationLogParameter must not be null");
  }
}
