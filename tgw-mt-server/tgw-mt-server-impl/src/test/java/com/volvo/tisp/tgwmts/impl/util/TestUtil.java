package com.volvo.tisp.tgwmts.impl.util;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.mockito.Mockito;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.tgw.device.info.database.model.AesKey;
import com.volvo.tisp.tgw.device.info.database.model.CryptoKeyInfo;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfoBuilder;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfoId;
import com.volvo.tisp.tgw.device.info.database.model.Handle;
import com.volvo.tisp.tgw.device.info.database.model.Ipv4Port;
import com.volvo.tisp.tgw.device.info.database.model.MobileNetworkOperator;
import com.volvo.tisp.tgw.device.info.database.model.PendingCryptoKeyInfo;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfoBuilder;
import com.volvo.tisp.tgw.device.info.database.model.SimInfoBuilder;
import com.volvo.tisp.tgw.device.info.database.model.WtpVersion;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameterBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationVersion;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpPayload;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.conf.MtMessageDefaultParameters;
import com.volvo.tisp.tgwmts.impl.converters.SendSchemaNameMappingFunction;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameterBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationMessage;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaDataBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.VehicleDetail;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessageBuilder;
import com.volvo.tisp.tgwmts.impl.model.ActiveMessageDetails;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.IdentifiedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.MtMessageDetails;
import com.volvo.tisp.tgwmts.impl.model.MtMessageInformation;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatusMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.TransportType;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.DynamicSendSchemaRetriever;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.DestinationVersion;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.Priority;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v12.Srp12WrapperBuilder;
import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.wirelesscar.componentbase.logging.Logging;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpLevel;

public final class TestUtil {
  public static final String ACTIVE_MESSAGE_ID = "1";
  public static final ActiveMtMessageId ACTIVE_MT_MESSAGE_ID = ActiveMtMessageId.ofLong(1L);
  public static final String CLIENT_ID = "myClientId";
  public static final CorrelationId CORRELATION_ID = CorrelationId.ofString("CorrelationId");
  public static final String CRYPTO_KEY_ID = "cryptoKeyId";
  public static final AesKey CRYPTO_KEY_INFO_AES_KEY = AesKey.create(ImmutableByteArray.of(new byte[] {1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16}));
  public static final EnqueueingType ENQUEUEING_TYPE = EnqueueingType.NORMAL;
  public static final Handle HANDLE = Handle.ofLong(12345L);
  public static final InitializationVector INITIALIZATION_VECTOR = InitializationVector
      .create(ImmutableByteArray.of(new byte[InitializationVector.IV_LENGTH_IN_BYTES]));
  public static final Instant INSTANT = Instant.ofEpochSecond(42);
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  public static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(9052);
  public static final String MESSAGE_ID = createMessageId();
  public static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("operator");
  public static final Msisdn MSISDN = Msisdn.ofString("+3245678");
  public static final MtMessageId MT_MESSAGE_ID = MtMessageId.ofLong(1L);
  public static final ObsAlias OBS_ALIAS = ObsAlias.ofLong(42);
  public static final AesKey PENDING_CRYPTO_KEY_INFO_AES_KEY = AesKey
      .create(ImmutableByteArray.of(new byte[] {16, 15, 14, 13, 12, 11, 10, 9, 8, 7, 6, 5, 4, 3, 2, 1}));
  public static final QueueId QUEUE_ID = QueueId.ofString("queueId");
  public static final ReplyTo REPLY_TO = ReplyTo.ofString("ReplyTo");
  public static final RetryAttempt RETRY_ATTEMPT = RetryAttempt.ofShort((short) 0);
  public static final String SCHEDULER_HINT = "common-very-high";
  public static final String SCHEDULER_OPTION_QUEUE_ID = "queueId";
  public static final SendSchemaName SEND_SCHEMA_NAME = SendSchemaName.COMMON_LOW;
  public static final SendSchemaStepId SEND_SCHEMA_STEP_ID = SendSchemaStepId.ofInt(1);
  public static final SendSchemaStepType SEND_SCHEMA_STEP_TYPE = SendSchemaStepType.SMS;
  public static final Duration SEND_SCHEMA_STEP_WAIT_DURATION = Duration.ofDays(7);
  public static final SrpDestinationService SRP_DESTINATION_SERVICE = SrpDestinationService.ofInt(5);
  public static final SrpDestinationVersion SRP_DESTINATION_VERSION = SrpDestinationVersion.ofShort((short) 3);
  public static final SrpLevel SRP_LEVEL = SrpLevel.SRP_10;
  public static final SrpPayload SRP_PAYLOAD = SrpPayload.ofImmutableByteArray(ImmutableByteArray.of(new byte[58]));
  public static final Priority SRP_PRIORITY = Priority.ofShort((short) 7);
  public static final Tid TID = Tid.ofString("123456789012345678901234567890FF");
  public static final VehicleLockId VEHICLE_LOCK_ID = VehicleLockId.ofLong(1L);
  public static final Vpi VPI = Vpi.ofString("12345678901234567890ABCDEFABCDEF");
  private static final DeviceInfoId DEVICE_INFO_ID = DeviceInfoId.ofLong(1L);
  private static final Instant TIMEOUT = Instant.ofEpochSecond(42);

  private TestUtil() {
    throw new IllegalStateException();
  }

  public static ActiveMessageDetails createActiveMessageDetails() {
    return new ActiveMessageDetails(ACTIVE_MT_MESSAGE_ID.toLong(),
        SEND_SCHEMA_STEP_TYPE,
        RETRY_ATTEMPT.toShort(),
        INSTANT,
        INSTANT,
        INSTANT
    );
  }

  public static ActiveMtMessage createActiveMtMessage(RetryAttempt retryAttempt, SendSchemaStepId sendSchemaStepId) {
    return new ActiveMtMessageBuilder()
        .setMtMessageId(MT_MESSAGE_ID)
        .setSendSchemaStepId(sendSchemaStepId)
        .setRetryAttempt(retryAttempt)
        .setTimeout(INSTANT)
        .build();
  }

  public static CryptoKeyInfo createCryptoKeyInfo() {
    return new CryptoKeyInfo(CRYPTO_KEY_INFO_AES_KEY, CRYPTO_KEY_ID);
  }

  public static DeviceInfoBuilder createDeviceInfoBuilder() {
    return new DeviceInfoBuilder()
        .setCryptoKeyInfo(Optional.empty())
        .setHandle(HANDLE)
        .setObsAlias(OBS_ALIAS)
        .setPendingCryptoKeyInfo(Optional.empty())
        .setSimInfo(createSimInfoBuilder().build())
        .setSrpLevel(com.volvo.tisp.tgw.device.info.database.model.SrpLevel.SRP_10)
        .setVpi(VPI)
        .setWtpVersion(WtpVersion.VERSION_1)
        .setSatEnabled(true);
  }

  public static EncodedActiveMtMessage createEncodedActiveMtMessage() {
    return new EncodedActiveMtMessage(createScheduledActiveMtMessage(), createSrp12ServiceRoutingPduWrapper());
  }

  public static IdentifiedActiveMtMessage createIdentifiedActiveMtMessage() {
    return new IdentifiedActiveMtMessage(createJoinedActiveMtMessage(ACTIVE_MT_MESSAGE_ID, createSrpOption()), createPersistedDeviceInfo());
  }

  public static IntegrationLogParameter createIntegrationLogParameter() {
    return createIntegrationLogParameterBuilder().build();
  }

  public static IntegrationLogParameterBuilder createIntegrationLogParameterBuilder() {
    return new IntegrationLogParameterBuilder()
        .setDirection(Logging.Direction.SERVER_IN)
        .setStatus(Logging.Status.SUCCESS)
        .setIntegrationMessage(IntegrationMessage.onPersist(SEND_SCHEMA_NAME))
        .setMetaData(createMetaDataBuilder().build())
        .setVehicleDetail(VehicleDetail.create(HANDLE, IPV4_ADDRESS, MSISDN, VPI));
  }

  public static JoinedActiveMtMessage createJoinedActiveMtMessage() {
    return createJoinedActiveMtMessage(ACTIVE_MT_MESSAGE_ID, createSrpOption(), SEND_SCHEMA_STEP_ID);
  }

  public static JoinedActiveMtMessage createJoinedActiveMtMessage(PersistedActiveMtMessage persistedActiveMtMessage) {
    PersistedVehicleLock persistedVehicleLock = createPersistedVehicleLock();
    PersistedMtMessage persistedMtMessage = createPersistedMtMessage(createSrpOption());
    return new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);
  }

  public static JoinedActiveMtMessage createJoinedActiveMtMessage(ActiveMtMessageId activeMtMessageId, SrpOption srpOption) {
    PersistedVehicleLock persistedVehicleLock = createPersistedVehicleLock();
    PersistedActiveMtMessage persistedActiveMtMessage = createPersistedActiveMtMessage(activeMtMessageId, RETRY_ATTEMPT, SEND_SCHEMA_STEP_ID);
    PersistedMtMessage persistedMtMessage = createPersistedMtMessage(srpOption);

    return new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);
  }

  public static JoinedActiveMtMessage createJoinedActiveMtMessage(SendSchemaStepId sendSchemaStepId) {
    return createJoinedActiveMtMessage(ACTIVE_MT_MESSAGE_ID, createSrpOption(), sendSchemaStepId);
  }

  public static JoinedActiveMtMessage createJoinedActiveMtMessage(ActiveMtMessageId activeMtMessageId, RetryAttempt retryAttempt,
      SendSchemaStepId sendSchemaStepId) {
    PersistedVehicleLock persistedVehicleLock = createPersistedVehicleLock();
    PersistedActiveMtMessage persistedActiveMtMessage = createPersistedActiveMtMessage(activeMtMessageId, retryAttempt, sendSchemaStepId);
    PersistedMtMessage persistedMtMessage = createPersistedMtMessage(createSrpOption());

    return new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);
  }

  public static JoinedMtMessage createJoinedMtMessage() {
    PersistedVehicleLock persistedVehicleLock = createPersistedVehicleLock();
    PersistedActiveMtMessage persistedActiveMtMessage = createPersistedActiveMtMessage(ACTIVE_MT_MESSAGE_ID, RETRY_ATTEMPT, SEND_SCHEMA_STEP_ID);
    PersistedMtMessage persistedMtMessage = createPersistedMtMessage(createSrpOption());

    return new JoinedMtMessage(Optional.ofNullable(persistedActiveMtMessage), persistedMtMessage, persistedVehicleLock);
  }

  public static MetaDataBuilder createMetaDataBuilder() {
    return new MetaDataBuilder().setSrpDestinationService(SRP_DESTINATION_SERVICE);
  }

  public static MtMessage createMtMessage() {
    MtMessage mtMessage = new MtMessage();
    mtMessage.setClientId(CLIENT_ID);
    mtMessage.setMtStatusReplyOption(createMtStatusReplyOption());
    mtMessage.setPayload(SRP_PAYLOAD.getImmutableByteArray().toByteArray());
    mtMessage.setSchedulerOption(createSchedulerOption());
    mtMessage.setSrpOption(createApiSrpOption());
    mtMessage.setVehiclePlatformId(VPI.toString());

    return mtMessage;
  }

  public static MtMessageBuilder createMtMessageBuilder(SrpOption srpOption) {
    return new MtMessageBuilder()
        .setQueueId(QUEUE_ID)
        .setReplyOption(Optional.of(createReplyOption()))
        .setSendSchemaName(SEND_SCHEMA_NAME)
        .setSrpOption(srpOption)
        .setTid(TID)
        .setVehicleLockId(VEHICLE_LOCK_ID);
  }

  public static MtMessageBuilder createMtMessageBuilder() {
    return createMtMessageBuilder(createSrpOption());
  }

  public static MtMessageDefaultParameters createMtMessageDefaultParameters() {
    return new MtMessageDefaultParameters(ENQUEUEING_TYPE, QUEUE_ID, SEND_SCHEMA_NAME);
  }

  public static MtMessageDetails createMtMessageDetails() {
    return createMtMessageDetails(createActiveMessageDetails());
  }

  public static MtMessageDetails createMtMessageDetails(ActiveMessageDetails activeMessageDetails) {
    return new MtMessageDetails(
        TID.toString(),
        SEND_SCHEMA_NAME.value(),
        INSTANT,
        QUEUE_ID.toString(),
        SRP_DESTINATION_SERVICE.toInt(),
        SRP_DESTINATION_VERSION.toShort(),
        Optional.of(REPLY_TO.toString()),
        activeMessageDetails
    );
  }

  public static MtMessageInformation createMtMessagesInfo() {
    return new MtMessageInformation(VPI.toString(),
        List.of(createMtMessageDetails())
    );
  }

  public static com.volvo.connectivity.proto.MtMessage.Builder createMtRouterMtMessageBuilder(byte[] payload) {
    return com.volvo.connectivity.proto.MtMessage.newBuilder()
        .setMessageId(ACTIVE_MESSAGE_ID)
        .setPayload(ByteString.copyFrom(payload))
        .setTransport(Transport.SAT)
        .setVpi(VPI.toString());
  }

  public static MtStatus.Builder createMtStatusBuilder() {
    return MtStatus
        .newBuilder()
        .setTransport(Transport.UDP)
        .setMessageId(ACTIVE_MT_MESSAGE_ID.toString())
        .setStatus(com.volvo.connectivity.proto.Status.DELIVERED);
  }

  public static MtStatus createMtStatusMessage() {
    return createMtStatusBuilder().build();
  }

  public static PendingCryptoKeyInfo createPendingCryptoKeyInfo() {
    return new PendingCryptoKeyInfo(PENDING_CRYPTO_KEY_INFO_AES_KEY);
  }

  public static PersistedActiveMtMessageBuilder createPersistedActiveMtMessageBuilder() {
    return new PersistedActiveMtMessageBuilder()
        .setActiveMtMessage(createActiveMtMessage(RETRY_ATTEMPT, SEND_SCHEMA_STEP_ID))
        .setActiveMtMessageId(ACTIVE_MT_MESSAGE_ID)
        .setCreated(INSTANT)
        .setUpdated(INSTANT);
  }

  public static PersistedDeviceInfo createPersistedDeviceInfo() {
    return createPersistedDeviceInfoBuilder().build();
  }

  public static PersistedDeviceInfo createPersistedDeviceInfo(Optional<CryptoKeyInfo> cryptoKeyInfo, Optional<PendingCryptoKeyInfo> pendingCryptoKeyInfo,
      com.volvo.tisp.tgw.device.info.database.model.SrpLevel srpLevel) {
    return new PersistedDeviceInfoBuilder()
        .setCreated(INSTANT)
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setDeviceInfo(createDeviceInfo(cryptoKeyInfo, pendingCryptoKeyInfo, srpLevel))
        .setLastUpdated(INSTANT)
        .build();
  }

  public static PersistedDeviceInfoBuilder createPersistedDeviceInfoBuilder() {
    return new PersistedDeviceInfoBuilder()
        .setCreated(INSTANT)
        .setDeviceInfoId(DEVICE_INFO_ID)
        .setDeviceInfo(createDeviceInfoBuilder().build())
        .setLastUpdated(INSTANT);
  }

  public static PersistedMtMessage createPersistedMtMessage(SrpOption srpOption) {
    return createPersistedMtMessageBuilder()
        .setMtMessage(createMtMessageBuilder(srpOption).build())
        .build();
  }

  public static PersistedMtMessageBuilder createPersistedMtMessageBuilder() {
    return new PersistedMtMessageBuilder()
        .setCreated(INSTANT)
        .setMtMessage(createMtMessageBuilder().build())
        .setMtMessageId(MT_MESSAGE_ID);
  }

  public static PersistedVehicleLock createPersistedVehicleLock() {
    return new PersistedVehicleLockBuilder()
        .setCreated(INSTANT)
        .setVehicleLock(VehicleLockBuilder.ofVpi(VPI))
        .setVehicleLockId(VEHICLE_LOCK_ID)
        .build();
  }

  public static ReceivedMtMessage createReceivedMtMessage() {
    return createReceivedMtMessage(ENQUEUEING_TYPE);
  }

  public static ReceivedMtMessage createReceivedMtMessage(EnqueueingType enqueueingType) {
    return new ReceivedMtMessageBuilder()
        .setEnqueueingType(enqueueingType)
        .setQueueId(QUEUE_ID)
        .setReplyOption(Optional.of(createReplyOption()))
        .setSendSchemaName(SEND_SCHEMA_NAME)
        .setSrpOption(createSrpOption())
        .setTid(TID)
        .setVpi(VPI)
        .build();
  }

  public static ReceivedMtMessage createReceivedMtMessageWithoutReply() {
    return new ReceivedMtMessageBuilder()
        .setEnqueueingType(ENQUEUEING_TYPE)
        .setQueueId(QUEUE_ID)
        .setReplyOption(Optional.empty())
        .setSendSchemaName(SEND_SCHEMA_NAME)
        .setSrpOption(createSrpOption())
        .setTid(TID)
        .setVpi(VPI)
        .build();
  }

  public static ReceivedMtStatusMessage createReceivedMtStatusMessage(ReceivedMtStatus receivedMtStatus) {
    return new ReceivedMtStatusMessage(ACTIVE_MT_MESSAGE_ID, receivedMtStatus, TransportType.UDP);
  }

  public static ReceivedMtStatusMessage createReceivedMtStatusMessage(TransportType transportType) {
    return new ReceivedMtStatusMessage(ACTIVE_MT_MESSAGE_ID, ReceivedMtStatus.DELIVERED, transportType);
  }

  public static ReplyOption createReplyOption() {
    return createReplyOptionBuilder().build();
  }

  public static ReplyOptionBuilder createReplyOptionBuilder() {
    return new ReplyOptionBuilder()
        .setReplyTo(REPLY_TO)
        .setCorrelationId(CORRELATION_ID);
  }

  public static SendSchemaStep createSatSendSchemaStep() {
    return SendSchemaStep.forSat(SEND_SCHEMA_STEP_ID, SEND_SCHEMA_STEP_WAIT_DURATION);
  }

  public static ScheduledActiveMtMessage createScheduledActiveMtMessage() {
    return new ScheduledActiveMtMessage(createIdentifiedActiveMtMessage(), createWaitSendSchemaStep());
  }

  public static ScheduledActiveMtMessage createScheduledActiveMtMessage(com.volvo.tisp.tgw.device.info.database.model.SrpLevel srpLevel, SrpOption srpOption) {
    return new ScheduledActiveMtMessage(createIdentifiedActiveMtMessage(srpLevel, srpOption), createWaitSendSchemaStep());
  }

  public static SendSchemaNameMappingFunction createSendSchemaNameMappingFunction() {
    DynamicSendSchemaRetriever dynamicSendSchemaRetriever = Mockito.mock(DynamicSendSchemaRetriever.class);
    return new SendSchemaNameMappingFunction(dynamicSendSchemaRetriever);
  }

  public static SimInfoBuilder createSimInfoBuilder() {
    return new SimInfoBuilder()
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .setMsisdn(MSISDN);
  }

  public static SendSchemaStep createSmsSendSchemaStep() {
    return SendSchemaStep.forSms(SEND_SCHEMA_STEP_ID, SEND_SCHEMA_STEP_WAIT_DURATION);
  }

  public static ServiceRoutingPduWrapper createSrp12ServiceRoutingPduWrapper() {
    return ServiceRoutingPduWrapper.ofSrp12Wrapper(createSrp12WrapperBuilder().build());
  }

  public static SrpOption createSrpOption() {
    return createSrpOptionBuilder().build();
  }

  public static SrpOptionBuilder createSrpOptionBuilder() {
    return new SrpOptionBuilder()
        .setSrpDestinationService(SRP_DESTINATION_SERVICE)
        .setSrpDestinationVersion(SRP_DESTINATION_VERSION)
        .setSrpLevel12(true)
        .setSrpPayload(SRP_PAYLOAD);
  }

  public static com.volvo.connectivity.proto.MtMessage createTgwMtMessage(byte[] payload) {
    return createMtRouterMtMessageBuilder(payload).build();
  }

  public static SendSchemaStep createUdpSendSchemaStep() {
    return SendSchemaStep.forUdp(SEND_SCHEMA_STEP_ID, SEND_SCHEMA_STEP_WAIT_DURATION);
  }

  public static SendSchemaStep createUpdSendSchemaStep() {
    return SendSchemaStep.forUdp(SEND_SCHEMA_STEP_ID, SEND_SCHEMA_STEP_WAIT_DURATION);
  }

  public static UpdateActiveMtMessageParameter createUpdateActiveMtMessageParameter() {
    return new UpdateActiveMtMessageParameterBuilder()
        .setActiveMtMessageId(ACTIVE_MT_MESSAGE_ID)
        .setRetryAttempt(RETRY_ATTEMPT)
        .setSendSchemaStepId(SEND_SCHEMA_STEP_ID)
        .setTimeout(TIMEOUT)
        .build();
  }

  public static SendSchemaStep createWaitSendSchemaStep() {
    return SendSchemaStep.forWait(SEND_SCHEMA_STEP_ID, SEND_SCHEMA_STEP_WAIT_DURATION);
  }

  public static SendSchemaStep createWifiSendSchemaStep() {
    return SendSchemaStep.forWifi(SEND_SCHEMA_STEP_ID, SEND_SCHEMA_STEP_WAIT_DURATION);
  }

  public static <T, R> Function<T, R> mockFunction() {
    return Mockito.mock(Function.class);
  }

  public static <T> JmsMessage<T> mockJmsMessage(T body) {
    JmsMessage<T> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(body);
    Mockito.when(jmsMessage.header(TispJmsHeader.CLIENT, String.class)).thenReturn(Optional.empty());
    return jmsMessage;
  }

  public static String zeroPadAndStringifyActiveMtMessageId(ActiveMtMessageId activeMtMessageId) {
    return StringUtils.leftPad(activeMtMessageId.toString(), 32, "0");
  }

  private static com.wirelesscar.tce.api.v2.SrpOption createApiSrpOption() {
    com.wirelesscar.tce.api.v2.SrpOption srpOption = new com.wirelesscar.tce.api.v2.SrpOption();
    srpOption.setDstService(SRP_DESTINATION_SERVICE.toInt());
    srpOption.setDstVersion(SRP_DESTINATION_VERSION.toShort());
    srpOption.setSrpLevel(SRP_LEVEL);
    return srpOption;
  }

  private static DeviceInfo createDeviceInfo(Optional<CryptoKeyInfo> cryptoKeyInfo, Optional<PendingCryptoKeyInfo> pendingCryptoKeyInfo,
      com.volvo.tisp.tgw.device.info.database.model.SrpLevel srpLevel) {
    return createDeviceInfoBuilder()
        .setCryptoKeyInfo(cryptoKeyInfo)
        .setPendingCryptoKeyInfo(pendingCryptoKeyInfo)
        .setSrpLevel(srpLevel)
        .build();
  }

  private static IdentifiedActiveMtMessage createIdentifiedActiveMtMessage(com.volvo.tisp.tgw.device.info.database.model.SrpLevel srpLevel,
      SrpOption srpOption) {
    return new IdentifiedActiveMtMessage(
        createJoinedActiveMtMessage(ACTIVE_MT_MESSAGE_ID, srpOption, SEND_SCHEMA_STEP_ID),
        createPersistedDeviceInfo(Optional.empty(), Optional.empty(), srpLevel));
  }

  private static JoinedActiveMtMessage createJoinedActiveMtMessage(ActiveMtMessageId activeMtMessageId, SrpOption srpOption,
      SendSchemaStepId sendSchemaStepId) {
    PersistedVehicleLock persistedVehicleLock = createPersistedVehicleLock();
    PersistedActiveMtMessage persistedActiveMtMessage = createPersistedActiveMtMessage(activeMtMessageId, RETRY_ATTEMPT, sendSchemaStepId);
    PersistedMtMessage persistedMtMessage = createPersistedMtMessage(srpOption);

    return new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, persistedVehicleLock);
  }

  private static String createMessageId() {
    return zeroPadAndStringifyActiveMtMessageId(ACTIVE_MT_MESSAGE_ID);
  }

  private static MtStatusReplyOption createMtStatusReplyOption() {
    MtStatusReplyOption mtStatusReplyOption = new MtStatusReplyOption();
    mtStatusReplyOption.setCorrelationId(CORRELATION_ID.toString());
    mtStatusReplyOption.setReplyDestination(REPLY_TO.toString());
    return mtStatusReplyOption;
  }

  private static PersistedActiveMtMessage createPersistedActiveMtMessage(ActiveMtMessageId activeMtMessageId, RetryAttempt retryAttempt,
      SendSchemaStepId sendSchemaStepId) {
    return createPersistedActiveMtMessageBuilder()
        .setActiveMtMessage(createActiveMtMessage(retryAttempt, sendSchemaStepId))
        .setActiveMtMessageId(activeMtMessageId)
        .build();
  }

  private static SchedulerOption createSchedulerOption() {
    SchedulerOption schedulerOption = new SchedulerOption();
    schedulerOption.setEnqueueingType(ENQUEUEING_TYPE.name());
    schedulerOption.setHint(SCHEDULER_HINT);
    schedulerOption.setPriority(8);
    schedulerOption.setQueueId(SCHEDULER_OPTION_QUEUE_ID);
    return schedulerOption;
  }

  private static Srp12WrapperBuilder createSrp12WrapperBuilder() {
    return new Srp12WrapperBuilder()
        .setDestinationVersion(DestinationVersion.ofShort((short) 0))
        .setPayload(ImmutableByteArray.EMPTY);
  }
}
