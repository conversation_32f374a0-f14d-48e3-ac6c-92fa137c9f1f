package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DfolHighSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), DfolHighSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, DfolHighSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> DfolHighSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.DFOL_HIGH, DfolHighSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(DfolHighSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(DfolHighSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(DfolHighSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepSat(DfolHighSendSchema.INSTANCE, 4);
    VerificationUtil.verifySendSchemaStepWait(DfolHighSendSchema.INSTANCE, 5, Duration.ofDays(7));

    Assertions.assertTrue(DfolHighSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
  }
}
