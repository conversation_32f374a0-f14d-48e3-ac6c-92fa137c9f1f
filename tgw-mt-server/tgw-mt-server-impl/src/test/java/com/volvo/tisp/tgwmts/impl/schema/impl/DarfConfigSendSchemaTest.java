package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DarfConfigSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), DarfConfigSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, DarfConfigSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> DarfConfigSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.DARF_CONFIG, DarfConfigSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(DarfConfigSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(DarfConfigSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSat(DarfConfigSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepWait(DarfConfigSendSchema.INSTANCE, 4, Duration.ofDays(7));

    Assertions.assertTrue(DarfConfigSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
  }
}