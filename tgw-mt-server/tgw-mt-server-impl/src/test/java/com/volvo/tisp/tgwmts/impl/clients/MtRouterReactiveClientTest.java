package com.volvo.tisp.tgwmts.impl.clients;

import java.io.Closeable;
import java.net.URI;
import java.time.Duration;
import java.util.List;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.web.reactive.function.client.WebClient;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtRouterReactiveClientConfig;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtRouterReactiveClientTest {
  public static final URI BASE_URL = URI.create("http://mtrouter");
  private static final Duration ONE_SECOND = Duration.ofSeconds(1);
  private static final String REQUEST_PATH = "/request-path";
  private static final Duration TIMEOUT = Duration.ofSeconds(3);

  private static List<LoggedRequest> awaitRequest(WireMockServer wireMockServer) {
    return Awaitility.await()
        .atMost(TIMEOUT)
        .until(() -> wireMockServer.findAll(WireMock.postRequestedFor(WireMock.urlPathEqualTo(REQUEST_PATH))), loggedRequests -> !loggedRequests.isEmpty());
  }

  private static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  private static MtRouterReactiveClientConfig createMtRouterReactiveClientConfig() {
    return new MtRouterReactiveClientConfig(BASE_URL, REQUEST_PATH, ONE_SECOND);
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(true));
    return new WireMockServer(wireMockConfiguration);
  }

  private static void stubMtRouterServer(WireMockServer wiremockServer, ResponseDefinitionBuilder responseDefinitionBuilder) {
    wiremockServer.stubFor(WireMock.post(REQUEST_PATH).willReturn(responseDefinitionBuilder));
  }

  @Test
  void invalidConstructorTest() {
    MtRouterReactiveClientConfig mtRouterReactiveClientConfig = createMtRouterReactiveClientConfig();

    AssertThrows.illegalArgumentException(
        () -> new MtRouterReactiveClient(null, Mockito.mock(WebClient.class)),
        "mtRouterRestClientConfig must not be null");
    AssertThrows.illegalArgumentException(
        () -> new MtRouterReactiveClient(mtRouterReactiveClientConfig, null), "mtRouterWebClient must not be null");
  }

  @Test
  void invalidPostParameterTest() {
    MtRouterReactiveClient mtRouterReactiveClient = new MtRouterReactiveClient(createMtRouterReactiveClientConfig(), Mockito.mock(WebClient.class));

    AssertThrows.illegalArgumentException(() -> mtRouterReactiveClient.post(null), "mtMessage must not be null");
  }

  @Test
  void onTimeoutTest() {
    MtRouterReactiveClientConfig mtRouterReactiveClientConfig = createMtRouterReactiveClientConfig();
    MtMessage mtMessage = TestUtil.createTgwMtMessage("Test".getBytes());

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMtRouterServer(wireMockServer, WireMock.aResponse().withStatus(200).withFixedDelay(2000));
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());

      MtRouterReactiveClient mtRouterReactiveClient = new MtRouterReactiveClient(mtRouterReactiveClientConfig, webClient);
      Boolean isSuccess = mtRouterReactiveClient.post(mtMessage).block(Duration.ofSeconds(3));

      Assertions.assertNotNull(isSuccess);
      Assertions.assertFalse(isSuccess);
    }
  }

  @Test
  void postTest() throws Exception {
    MtRouterReactiveClientConfig mtRouterReactiveClientConfig = createMtRouterReactiveClientConfig();
    MtMessage mtMessage = TestUtil.createTgwMtMessage("Test".getBytes());

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMtRouterServer(wireMockServer, WireMock.ok());
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());

      MtRouterReactiveClient mtRouterReactiveClient = new MtRouterReactiveClient(mtRouterReactiveClientConfig, webClient);
      Boolean isSuccess = mtRouterReactiveClient.post(mtMessage).block();

      Assertions.assertNotNull(isSuccess);
      Assertions.assertTrue(isSuccess);

      LoggedRequest loggedRequest = awaitRequest(wireMockServer).iterator().next();
      Assertions.assertEquals("application/x-protobuf", loggedRequest.getHeader("Content-Type"));
      Assertions.assertEquals(mtMessage, MtMessage.parseFrom(loggedRequest.getBody()));
    }
  }

  @Test
  void responseErrorTest() {
    MtRouterReactiveClientConfig mtRouterReactiveClientConfig = createMtRouterReactiveClientConfig();
    MtMessage mtMessage = TestUtil.createTgwMtMessage("Test".getBytes());

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMtRouterServer(wireMockServer, WireMock.badRequest());
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());

      MtRouterReactiveClient mtRouterReactiveClient = new MtRouterReactiveClient(mtRouterReactiveClientConfig, webClient);
      Boolean isSuccess = mtRouterReactiveClient.post(mtMessage).block();

      Assertions.assertNotNull(isSuccess);
      Assertions.assertFalse(isSuccess);
    }
  }

  private record WireMockServerWrapper(WireMockServer wireMockServer) implements Closeable {
    @Override
    public void close() {
      wireMockServer.stop();
    }
  }
}
