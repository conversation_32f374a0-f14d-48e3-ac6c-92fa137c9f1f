package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.impl.converters.MtMessageOutputConverterBiFunction;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.componentbase.logging.Logging;

class MtPersisterTest {
  private static IntegrationLogParameter captureIntegrationLogParameter(Consumer<IntegrationLogParameter> loggingHelper) {
    ArgumentCaptor<IntegrationLogParameter> captor = ArgumentCaptor.forClass(IntegrationLogParameter.class);
    Mockito.verify(loggingHelper).accept(captor.capture());
    return captor.getValue();
  }

  private static ActiveMtMessageWriter mockActiveMtMessageWriter(Either<InsertionFailure, MtMessageId> mtMessageIdEither,
      Either<InsertionFailure, ActiveMtMessageId> activeMtMessageIdEither) {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(activeMtMessageWriter.insertMtMessage(ArgumentMatchers.any())).thenReturn(mtMessageIdEither);
    Mockito.when(activeMtMessageWriter.insertActiveMtMessage(ArgumentMatchers.any())).thenReturn(activeMtMessageIdEither);
    return activeMtMessageWriter;
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(1), Instant.ofEpochSecond(2), Instant.ofEpochSecond(3));
    return clock;
  }

  private static Consumer<IntegrationLogParameter> mockLoggingHelper() {
    return Mockito.mock(Consumer.class);
  }

  private static void verifyActiveMtMessage(ActiveMtMessage activeMtMessage) {
    Assertions.assertEquals(TestUtil.MT_MESSAGE_ID, activeMtMessage.getMtMessageId());
    Assertions.assertSame(SendSchemaStepId.ofInt(0), activeMtMessage.getSendSchemaStepId());
    Assertions.assertEquals(RetryAttempt.ofShort((short) 0), activeMtMessage.getRetryAttempt());
    Assertions.assertEquals(Instant.ofEpochSecond(1L), activeMtMessage.getTimeout());
  }

  private static void verifyFailedIntegrationLoggingMessage(Consumer<IntegrationLogParameter> loggingHelper) {
    IntegrationLogParameter integrationLogParameter = captureIntegrationLogParameter(loggingHelper);
    Assertions.assertEquals(Logging.Status.FAILED, integrationLogParameter.getStatus());
    Assertions.assertEquals("MT/Failed to persist", integrationLogParameter.getIntegrationMessage().toString());
  }

  private static void verifySuccessfulIntegrationLoggingMessage(Consumer<IntegrationLogParameter> loggingHelper) {
    IntegrationLogParameter integrationLogParameter = captureIntegrationLogParameter(loggingHelper);
    Assertions.assertEquals(Logging.Status.SUCCESS, integrationLogParameter.getStatus());
    Assertions.assertEquals("MT/COMMON_LOW", integrationLogParameter.getIntegrationMessage().toString());
  }

  @Test
  void insertActiveMtMessageExceptionTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.MT_MESSAGE_ID),
        Either.left(new InsertionFailure(InsertionFailureReason.UNKNOWN, new RuntimeException("test"))));
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Clock clock = mockClock();
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtPersister mtPersister = new MtPersister(clock, loggingHelper, mtMessageMetricReporter, MtMessageOutputConverterBiFunction.INSTANCE);

    AssertThrows.exception(() -> mtPersister.insertActiveMtMessage(TestUtil.MT_MESSAGE_ID, activeMtMessageWriter), "test", RuntimeException.class);

    ArgumentCaptor<ActiveMtMessage> activeMtMessageCaptor = ArgumentCaptor.forClass(ActiveMtMessage.class);
    Mockito.verify(activeMtMessageWriter).insertActiveMtMessage(activeMtMessageCaptor.capture());
    verifyActiveMtMessage(activeMtMessageCaptor.getValue());
    Mockito.verify(mtMessageMetricReporter).onActiveMtInsertFailure(Duration.ofSeconds(1));
    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, clock);
  }

  @Test
  void insertActiveMtMessageInvalidParametersTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.MT_MESSAGE_ID), Either.right(TestUtil.ACTIVE_MT_MESSAGE_ID));
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Clock clock = mockClock();
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtPersister mtPersister = new MtPersister(clock, loggingHelper, mtMessageMetricReporter, MtMessageOutputConverterBiFunction.INSTANCE);

    AssertThrows.illegalArgumentException(() -> mtPersister.insertActiveMtMessage(null, activeMtMessageWriter), "mtMessageId must not be null");
    AssertThrows.illegalArgumentException(() -> mtPersister.insertActiveMtMessage(TestUtil.MT_MESSAGE_ID, null), "activeMtMessageWriter must not be null");
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, clock);
  }

  @Test
  void insertActiveMtMessageTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.MT_MESSAGE_ID), Either.right(TestUtil.ACTIVE_MT_MESSAGE_ID));
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Clock clock = mockClock();
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtPersister mtPersister = new MtPersister(clock, loggingHelper, mtMessageMetricReporter, MtMessageOutputConverterBiFunction.INSTANCE);

    mtPersister.insertActiveMtMessage(TestUtil.MT_MESSAGE_ID, activeMtMessageWriter);

    ArgumentCaptor<ActiveMtMessage> activeMtMessageCaptor = ArgumentCaptor.forClass(ActiveMtMessage.class);
    Mockito.verify(activeMtMessageWriter).insertActiveMtMessage(activeMtMessageCaptor.capture());
    verifyActiveMtMessage(activeMtMessageCaptor.getValue());
    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verify(mtMessageMetricReporter).onActiveMtInsertSuccess(Duration.ofSeconds(1));
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, clock, loggingHelper);
  }

  @Test
  void insertMtMessageExceptionTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(
        Either.left(new InsertionFailure(InsertionFailureReason.UNKNOWN, new RuntimeException("test"))), Either.right(TestUtil.ACTIVE_MT_MESSAGE_ID));
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Clock clock = mockClock();
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtPersister mtPersister = new MtPersister(clock, loggingHelper, mtMessageMetricReporter, MtMessageOutputConverterBiFunction.INSTANCE);

    AssertThrows.exception(() -> mtPersister.insertMtMessage(TestUtil.VEHICLE_LOCK_ID, TestUtil.createReceivedMtMessage(), activeMtMessageWriter), "test",
        RuntimeException.class);

    verifyFailedIntegrationLoggingMessage(loggingHelper);
    Mockito.verify(activeMtMessageWriter).insertMtMessage(ArgumentMatchers.any());
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(mtMessageMetricReporter).onMtInsertFailure(Duration.ofSeconds(1));
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, clock);
  }

  @Test
  void insertMtMessageInvalidParametersTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.MT_MESSAGE_ID), Either.right(TestUtil.ACTIVE_MT_MESSAGE_ID));
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Clock clock = mockClock();
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtPersister mtPersister = new MtPersister(clock, loggingHelper, mtMessageMetricReporter, MtMessageOutputConverterBiFunction.INSTANCE);
    ReceivedMtMessage receivedMtMessage = TestUtil.createReceivedMtMessage();

    AssertThrows.illegalArgumentException(() -> mtPersister.insertMtMessage(null, receivedMtMessage, activeMtMessageWriter), "vehicleLockId must not be null");
    AssertThrows.illegalArgumentException(() -> mtPersister.insertMtMessage(TestUtil.VEHICLE_LOCK_ID, null, activeMtMessageWriter),
        "receivedMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> mtPersister.insertMtMessage(TestUtil.VEHICLE_LOCK_ID, receivedMtMessage, null),
        "activeMtMessageWriter must not be null");
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter);
  }

  @Test
  void insertMtMessageTest() {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.MT_MESSAGE_ID), Either.right(TestUtil.ACTIVE_MT_MESSAGE_ID));
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Clock clock = mockClock();
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtPersister mtPersister = new MtPersister(clock, loggingHelper, mtMessageMetricReporter, MtMessageOutputConverterBiFunction.INSTANCE);

    MtMessageId mtMessageId = mtPersister.insertMtMessage(TestUtil.VEHICLE_LOCK_ID, TestUtil.createReceivedMtMessage(), activeMtMessageWriter);

    Assertions.assertSame(TestUtil.MT_MESSAGE_ID, mtMessageId);
    verifySuccessfulIntegrationLoggingMessage(loggingHelper);
    Mockito.verify(activeMtMessageWriter).insertMtMessage(ArgumentMatchers.any());
    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(mtMessageMetricReporter).onMtInsertSuccess(Duration.ofSeconds(1L));
    Mockito.verifyNoMoreInteractions(mtMessageMetricReporter, activeMtMessageWriter, clock);
  }
}
