package com.volvo.tisp.tgwmts.impl.services;

import java.time.Instant;
import java.util.List;
import java.util.Map;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;
import com.volvo.tisp.tgwmts.impl.clients.ConnectionEstablishedRegistrationClient;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

import reactor.core.publisher.Mono;

class ConnectionEstablishedRegistrationSchedulerTest {
  private static final Instant INSTANT_1 = Instant.ofEpochSecond(10);
  private static final Instant INSTANT_2 = Instant.ofEpochSecond(20);
  private static final Vpi VPI_1 = Vpi.ofString("12345678901234567890ABCDEFABCDEF");
  private static final Vpi VPI_2 = Vpi.ofString("12345678901234567890ABCDEFABCDFF");

  @Test
  void reRegisterConnectionEstablishedTest() {
    ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient = Mockito.mock(
        ConnectionEstablishedRegistrationClient.class);
    Mockito.when(connectionEstablishedRegistrationClient.reRegister(Map.of(VPI_1, INSTANT_1, VPI_2, INSTANT_2).entrySet().stream().toList()))
        .thenReturn(Mono.just(true));
    ConnectionEstablishedCacheService connectionEstablishedCacheService = Mockito.mock(ConnectionEstablishedCacheService.class);
    Mockito.when(connectionEstablishedCacheService.getEntries(2)).thenReturn(Map.of(VPI_1, INSTANT_1, VPI_2, INSTANT_2).entrySet().stream().toList());

    ConnectionEstablishedRegistrationScheduler connectionEstablishedRegistrationScheduler = new ConnectionEstablishedRegistrationScheduler(
        connectionEstablishedRegistrationClient, connectionEstablishedCacheService, 2);

    connectionEstablishedRegistrationScheduler.retryConnectionEstablishedRegistration();

    Mockito.verify(connectionEstablishedCacheService).getEntries(2);
    Mockito.verify(connectionEstablishedRegistrationClient).reRegister(Map.of(VPI_1, INSTANT_1, VPI_2, INSTANT_2).entrySet().stream().toList());
    Mockito.verify(connectionEstablishedCacheService).remove(VPI_1);
    Mockito.verify(connectionEstablishedCacheService).remove(VPI_2);
    Mockito.verify(connectionEstablishedCacheService).reportCacheSizeMetric();

    Mockito.verifyNoMoreInteractions(connectionEstablishedCacheService, connectionEstablishedRegistrationClient);
  }

  @Test
  void reRegisterConnectionEstablishedWhenCacheEmptyTest() {
    ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient = Mockito.mock(
        ConnectionEstablishedRegistrationClient.class);
    ConnectionEstablishedCacheService connectionEstablishedCacheService = Mockito.mock(ConnectionEstablishedCacheService.class);
    Mockito.when(connectionEstablishedCacheService.getEntries(2)).thenReturn(List.of());

    ConnectionEstablishedRegistrationScheduler connectionEstablishedRegistrationScheduler = new ConnectionEstablishedRegistrationScheduler(
        connectionEstablishedRegistrationClient, connectionEstablishedCacheService, 2);

    connectionEstablishedRegistrationScheduler.retryConnectionEstablishedRegistration();

    Mockito.verify(connectionEstablishedCacheService).getEntries(2);

    Mockito.verifyNoMoreInteractions(connectionEstablishedCacheService, connectionEstablishedRegistrationClient);
  }
}
