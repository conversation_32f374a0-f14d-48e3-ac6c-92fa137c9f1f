package com.volvo.tisp.tgwmts.impl.services;

import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.impl.clients.ConnectionEstablishedRegistrationClient;
import com.volvo.tisp.tgwmts.impl.clients.MtRouterRestClient;
import com.volvo.tisp.tgwmts.impl.clients.MtSoftcarRestClient;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.IdentifiedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.TransmissionRouterMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;

import reactor.core.publisher.Mono;

class TransmissionRouterTest {

  private static ScheduledActiveMtMessage createScheduledActiveMtMessage(SendSchemaStep sendSchemaStep) {
    return new ScheduledActiveMtMessage(TestUtil.createIdentifiedActiveMtMessage(), sendSchemaStep);
  }

  private static ScheduledActiveMtMessage createSoftcarScheduledActiveMtMessage() {
    PersistedDeviceInfo persistedDeviceInfo = TestUtil.createPersistedDeviceInfoBuilder()
        .setDeviceInfo(TestUtil.createDeviceInfoBuilder()
            .setSimInfo(TestUtil.createSimInfoBuilder().setMobileNetworkOperator(TransmissionRouter.SOFTCAR_MOBILE_NETWORK_OPERATOR).build())
            .build())
        .build();

    return new ScheduledActiveMtMessage(new IdentifiedActiveMtMessage(TestUtil.createJoinedActiveMtMessage(), persistedDeviceInfo),
        TestUtil.createUpdSendSchemaStep());
  }

  private static TransmissionRouterContext createTransmissionRouterContext(MtSoftcarRestClient mtSoftcarRestClient) {
    ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient = Mockito.mock(
        ConnectionEstablishedRegistrationClient.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtRouterRestClient mtRouterRestClient = Mockito.mock(MtRouterRestClient.class);

    return new TransmissionRouterContext(connectionEstablishedRegistrationClient, loggingHelper, mtRouterRestClient, mtSoftcarRestClient);
  }

  private static Consumer<IntegrationLogParameter> mockLoggingHelper() {
    return Mockito.mock(Consumer.class);
  }

  private static Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> mockSrpEncoderFunction(EncodedActiveMtMessage encodedActiveMtMessage,
      ScheduledActiveMtMessage scheduledActiveMtMessage) {
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction = TestUtil.mockFunction();
    Mockito.when(srpEncoderFunction.apply(scheduledActiveMtMessage)).thenReturn(encodedActiveMtMessage);
    return srpEncoderFunction;
  }

  @Test
  void processScheduledActiveMtMessageSatMtRouterTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = createScheduledActiveMtMessage(TestUtil.createSatSendSchemaStep());
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction = mockSrpEncoderFunction(encodedActiveMtMessage, scheduledActiveMtMessage);

    MtSoftcarRestClient mtSoftcarRestClient = Mockito.mock(MtSoftcarRestClient.class);
    Mockito.when(mtSoftcarRestClient.sendMtMessage(Mockito.any())).thenReturn(Mono.just(true));

    TransmissionRouterContext transmissionRouterContext = createTransmissionRouterContext(mtSoftcarRestClient);

    TransmissionRouterMetricReporter transmissionRouterMetricReporter = Mockito.mock(TransmissionRouterMetricReporter.class);

    TransmissionRouter transmissionRouter = new TransmissionRouter(srpEncoderFunction, transmissionRouterContext, transmissionRouterMetricReporter);
    transmissionRouter.processScheduledActiveMtMessage(scheduledActiveMtMessage);

    Mockito.verify(srpEncoderFunction).apply(scheduledActiveMtMessage);
    Mockito.verify(transmissionRouterContext.getMtRouterRestClient()).sendMtMessage(encodedActiveMtMessage);
    Mockito.verify(transmissionRouterMetricReporter).onRoute(SendSchemaStepType.SAT);
    Mockito.verify(transmissionRouterContext.getLoggingHelper()).accept(ArgumentMatchers.any(IntegrationLogParameter.class));
    Mockito.verifyNoMoreInteractions(transmissionRouterContext.getMtRouterRestClient(), transmissionRouterContext.getLoggingHelper(),
        transmissionRouterMetricReporter);
    Mockito.verifyNoInteractions(transmissionRouterContext.getMtSoftcarRestClient(), transmissionRouterContext.getConnectionEstablishedWebClient());
  }

  @Test
  void processScheduledActiveMtMessageSmsMtRouterTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = createScheduledActiveMtMessage(TestUtil.createSmsSendSchemaStep());
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction = mockSrpEncoderFunction(encodedActiveMtMessage, scheduledActiveMtMessage);

    MtSoftcarRestClient mtSoftcarRestClient = Mockito.mock(MtSoftcarRestClient.class);
    Mockito.when(mtSoftcarRestClient.sendMtMessage(Mockito.any())).thenReturn(Mono.just(true));

    TransmissionRouterContext transmissionRouterContext = createTransmissionRouterContext(mtSoftcarRestClient);
    TransmissionRouterMetricReporter transmissionRouterMetricReporter = Mockito.mock(TransmissionRouterMetricReporter.class);

    TransmissionRouter transmissionRouter = new TransmissionRouter(srpEncoderFunction, transmissionRouterContext, transmissionRouterMetricReporter);
    transmissionRouter.processScheduledActiveMtMessage(scheduledActiveMtMessage);

    Mockito.verify(srpEncoderFunction).apply(scheduledActiveMtMessage);
    Mockito.verify(transmissionRouterContext.getMtRouterRestClient()).sendMtMessage(encodedActiveMtMessage);
    Mockito.verify(transmissionRouterMetricReporter).onRoute(SendSchemaStepType.SMS);
    Mockito.verify(transmissionRouterContext.getLoggingHelper()).accept(ArgumentMatchers.any(IntegrationLogParameter.class));
    Mockito.verifyNoMoreInteractions(transmissionRouterContext.getLoggingHelper(), transmissionRouterMetricReporter);
    Mockito.verifyNoInteractions(transmissionRouterContext.getMtSoftcarRestClient(), transmissionRouterContext.getConnectionEstablishedWebClient());
  }

  @Test
  void processScheduledActiveMtMessageSoftcarTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = createSoftcarScheduledActiveMtMessage();
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction = mockSrpEncoderFunction(encodedActiveMtMessage, scheduledActiveMtMessage);

    MtSoftcarRestClient mtSoftcarRestClient = Mockito.mock(MtSoftcarRestClient.class);
    Mockito.when(mtSoftcarRestClient.sendMtMessage(Mockito.any())).thenReturn(Mono.just(true));

    TransmissionRouterContext transmissionRouterContext = createTransmissionRouterContext(mtSoftcarRestClient);

    TransmissionRouterMetricReporter transmissionRouterMetricReporter = Mockito.mock(TransmissionRouterMetricReporter.class);

    TransmissionRouter transmissionRouter = new TransmissionRouter(srpEncoderFunction, transmissionRouterContext, transmissionRouterMetricReporter);

    transmissionRouter.processScheduledActiveMtMessage(scheduledActiveMtMessage);

    Mockito.verify(srpEncoderFunction).apply(scheduledActiveMtMessage);
    Mockito.verify(transmissionRouterContext.getMtSoftcarRestClient()).sendMtMessage(encodedActiveMtMessage);
    Mockito.verify(transmissionRouterMetricReporter).onSoftcar();
    Mockito.verifyNoMoreInteractions(transmissionRouterContext.getMtSoftcarRestClient(), transmissionRouterMetricReporter);
    Mockito.verifyNoInteractions(transmissionRouterContext.getConnectionEstablishedWebClient());
  }

  @Test
  void processScheduledActiveMtMessageUdpMtRouterTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = createScheduledActiveMtMessage(TestUtil.createUpdSendSchemaStep());
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction = mockSrpEncoderFunction(encodedActiveMtMessage, scheduledActiveMtMessage);

    MtSoftcarRestClient mtSoftcarRestClient = Mockito.mock(MtSoftcarRestClient.class);
    Mockito.when(mtSoftcarRestClient.sendMtMessage(Mockito.any())).thenReturn(Mono.just(true));

    TransmissionRouterContext transmissionRouterContext = createTransmissionRouterContext(mtSoftcarRestClient);
    TransmissionRouterMetricReporter transmissionRouterMetricReporter = Mockito.mock(TransmissionRouterMetricReporter.class);

    TransmissionRouter transmissionRouter = new TransmissionRouter(srpEncoderFunction, transmissionRouterContext, transmissionRouterMetricReporter);
    transmissionRouter.processScheduledActiveMtMessage(scheduledActiveMtMessage);

    Mockito.verify(srpEncoderFunction).apply(scheduledActiveMtMessage);
    Mockito.verify(transmissionRouterContext.getMtRouterRestClient()).sendMtMessage(encodedActiveMtMessage);
    Mockito.verify(transmissionRouterMetricReporter).onRoute(SendSchemaStepType.UDP);
    Mockito.verifyNoMoreInteractions(transmissionRouterContext.getMtRouterRestClient(), transmissionRouterMetricReporter);
    Mockito.verifyNoInteractions(transmissionRouterContext.getMtSoftcarRestClient(), transmissionRouterContext.getConnectionEstablishedWebClient());
  }

  @Test
  void processScheduledActiveMtMessageWaitTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = createScheduledActiveMtMessage(TestUtil.createWaitSendSchemaStep());
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction = mockSrpEncoderFunction(TestUtil.createEncodedActiveMtMessage(),
        scheduledActiveMtMessage);
    MtSoftcarRestClient mtSoftcarRestClient = Mockito.mock(MtSoftcarRestClient.class);
    Mockito.when(mtSoftcarRestClient.sendMtMessage(Mockito.any())).thenReturn(Mono.just(true));

    TransmissionRouterContext transmissionRouterContext = createTransmissionRouterContext(mtSoftcarRestClient);
    TransmissionRouterMetricReporter transmissionRouterMetricReporter = Mockito.mock(TransmissionRouterMetricReporter.class);

    TransmissionRouter transmissionRouter = new TransmissionRouter(srpEncoderFunction, transmissionRouterContext, transmissionRouterMetricReporter);
    transmissionRouter.processScheduledActiveMtMessage(scheduledActiveMtMessage);

    Mockito.verify(transmissionRouterContext.getConnectionEstablishedWebClient()).registerConnectionEstablished(scheduledActiveMtMessage);
    Mockito.verify(transmissionRouterMetricReporter).onWait();
    Mockito.verifyNoMoreInteractions(transmissionRouterContext.getConnectionEstablishedWebClient(), transmissionRouterMetricReporter);
    Mockito.verifyNoInteractions(transmissionRouterContext.getMtSoftcarRestClient(), srpEncoderFunction);
  }

  @Test
  void processScheduledActiveMtMessageWifiMtRouterTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = createScheduledActiveMtMessage(TestUtil.createWifiSendSchemaStep());
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction = mockSrpEncoderFunction(encodedActiveMtMessage, scheduledActiveMtMessage);

    MtSoftcarRestClient mtSoftcarRestClient = Mockito.mock(MtSoftcarRestClient.class);
    Mockito.when(mtSoftcarRestClient.sendMtMessage(Mockito.any())).thenReturn(Mono.just(true));

    TransmissionRouterContext transmissionRouterContext = createTransmissionRouterContext(mtSoftcarRestClient);

    TransmissionRouterMetricReporter transmissionRouterMetricReporter = Mockito.mock(TransmissionRouterMetricReporter.class);

    TransmissionRouter transmissionRouter = new TransmissionRouter(srpEncoderFunction, transmissionRouterContext, transmissionRouterMetricReporter);
    transmissionRouter.processScheduledActiveMtMessage(scheduledActiveMtMessage);

    Mockito.verify(srpEncoderFunction).apply(scheduledActiveMtMessage);
    Mockito.verify(transmissionRouterContext.getMtRouterRestClient()).sendMtMessage(encodedActiveMtMessage);
    Mockito.verify(transmissionRouterMetricReporter).onRoute(SendSchemaStepType.WIFI);
    Mockito.verify(transmissionRouterContext.getLoggingHelper()).accept(ArgumentMatchers.any(IntegrationLogParameter.class));
    Mockito.verifyNoMoreInteractions(transmissionRouterContext.getMtRouterRestClient(), transmissionRouterContext.getLoggingHelper(),
        transmissionRouterMetricReporter);
    Mockito.verifyNoInteractions(transmissionRouterContext.getMtSoftcarRestClient(), transmissionRouterContext.getConnectionEstablishedWebClient());
  }
}
