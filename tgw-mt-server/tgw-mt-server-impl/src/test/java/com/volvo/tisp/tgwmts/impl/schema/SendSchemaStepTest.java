package com.volvo.tisp.tgwmts.impl.schema;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class SendSchemaStepTest {
  private static final SendSchemaStepId SEND_SCHEMA_STEP_ID = SendSchemaStepId.ofInt(1);
  private static final Duration WAIT_DURATION = Duration.ofHours(1);

  @Test
  void equalsAndHashcodeTest() {
    SendSchemaStep sendSchemaStep = SendSchemaStep.forUdp(SEND_SCHEMA_STEP_ID, WAIT_DURATION);
    AssertUtils.assertEqualsAndHashCode(sendSchemaStep, SendSchemaStep.forUdp(SEND_SCHEMA_STEP_ID, WAIT_DURATION));

    Assertions.assertNotEquals(sendSchemaStep, SendSchemaStep.forUdp(SendSchemaStepId.ofInt(2), WAIT_DURATION));
    Assertions.assertNotEquals(sendSchemaStep, SendSchemaStep.forSms(SEND_SCHEMA_STEP_ID, WAIT_DURATION));
    Assertions.assertNotEquals(sendSchemaStep, SendSchemaStep.forUdp(SEND_SCHEMA_STEP_ID, Duration.ofHours(2)));
  }

  @Test
  void getSendSchemaStepIdTest() {
    Assertions.assertSame(TestUtil.SEND_SCHEMA_STEP_ID, TestUtil.createWaitSendSchemaStep().getSendSchemaStepId());
  }

  @Test
  void getSendSchemaTypeTest() {
    Assertions.assertSame(SendSchemaStepType.WAIT, TestUtil.createWaitSendSchemaStep().getSendSchemaStepType());
  }

  @Test
  void getWaitDurationTest() {
    Assertions.assertSame(TestUtil.SEND_SCHEMA_STEP_WAIT_DURATION, TestUtil.createWaitSendSchemaStep().getWaitDuration());
  }

  @Test
  void invalidForSatTest() {
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWifi(null, WAIT_DURATION), "sendSchemaStepId must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWifi(SEND_SCHEMA_STEP_ID, null), "waitDuration must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWifi(SEND_SCHEMA_STEP_ID, Duration.ZERO),
        "waitDuration must be positive: PT0S");
  }

  @Test
  void invalidForSmsTest() {
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forSms(null, WAIT_DURATION), "sendSchemaStepId must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forSms(SEND_SCHEMA_STEP_ID, null), "waitDuration must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forSms(SEND_SCHEMA_STEP_ID, Duration.ZERO),
        "waitDuration must be positive: PT0S");
  }

  @Test
  void invalidForUdpTest() {
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forUdp(null, WAIT_DURATION), "sendSchemaStepId must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forUdp(SEND_SCHEMA_STEP_ID, null), "waitDuration must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forUdp(SEND_SCHEMA_STEP_ID, Duration.ZERO),
        "waitDuration must be positive: PT0S");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forSms(SEND_SCHEMA_STEP_ID, Duration.ZERO),
        "waitDuration must be positive: PT0S");
  }

  @Test
  void invalidForWaitTest() {
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWait(SEND_SCHEMA_STEP_ID, null), "waitDuration must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWait(null, WAIT_DURATION), "sendSchemaStepId must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWait(SEND_SCHEMA_STEP_ID, Duration.ZERO), "waitDuration must be positive: PT0S");
  }

  @Test
  void invalidForWifiTest() {
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWifi(null, WAIT_DURATION), "sendSchemaStepId must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWifi(SEND_SCHEMA_STEP_ID, null), "waitDuration must not be null");
    AssertThrows.illegalArgumentException(() -> SendSchemaStep.forWifi(SEND_SCHEMA_STEP_ID, Duration.ZERO),
        "waitDuration must be positive: PT0S");
  }

  @Test
  void toStringTest() {
    Assertions.assertEquals("sendSchemaStepId=1, sendSchemaStepType=UDP, waitDuration=PT168H", TestUtil.createUpdSendSchemaStep().toString());
  }
}
