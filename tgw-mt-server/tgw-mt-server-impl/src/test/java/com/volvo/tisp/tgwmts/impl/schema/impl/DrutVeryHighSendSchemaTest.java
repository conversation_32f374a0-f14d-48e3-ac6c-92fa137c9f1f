package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DrutVeryHighSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofMinutes(10), DrutVeryHighSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, DrutVeryHighSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> DrutVeryHighSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.DRUT_VERY_HIGH, DrutVeryHighSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(DrutVeryHighSendSchema.INSTANCE, 1, Duration.ofSeconds(30));
    VerificationUtil.verifySendSchemaStepUdp(DrutVeryHighSendSchema.INSTANCE, 2, Duration.ofSeconds(30));
    VerificationUtil.verifySendSchemaStepSms(DrutVeryHighSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepWait(DrutVeryHighSendSchema.INSTANCE, 4, Duration.ofMinutes(10));

    Assertions.assertTrue(DrutVeryHighSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
  }
}
