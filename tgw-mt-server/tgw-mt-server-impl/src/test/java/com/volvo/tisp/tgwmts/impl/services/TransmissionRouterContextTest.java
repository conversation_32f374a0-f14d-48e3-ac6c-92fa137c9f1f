package com.volvo.tisp.tgwmts.impl.services;

import java.util.function.Consumer;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.impl.clients.ConnectionEstablishedRegistrationClient;
import com.volvo.tisp.tgwmts.impl.clients.MtRouterRestClient;
import com.volvo.tisp.tgwmts.impl.clients.MtSoftcarRestClient;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;

class TransmissionRouterContextTest {
  private static Consumer<IntegrationLogParameter> mockLoggingHelper() {
    return Mockito.mock(Consumer.class);
  }

  @Test
  void gettersTest() {
    ConnectionEstablishedRegistrationClient connectionEstablishedRestClient = Mockito.mock(ConnectionEstablishedRegistrationClient.class);
    Consumer<IntegrationLogParameter> loggingHelper = mockLoggingHelper();
    MtRouterRestClient mtRouterRestClient = Mockito.mock(MtRouterRestClient.class);
    MtSoftcarRestClient mtSoftcarRestClient = Mockito.mock(MtSoftcarRestClient.class);

    TransmissionRouterContext transmissionRouterContext = new TransmissionRouterContext(connectionEstablishedRestClient, loggingHelper, mtRouterRestClient,
        mtSoftcarRestClient);

    Assertions.assertSame(connectionEstablishedRestClient, transmissionRouterContext.getConnectionEstablishedWebClient());
    Assertions.assertSame(loggingHelper, transmissionRouterContext.getLoggingHelper());
    Assertions.assertSame(mtRouterRestClient, transmissionRouterContext.getMtRouterRestClient());
    Assertions.assertSame(mtSoftcarRestClient, transmissionRouterContext.getMtSoftcarRestClient());
  }
}
