package com.volvo.tisp.tgwmts.impl.integration.logging;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.wirelesscar.componentbase.logging.Logging;

class IntegrationLogParameterTest {
  @Test
  void getDirectionTest() {
    Assertions.assertSame(Logging.Direction.SERVER_IN, TestUtil.createIntegrationLogParameter().getDirection());
  }

  @Test
  void getIntegrationMessageTest() {
    Assertions.assertEquals("MT/COMMON_LOW", TestUtil.createIntegrationLogParameter().getIntegrationMessage().toString());
  }

  @Test
  void getMetaDataTest() {
    Assertions.assertEquals("{serviceId=5}", TestUtil.createIntegrationLogParameter().getMetaData().toString());
  }

  @Test
  void getStatusTest() {
    Assertions.assertSame(Logging.Status.SUCCESS, TestUtil.createIntegrationLogParameter().getStatus());
  }

  @Test
  void getVehicleDetailTest() {
    Assertions.assertEquals(TestUtil.VPI, TestUtil.createIntegrationLogParameter().getVehicleDetail().getVpi());
  }

  @Test
  void testToStringTest() {
    Assertions.assertEquals(
        "status=SUCCESS, vehicleDetail={handle=12345, ipv4Address={*******, msisdn=+3245678, vpi=12345678901234567890ABCDEFABCDEF}, integrationMessage=MT/COMMON_LOW, metadata={serviceId=5}",
        TestUtil.createIntegrationLogParameter().toString());
  }
}
