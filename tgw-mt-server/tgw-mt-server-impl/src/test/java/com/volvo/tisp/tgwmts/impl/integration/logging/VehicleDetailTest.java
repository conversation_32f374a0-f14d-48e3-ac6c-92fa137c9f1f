package com.volvo.tisp.tgwmts.impl.integration.logging;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgw.device.info.database.model.Handle;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VehicleDetailTest {
  private static final Handle HANDLE = Handle.ofLong(42);

  private static VehicleDetail createVehicleDetail() {
    return VehicleDetail.create(HANDLE, TestUtil.IPV4_ADDRESS, TestUtil.MSISDN, TestUtil.VPI);
  }

  @Test
  void getHandleTest() {
    Assertions.assertEquals(HANDLE, createVehicleDetail().getHandle().orElseThrow());
  }

  @Test
  void getIpv4Address() {
    Assertions.assertEquals(TestUtil.IPV4_ADDRESS, createVehicleDetail().getIpv4Address().orElseThrow());
  }

  @Test
  void getMsisdn() {
    Assertions.assertEquals(TestUtil.MSISDN, createVehicleDetail().getMsisdn().orElseThrow());
  }

  @Test
  void getVpi() {
    Assertions.assertEquals(TestUtil.VPI, createVehicleDetail().getVpi());
  }

  @Test
  void invalidCreateTest() {
    AssertThrows.illegalArgumentException(() -> VehicleDetail.create(null), "vpi must not be null");

    AssertThrows.illegalArgumentException(() -> VehicleDetail.create(null, TestUtil.IPV4_ADDRESS, TestUtil.MSISDN, TestUtil.VPI), "handle must not be null");
    AssertThrows.illegalArgumentException(() -> VehicleDetail.create(HANDLE, null, TestUtil.MSISDN, TestUtil.VPI), "ipv4Address must not be null");
    AssertThrows.illegalArgumentException(() -> VehicleDetail.create(HANDLE, TestUtil.IPV4_ADDRESS, null, TestUtil.VPI), "msisdn must not be null");
    AssertThrows.illegalArgumentException(() -> VehicleDetail.create(HANDLE, TestUtil.IPV4_ADDRESS, TestUtil.MSISDN, null), "vpi must not be null");

  }

  @Test
  void testToString() {
    Assertions.assertEquals("handle=42, ipv4Address={*******, msisdn=+3245678, vpi=12345678901234567890ABCDEFABCDEF", createVehicleDetail().toString());
  }
}