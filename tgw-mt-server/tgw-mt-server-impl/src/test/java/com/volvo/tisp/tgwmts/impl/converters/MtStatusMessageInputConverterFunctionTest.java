package com.volvo.tisp.tgwmts.impl.converters;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.tgwmts.impl.model.MtStatusClient;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.model.TransportType;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtStatusMessageInputConverterFunctionTest {
  private static void verifyRightStatus(ReceivedMtStatus receivedMtStatus, Status status) {
    Assertions.assertEquals(TestUtil.createReceivedMtStatusMessage(receivedMtStatus),
        MtStatusMessageInputConverterFunction.INSTANCE.apply(TestUtil.createMtStatusBuilder().setStatus(status).build(), MtStatusClient.VWTP_INIT).getRight());
  }

  private static void verifyRightTransport(Transport transport, MtStatusClient mtStatusClient, TransportType expectedTransportType) {
    Assertions.assertEquals(expectedTransportType,
        MtStatusMessageInputConverterFunction.INSTANCE.apply(TestUtil.createMtStatusBuilder().setTransport(transport).build(), mtStatusClient)
            .getRight()
            .transportType());
  }

  @Test
  void applyTest() {
    AssertThrows.illegalArgumentException(() -> MtStatusMessageInputConverterFunction.INSTANCE.apply(null, MtStatusClient.VWTP_INIT),
        "mtStatus must not be null");
    AssertThrows.illegalArgumentException(
        () -> MtStatusMessageInputConverterFunction.INSTANCE.apply(MtStatus.getDefaultInstance(), null),
        "mtStatusClient must not be null");

    verifyRightStatus(ReceivedMtStatus.CANCELED, Status.CANCELED);
    verifyRightStatus(ReceivedMtStatus.DELIVERED, Status.DELIVERED);
    verifyRightStatus(ReceivedMtStatus.REJECTED, Status.REJECTED);
    verifyRightStatus(ReceivedMtStatus.THROTTLED, Status.THROTTLED);
    verifyRightStatus(ReceivedMtStatus.SERVICE_UNSUPPORTED, Status.SERVICE_UNSUPPORTED);

    verifyRightTransport(Transport.UDP, MtStatusClient.VWTP_INIT, TransportType.UDP);
    verifyRightTransport(Transport.SMS, MtStatusClient.VWTP_INIT, TransportType.SMS);
    verifyRightTransport(Transport.SAT, MtStatusClient.VWTP_INIT, TransportType.SAT);
    verifyRightTransport(Transport.VPN, MtStatusClient.VWTP_INIT, TransportType.WIFI);
    verifyRightTransport(Transport.UDP, MtStatusClient.TUCS, TransportType.SOFTCAR);
  }

  @Test
  void exceptionTest() {
    Assertions.assertEquals("invalid activeMtMessageId: ",
        MtStatusMessageInputConverterFunction.INSTANCE.apply(MtStatus.getDefaultInstance(), MtStatusClient.VWTP_INIT).getLeft().getMessage());
  }
}
