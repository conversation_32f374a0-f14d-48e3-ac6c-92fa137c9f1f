package com.volvo.tisp.tgwmts.impl.clients;

import java.io.Closeable;
import java.net.URI;
import java.time.Duration;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.web.reactive.function.client.WebClient;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.volvo.tisp.tce.proto.MtMessage;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtSoftCarReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtSoftcarRestClientTest {
  public static final URI BASE_URL = URI.create("http://tucs");
  private static final Duration ONE_SECOND = Duration.ofSeconds(1);
  private static final String REQUEST_PATH = "/tucs/tgw/mtmessage";

  private static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  private static MtSoftCarReactiveClientProperties createMtSoftCarReactiveClientProperties() {
    return new MtSoftCarReactiveClientProperties(BASE_URL, REQUEST_PATH, ONE_SECOND);
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(true));
    return new WireMockServer(wireMockConfiguration);
  }

  private static void stubMtSoftcarServer(WireMockServer wiremockServer, ResponseDefinitionBuilder responseDefinitionBuilder) {
    wiremockServer.stubFor(WireMock.post(REQUEST_PATH).willReturn(responseDefinitionBuilder));
  }

  @Test
  void invalidSendMtMessageParameterTest() {
    Function<EncodedActiveMtMessage, MtMessage> mtMessageProtobufOutputConverterFunction = TestUtil.mockFunction();
    MtSoftcarRestClient mtSoftcarRestClient = new MtSoftcarRestClient(Mockito.mock(MtSoftCarReactiveClientProperties.class), Mockito.mock(WebClient.class),
        mtMessageProtobufOutputConverterFunction);

    AssertThrows.illegalArgumentException(() -> mtSoftcarRestClient.sendMtMessage(null), "encodedActiveMtMessage must not be null");
  }

  @Test
  void responseErrorTest() {
    MtSoftCarReactiveClientProperties mtRouterReactiveClientConfig = createMtSoftCarReactiveClientProperties();

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMtSoftcarServer(wireMockServer, WireMock.badRequest());

      Function<EncodedActiveMtMessage, MtMessage> mtMessageProtobufOutputConverterFunction = TestUtil.mockFunction();
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());
      MtSoftcarRestClient mtSoftcarRestClient = new MtSoftcarRestClient(mtRouterReactiveClientConfig, webClient, mtMessageProtobufOutputConverterFunction);
      EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();

      MtMessage mtMessage = MtMessage.newBuilder().setVehicleID(TestUtil.VPI.toString()).build();
      Mockito.when(mtMessageProtobufOutputConverterFunction.apply(encodedActiveMtMessage)).thenReturn(mtMessage);

      boolean isSuccess = mtSoftcarRestClient.sendMtMessage(encodedActiveMtMessage).block();

      Assertions.assertFalse(isSuccess);

      Mockito.verify(mtMessageProtobufOutputConverterFunction).apply(encodedActiveMtMessage);
      Mockito.verifyNoMoreInteractions(mtMessageProtobufOutputConverterFunction);

    }
  }

  @Test
  void sendMtMessageTest() throws Exception {
    MtSoftCarReactiveClientProperties mtRouterReactiveClientConfig = createMtSoftCarReactiveClientProperties();

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMtSoftcarServer(wireMockServer, WireMock.ok());

      Function<EncodedActiveMtMessage, MtMessage> mtMessageProtobufOutputConverterFunction = TestUtil.mockFunction();
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());
      MtSoftcarRestClient mtSoftcarRestClient = new MtSoftcarRestClient(mtRouterReactiveClientConfig, webClient, mtMessageProtobufOutputConverterFunction);
      EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();

      MtMessage mtMessage = MtMessage.newBuilder().setVehicleID(TestUtil.VPI.toString()).build();
      Mockito.when(mtMessageProtobufOutputConverterFunction.apply(encodedActiveMtMessage)).thenReturn(mtMessage);

      boolean isSuccess = mtSoftcarRestClient.sendMtMessage(encodedActiveMtMessage).block();
      Assertions.assertTrue(isSuccess);

      Mockito.verify(mtMessageProtobufOutputConverterFunction).apply(encodedActiveMtMessage);
      Mockito.verifyNoMoreInteractions(mtMessageProtobufOutputConverterFunction);
    }
  }

  @Test
  void timeoutTest() {
    MtSoftCarReactiveClientProperties mtRouterReactiveClientConfig = createMtSoftCarReactiveClientProperties();

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMtSoftcarServer(wireMockServer, WireMock.aResponse().withStatus(200).withFixedDelay(2000));

      Function<EncodedActiveMtMessage, MtMessage> mtMessageProtobufOutputConverterFunction = TestUtil.mockFunction();
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());
      MtSoftcarRestClient mtSoftcarRestClient = new MtSoftcarRestClient(mtRouterReactiveClientConfig, webClient, mtMessageProtobufOutputConverterFunction);
      EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();

      MtMessage mtMessage = MtMessage.newBuilder().setVehicleID(TestUtil.VPI.toString()).build();
      Mockito.when(mtMessageProtobufOutputConverterFunction.apply(encodedActiveMtMessage)).thenReturn(mtMessage);

      boolean isSuccess = mtSoftcarRestClient.sendMtMessage(encodedActiveMtMessage).block(Duration.ofSeconds(3));

      Assertions.assertFalse(isSuccess);

      Mockito.verify(mtMessageProtobufOutputConverterFunction).apply(encodedActiveMtMessage);
      Mockito.verifyNoMoreInteractions(mtMessageProtobufOutputConverterFunction);

    }
  }

  private record WireMockServerWrapper(WireMockServer wireMockServer) implements Closeable {
    @Override
    public void close() {
      wireMockServer.stop();
    }
  }

}
