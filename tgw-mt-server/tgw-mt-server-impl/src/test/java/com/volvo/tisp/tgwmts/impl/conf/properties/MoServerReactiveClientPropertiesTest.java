package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.net.URI;
import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MoServerReactiveClientPropertiesTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(
        () -> new MoServerReactiveClientProperties(URI.create("uri"), "/register", "/register", Duration.ofSeconds(1), Duration.ofSeconds(-10), true),
        "requestTimeout must not be negative");
    AssertThrows.illegalArgumentException(
        () -> new MoServerReactiveClientProperties(URI.create("uri"), "/register", "/register", Duration.ofSeconds(-10), Duration.ofSeconds(1), false),
        "connectionTimeout must not be negative");
  }
}