package com.volvo.tisp.tgwmts.impl.model;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgw.device.info.database.model.SrpLevel;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class EncodedActiveMtMessageTest {
  private static final ScheduledActiveMtMessage SCHEDULED_ACTIVE_MT_MESSAGE = TestUtil.createScheduledActiveMtMessage();
  private static final ServiceRoutingPduWrapper SRP_12_SERVICE_ROUTING_PDU_WRAPPER = TestUtil.createSrp12ServiceRoutingPduWrapper();

  @Test
  void equalsAndHashcodeTest() {
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();
    AssertUtils.assertEqualsAndHashCode(encodedActiveMtMessage, TestUtil.createEncodedActiveMtMessage());

    EncodedActiveMtMessage encodedActiveMtMessage2 = new EncodedActiveMtMessage(SCHEDULED_ACTIVE_MT_MESSAGE,
        ServiceRoutingPduWrapper.ofSrp10Wrappers(List.of()));

    Assertions.assertNotEquals(encodedActiveMtMessage, encodedActiveMtMessage2);

    EncodedActiveMtMessage encodedActiveMtMessage3 = new EncodedActiveMtMessage(
        TestUtil.createScheduledActiveMtMessage(SrpLevel.SRP_11, TestUtil.createSrpOption()), SRP_12_SERVICE_ROUTING_PDU_WRAPPER);

    Assertions.assertNotEquals(encodedActiveMtMessage, encodedActiveMtMessage3);
  }

  @Test
  void getScheduledActiveMtMessageTest() {
    EncodedActiveMtMessage encodedActiveMtMessage = new EncodedActiveMtMessage(SCHEDULED_ACTIVE_MT_MESSAGE, SRP_12_SERVICE_ROUTING_PDU_WRAPPER);

    Assertions.assertSame(SCHEDULED_ACTIVE_MT_MESSAGE, encodedActiveMtMessage.scheduledActiveMtMessage());
  }

  @Test
  void getServiceRoutingPduWrapperTest() {
    EncodedActiveMtMessage encodedActiveMtMessage = new EncodedActiveMtMessage(SCHEDULED_ACTIVE_MT_MESSAGE, SRP_12_SERVICE_ROUTING_PDU_WRAPPER);

    Assertions.assertSame(SRP_12_SERVICE_ROUTING_PDU_WRAPPER, encodedActiveMtMessage.serviceRoutingPduWrapper());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new EncodedActiveMtMessage(null, SRP_12_SERVICE_ROUTING_PDU_WRAPPER),
        "scheduledActiveMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> new EncodedActiveMtMessage(SCHEDULED_ACTIVE_MT_MESSAGE, null), "serviceRoutingPduWrapper must not be null");
  }
}
