package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VLinkHighSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), VLinkHighSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, VLinkHighSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> VLinkHighSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.VLINK_HIGH, VLinkHighSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(VLinkHighSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(VLinkHighSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(VLinkHighSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepSat(VLinkHighSendSchema.INSTANCE, 4);
    VerificationUtil.verifySendSchemaStepWait(VLinkHighSendSchema.INSTANCE, 5, Duration.ofDays(7));

    Assertions.assertTrue(VLinkHighSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
  }
}
