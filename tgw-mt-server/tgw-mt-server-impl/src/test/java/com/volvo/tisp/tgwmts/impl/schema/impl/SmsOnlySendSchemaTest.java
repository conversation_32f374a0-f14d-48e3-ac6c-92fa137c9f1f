package com.volvo.tisp.tgwmts.impl.schema.impl;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SmsOnlySendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(SendSchemaStep.DEFAULT_SMS_TIMEOUT, SmsOnlySendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(1, SmsOnlySendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> SmsOnlySendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.SMS_ONLY, SmsOnlySendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepSms(SmsOnlySendSchema.INSTANCE, 1);

    Assertions.assertTrue(SmsOnlySendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(2)).isEmpty());
  }
}
