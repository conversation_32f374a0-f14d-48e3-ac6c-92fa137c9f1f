package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class UptimeVeryHighSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofMinutes(1), UptimeVeryHighSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, UptimeVeryHighSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> UptimeVeryHighSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.UPTIME_VERY_HIGH, UptimeVeryHighSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(UptimeVeryHighSendSchema.INSTANCE, 1, Duration.ofSeconds(20));
    VerificationUtil.verifySendSchemaStepUdp(UptimeVeryHighSendSchema.INSTANCE, 2, Duration.ofSeconds(20));
    VerificationUtil.verifySendSchemaStepSms(UptimeVeryHighSendSchema.INSTANCE, 3, Duration.ofSeconds(40));
    VerificationUtil.verifySendSchemaStepWait(UptimeVeryHighSendSchema.INSTANCE, 4, Duration.ofMinutes(1));

    Assertions.assertTrue(UptimeVeryHighSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
  }
}
