package com.volvo.tisp.tgwmts.impl.scheduler;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class MtSchedulerTest {
  @Test
  void closeTest() {
    MtSchedulerContext mtSchedulerContext = Mockito.mock(MtSchedulerContext.class);

    try (MtScheduler mtScheduler = new MtScheduler(mtSchedulerContext)) {
      /* do nothing */
    }

    Mockito.verify(mtSchedulerContext, Mockito.times(1)).close();
  }

  @Test
  void startTest() {
    MtSchedulerContext mtSchedulerContext = Mockito.mock(MtSchedulerContext.class);

    try (MtScheduler mtScheduler = new MtScheduler(mtSchedulerContext)) {
      mtScheduler.start();
    }

    Mockito.verify(mtSchedulerContext).start();
    Mockito.verify(mtSchedulerContext).close();
  }
}
