package com.volvo.tisp.tgwmts.impl.converters;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.conf.MtMessageDefaultParameters;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;

class MtMessageInputConverterFunctionTest {
  private static final MtMessageDefaultParameters MT_MESSAGE_DEFAULT_PARAMETERS = TestUtil.createMtMessageDefaultParameters();
  private static final String QUEUE_ID_WITH_CLIENT_ID = TestUtil.CLIENT_ID + '.' + TestUtil.SCHEDULER_OPTION_QUEUE_ID;

  private static void assertReceiveMtMessage(EnqueueingType expectedEnqueueingType, String expectedQueueIdString, SendSchemaName expectedSendSchemaName,
      MtMessage mtMessage, ReceivedMtMessage receivedMtMessage) {
    MtStatusReplyOption mtStatusReplyOption = mtMessage.getMtStatusReplyOption();
    if (mtStatusReplyOption == null) {
      Assertions.assertTrue(receivedMtMessage.getReplyOption().isEmpty());
    } else {
      assertReplyOption(mtStatusReplyOption, receivedMtMessage.getReplyOption().get());
    }
    assertSchedulerOption(expectedEnqueueingType, expectedQueueIdString, expectedSendSchemaName, receivedMtMessage);
    assertSrpOption(mtMessage.getSrpOption(), mtMessage.getPayload(), receivedMtMessage.getSrpOption());
    assertTidAndVpi(mtMessage, receivedMtMessage);
  }

  private static void assertReplyOption(MtStatusReplyOption exceptedMtStatusReplyOption, ReplyOption actualReplyOption) {
    Assertions.assertEquals(exceptedMtStatusReplyOption.getCorrelationId(), actualReplyOption.getCorrelationId().toString());
    Assertions.assertEquals(exceptedMtStatusReplyOption.getReplyDestination(), actualReplyOption.getReplyTo().toString());
  }

  private static void assertSchedulerOption(EnqueueingType expectedEnqueueingType, String expectedQueueIdString, SendSchemaName expectedSendSchemaName,
      ReceivedMtMessage receivedMtMessage) {
    Assertions.assertEquals(expectedEnqueueingType, receivedMtMessage.getEnqueueingType());
    Assertions.assertEquals(expectedQueueIdString, receivedMtMessage.getQueueId().toString());
    Assertions.assertEquals(expectedSendSchemaName, receivedMtMessage.getSendSchemaName());
  }

  private static void assertSrpOption(SrpOption expectedSrpOption, byte[] expectedPayload,
      com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption actualSrpOption) {
    Assertions.assertEquals(expectedSrpOption.getDstService(), actualSrpOption.getSrpDestinationService().toInt());
    Assertions.assertEquals(expectedSrpOption.getDstVersion(), actualSrpOption.getSrpDestinationVersion().toShort());

    if (expectedSrpOption.getSrpLevel() == SrpLevel.SRP_12) {
      Assertions.assertTrue(actualSrpOption.isSrpLevel12());
    } else {
      Assertions.assertFalse(actualSrpOption.isSrpLevel12());
    }

    Assertions.assertArrayEquals(expectedPayload, actualSrpOption.getSrpPayload().getImmutableByteArray().toByteArray());
  }

  private static void assertTidAndVpi(MtMessage mtMessage, ReceivedMtMessage receivedMtMessage) {
    Assertions.assertEquals(TispContext.current().tid().toString(), receivedMtMessage.getTid().toString());
    Assertions.assertEquals(mtMessage.getVehiclePlatformId(), receivedMtMessage.getVpi().toString());
  }

  private static Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> createMtMessageConverterFunction(
      Either<Optional<SendSchemaName>, RuntimeException> either) {
    Function<String, Optional<SendSchemaName>> function = TestUtil.mockFunction();

    if (either.isLeft()) {
      Mockito.when(function.apply(ArgumentMatchers.any())).thenReturn(either.getLeft());
    } else {
      Mockito.when(function.apply(ArgumentMatchers.any())).thenThrow(either.getRight());
    }

    return MtMessageInputConverterFunction.create(function, MT_MESSAGE_DEFAULT_PARAMETERS);
  }

  private static void invalidTest(Consumer<MtMessage> mtMessageConsumer) {
    MtMessage mtMessage = TestUtil.createMtMessage();
    mtMessageConsumer.accept(mtMessage);

    TispContext.runInContext(() -> {
      Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = createMtMessageConverterFunction(
          Either.left(Optional.of(TestUtil.SEND_SCHEMA_NAME)));

      Assertions.assertTrue(mtMessageInputConverterFunction.apply(mtMessage).isLeft());
    });
  }

  private static void validTest(Consumer<MtMessage> mtMessageConsumer, Optional<SendSchemaName> sendSchemaNameOptional,
      EnqueueingType expectedEnqueueingType, String expectedQueueIdString, SendSchemaName expectedSendSchemaName) {
    MtMessage mtMessage = TestUtil.createMtMessage();

    mtMessageConsumer.accept(mtMessage);
    Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = createMtMessageConverterFunction(
        Either.left(sendSchemaNameOptional));

    TispContext.runInContext(() -> {
      ReceivedMtMessage receivedMtMessage = mtMessageInputConverterFunction.apply(mtMessage).getRight();

      assertReceiveMtMessage(expectedEnqueueingType, expectedQueueIdString, expectedSendSchemaName, mtMessage, receivedMtMessage);
    });
  }

  @Test
  void defaultSrpLevelTest() {
    validTest(mtMessage -> mtMessage.getSrpOption().setSrpLevel(null), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE,
        QUEUE_ID_WITH_CLIENT_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void defaultSrpPriorityTest() {
    validTest(mtMessage -> mtMessage.getSrpOption().setPriority(null), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE,
        QUEUE_ID_WITH_CLIENT_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> MtMessageInputConverterFunction.create(null, MT_MESSAGE_DEFAULT_PARAMETERS),
        "schedulerHintConverter must not be null");
    AssertThrows.illegalArgumentException(() -> MtMessageInputConverterFunction.create(TestUtil.mockFunction(), null),
        "mtMessageDefaultParameters must not be null");
  }

  @Test
  void invalidParameterTest() {
    Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = createMtMessageConverterFunction(
        Either.left(Optional.of(TestUtil.SEND_SCHEMA_NAME)));

    AssertThrows.illegalArgumentException(() -> mtMessageInputConverterFunction.apply(null), "mtMessage must not be null");
  }

  @Test
  void mtMessageSrpLevel12Test() {
    validTest(mtMessage -> mtMessage.getSrpOption().setSrpLevel(SrpLevel.SRP_12), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE,
        QUEUE_ID_WITH_CLIENT_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageTest() {
    validTest(mtMessage -> {
    }, Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE, QUEUE_ID_WITH_CLIENT_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageWithEmptyEnqueueingTypeTest() {
    validTest(mtMessage -> mtMessage.getSchedulerOption().setEnqueueingType(""), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE,
        QUEUE_ID_WITH_CLIENT_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageWithEmptyPayloadTest() {
    invalidTest(mtMessage -> mtMessage.setPayload(new byte[0]));
  }

  @Test
  void mtMessageWithInvalidCorrelationIdTest() {
    invalidTest(mtMessage -> mtMessage.getMtStatusReplyOption().setCorrelationId(""));
  }

  @Test
  void mtMessageWithInvalidDstServiceTest() {
    invalidTest(mtMessage -> mtMessage.getSrpOption().setDstService(-1));
  }

  @Test
  void mtMessageWithInvalidDstVersionTest() {
    invalidTest(mtMessage -> mtMessage.getSrpOption().setDstVersion(-1));
  }

  @Test
  void mtMessageWithInvalidHintTest() {
    MtMessage mtMessage = TestUtil.createMtMessage();
    RuntimeException runtimeException = new RuntimeException("test");

    Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = createMtMessageConverterFunction(
        Either.right(runtimeException));

    Assertions.assertEquals(mtMessageInputConverterFunction.apply(mtMessage).getLeft(), runtimeException);
  }

  @Test
  void mtMessageWithInvalidVpiTest() {
    invalidTest(mtMessage -> mtMessage.setVehiclePlatformId("invalidVpi"));
  }

  @Test
  void mtMessageWithoutClientIdTest() {
    invalidTest(mtMessage -> mtMessage.setClientId(null));
  }

  @Test
  void mtMessageWithoutCorrelationIdTest() {
    invalidTest(mtMessage -> mtMessage.getMtStatusReplyOption().setCorrelationId(null));
  }

  @Test
  void mtMessageWithoutEnqueueingTypeTest() {
    validTest(mtMessage -> mtMessage.getSchedulerOption().setEnqueueingType(null), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE,
        QUEUE_ID_WITH_CLIENT_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageWithoutHintTest() {
    validTest(mtMessage -> mtMessage.getSchedulerOption().setHint(null), Optional.empty(), TestUtil.ENQUEUEING_TYPE, QUEUE_ID_WITH_CLIENT_ID,
        TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageWithoutMtStatusReplyOptionTest() {
    validTest(mtMessage -> mtMessage.setMtStatusReplyOption(null), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE, QUEUE_ID_WITH_CLIENT_ID,
        TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageWithoutQueueIdTest() {
    validTest(mtMessage -> mtMessage.getSchedulerOption().setQueueId(null), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE,
        TestUtil.CLIENT_ID, TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageWithoutSchedulerOptionTest() {
    validTest(mtMessage -> mtMessage.setSchedulerOption(null), Optional.of(TestUtil.SEND_SCHEMA_NAME), TestUtil.ENQUEUEING_TYPE,
        TestUtil.QUEUE_ID.toString(), TestUtil.SEND_SCHEMA_NAME);
  }

  @Test
  void mtMessageWithoutSrpOptionTest() {
    invalidTest(mtMessage -> mtMessage.setSrpOption(null));
  }
}
