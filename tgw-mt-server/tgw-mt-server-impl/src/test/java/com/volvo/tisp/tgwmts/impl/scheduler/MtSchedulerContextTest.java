package com.volvo.tisp.tgwmts.impl.scheduler;

import java.util.Collection;
import java.util.Collections;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtSchedulerContextTest {
  @Test
  void closeTest() {
    Thread thread = Mockito.mock(Thread.class);
    Collection<Thread> threads = Collections.singletonList(thread);

    try (MtSchedulerContext mtSchedulerContext = new MtSchedulerContext(threads)) {
      /* do nothing */
    }

    Mockito.verify(thread).interrupt();
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new MtSchedulerContext(null), "threads must not be null");
    AssertThrows.illegalArgumentException(() -> new MtSchedulerContext(Collections.emptyList()), "threads must not be empty");
  }

  @Test
  void startTest() {
    Thread thread = Mockito.mock(Thread.class);
    Collection<Thread> threads = Collections.singletonList(thread);

    try (MtSchedulerContext mtSchedulerContext = new MtSchedulerContext(threads)) {
      mtSchedulerContext.start();

      Mockito.verify(thread).start();
    }
  }
}
