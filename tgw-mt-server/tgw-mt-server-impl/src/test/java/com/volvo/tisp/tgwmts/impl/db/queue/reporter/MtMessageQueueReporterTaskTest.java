package com.volvo.tisp.tgwmts.impl.db.queue.reporter;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;

class MtMessageQueueReporterTaskTest {
  private static ActiveMtMessageReader mockActiveMtMessageReader(int mtMessageCount, int activeMtMessageCount) {
    ActiveMtMessageReader activeMtMessageReader = Mockito.mock(ActiveMtMessageReader.class);
    Mockito.when(activeMtMessageReader.countMtMessages()).thenReturn(mtMessageCount);
    Mockito.when(activeMtMessageReader.countActiveMtMessages()).thenReturn(activeMtMessageCount);
    return activeMtMessageReader;
  }

  private static ActiveMtMessageReaderFactory mockActiveMtMessageReaderFactory(ActiveMtMessageReader activeMtMessageReader) {
    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = Mockito.mock(ActiveMtMessageReaderFactory.class);
    Mockito.when(activeMtMessageReaderFactory.create()).thenReturn(activeMtMessageReader);
    return activeMtMessageReaderFactory;
  }

  @Test
  void exceptionTest() {
    ActiveMtMessageReader activeMtMessageReader = mockActiveMtMessageReader(10, 2);
    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = mockActiveMtMessageReaderFactory(activeMtMessageReader);
    MtMessageQueueMetricReporter mtMessageQueueMetricReporter = Mockito.mock(MtMessageQueueMetricReporter.class);
    Mockito.doThrow(new RuntimeException("test")).when(mtMessageQueueMetricReporter).mtDbQueueSize(10);
    MtMessageQueueReporterTask mtMessageQueueReporterTask = new MtMessageQueueReporterTask(activeMtMessageReaderFactory, mtMessageQueueMetricReporter);

    mtMessageQueueReporterTask.run();

    Mockito.verify(mtMessageQueueMetricReporter).mtDbQueueSize(10);
    Mockito.verifyNoMoreInteractions(mtMessageQueueMetricReporter);
  }

  @Test
  void runTest() {
    ActiveMtMessageReader activeMtMessageReader = mockActiveMtMessageReader(10, 2);
    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = mockActiveMtMessageReaderFactory(activeMtMessageReader);
    MtMessageQueueMetricReporter mtMessageQueueMetricReporter = Mockito.mock(MtMessageQueueMetricReporter.class);
    MtMessageQueueReporterTask mtMessageQueueReporterTask = new MtMessageQueueReporterTask(activeMtMessageReaderFactory, mtMessageQueueMetricReporter);

    mtMessageQueueReporterTask.run();

    Mockito.verify(mtMessageQueueMetricReporter).mtDbQueueSize(10);
    Mockito.verify(mtMessageQueueMetricReporter).activeMtDbQueueSize(2);
    Mockito.verifyNoMoreInteractions(mtMessageQueueMetricReporter);
  }
}
