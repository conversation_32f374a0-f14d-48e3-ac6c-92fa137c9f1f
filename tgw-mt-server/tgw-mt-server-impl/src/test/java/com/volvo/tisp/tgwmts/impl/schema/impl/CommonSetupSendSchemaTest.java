package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CommonSetupSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), CommonSetupSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(10, CommonSetupSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> CommonSetupSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.COMMON_SETUP, CommonSetupSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(CommonSetupSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepSms(CommonSetupSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSat(CommonSetupSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepWait(CommonSetupSendSchema.INSTANCE, 4, Duration.ofHours(166));

    Assertions.assertTrue(CommonSetupSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
  }
}
