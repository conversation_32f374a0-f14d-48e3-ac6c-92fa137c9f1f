package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class TransmissionRouterMetricReporterTest {
  private static final String TRANSMISSION_ROUTER = "transmission.router";
  private static final String TYPE = "TYPE";

  @Test
  void onRouteTest() {
    MetricsReporterTestUtils.initReporterAndTest(TransmissionRouterMetricReporter::new, (meterRegistry, transmissionRouterMetricReporter) -> {
      transmissionRouterMetricReporter.onRoute(SendSchemaStepType.SAT);
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SAT"), 1);

      transmissionRouterMetricReporter.onRoute(SendSchemaStepType.SMS);
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SMS"), 1);

      transmissionRouterMetricReporter.onRoute(SendSchemaStepType.UDP);
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "UDP"), 1);

      transmissionRouterMetricReporter.onRoute(SendSchemaStepType.WAIT);
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "WAIT"), 1);
    });
  }

  @Test
  void onSatTest() {
    MetricsReporterTestUtils.initReporterAndTest(TransmissionRouterMetricReporter::new, (meterRegistry, transmissionRouterMetricReporter) -> {
      transmissionRouterMetricReporter.onSat();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SAT"), 1);

      transmissionRouterMetricReporter.onSat();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SAT"), 2);
    });
  }

  @Test
  void onSmsTest() {
    MetricsReporterTestUtils.initReporterAndTest(TransmissionRouterMetricReporter::new, (meterRegistry, transmissionRouterMetricReporter) -> {
      transmissionRouterMetricReporter.onSms();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SMS"), 1);

      transmissionRouterMetricReporter.onSms();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SMS"), 2);
    });
  }

  @Test
  void onSoftcarTest() {
    MetricsReporterTestUtils.initReporterAndTest(TransmissionRouterMetricReporter::new, (meterRegistry, transmissionRouterMetricReporter) -> {
      transmissionRouterMetricReporter.onSoftcar();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SOFTCAR"), 1);

      transmissionRouterMetricReporter.onSoftcar();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "SOFTCAR"), 2);
    });
  }

  @Test
  void onUdpTest() {
    MetricsReporterTestUtils.initReporterAndTest(TransmissionRouterMetricReporter::new, (meterRegistry, transmissionRouterMetricReporter) -> {
      transmissionRouterMetricReporter.onUdp();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "UDP"), 1);

      transmissionRouterMetricReporter.onUdp();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "UDP"), 2);
    });
  }

  @Test
  void onUnimplementedRouteTest() {
    MetricsReporterTestUtils.initReporterAndTest(TransmissionRouterMetricReporter::new, (meterRegistry, transmissionRouterMetricReporter) -> {
      transmissionRouterMetricReporter.onUnimplementedRoute();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "UNIMPLEMENTED"), 1);

      transmissionRouterMetricReporter.onUnimplementedRoute();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "UNIMPLEMENTED"), 2);
    });
  }

  @Test
  void onWaitTest() {
    MetricsReporterTestUtils.initReporterAndTest(TransmissionRouterMetricReporter::new, (meterRegistry, transmissionRouterMetricReporter) -> {
      transmissionRouterMetricReporter.onWait();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "WAIT"), 1);

      transmissionRouterMetricReporter.onWait();
      MetricsReporterTestUtils.checkCounter(meterRegistry, TRANSMISSION_ROUTER, Tags.of(TYPE, "WAIT"), 2);
    });
  }
}
