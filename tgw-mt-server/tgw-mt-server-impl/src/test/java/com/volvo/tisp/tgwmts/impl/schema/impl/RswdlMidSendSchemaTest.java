package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class RswdlMidSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(4), RswdlMidSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, RswdlMidSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> RswdlMidSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.RSWDL_MID, RswdlMidSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(RswdlMidSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(RswdlMidSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(RswdlMidSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepSat(RswdlMidSendSchema.INSTANCE, 4);
    VerificationUtil.verifySendSchemaStepWait(RswdlMidSendSchema.INSTANCE, 5, Duration.ofDays(4));

    Assertions.assertTrue(RswdlMidSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
  }
}
