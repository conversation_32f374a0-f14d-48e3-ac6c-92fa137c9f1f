package com.volvo.tisp.tgwmts.impl.converters;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.cache.core.api.CacheDeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

class MtStatusMessageOutputConverterFunctionTest {
  private static CacheDeviceInfoReader mockCacheDeviceInfoReader(Optional<PersistedDeviceInfo> optional) {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    Mockito.when(cacheDeviceInfoReader.findDeviceInfoByVpi(TestUtil.VPI)).thenReturn(optional);
    return cacheDeviceInfoReader;
  }

  private static void verifyMtStatusMessage(MtStatusMessage mtStatusMessage) {
    Assertions.assertEquals(TestUtil.CORRELATION_ID.toString(), mtStatusMessage.getCorrelationId());
    Assertions.assertEquals(MtStatus.FAILED.name(), mtStatusMessage.getStatus());
    Assertions.assertEquals(TestUtil.VPI.toString(), mtStatusMessage.getVehiclePlatformId());
  }

  @Test
  void convertNoHandleTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = mockCacheDeviceInfoReader(Optional.empty());
    MtStatusMessageOutputConverter mtStatusMessageOutputConverter = new MtStatusMessageOutputConverter(cacheDeviceInfoReader);
    ReplyOption replyOption = TestUtil.createReplyOption();

    MtStatusMessage mtStatusMessage = mtStatusMessageOutputConverter.convert(MtStatus.FAILED, replyOption, TestUtil.VPI);

    verifyMtStatusMessage(mtStatusMessage);
    Assertions.assertNull(mtStatusMessage.getHandle());
    Mockito.verify(cacheDeviceInfoReader).findDeviceInfoByVpi(TestUtil.VPI);
    Mockito.verifyNoMoreInteractions(cacheDeviceInfoReader);
  }

  @Test
  void convertTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = mockCacheDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    MtStatusMessageOutputConverter mtStatusMessageOutputConverter = new MtStatusMessageOutputConverter(cacheDeviceInfoReader);
    ReplyOption replyOption = TestUtil.createReplyOption();

    MtStatusMessage mtStatusMessage = mtStatusMessageOutputConverter.convert(MtStatus.FAILED, replyOption, TestUtil.VPI);

    verifyMtStatusMessage(mtStatusMessage);
    Assertions.assertEquals(TestUtil.HANDLE.toString(), mtStatusMessage.getHandle());
    Mockito.verify(cacheDeviceInfoReader).findDeviceInfoByVpi(TestUtil.VPI);
    Mockito.verifyNoMoreInteractions(cacheDeviceInfoReader);
  }

  @Test
  void invalidParameterTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    MtStatusMessageOutputConverter mtStatusMessageOutputConverter = new MtStatusMessageOutputConverter(cacheDeviceInfoReader);
    ReplyOption replyOption = TestUtil.createReplyOption();

    AssertThrows.illegalArgumentException(() -> mtStatusMessageOutputConverter.convert(null, replyOption, TestUtil.VPI),
        "mtStatus must not be null");
    AssertThrows.illegalArgumentException(() -> mtStatusMessageOutputConverter.convert(MtStatus.DELIVERED, null, TestUtil.VPI),
        "replyOption must not be null");
    AssertThrows.illegalArgumentException(() -> mtStatusMessageOutputConverter.convert(MtStatus.DELIVERED, replyOption, null),
        "vpi must not be null");
  }
}
