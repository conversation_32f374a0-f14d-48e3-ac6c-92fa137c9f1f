package com.volvo.tisp.tgwmts.impl.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class MtStatusClientTest {
  @Test
  void fromStringTest() {
    Assertions.assertSame(MtStatusClient.VWTP_INIT, MtStatusClient.fromString("vwtpinit").orElseThrow());
    Assertions.assertSame(MtStatusClient.SMS_GATE, MtStatusClient.fromString("smsgate").orElseThrow());
    Assertions.assertSame(MtStatusClient.SAT_GW, MtStatusClient.fromString("satgw").orElseThrow());
    Assertions.assertSame(MtStatusClient.TUCS, MtStatusClient.fromString("tucs").orElseThrow());
    Assertions.assertSame(MtStatusClient.UNKNOWN, MtStatusClient.fromString("unknown").orElseThrow());
    Assertions.assertTrue(MtStatusClient.fromString("something-else").isEmpty());
  }
}