package com.volvo.tisp.tgwmts.impl.services;

import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.function.BiConsumer;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.servicemonitoring.ServiceMonitoring;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.FinishedMtMessageHandlerMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

class FinishedMtMessageHandlerTest {
  private static final Either<InsertionFailure, ActiveMtMessageId> FAILURE_EITHER = Either.left(
      new InsertionFailure(InsertionFailureReason.UNKNOWN, new RuntimeException("test")));
  private static final int LIMIT = 1;
  private static final Either<InsertionFailure, ActiveMtMessageId> SUCCESS_EITHER = Either.right(TestUtil.ACTIVE_MT_MESSAGE_ID);

  private static List<PersistedMtMessage> createPersistedMtMessages() {
    return List.of(TestUtil.createPersistedMtMessageBuilder().build());
  }

  private static void initAndVerify(BiConsumer<ActiveMtMessageWriter, JoinedActiveMtMessage> biConsumer, MtStatusPublisher mtStatusPublisher,
      MtStatus mtStatus) {
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(1, SUCCESS_EITHER);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();

    biConsumer.accept(activeMtMessageWriter, joinedActiveMtMessage);

    verifyPublishAndTransitionInteractions(mtStatusPublisher, activeMtMessageWriter, mtStatus);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, mtStatusPublisher);
  }

  private static ActiveMtMessageWriter mockActiveMtMessageWriter(int expectedRowCount, Either<InsertionFailure, ActiveMtMessageId> either) {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(activeMtMessageWriter.findMtMessagesByVpi(TestUtil.VPI)).thenReturn(createPersistedMtMessages());
    Mockito.when(activeMtMessageWriter.deleteVehicleLockByVpi(TestUtil.VPI)).thenReturn(expectedRowCount);
    Mockito.when(activeMtMessageWriter.deleteMtMessageById(TestUtil.MT_MESSAGE_ID)).thenReturn(expectedRowCount);
    Mockito.when(activeMtMessageWriter.findOldestNonActiveMtMessages(TestUtil.VPI, LIMIT)).thenReturn(List.of(createPersistedMtMessages().get(0)));
    Mockito.when(activeMtMessageWriter.insertActiveMtMessage(ArgumentMatchers.any(ActiveMtMessage.class))).thenReturn(either);
    return activeMtMessageWriter;
  }

  private static void verifyDeviceNotFoundInteractions(MtStatusPublisher mtStatusPublisher, ActiveMtMessageWriter activeMtMessageWriter) {
    Mockito.verify(mtStatusPublisher).publishMtStatus(MtStatus.FAILED, TestUtil.createReplyOption(), TestUtil.VPI);
    Mockito.verify(activeMtMessageWriter).findMtMessagesByVpi(TestUtil.VPI);
    Mockito.verify(activeMtMessageWriter).deleteVehicleLockByVpi(TestUtil.VPI);
  }

  private static void verifyPublishAndTransitionInteractions(MtStatusPublisher mtStatusPublisher, ActiveMtMessageWriter activeMtMessageWriter,
      MtStatus mtStatus) {
    Mockito.verify(mtStatusPublisher).publishMtStatus(mtStatus, TestUtil.createReplyOption(), TestUtil.VPI);
    Mockito.verify(activeMtMessageWriter).findOldestNonActiveMtMessages(TestUtil.VPI, LIMIT);
    Mockito.verify(activeMtMessageWriter).deleteMtMessageById(TestUtil.MT_MESSAGE_ID);
    Mockito.verify(activeMtMessageWriter).insertActiveMtMessage(ArgumentMatchers.any(ActiveMtMessage.class));
  }

  @Test
  void onDeliveredTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(1, SUCCESS_EITHER);
    ServiceMonitoring serviceMonitoring = Mockito.mock(ServiceMonitoring.class);
    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler,
        finishedMtMessageHandlerMetricReporter, mtStatusPublisher,
        throttledActiveMtMessageParameterProvider, true, serviceMonitoring);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();

    finishedMtMessageHandler.onDelivered(activeMtMessageWriter, joinedActiveMtMessage);

    Vpi vpi = joinedActiveMtMessage.persistedVehicleLock().getVehicleLock().getVpi();
    Mockito.verify(mtMessageRetryHandler)
        .initiateRetryForWaitingMtMessages(activeMtMessageWriter, vpi);
    verifyPublishAndTransitionInteractions(mtStatusPublisher, activeMtMessageWriter, MtStatus.DELIVERED);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, mtStatusPublisher);
    Mockito.verifyNoInteractions(finishedMtMessageHandlerMetricReporter, throttledActiveMtMessageParameterProvider);

    VehicleIdentifier vehicleIdentifier = VehicleIdentifier.fromString(vpi.toString());
    Instant created = joinedActiveMtMessage.persistedMtMessage().getCreated();
    Mockito.verify(serviceMonitoring).ifEnabled(Mockito.eq(vehicleIdentifier), Mockito.eq(created), Mockito.eq(created), Mockito.any(Duration.class));
  }

  @Test
  void onDeviceNotFoundDeletionFailureTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(0, SUCCESS_EITHER);
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler, finishedMtMessageHandlerMetricReporter,
        mtStatusPublisher, throttledActiveMtMessageParameterProvider, true, Mockito.mock(ServiceMonitoring.class));
    finishedMtMessageHandler.onDeviceNotFound(activeMtMessageWriter, TestUtil.VPI);

    verifyDeviceNotFoundInteractions(mtStatusPublisher, activeMtMessageWriter);
    Mockito.verify(finishedMtMessageHandlerMetricReporter).onFailedVehicleLockDeletion();
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, finishedMtMessageHandlerMetricReporter, mtStatusPublisher);
    Mockito.verifyNoInteractions(mtMessageRetryHandler, throttledActiveMtMessageParameterProvider);
  }

  @Test
  void onDeviceNotFoundTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(1, SUCCESS_EITHER);
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler, finishedMtMessageHandlerMetricReporter,
        mtStatusPublisher, throttledActiveMtMessageParameterProvider, true, Mockito.mock(ServiceMonitoring.class));
    finishedMtMessageHandler.onDeviceNotFound(activeMtMessageWriter, TestUtil.VPI);

    verifyDeviceNotFoundInteractions(mtStatusPublisher, activeMtMessageWriter);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, mtStatusPublisher);
    Mockito.verifyNoInteractions(mtMessageRetryHandler, finishedMtMessageHandlerMetricReporter, throttledActiveMtMessageParameterProvider);
  }

  @Test
  void onRejectedTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler,
        finishedMtMessageHandlerMetricReporter, mtStatusPublisher,
        throttledActiveMtMessageParameterProvider, true, Mockito.mock(ServiceMonitoring.class));

    initAndVerify(finishedMtMessageHandler::onRejected, mtStatusPublisher, MtStatus.FAILED);
    Mockito.verifyNoInteractions(finishedMtMessageHandlerMetricReporter, throttledActiveMtMessageParameterProvider);
  }

  @Test
  void onServiceUnsupported() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler,
        finishedMtMessageHandlerMetricReporter, mtStatusPublisher,
        throttledActiveMtMessageParameterProvider, true, Mockito.mock(ServiceMonitoring.class));

    initAndVerify(finishedMtMessageHandler::onServiceUnsupported, mtStatusPublisher, MtStatus.SERVICE_UNSUPPORTED);
    Mockito.verifyNoInteractions(finishedMtMessageHandlerMetricReporter, throttledActiveMtMessageParameterProvider);
  }

  @Test
  void onThrottledTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ThrottledActiveMtMessageParameterProvider provider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    UpdateActiveMtMessageParameter updateActiveMtMessageParameter = TestUtil.createUpdateActiveMtMessageParameter();
    Mockito.when(provider.get(joinedActiveMtMessage.persistedActiveMtMessage())).thenReturn(updateActiveMtMessageParameter);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler,
        finishedMtMessageHandlerMetricReporter, mtStatusPublisher, provider, true, Mockito.mock(ServiceMonitoring.class));
    finishedMtMessageHandler.onThrottled(activeMtMessageWriter, joinedActiveMtMessage);

    Mockito.verify(provider).get(joinedActiveMtMessage.persistedActiveMtMessage());
    Mockito.verify(activeMtMessageWriter).updateActiveMtMessages(List.of(updateActiveMtMessageParameter));
    Mockito.verifyNoMoreInteractions(provider, activeMtMessageWriter);
    Mockito.verifyNoInteractions(mtMessageRetryHandler, finishedMtMessageHandlerMetricReporter, mtStatusPublisher);
  }

  @Test
  void onTimeoutDeletionFailureTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(0, SUCCESS_EITHER);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler,
        finishedMtMessageHandlerMetricReporter, mtStatusPublisher,
        throttledActiveMtMessageParameterProvider, true, Mockito.mock(ServiceMonitoring.class));
    finishedMtMessageHandler.onTimeout(activeMtMessageWriter, joinedActiveMtMessage);

    verifyPublishAndTransitionInteractions(mtStatusPublisher, activeMtMessageWriter, MtStatus.TIMEOUT);
    Mockito.verify(finishedMtMessageHandlerMetricReporter).onFailedMtMessageDeletion();
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, finishedMtMessageHandlerMetricReporter, mtStatusPublisher);
    Mockito.verifyNoInteractions(mtMessageRetryHandler, throttledActiveMtMessageParameterProvider);
  }

  @Test
  void onTimeoutInsertionFailureTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(1, FAILURE_EITHER);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler,
        finishedMtMessageHandlerMetricReporter, mtStatusPublisher,
        throttledActiveMtMessageParameterProvider, true, Mockito.mock(ServiceMonitoring.class));
    finishedMtMessageHandler.onTimeout(activeMtMessageWriter, joinedActiveMtMessage);

    verifyPublishAndTransitionInteractions(mtStatusPublisher, activeMtMessageWriter, MtStatus.TIMEOUT);
    Mockito.verify(finishedMtMessageHandlerMetricReporter).onFailedActiveMtMessageInsertion();
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, finishedMtMessageHandlerMetricReporter, mtStatusPublisher);
    Mockito.verifyNoInteractions(mtMessageRetryHandler, throttledActiveMtMessageParameterProvider);
  }

  @Test
  void onTimeoutTest() {
    FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter = Mockito.mock(FinishedMtMessageHandlerMetricReporter.class);
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);

    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = Mockito.mock(ThrottledActiveMtMessageParameterProvider.class);

    FinishedMtMessageHandler finishedMtMessageHandler = new FinishedMtMessageHandler(mtMessageRetryHandler,
        finishedMtMessageHandlerMetricReporter, mtStatusPublisher,
        throttledActiveMtMessageParameterProvider, true, Mockito.mock(ServiceMonitoring.class));

    initAndVerify(finishedMtMessageHandler::onTimeout, mtStatusPublisher, MtStatus.TIMEOUT);
    Mockito.verifyNoInteractions(mtMessageRetryHandler, finishedMtMessageHandlerMetricReporter, throttledActiveMtMessageParameterProvider);
  }
}
