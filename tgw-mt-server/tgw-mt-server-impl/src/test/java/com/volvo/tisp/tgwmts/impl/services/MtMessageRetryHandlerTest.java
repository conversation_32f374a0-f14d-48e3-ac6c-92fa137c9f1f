package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.impl.schema.FilterableSendSchemaFetcher;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepFilterProducer;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtMessageRetryHandlerMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageRetryHandlerTest {
  private static FilterableSendSchemaFetcher createFilterableSendSchemaFetcher() {
    return new FilterableSendSchemaFetcher(TestUtil.createSendSchemaNameMappingFunction());
  }

  private static ActiveMtMessageWriter mockActiveMtMessageWriter(List<JoinedActiveMtMessage> joinedActiveMtMessages) {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(activeMtMessageWriter.findActiveMtMessagesByVpiWithVpiLock(TestUtil.VPI)).thenReturn(joinedActiveMtMessages);
    return activeMtMessageWriter;
  }

  private static ActiveMtMessageWriterFactory mockActiveMtMessageWriterFactory(ActiveMtMessageWriter activeMtMessageWriter) {
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = Mockito.mock(ActiveMtMessageWriterFactory.class);
    Mockito.when(activeMtMessageWriterFactory.createReadCommitted()).thenReturn(activeMtMessageWriter);
    return activeMtMessageWriterFactory;
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(2))
        .thenReturn(Instant.ofEpochSecond(3))
        .thenReturn(Instant.ofEpochSecond(5))
        .thenReturn(Instant.ofEpochSecond(8));
    return clock;
  }

  private static DeviceInfoReader mockDeviceInfoReader(Optional<PersistedDeviceInfo> optional) {
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    Mockito.when(deviceInfoReader.findDeviceInfoByVpi(TestUtil.VPI)).thenReturn(optional);
    return deviceInfoReader;
  }

  private static DeviceInfoReaderFactory mockDeviceInfoReaderFactory(DeviceInfoReader deviceInfoReader) {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    return deviceInfoReaderFactory;
  }

  private static void verifyActiveMtMessageUpdatedInDb(ActiveMtMessageWriter activeMtMessageWriter, ActiveMtMessageId expectedActiveMtMessageId,
      RetryAttempt expectedRetryAttempt, SendSchemaStepId expectedSendSchemaStepId) {
    ArgumentCaptor<List<UpdateActiveMtMessageParameter>> listArgumentCaptor = ArgumentCaptor.forClass(List.class);
    Mockito.verify(activeMtMessageWriter).updateActiveMtMessages(listArgumentCaptor.capture());
    List<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters = listArgumentCaptor.getValue();

    Assertions.assertEquals(1, updateActiveMtMessageParameters.size());
    verifyUpdateActiveMtMessageParameter(expectedActiveMtMessageId, expectedRetryAttempt, expectedSendSchemaStepId, updateActiveMtMessageParameters.get(0));
  }

  private static void verifyDatabaseDeviceApiInteractions(DeviceInfoReader deviceInfoReader, DeviceInfoReaderFactory deviceInfoReaderFactory) {
    Mockito.verify(deviceInfoReaderFactory).create();
    Mockito.verify(deviceInfoReader).findDeviceInfoByVpi(TestUtil.VPI);
    Mockito.verify(deviceInfoReader).close();
    Mockito.verifyNoMoreInteractions(deviceInfoReader);
    Mockito.verifyNoMoreInteractions(deviceInfoReaderFactory);
  }

  private static void verifyDatabaseMessageApiInteractions(ActiveMtMessageWriter activeMtMessageWriter,
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory) {
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).findActiveMtMessagesByVpiWithVpiLock(TestUtil.VPI);
    Mockito.verify(activeMtMessageWriter).commitTransaction();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriterFactory);
  }

  private static void verifyMetricReporterInteractions(MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter) {
    Mockito.verify(mtMessageRetryHandlerMetricReporter).onFindActiveMtMessagesByVpiWithVpiLock(Duration.ofSeconds(1));
    Mockito.verify(mtMessageRetryHandlerMetricReporter).onRetryInitiated(1, Duration.ofSeconds(6));
    Mockito.verifyNoMoreInteractions(mtMessageRetryHandlerMetricReporter);
    Mockito.verify(mtMessageRetryHandlerMetricReporter, Mockito.never()).onNoActiveMtMessageFound();
  }

  private static void verifyMetricReporterInteractionsWhenNoActiveMtMessageInWaitState(
      MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter) {
    Mockito.verify(mtMessageRetryHandlerMetricReporter).onNoActiveMtMessageFound();
    Mockito.verify(mtMessageRetryHandlerMetricReporter).onFindActiveMtMessagesByVpiWithVpiLock(Duration.ofSeconds(1));
    Mockito.verifyNoMoreInteractions(mtMessageRetryHandlerMetricReporter);
  }

  private static void verifyUpdateActiveMtMessageParameter(ActiveMtMessageId expectedActiveMtMessageId, RetryAttempt expectedRetryAttempt,
      SendSchemaStepId expectedSendSchemaStepId, UpdateActiveMtMessageParameter updateActiveMtMessageParameter) {
    Assertions.assertSame(expectedActiveMtMessageId, updateActiveMtMessageParameter.getActiveMtMessageId());
    Assertions.assertEquals(expectedRetryAttempt, updateActiveMtMessageParameter.getRetryAttempt());
    Assertions.assertEquals(expectedSendSchemaStepId, updateActiveMtMessageParameter.getSendSchemaStepId());
    Assertions.assertEquals(Instant.ofEpochSecond(5), updateActiveMtMessageParameter.getTimeout());
  }

  @Test
  void invalidProcessParameterTest() {
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = Mockito.mock(ActiveMtMessageWriterFactory.class);
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = createFilterableSendSchemaFetcher();
    Clock clock = mockClock();
    MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter = Mockito.mock(MtMessageRetryHandlerMetricReporter.class);

    MtMessageRetryHandler mtMessageRetryHandler = new MtMessageRetryHandler(activeMtMessageWriterFactory, clock, deviceInfoReaderFactory,
        filterableSendSchemaFetcher, mtMessageRetryHandlerMetricReporter, new SendSchemaStepFilterProducer());

    AssertThrows.illegalArgumentException(() -> mtMessageRetryHandler.initiateRetryForWaitingMtMessages(null), "vpi must not be null");
  }

  @Test
  void noDeviceInfoTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.empty());
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = createFilterableSendSchemaFetcher();

    MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter = Mockito.mock(MtMessageRetryHandlerMetricReporter.class);

    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(List.of(joinedActiveMtMessage));
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);

    MtMessageRetryHandler mtMessageRetryHandler = new MtMessageRetryHandler(activeMtMessageWriterFactory, clock, deviceInfoReaderFactory,
        filterableSendSchemaFetcher, mtMessageRetryHandlerMetricReporter, new SendSchemaStepFilterProducer());

    mtMessageRetryHandler.initiateRetryForWaitingMtMessages(TestUtil.VPI);

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(mtMessageRetryHandlerMetricReporter).onMissingDeviceCounter();
    verifyDatabaseMessageApiInteractions(activeMtMessageWriter, activeMtMessageWriterFactory);
    verifyDatabaseDeviceApiInteractions(deviceInfoReader, deviceInfoReaderFactory);
  }

  @Test
  void processConnectionEstablishedEventForRetryExhaustedActiveMtMessageTest() {
    Clock clock = mockClock();
    MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter = Mockito.mock(MtMessageRetryHandlerMetricReporter.class);
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = createFilterableSendSchemaFetcher();

    ActiveMtMessageId activeMtMessageId = ActiveMtMessageId.ofLong(2);

    SendSchemaStepId waitSendSchemaStepId = SendSchemaStepId.ofInt(2);
    JoinedActiveMtMessage joinedActiveMtMessageInWaitWithRetryExhausted = TestUtil.createJoinedActiveMtMessage(activeMtMessageId,
        RetryAttempt.ofShort((short) 10), waitSendSchemaStepId);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();

    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(List.of(joinedActiveMtMessage, joinedActiveMtMessageInWaitWithRetryExhausted));
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);

    MtMessageRetryHandler mtMessageRetryHandler = new MtMessageRetryHandler(activeMtMessageWriterFactory, clock, deviceInfoReaderFactory,
        filterableSendSchemaFetcher, mtMessageRetryHandlerMetricReporter, new SendSchemaStepFilterProducer());

    mtMessageRetryHandler.initiateRetryForWaitingMtMessages(TestUtil.VPI);

    verifyActiveMtMessageUpdatedInDb(activeMtMessageWriter, activeMtMessageId, RetryAttempt.ofShort((short) 11), waitSendSchemaStepId);
    Mockito.verify(clock, Mockito.times(4)).instant();
    verifyMetricReporterInteractions(mtMessageRetryHandlerMetricReporter);
    verifyDatabaseMessageApiInteractions(activeMtMessageWriter, activeMtMessageWriterFactory);
    verifyDatabaseDeviceApiInteractions(deviceInfoReader, deviceInfoReaderFactory);
  }

  @Test
  void processConnectionEstablishedEventTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = createFilterableSendSchemaFetcher();
    MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter = Mockito.mock(MtMessageRetryHandlerMetricReporter.class);
    ActiveMtMessageId activeMtMessageId = ActiveMtMessageId.ofLong(2);

    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    JoinedActiveMtMessage joinedActiveMtMessageInWait = TestUtil.createJoinedActiveMtMessage(activeMtMessageId, TestUtil.RETRY_ATTEMPT,
        SendSchemaStepId.ofInt(2));
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(List.of(joinedActiveMtMessage, joinedActiveMtMessageInWait));
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);

    MtMessageRetryHandler mtMessageRetryHandler = new MtMessageRetryHandler(activeMtMessageWriterFactory, clock, deviceInfoReaderFactory,
        filterableSendSchemaFetcher, mtMessageRetryHandlerMetricReporter, new SendSchemaStepFilterProducer());

    mtMessageRetryHandler.initiateRetryForWaitingMtMessages(TestUtil.VPI);

    verifyActiveMtMessageUpdatedInDb(activeMtMessageWriter, activeMtMessageId, RetryAttempt.ofShort((short) 1), SendSchemaStepId.ofInt(0));
    Mockito.verify(clock, Mockito.times(4)).instant();
    verifyMetricReporterInteractions(mtMessageRetryHandlerMetricReporter);
    verifyDatabaseMessageApiInteractions(activeMtMessageWriter, activeMtMessageWriterFactory);
    verifyDatabaseDeviceApiInteractions(deviceInfoReader, deviceInfoReaderFactory);
  }

  @Test
  void processConnectionEstablishedEventWhenNoActiveMtMessageInWaitStateTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = createFilterableSendSchemaFetcher();

    MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter = Mockito.mock(MtMessageRetryHandlerMetricReporter.class);

    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(List.of(joinedActiveMtMessage));
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);

    MtMessageRetryHandler mtMessageRetryHandler = new MtMessageRetryHandler(activeMtMessageWriterFactory, clock, deviceInfoReaderFactory,
        filterableSendSchemaFetcher, mtMessageRetryHandlerMetricReporter, new SendSchemaStepFilterProducer());

    mtMessageRetryHandler.initiateRetryForWaitingMtMessages(TestUtil.VPI);

    Mockito.verify(clock, Mockito.times(2)).instant();
    verifyMetricReporterInteractionsWhenNoActiveMtMessageInWaitState(mtMessageRetryHandlerMetricReporter);
    verifyDatabaseMessageApiInteractions(activeMtMessageWriter, activeMtMessageWriterFactory);
    verifyDatabaseDeviceApiInteractions(deviceInfoReader, deviceInfoReaderFactory);
  }
}
