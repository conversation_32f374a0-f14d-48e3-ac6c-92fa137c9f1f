package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CommonNormalSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), CommonNormalSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, CommonNormalSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> CommonNormalSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.COMMON_NORMAL, CommonNormalSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(CommonNormalSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(CommonNormalSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(CommonNormalSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepSat(CommonNormalSendSchema.INSTANCE, 4);
    VerificationUtil.verifySendSchemaStepWait(CommonNormalSendSchema.INSTANCE, 5, Duration.ofDays(7));

    Assertions.assertTrue(CommonNormalSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
  }
}
