package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ThrottledStatusConfigurationTest {
  @Test
  void getterTest() {
    Assertions.assertEquals(Duration.ofSeconds(42), new ThrottledStatusConfiguration(Duration.ofSeconds(42)).getWaitTimeout());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new ThrottledStatusConfiguration(null), "waitTimeout must not be null");
    AssertThrows.illegalArgumentException(() -> new ThrottledStatusConfiguration(Duration.ofSeconds(-1)), "waitTimeout must not be negative: PT-1S");
  }
}
