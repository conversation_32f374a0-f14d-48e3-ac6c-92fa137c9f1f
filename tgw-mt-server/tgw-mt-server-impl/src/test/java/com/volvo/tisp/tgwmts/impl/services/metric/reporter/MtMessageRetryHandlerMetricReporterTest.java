package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Tags;

class MtMessageRetryHandlerMetricReporterTest {
  private static final Duration DURATION = Duration.ofSeconds(-1);
  private static final String TYPE = "TYPE";

  @Test
  void onConnectionEstablishedEventProcessedTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageRetryHandlerMetricReporter::new, (meterRegistry, mtMessageRetryHandlerMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtMessageRetryHandlerMetricReporter.onRetryInitiated(-1, Duration.ofMillis(100)),
          "count must be positive: -1");
      AssertThrows.illegalArgumentException(() -> mtMessageRetryHandlerMetricReporter.onRetryInitiated(0, Duration.ofMillis(100)),
          "count must be positive: 0");
      AssertThrows.illegalArgumentException(() -> new MtMessageRetryHandlerMetricReporter(meterRegistry).onRetryInitiated(1, null),
          "duration must not be null");
      AssertThrows.illegalArgumentException(
          () -> new MtMessageRetryHandlerMetricReporter(meterRegistry).onRetryInitiated(1, DURATION),
          "duration must not be negative: PT-1S");

      mtMessageRetryHandlerMetricReporter.onRetryInitiated(1, Duration.ofMillis(100));
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-retry", Tags.of(TYPE, "PROCESSED"), 1);
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt-retry", Tags.of(TYPE, "DURATION"), Duration.ofMillis(100), 1);

      mtMessageRetryHandlerMetricReporter.onRetryInitiated(2, Duration.ofMillis(100));
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-retry", Tags.of(TYPE, "PROCESSED"), 3);
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt-retry", Tags.of(TYPE, "DURATION"), Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onFindActiveMtMessagesByVpiWithVpiLockTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageRetryHandlerMetricReporter::new, (meterRegistry, mtMessageRetryHandlerMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> new MtMessageRetryHandlerMetricReporter(meterRegistry).onFindActiveMtMessagesByVpiWithVpiLock(null),
          "duration must not be null");
      AssertThrows.illegalArgumentException(() -> new MtMessageRetryHandlerMetricReporter(meterRegistry).onFindActiveMtMessagesByVpiWithVpiLock(DURATION),
          "duration must not be negative: PT-1S");

      mtMessageRetryHandlerMetricReporter.onFindActiveMtMessagesByVpiWithVpiLock(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt-retry", Tags.of(TYPE, "FIND_ACTIVE_MT_MESSAGE"), Duration.ofMillis(100), 1);

      mtMessageRetryHandlerMetricReporter.onFindActiveMtMessagesByVpiWithVpiLock(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt-retry", Tags.of(TYPE, "FIND_ACTIVE_MT_MESSAGE"), Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onMissingDeviceCounterTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageRetryHandlerMetricReporter::new, (meterRegistry, mtMessageRetryHandlerMetricReporter) -> {
      mtMessageRetryHandlerMetricReporter.onMissingDeviceCounter();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-retry", Tags.of(TYPE, "MISSING_DEVICE"), 1);

      mtMessageRetryHandlerMetricReporter.onMissingDeviceCounter();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-retry", Tags.of(TYPE, "MISSING_DEVICE"), 2);
    });
  }

  @Test
  void onNoActiveMtMessageFoundTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageRetryHandlerMetricReporter::new, (meterRegistry, mtMessageRetryHandlerMetricReporter) -> {
      mtMessageRetryHandlerMetricReporter.onNoActiveMtMessageFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-retry", Tags.of(TYPE, "NO_ACTIVE_MT_MESSAGE"), 1);

      mtMessageRetryHandlerMetricReporter.onNoActiveMtMessageFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-retry", Tags.of(TYPE, "NO_ACTIVE_MT_MESSAGE"), 2);
    });
  }
}
