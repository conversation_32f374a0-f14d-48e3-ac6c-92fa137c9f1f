package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CommonMidSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), CommonMidSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, CommonMidSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> CommonMidSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.COMMON_MID, CommonMidSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(CommonMidSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(CommonMidSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(CommonMidSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepSat(CommonMidSendSchema.INSTANCE, 4);
    VerificationUtil.verifySendSchemaStepWait(CommonMidSendSchema.INSTANCE, 5, Duration.ofDays(7));

    Assertions.assertTrue(CommonMidSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(6)).isEmpty());
  }
}
