package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CommonVeryHighSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofMinutes(10), CommonVeryHighSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, CommonVeryHighSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> CommonVeryHighSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.COMMON_VERY_HIGH, CommonVeryHighSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(CommonVeryHighSendSchema.INSTANCE, 1, Duration.ofSeconds(30));
    VerificationUtil.verifySendSchemaStepUdp(CommonVeryHighSendSchema.INSTANCE, 2, Duration.ofSeconds(30));
    VerificationUtil.verifySendSchemaStepSms(CommonVeryHighSendSchema.INSTANCE, 3, Duration.ofMinutes(65));
    VerificationUtil.verifySendSchemaStepWait(CommonVeryHighSendSchema.INSTANCE, 4, Duration.ofMinutes(10));

    Assertions.assertTrue(CommonVeryHighSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
  }
}
