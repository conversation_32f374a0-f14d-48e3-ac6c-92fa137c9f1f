package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import static com.volvo.tisp.tgwmts.impl.services.metric.reporter.ReceivedMtStatusMessageMetricReporter.RECEIVED_MT_STATUS;
import static com.volvo.tisp.tgwmts.impl.services.metric.reporter.ReceivedMtStatusMessageMetricReporter.RECEIVED_MT_STATUS_ANOMALY;
import static com.volvo.tisp.tgwmts.impl.services.metric.reporter.ReceivedMtStatusMessageMetricReporter.TYPE;
import static com.volvo.tisp.vc.vcss.client.protobuf.common.TransportTypeProtobuf.TransportType;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Tags;

class ReceivedMtStatusMessageMetricReporterTest {
  private static final Duration DURATION = Duration.ofSeconds(-1);

  @Test
  void onCanceledTest() {
    MetricsReporterTestUtils.initReporterAndTest(ReceivedMtStatusMessageMetricReporter::new, (meterRegistry, receivedMtStatusMessageMetricReporter) -> {
      receivedMtStatusMessageMetricReporter.onCanceled();
      MetricsReporterTestUtils.checkCounter(meterRegistry, RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "CANCELED"), 1);

      receivedMtStatusMessageMetricReporter.onCanceled();
      MetricsReporterTestUtils.checkCounter(meterRegistry, RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "CANCELED"), 2);
    });
  }

  @Test
  void onFailedToProcessTest() {
    MetricsReporterTestUtils.initReporterAndTest(ReceivedMtStatusMessageMetricReporter::new, (meterRegistry, receivedMtStatusMessageMetricReporter) -> {
      receivedMtStatusMessageMetricReporter.onFailedToProcess();
      MetricsReporterTestUtils.checkCounter(meterRegistry, RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "FAILED_TO_PROCESS"), 1);

      receivedMtStatusMessageMetricReporter.onFailedToProcess();
      MetricsReporterTestUtils.checkCounter(meterRegistry, RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "FAILED_TO_PROCESS"), 2);
    });
  }

  @Test
  void onFindActiveMtMessageWithVpiLockTest() {
    MetricsReporterTestUtils.initReporterAndTest(ReceivedMtStatusMessageMetricReporter::new, (meterRegistry, receivedMtStatusMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> new ReceivedMtStatusMessageMetricReporter(meterRegistry).onFindActiveMtMessageWithVpiLock(null),
          "duration must not be null");
      AssertThrows.illegalArgumentException(() -> new ReceivedMtStatusMessageMetricReporter(meterRegistry).onFindActiveMtMessageWithVpiLock(DURATION),
          "duration must not be negative: PT-1S");

      receivedMtStatusMessageMetricReporter.onFindActiveMtMessageWithVpiLock(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "received-mt-status.find-active-mt-message", Duration.ofMillis(100), 1);

      receivedMtStatusMessageMetricReporter.onFindActiveMtMessageWithVpiLock(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "received-mt-status.find-active-mt-message", Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onMtStatusStatisticTransportType() {
    MetricsReporterTestUtils.initReporterAndTest(ReceivedMtStatusMessageMetricReporter::new, (meterRegistry, receivedMtStatusMessageMetricReporter) -> {
      receivedMtStatusMessageMetricReporter.onMtStatusStatisticTransportType(TransportType.UDP);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-status-statistic.transport-type", Tags.of(TYPE, "UDP"), 1);

      receivedMtStatusMessageMetricReporter.onMtStatusStatisticTransportType(TransportType.UDP);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-status-statistic.transport-type", Tags.of(TYPE, "UDP"), 2);
    });
  }

  @Test
  void onNotFoundTest() {
    MetricsReporterTestUtils.initReporterAndTest(ReceivedMtStatusMessageMetricReporter::new, (meterRegistry, receivedMtStatusMessageMetricReporter) -> {
      receivedMtStatusMessageMetricReporter.onNotFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "NOT_FOUND"), 1);

      receivedMtStatusMessageMetricReporter.onNotFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "NOT_FOUND"), 2);
    });
  }

  @Test
  void onReceivedMtStatusTest() {
    MetricsReporterTestUtils.initReporterAndTest(ReceivedMtStatusMessageMetricReporter::new, (meterRegistry, receivedMtStatusMessageMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> new ReceivedMtStatusMessageMetricReporter(meterRegistry).onReceivedMtStatus(null, DURATION),
          "receivedMtStatus must not be null");
      AssertThrows.illegalArgumentException(() -> new ReceivedMtStatusMessageMetricReporter(meterRegistry).onReceivedMtStatus(ReceivedMtStatus.DELIVERED, null),
          "duration must not be null");
      AssertThrows.illegalArgumentException(
          () -> new ReceivedMtStatusMessageMetricReporter(meterRegistry).onReceivedMtStatus(ReceivedMtStatus.DELIVERED, DURATION),
          "duration must not be negative: PT-1S");

      receivedMtStatusMessageMetricReporter.onReceivedMtStatus(ReceivedMtStatus.DELIVERED, Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, RECEIVED_MT_STATUS, Tags.of(TYPE, ReceivedMtStatus.DELIVERED.name()), Duration.ofMillis(100), 1);

      receivedMtStatusMessageMetricReporter.onReceivedMtStatus(ReceivedMtStatus.DELIVERED, Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, RECEIVED_MT_STATUS, Tags.of(TYPE, ReceivedMtStatus.DELIVERED.name()), Duration.ofMillis(200), 2);
    });
  }
}
