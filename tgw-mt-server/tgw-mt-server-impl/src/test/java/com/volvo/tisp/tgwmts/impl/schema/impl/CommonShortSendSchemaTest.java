package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class CommonShortSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofMinutes(10), CommonShortSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(10, CommonShortSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> CommonShortSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.COMMON_SHORT, CommonShortSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(CommonShortSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(CommonShortSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(CommonShortSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepWait(CommonShortSendSchema.INSTANCE, 4, Duration.ofMinutes(10));

    Assertions.assertTrue(CommonShortSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(5)).isEmpty());
  }
}
