package com.volvo.tisp.tgwmts.impl.jms.model;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ReceivedMtMessageTest {
  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new ReceivedMtMessage(null), "receivedMtMessageBuilder must not be null");
  }

  @Test
  void toStringTest() {
    String expectedString = "enqueueingType=NORMAL, queueId=queueId, replyOption={Optional[correlationId=CorrelationId, replyTo=ReplyTo]}, sendSchemaName=COMMON_LOW, srpOption={srpDestinationService=5, srpDestinationVersion=3, srpLevel12=true, srpPayload=[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, ... (length: 58 bytes)]}, tid=123456789012345678901234567890FF, vpi=12345678901234567890ABCDEFABCDEF";
    Assertions.assertEquals(expectedString, TestUtil.createReceivedMtMessage().toString());
  }
}
