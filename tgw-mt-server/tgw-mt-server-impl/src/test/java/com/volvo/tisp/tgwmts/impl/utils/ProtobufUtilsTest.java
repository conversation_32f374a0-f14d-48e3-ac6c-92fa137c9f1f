package com.volvo.tisp.tgwmts.impl.utils;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.google.protobuf.Timestamp;
import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;

class ProtobufUtilsTest {
  private static final Instant INSTANT = Instant.ofEpochSecond(42, 1337);
  private static final Timestamp TIMESTAMP = Timestamp.newBuilder().setNanos(1337).setSeconds(42).build();

  @Test
  void createInstantTest() {
    Assertions.assertEquals(INSTANT, ProtobufUtils.createInstant(TIMESTAMP));
  }

  @Test
  void createTimestampTest() {
    Assertions.assertEquals(TIMESTAMP, ProtobufUtils.createTimestamp(INSTANT));
  }

  @Test
  void verifyUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(ProtobufUtils.class);
  }
}
