package com.volvo.tisp.tgwmts.impl.converters;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class SchedulerHintConverterTest {
  private static void verifyHintToSendSchemaNameConversion(String schedulerOptionHint, SendSchemaName expectedSendSchemaName) {
    Assertions.assertEquals(expectedSendSchemaName, SchedulerHintConverter.INSTANCE.apply(schedulerOptionHint).orElseThrow());
  }

  @Test
  void applyTest() {
    verifyHintToSendSchemaNameConversion("common-very-high", SendSchemaName.COMMON_VERY_HIGH);
    verifyHintToSendSchemaNameConversion("darf-config", SendSchemaName.DARF_CONFIG);
    verifyHintToSendSchemaNameConversion("dfol-high", SendSchemaName.DFOL_HIGH);
    verifyHintToSendSchemaNameConversion("dfol-low", SendSchemaName.DFOL_LOW);
    verifyHintToSendSchemaNameConversion("dfol-mid", SendSchemaName.DFOL_MID);
    verifyHintToSendSchemaNameConversion("dfol-mid-plus", SendSchemaName.DFOL_MID_PLUS);
    verifyHintToSendSchemaNameConversion("dfol-setup", SendSchemaName.DFOL_SETUP);
    verifyHintToSendSchemaNameConversion("drut-very-high", SendSchemaName.DRUT_VERY_HIGH);
    verifyHintToSendSchemaNameConversion("high", SendSchemaName.COMMON_HIGH);
    verifyHintToSendSchemaNameConversion("long", SendSchemaName.COMMON_LONG);
    verifyHintToSendSchemaNameConversion("low", SendSchemaName.COMMON_LOW);
    verifyHintToSendSchemaNameConversion("mid", SendSchemaName.COMMON_MID);
    verifyHintToSendSchemaNameConversion("normal", SendSchemaName.COMMON_NORMAL);
    verifyHintToSendSchemaNameConversion("rdns-mid", SendSchemaName.RDNS_MID);
    verifyHintToSendSchemaNameConversion("rswdl", SendSchemaName.COMMON_RSWDL);
    verifyHintToSendSchemaNameConversion("rswdl-low", SendSchemaName.RSWDL_LOW);
    verifyHintToSendSchemaNameConversion("rswdl-mid", SendSchemaName.RSWDL_MID);
    verifyHintToSendSchemaNameConversion("setup", SendSchemaName.COMMON_SETUP);
    verifyHintToSendSchemaNameConversion("short", SendSchemaName.COMMON_SHORT);
    verifyHintToSendSchemaNameConversion("uptime-very-high", SendSchemaName.UPTIME_VERY_HIGH);
    verifyHintToSendSchemaNameConversion("vlink-high", SendSchemaName.VLINK_HIGH);
    verifyHintToSendSchemaNameConversion("vlink-low", SendSchemaName.VLINK_LOW);
    verifyHintToSendSchemaNameConversion("sat-only", SendSchemaName.SAT_ONLY);
    verifyHintToSendSchemaNameConversion("sms-only", SendSchemaName.SMS_ONLY);

    verifyHintToSendSchemaNameConversion("ONE_MINUTE-LOW", new SendSchemaName("ONE_MINUTE-LOW"));
    verifyHintToSendSchemaNameConversion("FIVE_MINUTES-LOW", new SendSchemaName("FIVE_MINUTES-LOW"));
    verifyHintToSendSchemaNameConversion("TEN_MINUTES-LOW", new SendSchemaName("TEN_MINUTES-LOW"));
    verifyHintToSendSchemaNameConversion("ONE_WEEK-NORMAL", new SendSchemaName("ONE_WEEK-NORMAL"));
    verifyHintToSendSchemaNameConversion("TWO_WEEKS-HIGH", new SendSchemaName("TWO_WEEKS-HIGH"));

    verifyHintToSendSchemaNameConversion("uptime-short", SendSchemaName.COMMON_SHORT);
    verifyHintToSendSchemaNameConversion("whatever-very-high", SendSchemaName.COMMON_VERY_HIGH);
    verifyHintToSendSchemaNameConversion("very-high", SendSchemaName.COMMON_HIGH);
    verifyHintToSendSchemaNameConversion("common-high", SendSchemaName.COMMON_HIGH);
    verifyHintToSendSchemaNameConversion("dfol:low", SendSchemaName.COMMON_NORMAL);
    verifyHintToSendSchemaNameConversion("common-normal", SendSchemaName.COMMON_NORMAL);
    verifyHintToSendSchemaNameConversion(" ", SendSchemaName.COMMON_NORMAL);
    verifyHintToSendSchemaNameConversion("xyz", SendSchemaName.COMMON_NORMAL);
  }

  @Test
  void invalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> SchedulerHintConverter.INSTANCE.apply(null), "schedulerOptionHint must not be null");
    AssertThrows.illegalArgumentException(() -> SchedulerHintConverter.INSTANCE.apply(""), "schedulerOptionHint must not be empty");
  }
}
