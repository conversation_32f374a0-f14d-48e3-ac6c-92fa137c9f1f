package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class SendSchemaProcessorMetricReporterTest {
  private static final String SEND_SCHEMA_PROCESSOR = "send-schema-processor";
  private static final String TYPE = "type";

  @Test
  void onDeviceNotFoundTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaProcessorMetricReporter::new, (meterRegistry, sendSchemaProcessorMetricReporter) -> {
      sendSchemaProcessorMetricReporter.onDeviceNotFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "DEVICE_NOT_FOUND"), 1);

      sendSchemaProcessorMetricReporter.onDeviceNotFound();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "DEVICE_NOT_FOUND"), 2);
    });
  }

  @Test
  void onFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaProcessorMetricReporter::new, (meterRegistry, sendSchemaProcessorMetricReporter) -> {
      sendSchemaProcessorMetricReporter.onFailure();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "FAILURE"), 1);

      sendSchemaProcessorMetricReporter.onFailure();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "FAILURE"), 2);
    });
  }

  @Test
  void onGlobalTimeoutTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaProcessorMetricReporter::new, (meterRegistry, sendSchemaProcessorMetricReporter) -> {
      sendSchemaProcessorMetricReporter.onGlobalTimeout();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "GLOBAL_TIMEOUT"), 1);

      sendSchemaProcessorMetricReporter.onGlobalTimeout();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "GLOBAL_TIMEOUT"), 2);
    });
  }

  @Test
  void onStepsExhaustedTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaProcessorMetricReporter::new, (meterRegistry, sendSchemaProcessorMetricReporter) -> {
      sendSchemaProcessorMetricReporter.onStepsExhausted();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "STEPS_EXHAUSTED"), 1);

      sendSchemaProcessorMetricReporter.onStepsExhausted();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "STEPS_EXHAUSTED"), 2);
    });
  }

  @Test
  void onSuccessTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaProcessorMetricReporter::new, (meterRegistry, sendSchemaProcessorMetricReporter) -> {
      sendSchemaProcessorMetricReporter.onSuccess();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "SUCCESS"), 1);

      sendSchemaProcessorMetricReporter.onSuccess();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "SUCCESS"), 2);
    });
  }

  @Test
  void onTimeoutTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaProcessorMetricReporter::new, (meterRegistry, sendSchemaProcessorMetricReporter) -> {
      sendSchemaProcessorMetricReporter.onTimeout();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "TIMEOUT"), 1);

      sendSchemaProcessorMetricReporter.onTimeout();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA_PROCESSOR, Tags.of(TYPE, "TIMEOUT"), 2);
    });
  }
}
