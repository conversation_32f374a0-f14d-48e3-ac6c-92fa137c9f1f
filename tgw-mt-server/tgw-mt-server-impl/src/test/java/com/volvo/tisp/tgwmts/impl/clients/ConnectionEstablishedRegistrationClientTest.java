package com.volvo.tisp.tgwmts.impl.clients;

import java.io.Closeable;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.web.reactive.function.client.WebClient;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Timestamp;
import com.volvo.tisp.protobuf.common.PlatformIdentifier;
import com.volvo.tisp.tce.proto.ConnectionEstablishedRegistration;
import com.volvo.tisp.tce.proto.ConnectionEstablishedRegistrations;
import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;
import com.volvo.tisp.tgwmts.impl.clients.metrics.reporter.ConnectionEstablishedRegistrationReporter;
import com.volvo.tisp.tgwmts.impl.conf.properties.MoServerReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.tgwmts.impl.utils.ProtobufUtils;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ConnectionEstablishedRegistrationClientTest {
  private static final boolean ENABLE_CONNECTION_ESTABLISHED_REGISTRATION_FAILURE_PERSIST = true;
  private static final Instant INSTANT_1 = Instant.ofEpochSecond(10);
  private static final Instant INSTANT_2 = Instant.ofEpochSecond(20);
  private static final String REQUEST_PATH = "/connectionEstablishedRegistrations";
  private static final Vpi VPI_1 = Vpi.ofString("12345678901234567890ABCDEFABCDEF");
  private static final Vpi VPI_2 = Vpi.ofString("12345678901234567890ABCDEFABCDFF");

  public static ConnectionEstablishedRegistration createConnectionEstablishedRegistration(Vpi vpi, Instant instant) {
    return ConnectionEstablishedRegistration.newBuilder()
        .setExpires(Timestamp.newBuilder().setSeconds(instant.getEpochSecond()).build())
        .setVpi(PlatformIdentifier.newBuilder().setValue(vpi.toString()).build())
        .build();
  }

  private static List<LoggedRequest> awaitRequest(WireMockServer wireMockServer) {
    return Awaitility.await()
        .atMost(Duration.ofSeconds(5))
        .until(() -> wireMockServer.findAll(WireMock.postRequestedFor(WireMock.urlPathEqualTo(REQUEST_PATH))), loggedRequests -> !loggedRequests.isEmpty());
  }

  private static Timestamp computeExpirationTimestamp(SendSchemaStep sendSchemaStep, Instant now) {
    return ProtobufUtils.createTimestamp(now.plus(sendSchemaStep.getWaitDuration()));
  }

  private static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  private static ConnectionEstablishedRegistration createConnectionEstablishedRegistration(ScheduledActiveMtMessage scheduledActiveMtMessage, Clock clock) {
    return ConnectionEstablishedRegistration.newBuilder()
        .setVpi(createPlatformIdentifier(scheduledActiveMtMessage.identifiedActiveMtMessage().persistedDeviceInfo().getDeviceInfo().getVpi()))
        .setExpires(computeExpirationTimestamp(scheduledActiveMtMessage.sendSchemaStep(), clock.instant()))
        .build();
  }

  private static ConnectionEstablishedRegistrations createConnectionEstablishedRegistrations() {
    return ConnectionEstablishedRegistrations.newBuilder()
        .addConnectionEstablishedRegistrations(createConnectionEstablishedRegistration(VPI_1, INSTANT_1))
        .addConnectionEstablishedRegistrations(createConnectionEstablishedRegistration(VPI_2, INSTANT_2))
        .build();
  }

  private static PlatformIdentifier createPlatformIdentifier(Vpi vpi) {
    return PlatformIdentifier.newBuilder()
        .setValue(vpi.toString())
        .build();
  }

  private static ScheduledActiveMtMessage createScheduledActiveMtMessage(SendSchemaStep sendSchemaStep) {
    return new ScheduledActiveMtMessage(TestUtil.createIdentifiedActiveMtMessage(), sendSchemaStep);
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(true));
    return new WireMockServer(wireMockConfiguration);
  }

  private static void stubMoServer(WireMockServer wiremockServer, ResponseDefinitionBuilder responseDefinitionBuilder) {
    wiremockServer.stubFor(WireMock.post(REQUEST_PATH).willReturn(responseDefinitionBuilder));
  }

  @Test
  void reRegisterConnectionEstablishedBadRequestTest() throws InvalidProtocolBufferException {
    ConnectionEstablishedRegistrationReporter connectionEstablishedRegistrationReporter = Mockito.mock(
        ConnectionEstablishedRegistrationReporter.class);
    MoServerReactiveClientProperties moServerReactiveClientProperties = Mockito.mock(MoServerReactiveClientProperties.class);
    Mockito.when(moServerReactiveClientProperties.getConnectionEstablishedRegistrationsMultiRequestPath()).thenReturn(REQUEST_PATH);

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMoServer(wireMockServer, WireMock.badRequest());
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());

      Clock clock = Mockito.mock(Clock.class);
      Mockito.when(clock.instant()).thenReturn(TestUtil.INSTANT);
      ConnectionEstablishedCacheService connectionEstablishedCacheService = Mockito.mock(ConnectionEstablishedCacheService.class);

      ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient = new ConnectionEstablishedRegistrationClient(
          clock, webClient, connectionEstablishedRegistrationReporter, moServerReactiveClientProperties, connectionEstablishedCacheService);

      Boolean result = connectionEstablishedRegistrationClient.reRegister(List.of(Map.entry(VPI_1, INSTANT_1), Map.entry(VPI_2, INSTANT_2)))
          .block();

      Assertions.assertFalse(result);

      LoggedRequest loggedRequest = awaitRequest(wireMockServer).iterator().next();
      Assertions.assertEquals("application/x-protobuf", loggedRequest.getHeader("Content-Type"));
      Assertions.assertEquals(createConnectionEstablishedRegistrations(), ConnectionEstablishedRegistrations.parseFrom(loggedRequest.getBody()));

      Mockito.verify(connectionEstablishedRegistrationReporter).onReRegister();
      Mockito.verify(moServerReactiveClientProperties).getConnectionEstablishedRegistrationsMultiRequestPath();
      Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationReporter, moServerReactiveClientProperties);
    }
  }

  @Test
  void reRegisterMultiConnectionEstablishedTest() throws InvalidProtocolBufferException {
    ConnectionEstablishedRegistrationReporter connectionEstablishedRegistrationReporter = Mockito.mock(
        ConnectionEstablishedRegistrationReporter.class);
    MoServerReactiveClientProperties moServerReactiveClientProperties = Mockito.mock(MoServerReactiveClientProperties.class);
    Mockito.when(moServerReactiveClientProperties.getConnectionEstablishedRegistrationsMultiRequestPath()).thenReturn(REQUEST_PATH);

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMoServer(wireMockServer, WireMock.ok());
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());

      Clock clock = Mockito.mock(Clock.class);
      Mockito.when(clock.instant()).thenReturn(TestUtil.INSTANT);
      ConnectionEstablishedCacheService connectionEstablishedCacheService = Mockito.mock(ConnectionEstablishedCacheService.class);

      ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient = new ConnectionEstablishedRegistrationClient(
          clock, webClient, connectionEstablishedRegistrationReporter, moServerReactiveClientProperties, connectionEstablishedCacheService);

      Boolean result = connectionEstablishedRegistrationClient.reRegister(List.of(Map.entry(VPI_1, INSTANT_1), Map.entry(VPI_2, INSTANT_2)))
          .block();

      Assertions.assertTrue(result);

      LoggedRequest loggedRequest = awaitRequest(wireMockServer).iterator().next();
      Assertions.assertEquals("application/x-protobuf", loggedRequest.getHeader("Content-Type"));
      Assertions.assertEquals(createConnectionEstablishedRegistrations(), ConnectionEstablishedRegistrations.parseFrom(loggedRequest.getBody()));

      Mockito.verify(connectionEstablishedRegistrationReporter).onReRegister();
      Mockito.verify(moServerReactiveClientProperties).getConnectionEstablishedRegistrationsMultiRequestPath();
      Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationReporter, connectionEstablishedCacheService, moServerReactiveClientProperties);
    }
  }

  @Test
  void registerConnectionEstablishedInvalidParameterTest() {
    ConnectionEstablishedRegistrationClient connectionEstablishedRestClient = new ConnectionEstablishedRegistrationClient(Mockito.mock(Clock.class),
        Mockito.mock(WebClient.class), Mockito.mock(ConnectionEstablishedRegistrationReporter.class), Mockito.mock(MoServerReactiveClientProperties.class),
        Mockito.mock(ConnectionEstablishedCacheService.class));

    AssertThrows.illegalArgumentException(() -> connectionEstablishedRestClient.registerConnectionEstablished(null),
        "scheduledActiveMtMessage must not be null");
  }

  @Test
  void registerSingleConnectionEstablishedBadRequestTest() throws InvalidProtocolBufferException {
    ConnectionEstablishedRegistrationReporter connectionEstablishedRegistrationReporter = Mockito.mock(
        ConnectionEstablishedRegistrationReporter.class);
    MoServerReactiveClientProperties moServerReactiveClientProperties = Mockito.mock(MoServerReactiveClientProperties.class);
    Mockito.when(moServerReactiveClientProperties.getConnectionEstablishedRegistrationsSingleRequestPath()).thenReturn(REQUEST_PATH);
    Mockito.when(moServerReactiveClientProperties.isEnableConnectionEstablishedRegistrationFailurePersist())
        .thenReturn(ENABLE_CONNECTION_ESTABLISHED_REGISTRATION_FAILURE_PERSIST);

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMoServer(wireMockServer, WireMock.badRequest());
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());

      Clock clock = Mockito.mock(Clock.class);
      Mockito.when(clock.instant()).thenReturn(TestUtil.INSTANT);
      ConnectionEstablishedCacheService connectionEstablishedCacheService = Mockito.mock(ConnectionEstablishedCacheService.class);

      ScheduledActiveMtMessage scheduledActiveMtMessage = createScheduledActiveMtMessage(TestUtil.createSatSendSchemaStep());

      ConnectionEstablishedRegistration connectionEstablishedRegistration = createConnectionEstablishedRegistration(scheduledActiveMtMessage, clock);

      ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient = new ConnectionEstablishedRegistrationClient(
          clock, webClient, connectionEstablishedRegistrationReporter, moServerReactiveClientProperties, connectionEstablishedCacheService);

      connectionEstablishedRegistrationClient.registerConnectionEstablished(scheduledActiveMtMessage);

      LoggedRequest loggedRequest = awaitRequest(wireMockServer).iterator().next();
      Assertions.assertEquals("application/x-protobuf", loggedRequest.getHeader("Content-Type"));
      Assertions.assertEquals(connectionEstablishedRegistration, ConnectionEstablishedRegistration.parseFrom(loggedRequest.getBody()));

      Mockito.verify(moServerReactiveClientProperties).getConnectionEstablishedRegistrationsSingleRequestPath();
      Mockito.verify(moServerReactiveClientProperties).isEnableConnectionEstablishedRegistrationFailurePersist();

      Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationReporter, moServerReactiveClientProperties);
    }
  }

  @Test
  void registerSingleConnectionEstablishedTest() throws InvalidProtocolBufferException {
    ConnectionEstablishedRegistrationReporter connectionEstablishedRegistrationReporter = Mockito.mock(
        ConnectionEstablishedRegistrationReporter.class);
    MoServerReactiveClientProperties moServerReactiveClientProperties = Mockito.mock(MoServerReactiveClientProperties.class);
    Mockito.when(moServerReactiveClientProperties.getConnectionEstablishedRegistrationsSingleRequestPath()).thenReturn(REQUEST_PATH);

    try (WireMockServerWrapper wireMockServerWrapper = createAndStartWireMockServer()) {
      WireMockServer wireMockServer = wireMockServerWrapper.wireMockServer;
      stubMoServer(wireMockServer, WireMock.ok());
      WebClient webClient = WebClient.create(wireMockServer.baseUrl());

      Clock clock = Mockito.mock(Clock.class);
      Mockito.when(clock.instant()).thenReturn(TestUtil.INSTANT);
      ConnectionEstablishedCacheService connectionEstablishedCacheService = Mockito.mock(ConnectionEstablishedCacheService.class);

      ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient = new ConnectionEstablishedRegistrationClient(
          clock, webClient, connectionEstablishedRegistrationReporter, moServerReactiveClientProperties, connectionEstablishedCacheService);

      ScheduledActiveMtMessage scheduledActiveMtMessage = createScheduledActiveMtMessage(TestUtil.createSatSendSchemaStep());
      ConnectionEstablishedRegistration connectionEstablishedRegistration = createConnectionEstablishedRegistration(scheduledActiveMtMessage, clock);

      connectionEstablishedRegistrationClient.registerConnectionEstablished(scheduledActiveMtMessage);

      LoggedRequest loggedRequest = awaitRequest(wireMockServer).iterator().next();
      Assertions.assertEquals("application/x-protobuf", loggedRequest.getHeader("Content-Type"));
      Assertions.assertEquals(connectionEstablishedRegistration,
          ConnectionEstablishedRegistration.parseFrom(loggedRequest.getBody()));

      Mockito.verify(moServerReactiveClientProperties).getConnectionEstablishedRegistrationsSingleRequestPath();
      Mockito.verify(moServerReactiveClientProperties).isEnableConnectionEstablishedRegistrationFailurePersist();
      Mockito.verifyNoMoreInteractions(connectionEstablishedRegistrationReporter, connectionEstablishedCacheService, moServerReactiveClientProperties);
    }
  }

  private record WireMockServerWrapper(WireMockServer wireMockServer) implements Closeable {
    @Override
    public void close() {
      wireMockServer.stop();
    }
  }
}
