package com.volvo.tisp.tgwmts.impl.converters;

import java.util.Map;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.google.protobuf.ByteString;
import com.volvo.tisp.tce.proto.MtMessage;
import com.volvo.tisp.tgw.device.info.database.model.WtpVersion;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.tgwmts.impl.utils.AppConstants;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageProtobufOutputConverterFunctionTest {
  private static EncodedActiveMtMessage createEncodedActiveMtMessage() {
    return new EncodedActiveMtMessage(new ScheduledActiveMtMessage(TestUtil.createIdentifiedActiveMtMessage(), TestUtil.createSmsSendSchemaStep()),
        TestUtil.createSrp12ServiceRoutingPduWrapper());
  }

  private static void verifyMessagePropertiesMap(Map<String, String> messagePropertiesMap, int expectedSize) {
    Assertions.assertEquals(expectedSize, messagePropertiesMap.size());
    Assertions.assertEquals(TestUtil.IPV4_ADDRESS.toString(), messagePropertiesMap.get("IP_DST_ADDRESS"));
    Assertions.assertEquals(TestUtil.IPV4_PORT.toString(), messagePropertiesMap.get("IP_DST_PORT"));
    Assertions.assertEquals(TestUtil.MOBILE_NETWORK_OPERATOR.toString(), messagePropertiesMap.get("OPERATOR"));
    Assertions.assertEquals(TestUtil.MSISDN.toString(), messagePropertiesMap.get("SMPP_DEST_ADDRESS"));
    Assertions.assertEquals(TestUtil.OBS_ALIAS.toString(), messagePropertiesMap.get("SRP_OBS_ALIAS"));
    Assertions.assertEquals(TestUtil.TID.toString(), messagePropertiesMap.get(AppConstants.TRACKING_ID));
    Assertions.assertEquals(TestUtil.VPI.toString(), messagePropertiesMap.get("VPI"));
    Assertions.assertEquals(WtpVersion.VERSION_1.name(), messagePropertiesMap.get("WTP_VERSION"));

    Assertions.assertEquals(String.valueOf(TestUtil.SEND_SCHEMA_STEP_WAIT_DURATION.getSeconds()), messagePropertiesMap.get("SMS_VALID_PERIOD_SECONDS"));
  }

  private static void verifyMtMessage(MtMessage mtMessage) {
    ByteString expectedPayload = ByteString.copyFrom(ImmutableByteArray.EMPTY.toByteArray());
    Assertions.assertEquals(expectedPayload, mtMessage.getPayload());
    Assertions.assertEquals(TestUtil.VPI.toString(), mtMessage.getVehicleID());
    Assertions.assertEquals(TestUtil.MESSAGE_ID, mtMessage.getMessageId().getValue());
  }

  @Test
  void applyTest() {
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();
    ServiceRoutingPduWrapper serviceRoutingPduWrapper = encodedActiveMtMessage.serviceRoutingPduWrapper();
    Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction = TestUtil.mockFunction();

    Mockito.when(encodeServiceRoutingPduWrapperFunction.apply(serviceRoutingPduWrapper)).thenReturn(ImmutableByteArray.EMPTY);

    MtMessage mtMessage = MtMessageProtobufOutputConverterFunction.create(encodeServiceRoutingPduWrapperFunction).apply(encodedActiveMtMessage);

    verifyMtMessage(mtMessage);
    verifyMessagePropertiesMap(mtMessage.getMessagePropertiesMap(), 9);
    Mockito.verify(encodeServiceRoutingPduWrapperFunction).apply(serviceRoutingPduWrapper);
    Mockito.verifyNoMoreInteractions(encodeServiceRoutingPduWrapperFunction);
  }

  @Test
  void applyVwtpTest() {
    EncodedActiveMtMessage encodedActiveMtMessage = createEncodedActiveMtMessage();
    ServiceRoutingPduWrapper serviceRoutingPduWrapper = encodedActiveMtMessage.serviceRoutingPduWrapper();
    Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction = TestUtil.mockFunction();

    Mockito.when(encodeServiceRoutingPduWrapperFunction.apply(serviceRoutingPduWrapper)).thenReturn(ImmutableByteArray.EMPTY);

    MtMessage mtMessage = MtMessageProtobufOutputConverterFunction.create(encodeServiceRoutingPduWrapperFunction).apply(encodedActiveMtMessage);

    verifyMtMessage(mtMessage);
    Mockito.verify(encodeServiceRoutingPduWrapperFunction).apply(serviceRoutingPduWrapper);
    Mockito.verifyNoMoreInteractions(encodeServiceRoutingPduWrapperFunction);
  }

  @Test
  void invalidCreateTest() {
    AssertThrows.illegalArgumentException(() -> MtMessageProtobufOutputConverterFunction.create(null),
        "encodeServiceRoutingPduWrapperFunction must not be null");
  }
}
