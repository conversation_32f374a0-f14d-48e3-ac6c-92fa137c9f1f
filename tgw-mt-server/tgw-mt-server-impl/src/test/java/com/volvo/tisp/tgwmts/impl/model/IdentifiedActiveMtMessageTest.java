package com.volvo.tisp.tgwmts.impl.model;

import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class IdentifiedActiveMtMessageTest {
  private static final JoinedActiveMtMessage JOINED_ACTIVE_MT_MESSAGE = TestUtil.createJoinedActiveMtMessage();
  private static final PersistedDeviceInfo PERSISTED_DEVICE_INFO = TestUtil.createPersistedDeviceInfo();

  @Test
  void equalsAndHashcodeTest() {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = TestUtil.createIdentifiedActiveMtMessage();
    AssertUtils.assertEqualsAndHashCode(identifiedActiveMtMessage, TestUtil.createIdentifiedActiveMtMessage());

    IdentifiedActiveMtMessage identifiedActiveMtMessage2 = new IdentifiedActiveMtMessage(JOINED_ACTIVE_MT_MESSAGE,
        TestUtil.createPersistedDeviceInfoBuilder().setCreated(Instant.now()).build());

    Assertions.assertNotEquals(identifiedActiveMtMessage, identifiedActiveMtMessage2);

    IdentifiedActiveMtMessage identifiedActiveMtMessage3 = new IdentifiedActiveMtMessage(
        TestUtil.createJoinedActiveMtMessage(ActiveMtMessageId.ofLong(15), TestUtil.createSrpOption()), PERSISTED_DEVICE_INFO);

    Assertions.assertNotEquals(identifiedActiveMtMessage, identifiedActiveMtMessage3);
  }

  @Test
  void getJoinedActiveMtMessageTest() {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = new IdentifiedActiveMtMessage(JOINED_ACTIVE_MT_MESSAGE, PERSISTED_DEVICE_INFO);

    Assertions.assertSame(JOINED_ACTIVE_MT_MESSAGE, identifiedActiveMtMessage.joinedActiveMtMessage());
  }

  @Test
  void getPersistedDeviceInfoTest() {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = new IdentifiedActiveMtMessage(JOINED_ACTIVE_MT_MESSAGE, PERSISTED_DEVICE_INFO);

    Assertions.assertSame(PERSISTED_DEVICE_INFO, identifiedActiveMtMessage.persistedDeviceInfo());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new IdentifiedActiveMtMessage(null, PERSISTED_DEVICE_INFO), "joinedActiveMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> new IdentifiedActiveMtMessage(JOINED_ACTIVE_MT_MESSAGE, null), "persistedDeviceInfo must not be null");
  }
}
