package com.volvo.tisp.tgwmts.impl.model;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.Optional;

import static com.volvo.tisp.tgwmts.impl.util.TestUtil.*;

class MtMessageDetailsTest {
    private static final MtMessageDetails MT_MESSAGE_DETAILS = TestUtil.createMtMessageDetails();

    @Test
    void tid() {
        Assertions.assertEquals(TID.toString(), MT_MESSAGE_DETAILS.tid());
    }

    @Test
    void sendSchemaName() {
        Assertions.assertEquals(SEND_SCHEMA_NAME.toString(), MT_MESSAGE_DETAILS.sendSchemaName());
    }

    @Test
    void created() {
        Assertions.assertEquals(INSTANT, MT_MESSAGE_DETAILS.created());

    }

    @Test
    void queueId() {
        Assertions.assertEquals(QUEUE_ID.toString(), MT_MESSAGE_DETAILS.queueId());
    }

    @Test
    void serviceId() {
        Assertions.assertEquals(SRP_DESTINATION_SERVICE.toInt(), MT_MESSAGE_DETAILS.serviceId());
    }

    @Test
    void serviceVersion() {
        Assertions.assertEquals(SRP_DESTINATION_VERSION.toShort(), MT_MESSAGE_DETAILS.serviceVersion());
    }

    @Test
    void replyTo() {
            Assertions.assertEquals(Optional.of(REPLY_TO.toString()), MT_MESSAGE_DETAILS.replyTo());
    }

    @Test
    void activeMessageDetails() {
        Assertions.assertEquals(createActiveMessageDetails(), MT_MESSAGE_DETAILS.activeMessageDetails());
    }

}