package com.volvo.tisp.tgwmts.impl.rest;

import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.volvo.tisp.tgwmts.impl.services.MtRetrySchedulerService;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;

class MtRetryRestControllerTest {
  @Test
  void attemptRetryTest() {
    MtRetrySchedulerService mtRetrySchedulerService = Mockito.mock(MtRetrySchedulerService.class);

    ResponseEntity<String> responseEntity = new MtRetryRestController(mtRetrySchedulerService).attemptRetry(List.of(TestUtil.VPI.toString()));

    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    Assertions.assertEquals("Attempting to resend MT-messages in WAIT state for 1 vehicle(s)", responseEntity.getBody());

    Mockito.verify(mtRetrySchedulerService).scheduleRetryTasks(List.of(TestUtil.VPI));
    Mockito.verifyNoMoreInteractions(mtRetrySchedulerService);
  }
}
