package com.volvo.tisp.tgwmts.impl.scheduler;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.impl.services.SendSchemaProcessor;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtSchedulerWorkerTest {
  private static final int BATCH_SIZE = 10;

  private static ActiveMtMessageWriterFactory mockActiveMtMessageWriterFactory(ActiveMtMessageWriter activeMtMessageWriter) {
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = Mockito.mock(ActiveMtMessageWriterFactory.class);
    Mockito.when(activeMtMessageWriterFactory.createReadCommitted()).thenReturn(activeMtMessageWriter);
    return activeMtMessageWriterFactory;
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(3), Instant.ofEpochSecond(5), Instant.ofEpochSecond(9));
    return clock;
  }

  private static SendSchemaProcessor mockSendSchemaProcessor(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage,
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter) {
    SendSchemaProcessor sendSchemaProcessor = Mockito.mock(SendSchemaProcessor.class);
    Mockito.when(sendSchemaProcessor.executeNextSendSchemaStep(activeMtMessageWriter, joinedActiveMtMessage)).thenReturn(updateActiveMtMessageParameter);
    return sendSchemaProcessor;
  }

  @Test
  void doWorkOnceTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    Mockito.when(activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(BATCH_SIZE)).thenReturn(List.of(joinedActiveMtMessage));
    UpdateActiveMtMessageParameter updateActiveMtMessageParameter = TestUtil.createUpdateActiveMtMessageParameter();
    SendSchemaProcessor sendSchemaProcessor = mockSendSchemaProcessor(activeMtMessageWriter, joinedActiveMtMessage,
        Optional.of(updateActiveMtMessageParameter));

    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = mockClock();
    MtSchedulerMetricReporter mtSchedulerMetricReporter = Mockito.mock(MtSchedulerMetricReporter.class);

    MtSchedulerWorker mtSchedulerWorker = new MtSchedulerWorker(activeMtMessageWriterFactory, BATCH_SIZE, clock, mtSchedulerMetricReporter,
        sendSchemaProcessor);
    Assertions.assertEquals(1, mtSchedulerWorker.doWorkOnce());

    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(activeMtMessageWriter).startTransaction();
    Mockito.verify(activeMtMessageWriter).findTimedOutActiveMtMessagesWithVpiLock(BATCH_SIZE);
    Mockito.verify(sendSchemaProcessor).executeNextSendSchemaStep(activeMtMessageWriter, joinedActiveMtMessage);
    Mockito.verify(activeMtMessageWriter).updateActiveMtMessages(List.of(updateActiveMtMessageParameter));
    Mockito.verify(activeMtMessageWriter).commitTransaction();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verify(mtSchedulerMetricReporter).onTimedOutMessagesSelected(Duration.ofSeconds(2));
    Mockito.verify(mtSchedulerMetricReporter).onTimedOutMessagesTotal(Duration.ofSeconds(6));
    Mockito.verifyNoMoreInteractions(activeMtMessageWriterFactory, activeMtMessageWriter, clock, mtSchedulerMetricReporter, sendSchemaProcessor);
  }

  @Test
  void invalidConstructorTest() {
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = Mockito.mock(ActiveMtMessageWriterFactory.class);
    Clock clock = Mockito.mock(Clock.class);
    MtSchedulerMetricReporter mtSchedulerMetricReporter = Mockito.mock(MtSchedulerMetricReporter.class);
    SendSchemaProcessor sendSchemaProcessor = Mockito.mock(SendSchemaProcessor.class);

    AssertThrows.illegalArgumentException(() -> new MtSchedulerWorker(null, 1, clock, mtSchedulerMetricReporter, sendSchemaProcessor),
        "activeMtMessageWriterFactory must not be null");
    AssertThrows.illegalArgumentException(() -> new MtSchedulerWorker(activeMtMessageWriterFactory, 0, clock, mtSchedulerMetricReporter, sendSchemaProcessor),
        "batchSize must be positive: 0");
    AssertThrows.illegalArgumentException(() -> new MtSchedulerWorker(activeMtMessageWriterFactory, 1, null, mtSchedulerMetricReporter, sendSchemaProcessor),
        "clock must not be null");
    AssertThrows.illegalArgumentException(() -> new MtSchedulerWorker(activeMtMessageWriterFactory, 1, clock, null, sendSchemaProcessor),
        "mtSchedulerMetricReporter must not be null");
    AssertThrows.illegalArgumentException(() -> new MtSchedulerWorker(activeMtMessageWriterFactory, 1, clock, mtSchedulerMetricReporter, null),
        "sendSchemaProcessor must not be null");
  }
}
