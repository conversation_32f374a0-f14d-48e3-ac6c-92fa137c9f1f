package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;

import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

class TgwNotifyEventMetricReporterTest {
  private static final String ASSET_NOTIFICATION = "asset-notification";
  private static final String CAPABILITY = "capability";
  private static final String PROCESS_STATUS = "process-status";
  private static final String STATE = "state";
  private static final AssetCapabilityState assetCapabilityState = new AssetCapabilityState(TestUtil.VPI, AssetCapabilityState.AssetCapability.WIFI, true);

  @Test
  void onAssetStateChangeProcessFailedTest() {
    MetricsReporterTestUtils.initReporterAndTest(TgwNotifyEventMetricReporter::new, (meterRegistry, tgwNotifyEventMetricReporter) -> {
      tgwNotifyEventMetricReporter.onAssetStateChangeProcessFailed(assetCapabilityState);

      MetricsReporterTestUtils.checkCounter(meterRegistry, ASSET_NOTIFICATION, Tags.of(Tag.of(CAPABILITY, assetCapabilityState.assetCapability().name()),
          Tag.of(STATE, String.valueOf(assetCapabilityState.isEnabled())),
          Tag.of(PROCESS_STATUS, "failure")), 1);
    });
  }

  @Test
  void onAssetStateChangeProcessedTest() {
    MetricsReporterTestUtils.initReporterAndTest(TgwNotifyEventMetricReporter::new, (meterRegistry, tgwNotifyEventMetricReporter) -> {
      tgwNotifyEventMetricReporter.onAssetStateChangeProcessed(assetCapabilityState);

      MetricsReporterTestUtils.checkCounter(meterRegistry, ASSET_NOTIFICATION, Tags.of(Tag.of(CAPABILITY, assetCapabilityState.assetCapability().name()),
          Tag.of(STATE, String.valueOf(assetCapabilityState.isEnabled())),
          Tag.of(PROCESS_STATUS, "success")), 1);
    });
  }

  @Test
  void onAssetStateChangeReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(TgwNotifyEventMetricReporter::new, (meterRegistry, tgwNotifyEventMetricReporter) -> {
      tgwNotifyEventMetricReporter.onAssetStateChangeReceived(assetCapabilityState);

      MetricsReporterTestUtils.checkCounter(meterRegistry, ASSET_NOTIFICATION, Tags.of(Tag.of(CAPABILITY, assetCapabilityState.assetCapability().name()),
          Tag.of(STATE, String.valueOf(assetCapabilityState.isEnabled())),
          Tag.of(PROCESS_STATUS, "received")), 1);
    });
  }

  @Test
  void onIgnoredNotifyEventTest() {
    MetricsReporterTestUtils.initReporterAndTest(TgwNotifyEventMetricReporter::new, (meterRegistry, tgwNotifyEventMetricReporter) -> {
      tgwNotifyEventMetricReporter.onIgnoredNotifyEvent();

      MetricsReporterTestUtils.checkCounter(meterRegistry, "asset-notification-ignored", 1);
    });
  }
}
