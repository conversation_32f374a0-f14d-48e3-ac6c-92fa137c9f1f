package com.volvo.tisp.tgwmts.impl.jms;

import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.tce.api.v2.MtMessage;

class MtMessageJmsControllerTest {
  private static final RuntimeException RUNTIME_EXCEPTION = new RuntimeException("test");

  private static void callReceiveMtMessageInTispContext(MtMessageJmsController mtMessageJmsController, JmsMessage<MtMessage> jmsMessage) {
    TispContext.runInContext(() -> {
      mtMessageJmsController.receiveMtMessage(jmsMessage);
    });
  }

  private static <T> JmsMessage<T> mockJmsMessage(T body) {
    JmsMessage<T> jmsMessage = Mockito.mock(JmsMessage.class);
    Mockito.when(jmsMessage.payload()).thenReturn(body);
    return jmsMessage;
  }

  private static Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mockMtMessageInputConverterFunction(
      Either<RuntimeException, ReceivedMtMessage> either) {
    Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = TestUtil.mockFunction();
    Mockito.when(mtMessageInputConverterFunction.apply(ArgumentMatchers.any())).thenReturn(either);
    return mtMessageInputConverterFunction;
  }

  private static Consumer<ReceivedMtMessage> mockReceivedMtMessageConsumer() {
    return Mockito.mock(Consumer.class);
  }

  @Test
  void failingReceiveMtMessageTest() {
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = mockMtMessageInputConverterFunction(
        Either.left(RUNTIME_EXCEPTION));
    Consumer<ReceivedMtMessage> receivedMtMessageConsumer = mockReceivedMtMessageConsumer();

    MtMessageJmsController mtMessageJmsController = new MtMessageJmsController(mtMessageInputConverterFunction, mtMessageMetricReporter,
        receivedMtMessageConsumer);

    JmsMessage<MtMessage> jmsMessage = mockJmsMessage(Mockito.mock(MtMessage.class));

    callReceiveMtMessageInTispContext(mtMessageJmsController, jmsMessage);

    Mockito.verify(mtMessageMetricReporter).onMtV2Invalid();
    Mockito.verify(mtMessageInputConverterFunction).apply(ArgumentMatchers.any());
    Mockito.verifyNoMoreInteractions(receivedMtMessageConsumer, mtMessageMetricReporter, mtMessageInputConverterFunction);
  }

  @Test
  void receiveMtMessageNullParameterTest() {
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = TestUtil.mockFunction();
    Consumer<ReceivedMtMessage> receivedMtMessageConsumer = mockReceivedMtMessageConsumer();

    MtMessageJmsController mtMessageJmsController = new MtMessageJmsController(mtMessageInputConverterFunction, mtMessageMetricReporter,
        receivedMtMessageConsumer);

    AssertThrows.illegalArgumentException(() -> mtMessageJmsController.receiveMtMessage(null), "jmsMessage must not be null");
    Mockito.verifyNoMoreInteractions(receivedMtMessageConsumer, mtMessageMetricReporter, mtMessageInputConverterFunction);
  }

  @Test
  void receiveMtMessageTest() {
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    ReceivedMtMessage receivedMtMessage = TestUtil.createReceivedMtMessage();
    Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction = mockMtMessageInputConverterFunction(
        Either.right(receivedMtMessage));
    Consumer<ReceivedMtMessage> receivedMtMessageConsumer = mockReceivedMtMessageConsumer();

    MtMessageJmsController mtMessageJmsController = new MtMessageJmsController(mtMessageInputConverterFunction, mtMessageMetricReporter,
        receivedMtMessageConsumer);
    JmsMessage<MtMessage> jmsMessage = mockJmsMessage(Mockito.mock(MtMessage.class));

    callReceiveMtMessageInTispContext(mtMessageJmsController, jmsMessage);

    Mockito.verify(mtMessageInputConverterFunction).apply(ArgumentMatchers.any());
    Mockito.verify(receivedMtMessageConsumer).accept(receivedMtMessage);
    Mockito.verifyNoMoreInteractions(receivedMtMessageConsumer, mtMessageMetricReporter, mtMessageInputConverterFunction);
  }
}
