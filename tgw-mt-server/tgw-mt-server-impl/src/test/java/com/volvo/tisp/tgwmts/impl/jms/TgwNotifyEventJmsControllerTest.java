package com.volvo.tisp.tgwmts.impl.jms;

import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.EnumSource;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.connectivity.cms.events.model.CapabilityState;
import com.volvo.connectivity.cms.events.model.ConnectivityCapability;
import com.volvo.connectivity.cms.events.model.Satellite;
import com.volvo.connectivity.cms.events.model.TgwAsset;
import com.volvo.connectivity.cms.events.model.TgwNotifyEvent;
import com.volvo.connectivity.cms.events.model.Wifi;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.impl.services.TgwNotifyEventProcessor;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.TgwNotifyEventMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class TgwNotifyEventJmsControllerTest {
  private TgwNotifyEventJmsController tgwNotifyEventJmsController;
  private TgwNotifyEventMetricReporter tgwNotifyEventMetricReporter;
  private TgwNotifyEventProcessor tgwNotifyEventProcessor;

  private static JmsMessage<TgwNotifyEvent> createJmsMessage(TgwNotifyEvent tgwNotifyEvent) {
    JmsMessage<TgwNotifyEvent> jmsMessage = mock(JmsMessage.class);

    when(jmsMessage.payload()).thenReturn(tgwNotifyEvent);

    return jmsMessage;
  }

  private static TgwNotifyEvent createNonWifiTgwNotifyEvent() {
    TgwNotifyEvent tgwNotifyEvent = createTgwNotifyEvent();

    tgwNotifyEvent.setConnectivityCapability(createSatelliteConnectivityCapability());

    return tgwNotifyEvent;
  }

  private static ConnectivityCapability createSatelliteConnectivityCapability() {
    ConnectivityCapability connectivityCapability = new ConnectivityCapability();

    Satellite satellite = new Satellite();
    satellite.setState(CapabilityState.AVAILABLE);
    connectivityCapability.setSatellite(satellite);
    return connectivityCapability;
  }

  private static TgwNotifyEvent createTgwNotifyEvent() {
    TgwNotifyEvent tgwNotifyEvent = new TgwNotifyEvent();
    TgwAsset tgwAsset = new TgwAsset();
    tgwAsset.setVpi(TestUtil.VPI.toString());
    tgwNotifyEvent.setAsset(tgwAsset);
    return tgwNotifyEvent;
  }

  private static ConnectivityCapability createWifiConnectivityCapability(CapabilityState capabilityState) {
    ConnectivityCapability connectivityCapability = new ConnectivityCapability();

    Wifi wifi = new Wifi();
    wifi.setState(capabilityState);
    connectivityCapability.setWifi(wifi);
    return connectivityCapability;
  }

  private static TgwNotifyEvent createWifiTgwNotifyEvent(CapabilityState capabilityState) {
    TgwNotifyEvent tgwNotifyEvent = createTgwNotifyEvent();
    tgwNotifyEvent.setConnectivityCapability(createWifiConnectivityCapability(capabilityState));

    return tgwNotifyEvent;
  }

  private static boolean isWifiEnabled(CapabilityState capabilityState) {
    return switch (capabilityState) {
      case AVAILABLE -> true;
      case UNAVAILABLE -> false;
      default -> throw new IllegalArgumentException();
    };
  }

  @BeforeEach
  void setUp() {
    tgwNotifyEventProcessor = Mockito.mock(TgwNotifyEventProcessor.class);
    tgwNotifyEventMetricReporter = Mockito.mock(TgwNotifyEventMetricReporter.class);

    tgwNotifyEventJmsController = new TgwNotifyEventJmsController(tgwNotifyEventProcessor, tgwNotifyEventMetricReporter);
  }

  @Test
  void testInvalidTgwNotifyEvent() {
    AssertThrows.illegalArgumentException(() -> tgwNotifyEventJmsController.receiveTgwNotifyEvent(null), "jmsMessage must not be null");

    TgwNotifyEvent tgwNotifyEvent = createNonWifiTgwNotifyEvent();

    tgwNotifyEventJmsController.receiveTgwNotifyEvent(createJmsMessage(tgwNotifyEvent));

    verify(tgwNotifyEventMetricReporter).onIgnoredNotifyEvent();

    Mockito.verifyNoInteractions(tgwNotifyEventProcessor);
    Mockito.verifyNoMoreInteractions(tgwNotifyEventMetricReporter);
  }

  @ParameterizedTest
  @EnumSource(value = CapabilityState.class, mode = EnumSource.Mode.EXCLUDE, names = {"AVAILABLE", "UNAVAILABLE"})
  void testReceiveInvalidStatus(CapabilityState capabilityState) {
    TgwNotifyEvent tgwNotifyEvent = createWifiTgwNotifyEvent(capabilityState);

    tgwNotifyEventJmsController.receiveTgwNotifyEvent(createJmsMessage(tgwNotifyEvent));

    Mockito.verify(tgwNotifyEventMetricReporter).onIgnoredNotifyEvent();

    Mockito.verifyNoInteractions(tgwNotifyEventProcessor);
    Mockito.verifyNoMoreInteractions(tgwNotifyEventMetricReporter);
  }

  @ParameterizedTest
  @EnumSource(value = CapabilityState.class, names = {"AVAILABLE", "UNAVAILABLE"})
  void testReceiveTgwNotifyEvent(CapabilityState capabilityState) {
    TgwNotifyEvent tgwNotifyEvent = createWifiTgwNotifyEvent(capabilityState);

    tgwNotifyEventJmsController.receiveTgwNotifyEvent(createJmsMessage(tgwNotifyEvent));

    verify(tgwNotifyEventProcessor).process(
        ArgumentMatchers.eq(new AssetCapabilityState(TestUtil.VPI, AssetCapabilityState.AssetCapability.WIFI, isWifiEnabled(capabilityState))));

    Mockito.verifyNoInteractions(tgwNotifyEventMetricReporter);
    Mockito.verifyNoMoreInteractions(tgwNotifyEventProcessor);
  }
}
