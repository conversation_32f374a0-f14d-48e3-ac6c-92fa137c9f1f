package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.VehicleLockIdProviderMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class VehicleLockIdProviderTest {
  private static final VehicleLock VEHICLE_LOCK = VehicleLockBuilder.ofVpi(TestUtil.VPI);
  private static final Set<Vpi> VPIS = Set.of(TestUtil.VPI);

  private static ActiveMtMessageWriter mockActiveMtMessageWriter(Either<InsertionFailure, VehicleLockId> either,
      List<PersistedVehicleLock> persistedVehicleLocks) {
    return mockActiveMtMessageWriter(either, persistedVehicleLocks, persistedVehicleLocks);
  }

  private static ActiveMtMessageWriter mockActiveMtMessageWriter(Either<InsertionFailure, VehicleLockId> either,
      List<PersistedVehicleLock> persistedVehicleLocks1, List<PersistedVehicleLock> persistedVehicleLocks2) {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    Mockito.when(activeMtMessageWriter.findAndLockByVpis(VPIS)).thenReturn(persistedVehicleLocks1).thenReturn(persistedVehicleLocks2);
    Mockito.when(activeMtMessageWriter.insertVehicleLock(VEHICLE_LOCK)).thenReturn(either);
    return activeMtMessageWriter;
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(3), toInstants(5, 11, 19, 29, 41));
    return clock;
  }

  private static Instant[] toInstants(long... longValues) {
    return Arrays.stream(longValues).mapToObj(Instant::ofEpochSecond).toArray(Instant[]::new);
  }

  @Test
  void insertVehicleLockIfMissingAndAcquireVehicleLockIdTest() {
    Clock clock = mockClock();
    VehicleLockIdProviderMetricReporter vehicleLockIdProviderMetricReporter = Mockito.mock(VehicleLockIdProviderMetricReporter.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.VEHICLE_LOCK_ID),
        List.of(TestUtil.createPersistedVehicleLock()));

    VehicleLockIdProvider vehicleLockIdProvider = new VehicleLockIdProvider(clock, vehicleLockIdProviderMetricReporter);
    List<VehicleLockId> vehicleLockIds = vehicleLockIdProvider.insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);

    Assertions.assertEquals(List.of(TestUtil.VEHICLE_LOCK_ID), vehicleLockIds);

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(activeMtMessageWriter).findAndLockByVpis(VPIS);
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(2));
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, clock, vehicleLockIdProviderMetricReporter);
  }

  @Test
  void insertVehicleLockIfMissingAndAcquireVehicleLockIdWithDuplicateKeyTest() {
    Clock clock = mockClock();
    VehicleLockIdProviderMetricReporter vehicleLockIdProviderMetricReporter = Mockito.mock(VehicleLockIdProviderMetricReporter.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(
        Either.left(new InsertionFailure(InsertionFailureReason.DUPLICATE_KEY, new RuntimeException("test"))), List.of(),
        List.of(TestUtil.createPersistedVehicleLock()));

    VehicleLockIdProvider vehicleLockIdProvider = new VehicleLockIdProvider(clock, vehicleLockIdProviderMetricReporter);
    List<VehicleLockId> vehicleLockIds = vehicleLockIdProvider.insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);

    Assertions.assertEquals(List.of(TestUtil.VEHICLE_LOCK_ID), vehicleLockIds);

    Mockito.verify(clock, Mockito.times(5)).instant();
    Mockito.verify(activeMtMessageWriter, Mockito.times(2)).findAndLockByVpis(VPIS);
    Mockito.verify(activeMtMessageWriter).insertVehicleLock(VEHICLE_LOCK);
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(2));
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVehicleLockInsertionFailure(InsertionFailureReason.DUPLICATE_KEY);
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(10));
    Mockito.verifyNoMoreInteractions(clock, activeMtMessageWriter, vehicleLockIdProviderMetricReporter);
  }

  @Test
  void insertVehicleLockIfMissingAndAcquireVehicleLockIdWithNoVehicleLockTest() {
    Clock clock = mockClock();
    VehicleLockIdProviderMetricReporter vehicleLockIdProviderMetricReporter = Mockito.mock(VehicleLockIdProviderMetricReporter.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.VEHICLE_LOCK_ID), List.of());

    VehicleLockIdProvider vehicleLockIdProvider = new VehicleLockIdProvider(clock, vehicleLockIdProviderMetricReporter);
    AssertThrows.illegalStateException(() -> vehicleLockIdProvider.insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter),
        "could not acquire all locks for vpis: " + VPIS);

    Mockito.verify(clock, Mockito.times(6)).instant();
    Mockito.verify(activeMtMessageWriter, Mockito.times(2)).findAndLockByVpis(VPIS);
    Mockito.verify(activeMtMessageWriter).insertVehicleLock(VEHICLE_LOCK);
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(2));
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVehicleLockInsertionSuccess(Duration.ofSeconds(8));
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(12));
    Mockito.verifyNoMoreInteractions(clock, activeMtMessageWriter, vehicleLockIdProviderMetricReporter);
  }

  @Test
  void insertVehicleLockIfMissingAndAcquireVehicleLockIdWithUnknownErrorTest() {
    Clock clock = mockClock();
    VehicleLockIdProviderMetricReporter vehicleLockIdProviderMetricReporter = Mockito.mock(VehicleLockIdProviderMetricReporter.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(
        Either.left(new InsertionFailure(InsertionFailureReason.UNKNOWN, new RuntimeException("test"))), List.of());

    VehicleLockIdProvider vehicleLockIdProvider = new VehicleLockIdProvider(clock, vehicleLockIdProviderMetricReporter);
    AssertThrows.exception(() -> vehicleLockIdProvider.insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter), "test",
        RuntimeException.class);

    Mockito.verify(clock, Mockito.times(3)).instant();
    Mockito.verify(activeMtMessageWriter).findAndLockByVpis(VPIS);
    Mockito.verify(activeMtMessageWriter).insertVehicleLock(VEHICLE_LOCK);
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(2));
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVehicleLockInsertionFailure(InsertionFailureReason.UNKNOWN);
    Mockito.verifyNoMoreInteractions(clock, activeMtMessageWriter, vehicleLockIdProviderMetricReporter);
  }

  @Test
  void insertVehicleLockIfMissingAndAcquireVehicleLockIdWithVehicleLockMissingTest() {
    Clock clock = mockClock();
    VehicleLockIdProviderMetricReporter vehicleLockIdProviderMetricReporter = Mockito.mock(VehicleLockIdProviderMetricReporter.class);
    ActiveMtMessageWriter activeMtMessageWriter = mockActiveMtMessageWriter(Either.right(TestUtil.VEHICLE_LOCK_ID), List.of(),
        List.of(TestUtil.createPersistedVehicleLock()));

    VehicleLockIdProvider vehicleLockIdProvider = new VehicleLockIdProvider(clock, vehicleLockIdProviderMetricReporter);
    List<VehicleLockId> vehicleLockIds = vehicleLockIdProvider.insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);

    Assertions.assertEquals(List.of(TestUtil.VEHICLE_LOCK_ID), vehicleLockIds);

    Mockito.verify(clock, Mockito.times(6)).instant();
    Mockito.verify(activeMtMessageWriter, Mockito.times(2)).findAndLockByVpis(VPIS);
    Mockito.verify(activeMtMessageWriter).insertVehicleLock(VEHICLE_LOCK);
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(2));
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVehicleLockInsertionSuccess(Duration.ofSeconds(8));
    Mockito.verify(vehicleLockIdProviderMetricReporter).onVpiLock(Duration.ofSeconds(12));
    Mockito.verifyNoMoreInteractions(clock, activeMtMessageWriter, vehicleLockIdProviderMetricReporter);
  }
}
