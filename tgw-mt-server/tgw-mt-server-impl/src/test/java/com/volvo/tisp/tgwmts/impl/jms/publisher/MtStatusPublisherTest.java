package com.volvo.tisp.tgwmts.impl.jms.publisher;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.ExecutorService;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.jms.core.MessagePostProcessor;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgw.device.info.cache.core.api.CacheDeviceInfoReader;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.impl.converters.MtStatusMessageOutputConverter;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtStatusPublisherTest {
  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(3), Instant.ofEpochSecond(5));
    return clock;
  }

  @Test
  void createAndPublishTgwMtStatusMessageInvalidParametersTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    Clock clock = Mockito.mock(Clock.class);
    ExecutorService executorService = Mockito.mock(ExecutorService.class);
    JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    ReplyOption replyOption = TestUtil.createReplyOption();
    MtStatusPublisher mtStatusPublisher = new MtStatusPublisher(clock, executorService, jmsTemplate, mtMessageMetricReporter,
        new MtStatusMessageOutputConverter(cacheDeviceInfoReader));

    AssertThrows.illegalArgumentException(() -> mtStatusPublisher.publishMtStatus(null, replyOption, TestUtil.VPI),
        "mtStatus must not be null");
    AssertThrows.illegalArgumentException(() -> mtStatusPublisher.publishMtStatus(MtStatus.FAILED, null, TestUtil.VPI),
        "replyOption must not be null");
    AssertThrows.illegalArgumentException(() -> mtStatusPublisher.publishMtStatus(MtStatus.FAILED, replyOption, null),
        "vpi must not be null");

    Mockito.verifyNoMoreInteractions(clock, jmsTemplate, mtMessageMetricReporter);
  }

  @Test
  void createAndPublishTgwMtStatusMessageTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    Clock clock = mockClock();
    ExecutorService executorService = Mockito.mock(ExecutorService.class);
    Mockito.doNothing().when(executorService).execute(ArgumentMatchers.any());
    JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    ReplyOption replyOption = TestUtil.createReplyOption();
    MtStatusPublisher mtStatusPublisher = new MtStatusPublisher(clock, executorService, jmsTemplate, mtMessageMetricReporter,
        new MtStatusMessageOutputConverter(cacheDeviceInfoReader));

    TispContext.runInContext(() -> mtStatusPublisher.publishMtStatus(MtStatus.FAILED, replyOption, TestUtil.VPI));

    verifyExecutor(executorService);
    Mockito.verify(clock, Mockito.timeout(1_000).times(2)).instant();
    Mockito.verify(jmsTemplate, Mockito.timeout(1_000))
        .convertAndSend(ArgumentMatchers.eq(TestUtil.REPLY_TO.toString()), ArgumentMatchers.any(), ArgumentMatchers.any(MessagePostProcessor.class));
    Mockito.verify(mtMessageMetricReporter, Mockito.timeout(1_000)).onMtStatusPublishDuration(Duration.ofSeconds(2));
    Mockito.verify(mtMessageMetricReporter).onMtStatus(MtStatus.FAILED);
    Mockito.verifyNoMoreInteractions(jmsTemplate, mtMessageMetricReporter);
  }

  @Test
  void publishErrorTest() {
    CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
    Clock clock = mockClock();
    ExecutorService executorService = Mockito.mock(ExecutorService.class);
    JmsTemplate jmsTemplate = Mockito.mock(JmsTemplate.class);
    Mockito.doThrow(new RuntimeException("test"))
        .when(jmsTemplate)
        .convertAndSend(ArgumentMatchers.eq(TestUtil.REPLY_TO.toString()), ArgumentMatchers.any(), ArgumentMatchers.any(MessagePostProcessor.class));
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    ReplyOption replyOption = TestUtil.createReplyOption();
    MtStatusPublisher mtStatusPublisher = new MtStatusPublisher(clock, executorService, jmsTemplate, mtMessageMetricReporter,
        new MtStatusMessageOutputConverter(cacheDeviceInfoReader));
    TispContext.runInContext(() -> mtStatusPublisher.publishMtStatus(MtStatus.FAILED, replyOption, TestUtil.VPI));

    verifyExecutor(executorService);
    Mockito.verify(mtMessageMetricReporter).onMtStatus(MtStatus.FAILED);
    Mockito.verify(clock, Mockito.timeout(1_000)).instant();
    Mockito.verify(jmsTemplate, Mockito.timeout(1_000))
        .convertAndSend(ArgumentMatchers.eq(TestUtil.REPLY_TO.toString()), ArgumentMatchers.any(), ArgumentMatchers.any(MessagePostProcessor.class));
    Mockito.verify(mtMessageMetricReporter, Mockito.timeout(1_000)).onMtStatusPublishError();
    Mockito.verify(cacheDeviceInfoReader).findDeviceInfoByVpi(TestUtil.VPI);
    Mockito.verifyNoMoreInteractions(jmsTemplate, mtMessageMetricReporter, cacheDeviceInfoReader);
  }

  private void verifyExecutor(ExecutorService executorService) {
    ArgumentCaptor<Runnable> runnableArgumentCaptor = ArgumentCaptor.forClass(Runnable.class);
    Mockito.verify(executorService).execute(runnableArgumentCaptor.capture());
    runnableArgumentCaptor.getValue().run();
  }
}
