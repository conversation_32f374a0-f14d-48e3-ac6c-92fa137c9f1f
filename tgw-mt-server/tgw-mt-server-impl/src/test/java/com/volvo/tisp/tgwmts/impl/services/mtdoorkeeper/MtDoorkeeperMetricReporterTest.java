package com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtDoorkeeperMetricReporterTest {
  @Test
  void onMtDoorkeeperTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtDoorkeeperMetricReporter::new, (meterRegistry, mtDoorkeeperMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> mtDoorkeeperMetricReporter.onMtDoorkeeper(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> mtDoorkeeperMetricReporter.onMtDoorkeeper(Duration.ofMillis(-1)), "duration must not be negative: PT-0.001S");

      mtDoorkeeperMetricReporter.onMtDoorkeeper(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.doorkeeper", Duration.ofMillis(100), 1);

      mtDoorkeeperMetricReporter.onMtDoorkeeper(Duration.ofMillis(100));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "mt.doorkeeper", Duration.ofMillis(200), 2);
    });
  }

  @Test
  void onMtDoorkeeperToggleTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtDoorkeeperMetricReporter::new, (meterRegistry, mtDoorkeeperMetricReporter) -> {
      mtDoorkeeperMetricReporter.onMtDoorkeeperToggle(true);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "mt.doorkeeper.toggle", 1);

      mtDoorkeeperMetricReporter.onMtDoorkeeperToggle(false);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "mt.doorkeeper.toggle", 0);
    });
  }
}
