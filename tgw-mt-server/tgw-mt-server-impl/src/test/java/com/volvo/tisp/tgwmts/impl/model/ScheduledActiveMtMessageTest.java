package com.volvo.tisp.tgwmts.impl.model;

import java.time.Duration;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgw.device.info.database.model.SrpLevel;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.tisp.vc.test.utils.lib.AssertUtils;

class ScheduledActiveMtMessageTest {
  private static final IdentifiedActiveMtMessage IDENTIFIED_ACTIVE_MT_MESSAGE = TestUtil.createIdentifiedActiveMtMessage();
  private static final SendSchemaStep SEND_SCHEMA_STEP = SendSchemaStep.forWait(SendSchemaStepId.ofInt(4), Duration.ofDays(7));

  @Test
  void equalsAndHashcodeTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = TestUtil.createScheduledActiveMtMessage();
    AssertUtils.assertEqualsAndHashCode(scheduledActiveMtMessage, TestUtil.createScheduledActiveMtMessage());

    ScheduledActiveMtMessage scheduledActiveMtMessage2 = new ScheduledActiveMtMessage(IDENTIFIED_ACTIVE_MT_MESSAGE, TestUtil.createSmsSendSchemaStep());

    Assertions.assertNotEquals(scheduledActiveMtMessage, scheduledActiveMtMessage2);

    IdentifiedActiveMtMessage anotherIdentifiedActiveMtMessage = new IdentifiedActiveMtMessage(TestUtil.createJoinedActiveMtMessage(),
        TestUtil.createPersistedDeviceInfo(Optional.of(TestUtil.createCryptoKeyInfo()), Optional.of(TestUtil.createPendingCryptoKeyInfo()), SrpLevel.SRP_10));
    ScheduledActiveMtMessage scheduledActiveMtMessage3 = new ScheduledActiveMtMessage(anotherIdentifiedActiveMtMessage, SEND_SCHEMA_STEP);

    Assertions.assertNotEquals(scheduledActiveMtMessage, scheduledActiveMtMessage3);
  }

  @Test
  void getIdentifiedActiveMtMessageTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = new ScheduledActiveMtMessage(IDENTIFIED_ACTIVE_MT_MESSAGE, SEND_SCHEMA_STEP);

    Assertions.assertSame(IDENTIFIED_ACTIVE_MT_MESSAGE, scheduledActiveMtMessage.identifiedActiveMtMessage());
  }

  @Test
  void getPersistedDeviceInfoTest() {
    ScheduledActiveMtMessage scheduledActiveMtMessage = new ScheduledActiveMtMessage(IDENTIFIED_ACTIVE_MT_MESSAGE, SEND_SCHEMA_STEP);

    Assertions.assertSame(SEND_SCHEMA_STEP, scheduledActiveMtMessage.sendSchemaStep());
  }

  @Test
  void invalidConstructorTest() {
    AssertThrows.illegalArgumentException(() -> new ScheduledActiveMtMessage(null, SEND_SCHEMA_STEP), "identifiedActiveMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> new ScheduledActiveMtMessage(IDENTIFIED_ACTIVE_MT_MESSAGE, null), "sendSchemaStep must not be null");
  }
}
