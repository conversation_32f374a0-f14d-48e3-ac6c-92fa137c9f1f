package com.volvo.tisp.tgwmts.impl.converters;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtMessageOutputConverterBiFunctionTest {
  private static void verifyMtMessage(ReceivedMtMessage receivedMtMessage, MtMessage mtMessage) {
    Assertions.assertSame(receivedMtMessage.getQueueId(), mtMessage.getQueueId());
    Assertions.assertSame(receivedMtMessage.getReplyOption(), mtMessage.getReplyOption());
    Assertions.assertSame(receivedMtMessage.getSendSchemaName(), mtMessage.getSendSchemaName());
    Assertions.assertSame(receivedMtMessage.getSrpOption(), mtMessage.getSrpOption());
    Assertions.assertSame(receivedMtMessage.getTid(), mtMessage.getTid());
    Assertions.assertSame(TestUtil.VEHICLE_LOCK_ID, mtMessage.getVehicleLockId());
  }

  @Test
  void applyTest() {
    ReceivedMtMessage receivedMtMessage = TestUtil.createReceivedMtMessage();

    MtMessage mtMessage = MtMessageOutputConverterBiFunction.INSTANCE.apply(receivedMtMessage, TestUtil.VEHICLE_LOCK_ID);

    verifyMtMessage(receivedMtMessage, mtMessage);
  }

  @Test
  void invalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> MtMessageOutputConverterBiFunction.INSTANCE.apply(null, TestUtil.VEHICLE_LOCK_ID),
        "receivedMtMessage must not be null");
    AssertThrows.illegalArgumentException(() -> MtMessageOutputConverterBiFunction.INSTANCE.apply(TestUtil.createReceivedMtMessage(), null),
        "vehicleLockId must not be null");
  }

}
