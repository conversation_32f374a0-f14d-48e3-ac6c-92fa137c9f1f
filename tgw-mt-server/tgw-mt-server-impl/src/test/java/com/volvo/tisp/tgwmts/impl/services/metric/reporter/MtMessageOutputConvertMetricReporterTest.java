package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

class MtMessageOutputConvertMetricReporterTest {
  private static final String TYPE = "TYPE";
  private static final String UDP_REST_CLIENT = "udp-rest-client";

  @Test
  void onUdpInvalidMessageTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtMessageOutputConvertMetricReporter::new, (meterRegistry, mtMessageOutputConvertMetricReporter) -> {
      mtMessageOutputConvertMetricReporter.onUdpInvalidMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, UDP_REST_CLIENT, Tags.of(Tag.of(TYPE, "INVALID_MT_MESSAGE")), 1);

      mtMessageOutputConvertMetricReporter.onUdpInvalidMessage();
      MetricsReporterTestUtils.checkCounter(meterRegistry, UDP_REST_CLIENT, Tags.of(Tag.of(TYPE, "INVALID_MT_MESSAGE")), 2);
    });
  }
}