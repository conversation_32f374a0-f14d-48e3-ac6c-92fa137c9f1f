package com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtDoorkeeperTest {
  private static ActiveMtMessageReader mockActiveMtMessageReader(int numberOfMessagesInQueue) {
    ActiveMtMessageReader activeMtMessageReader = Mockito.mock(ActiveMtMessageReader.class);
    Mockito.when(activeMtMessageReader.countMtMessagesByVehicleLockId(TestUtil.VEHICLE_LOCK_ID)).thenReturn(numberOfMessagesInQueue);
    return activeMtMessageReader;
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(3), Instant.ofEpochSecond(5));
    return clock;
  }

  private static MtDoorkeeperToggle mockMtDoorkeeperToggle(boolean enabled) {
    MtDoorkeeperToggle mtDoorkeeperToggle = Mockito.mock(MtDoorkeeperToggle.class);
    Mockito.when(mtDoorkeeperToggle.isEnabled()).thenReturn(Boolean.valueOf(enabled));
    return mtDoorkeeperToggle;
  }

  @Test
  void invalidConstructorTest() {
    Clock clock = Mockito.mock(Clock.class);
    MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter = Mockito.mock(MtDoorkeeperMetricReporter.class);
    final MtDoorkeeperToggle mtDoorkeeperToggle = Mockito.mock(MtDoorkeeperToggle.class);

    AssertThrows.illegalArgumentException(() -> new MtDoorkeeper(null, mtDoorkeeperMetricReporter, null, 1), "clock must not be null");
    AssertThrows.illegalArgumentException(() -> new MtDoorkeeper(clock, null, mtDoorkeeperToggle, 1), "mtDoorkeeperMetricReporter must not be null");
    AssertThrows.illegalArgumentException(() -> new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, null, 1), "mtDoorkeeperToggle must not be null");
    AssertThrows.illegalArgumentException(() -> new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, -1), "threshold must be positive: -1");
    AssertThrows.illegalArgumentException(() -> new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, 0), "threshold must be positive: 0");
  }

  @Test
  void mtDoorkeeperToggleDisabledTest() {
    Clock clock = Mockito.mock(Clock.class);
    MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter = Mockito.mock(MtDoorkeeperMetricReporter.class);
    MtDoorkeeperToggle mtDoorkeeperToggle = mockMtDoorkeeperToggle(false);
    ActiveMtMessageReader activeMtMessageReader = Mockito.mock(ActiveMtMessageReader.class);

    MtDoorkeeper mtDoorkeeper = new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, 10);
    Assertions.assertFalse(mtDoorkeeper.shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageReader));

    Mockito.verify(mtDoorkeeperToggle).isEnabled();
    Mockito.verifyNoMoreInteractions(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, activeMtMessageReader);
  }

  @Test
  void shouldMessageBeDiscardedFalseTest() {
    Clock clock = mockClock();
    MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter = Mockito.mock(MtDoorkeeperMetricReporter.class);
    MtDoorkeeperToggle mtDoorkeeperToggle = mockMtDoorkeeperToggle(true);
    ActiveMtMessageReader activeMtMessageReader = mockActiveMtMessageReader(9);

    MtDoorkeeper mtDoorkeeper = new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, 10);
    Assertions.assertFalse(mtDoorkeeper.shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageReader));

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(mtDoorkeeperMetricReporter).onMtDoorkeeper(Duration.ofSeconds(2));
    Mockito.verify(mtDoorkeeperToggle).isEnabled();
    Mockito.verify(activeMtMessageReader).countMtMessagesByVehicleLockId(TestUtil.VEHICLE_LOCK_ID);
    Mockito.verifyNoMoreInteractions(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, activeMtMessageReader);
  }

  @Test
  void shouldMessageBeDiscardedNullParameterTest() {
    Clock clock = Mockito.mock(Clock.class);
    MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter = Mockito.mock(MtDoorkeeperMetricReporter.class);
    MtDoorkeeperToggle mtDoorkeeperToggle = Mockito.mock(MtDoorkeeperToggle.class);
    ActiveMtMessageReader activeMtMessageReader = Mockito.mock(ActiveMtMessageReader.class);
    MtDoorkeeper mtDoorkeeper = new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, 10);

    AssertThrows.illegalArgumentException(() -> mtDoorkeeper.shouldMessageBeDiscarded(null, activeMtMessageReader), "vehicleLockId must not be null");
    AssertThrows.illegalArgumentException(() -> mtDoorkeeper.shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, null),
        "activeMtMessageReader must not be null");
  }

  @Test
  void shouldMessageBeDiscardedTrueTest() {
    Clock clock = mockClock();
    MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter = Mockito.mock(MtDoorkeeperMetricReporter.class);
    MtDoorkeeperToggle mtDoorkeeperToggle = mockMtDoorkeeperToggle(true);
    ActiveMtMessageReader activeMtMessageReader = mockActiveMtMessageReader(10);

    MtDoorkeeper mtDoorkeeper = new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, 10);
    Assertions.assertTrue(mtDoorkeeper.shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageReader));

    Mockito.verify(clock, Mockito.times(2)).instant();
    Mockito.verify(mtDoorkeeperMetricReporter).onMtDoorkeeper(Duration.ofSeconds(2));
    Mockito.verify(activeMtMessageReader).countMtMessagesByVehicleLockId(TestUtil.VEHICLE_LOCK_ID);
    Mockito.verifyNoMoreInteractions(clock, mtDoorkeeperMetricReporter, activeMtMessageReader);
  }
}
