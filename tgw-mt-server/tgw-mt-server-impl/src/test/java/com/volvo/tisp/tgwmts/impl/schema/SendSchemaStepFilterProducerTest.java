package com.volvo.tisp.tgwmts.impl.schema;

import java.util.Set;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;

class SendSchemaStepFilterProducerTest {
  public static final Set<SendSchemaStepType> FULL_SET = Set.of(
      SendSchemaStepType.UDP,
      SendSchemaStepType.SMS,
      SendSchemaStepType.SAT,
      SendSchemaStepType.WIFI);

  @Test
  void allStepsFilterTest() {
    SendSchemaStepFilterProducer sendSchemaStepFilterProducer = new SendSchemaStepFilterProducer();
    DeviceInfo device = TestUtil.createDeviceInfoBuilder().setWifiEnabled(false).setSatEnabled(false).build();
    SendSchemaStepFilter sendSchemaStepFilter = sendSchemaStepFilterProducer.createSendSchemaStepFilter(device);
    Assertions.assertEquals(SendSchemaStepFilterProducer.DEFAULT_SEND_SCHEMA_STEPS_TYPES, sendSchemaStepFilter.stepTypes());
  }

  @Test
  void defaultAndSatFilterTest() {
    SendSchemaStepFilterProducer sendSchemaStepFilterProducer = new SendSchemaStepFilterProducer();
    DeviceInfo device = TestUtil.createDeviceInfoBuilder().setWifiEnabled(false).setSatEnabled(true).build();
    SendSchemaStepFilter sendSchemaStepFilter = sendSchemaStepFilterProducer.createSendSchemaStepFilter(device);
    Assertions.assertEquals(Set.of(SendSchemaStepType.UDP, SendSchemaStepType.SMS, SendSchemaStepType.SAT), sendSchemaStepFilter.stepTypes());
  }

  @Test
  void defaultAndWifiFilterTest() {
    SendSchemaStepFilterProducer sendSchemaStepFilterProducer = new SendSchemaStepFilterProducer();
    DeviceInfo device = TestUtil.createDeviceInfoBuilder().setWifiEnabled(true).setSatEnabled(false).build();
    SendSchemaStepFilter sendSchemaStepFilter = sendSchemaStepFilterProducer.createSendSchemaStepFilter(device);
    Assertions.assertEquals(Set.of(SendSchemaStepType.UDP, SendSchemaStepType.SMS, SendSchemaStepType.WIFI), sendSchemaStepFilter.stepTypes());
  }

  @Test
  void defaultFilterTest() {
    SendSchemaStepFilterProducer sendSchemaStepFilterProducer = new SendSchemaStepFilterProducer();
    DeviceInfo device = TestUtil.createDeviceInfoBuilder().setWifiEnabled(true).setSatEnabled(true).build();
    SendSchemaStepFilter sendSchemaStepFilter = sendSchemaStepFilterProducer.createSendSchemaStepFilter(device);
    Assertions.assertEquals(FULL_SET, sendSchemaStepFilter.stepTypes());
  }
}