package com.volvo.tisp.tgwmts.impl.clients;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.connectivity.proto.MtMessage;
import com.volvo.tisp.tgwmts.impl.converters.MtRouterMessageOutputConverterFunction;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtRouterRestClientMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import reactor.core.publisher.Mono;

class MtRouterRestClientTest {

  @Test
  void invalidSendMtMessageParameterTest() {
    MtRouterRestClient mtRouterRestClient = new MtRouterRestClient(Mockito.mock(MtRouterMessageOutputConverterFunction.class),
        Mockito.mock(MtRouterRestClientMetricReporter.class),
        Mockito.mock(MtRouterReactiveClient.class));

    AssertThrows.illegalArgumentException(() -> mtRouterRestClient.sendMtMessage(null), "encodedActiveMtMessage must not be null");
  }

  @Test
  void sendMtMessageTest() {
    MtRouterMessageOutputConverterFunction mtRouterMessageOutputConverterFunction = Mockito.mock(MtRouterMessageOutputConverterFunction.class);
    MtRouterRestClientMetricReporter mtRouterRestClientMetricReporter = Mockito.mock(MtRouterRestClientMetricReporter.class);
    MtMessage mtMessage = TestUtil.createTgwMtMessage("Test".getBytes());
    MtRouterReactiveClient mtRouterReactiveClient = Mockito.mock(MtRouterReactiveClient.class);
    MtRouterRestClient mtRouterRestClient = new MtRouterRestClient(mtRouterMessageOutputConverterFunction, mtRouterRestClientMetricReporter,
        mtRouterReactiveClient);
    EncodedActiveMtMessage encodedActiveMtMessage = TestUtil.createEncodedActiveMtMessage();

    Mockito.when(mtRouterMessageOutputConverterFunction.apply(encodedActiveMtMessage)).thenReturn(Either.right(mtMessage));
    Mockito.when(mtRouterReactiveClient.post(mtMessage)).thenReturn(Mono.empty());

    mtRouterRestClient.sendMtMessage(encodedActiveMtMessage);

    Mockito.verify(mtRouterReactiveClient).post(mtMessage);
    Mockito.verify(mtRouterMessageOutputConverterFunction).apply(encodedActiveMtMessage);
    Mockito.verifyNoMoreInteractions(mtRouterRestClientMetricReporter, mtRouterReactiveClient, mtRouterMessageOutputConverterFunction);
  }
}
