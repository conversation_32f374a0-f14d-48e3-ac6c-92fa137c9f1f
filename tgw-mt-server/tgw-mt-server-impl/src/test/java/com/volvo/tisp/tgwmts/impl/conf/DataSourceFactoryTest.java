package com.volvo.tisp.tgwmts.impl.conf;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.UtilClassVerifier;

class DataSourceFactoryTest {
  private static DatasourceConfigProperties mockDatasourceConfigProperties() {
    return new DatasourceConfigProperties("foo", "postgres", "bar", 2, 1, Duration.ofSeconds(2));
  }

  @Test
  void createDataSourceTest() {
    DataSourceFactory.createDataSource(mockDatasourceConfigProperties());
  }

  @Test
  void verifyUtilClassTest() throws ReflectiveOperationException {
    UtilClassVerifier.verifyUtilClass(DataSourceFactory.class);
  }
}
