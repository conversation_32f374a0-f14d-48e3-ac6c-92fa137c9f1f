package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

class EncryptionProcessorMetricReporterTest {
  @Test
  void onEncryptPayloadDeviceFailTest() {
    MetricsReporterTestUtils.initReporterAndTest(EncryptionProcessorMetricReporter::new, (meterRegistry, encryptionProcessorMetricReporter) -> {
      encryptionProcessorMetricReporter.onEncryptPayloadDeviceFail();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encryption.encrypt.fail.encryptpayloadfail.deviceaeskey", 1);

      encryptionProcessorMetricReporter.onEncryptPayloadDeviceFail();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encryption.encrypt.fail.encryptpayloadfail.deviceaeskey", 2);
    });
  }

  @Test
  void onEncryptSrp11ReceivedTest() {
    MetricsReporterTestUtils.initReporterAndTest(EncryptionProcessorMetricReporter::new, (meterRegistry, encryptionProcessorMetricReporter) -> {
      encryptionProcessorMetricReporter.onEncryptSrp11Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encryption.encrypt.srp11.received", 1);

      encryptionProcessorMetricReporter.onEncryptSrp11Received();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "service.encryption.encrypt.srp11.received", 2);
    });
  }
}
