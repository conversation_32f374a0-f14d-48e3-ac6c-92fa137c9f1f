package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class RswdlLowSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), RswdlLowSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(10, RswdlLowSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> RswdlLowSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.RSWDL_LOW, RswdlLowSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(RswdlLowSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(RswdlLowSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepUdp(RswdlLowSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepWait(RswdlLowSendSchema.INSTANCE, 4, Duration.ofHours(1));
    VerificationUtil.verifySendSchemaStepUdp(RswdlLowSendSchema.INSTANCE, 5);
    VerificationUtil.verifySendSchemaStepWait(RswdlLowSendSchema.INSTANCE, 6, Duration.ofDays(7));

    Assertions.assertTrue(RswdlLowSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(7)).isEmpty());
  }
}
