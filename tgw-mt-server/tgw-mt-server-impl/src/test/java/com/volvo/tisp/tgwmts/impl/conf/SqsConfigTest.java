package com.volvo.tisp.tgwmts.impl.conf;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.conf.properties.SqsQueueProperties;

class SqsConfigTest {
  @Test
  void createSqsClientTest() {
    SqsQueueProperties sqsQueueProperties = new SqsQueueProperties("eu-west-1", "http://abcd");
    Assertions.assertNotNull(new SqsConfig().createSqsClient(sqsQueueProperties));
  }
}
