package com.volvo.tisp.tgwmts.impl.services.statistics;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeoutException;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtStatusStatisticsMetricReporter;
import com.volvo.tisp.vc.vcss.client.protobuf.MtStatusStatisticProtobuf;
import com.volvo.tisp.vc.vcss.client.protobuf.common.TransportTypeProtobuf;

import io.github.resilience4j.timelimiter.TimeLimiter;

class MtStatusStatisticsPublisherTest {

  private static MtStatusStatisticProtobuf.MtStatusStatistic createMtStatusStatistic() {
    return MtStatusStatisticProtobuf.MtStatusStatistic.newBuilder()
        .setStatus(MtStatusStatisticProtobuf.Status.DELIVERED)
        .setTransportType(TransportTypeProtobuf.TransportType.UDP)
        .setVpi("12345678901234567890123456789012")
        .build();
  }

  private static MtStatusStatisticsPublisher createMtStatusStatisticsPublisherInstance(MtStatusStatisticsMetricReporter MtStatusStatisticsMetricReporter,
      CompletableFuture<Integer> completableFuture) throws IOException {
    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
      createMtStatusStatistic().writeDelimitedTo(outputStream);
      MessagePublisher<ByteBuffer> messagePublisher = mockMessagePublisher(outputStream.toByteArray(), completableFuture);
      return new MtStatusStatisticsPublisher(messagePublisher, MtStatusStatisticsMetricReporter,
          TimeLimiter.of(Duration.ofSeconds(1)), new ScheduledThreadPoolExecutor(1));
    }
  }

  private static MessagePublisher<ByteBuffer> mockMessagePublisher() {
    return Mockito.mock(MessagePublisher.class);
  }

  private static MessagePublisher<ByteBuffer> mockMessagePublisher(byte[] bytes, CompletableFuture<Integer> completableFuture) {
    MessagePublisher.Message<ByteBuffer> message = Mockito.mock(MessagePublisher.Message.class);
    Mockito.when(message.publish(ByteBuffer.wrap(bytes))).thenReturn(completableFuture);

    MessagePublisher<ByteBuffer> messagePublisher = mockMessagePublisher();
    Mockito.when(messagePublisher.newMessage()).thenReturn(message);

    return messagePublisher;
  }

  @Test
  void publishMessageInterruptedExceptionTest() throws IOException {
    CompletableFuture<Integer> completableFuture = CompletableFuture.failedFuture(new InterruptedException("test"));

    MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter = Mockito.mock(MtStatusStatisticsMetricReporter.class);
    MtStatusStatisticsPublisher mtStatusStatisticsPublisher = createMtStatusStatisticsPublisherInstance(mtStatusStatisticsMetricReporter, completableFuture);

    mtStatusStatisticsPublisher.publish(List.of(createMtStatusStatistic()));

    Mockito.verify(mtStatusStatisticsMetricReporter).onStatisticsPublishException();
    Mockito.verifyNoMoreInteractions(mtStatusStatisticsMetricReporter);
  }

  @Test
  void publishMessageTimeoutExceptionTest() throws IOException {
    CompletableFuture<Integer> completableFuture = CompletableFuture.failedFuture(new TimeoutException("test"));

    MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter = Mockito.mock(MtStatusStatisticsMetricReporter.class);
    MtStatusStatisticsPublisher mtStatusStatisticsPublisher = createMtStatusStatisticsPublisherInstance(mtStatusStatisticsMetricReporter, completableFuture);

    mtStatusStatisticsPublisher.publish(List.of(createMtStatusStatistic()));

    Mockito.verify(mtStatusStatisticsMetricReporter).onStatisticsPublishException();
    Mockito.verifyNoMoreInteractions(mtStatusStatisticsMetricReporter);
  }

  @Test
  void publishSubscriptionMissingTest() throws IOException {
    MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter = Mockito.mock(MtStatusStatisticsMetricReporter.class);
    MtStatusStatisticsPublisher mtStatusStatisticsPublisher = createMtStatusStatisticsPublisherInstance(mtStatusStatisticsMetricReporter,
        CompletableFuture.completedFuture(0));

    mtStatusStatisticsPublisher.publish(List.of(createMtStatusStatistic()));

    Mockito.verify(mtStatusStatisticsMetricReporter).onSubscriptionMissing();
    Mockito.verifyNoMoreInteractions(mtStatusStatisticsMetricReporter);
  }

  @Test
  void publishSuccessTest() throws IOException {
    MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter = Mockito.mock(MtStatusStatisticsMetricReporter.class);
    MtStatusStatisticsPublisher mtStatusStatisticsPublisher = createMtStatusStatisticsPublisherInstance(mtStatusStatisticsMetricReporter,
        CompletableFuture.completedFuture(1));

    mtStatusStatisticsPublisher.publish(List.of(createMtStatusStatistic()));

    Mockito.verify(mtStatusStatisticsMetricReporter).onStatisticsPublishSuccess(Mockito.any());
    Mockito.verifyNoMoreInteractions(mtStatusStatisticsMetricReporter);
  }

}