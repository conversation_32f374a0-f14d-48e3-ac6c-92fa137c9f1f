package com.volvo.tisp.tgwmts.impl.converters;

import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtRouterMessageOutputConverterFunctionTest {
  private static final ImmutableByteArray IMMUTABLE_BYTE_ARRAY = ImmutableByteArray.of(new byte[] {1, 2, 3});
  private static final ByteString BYTESTRING = ByteString.copyFrom(IMMUTABLE_BYTE_ARRAY.toByteArray());

  private static EncodedActiveMtMessage createEncodedActiveMtMessage(SendSchemaStep sendSchemaStep) {
    return new EncodedActiveMtMessage(new ScheduledActiveMtMessage(TestUtil.createIdentifiedActiveMtMessage(), sendSchemaStep),
        TestUtil.createSrp12ServiceRoutingPduWrapper());
  }

  private static Function<ServiceRoutingPduWrapper, ImmutableByteArray> mockEncodeServiceRoutingPduWrapperFunction() {
    Function<ServiceRoutingPduWrapper, ImmutableByteArray> function = Mockito.mock(Function.class);
    Mockito.when(function.apply(ArgumentMatchers.any(ServiceRoutingPduWrapper.class))).thenReturn(IMMUTABLE_BYTE_ARRAY);
    return function;
  }

  private static void verifyMtRouterMessage(MtMessage mtMessage, Transport expectedTransport) {
    Assertions.assertEquals("00000000000000000000000000000001", mtMessage.getMessageId());
    Assertions.assertEquals(BYTESTRING, mtMessage.getPayload());
    Assertions.assertEquals(expectedTransport, mtMessage.getTransport());
    Assertions.assertEquals(TestUtil.VPI.toString(), mtMessage.getVpi());
  }

  @Test
  void applyTest() {
    Function<ServiceRoutingPduWrapper, ImmutableByteArray> function = mockEncodeServiceRoutingPduWrapperFunction();
    verifyMtRouterMessage(
        MtRouterMessageOutputConverterFunction.create(function).apply(createEncodedActiveMtMessage(TestUtil.createSmsSendSchemaStep())).getRight(),
        Transport.SMS);
    verifyMtRouterMessage(
        MtRouterMessageOutputConverterFunction.create(function).apply(createEncodedActiveMtMessage(TestUtil.createUdpSendSchemaStep())).getRight(),
        Transport.UDP);
    verifyMtRouterMessage(
        MtRouterMessageOutputConverterFunction.create(function).apply(createEncodedActiveMtMessage(TestUtil.createSatSendSchemaStep())).getRight(),
        Transport.SAT);
    verifyMtRouterMessage(
        MtRouterMessageOutputConverterFunction.create(function).apply(createEncodedActiveMtMessage(TestUtil.createWifiSendSchemaStep())).getRight(),
        Transport.VPN);

    Assertions.assertTrue(
        MtRouterMessageOutputConverterFunction.create(function).apply(createEncodedActiveMtMessage(TestUtil.createWaitSendSchemaStep())).isLeft());
  }

  @Test
  void invalidApplyParametersTest() {
    AssertThrows.illegalArgumentException(() -> MtRouterMessageOutputConverterFunction.create(TestUtil.mockFunction()).apply(null),
        "encodedActiveMtMessage must not be null");
  }
}
