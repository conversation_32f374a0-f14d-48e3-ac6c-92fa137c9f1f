package com.volvo.tisp.tgwmts.impl.services;

import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;

import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtRetrySchedulerServiceTest {
  private static final long DELAY_MILLIS = 10L;
  private static final Vpi VPI_A = Vpi.ofString("A".repeat(32));
  private static final Vpi VPI_B = Vpi.ofString("B".repeat(32));
  private static final Vpi VPI_C = Vpi.ofString("C".repeat(32));

  @Test
  void invalidConstructorTest() {
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);
    ScheduledExecutorService scheduledExecutorService = Mockito.mock(ScheduledExecutorService.class);

    AssertThrows.illegalArgumentException(() -> new MtRetrySchedulerService(0, mtMessageRetryHandler, scheduledExecutorService),
        "delayMillis must be positive: 0");
    AssertThrows.illegalArgumentException(() -> new MtRetrySchedulerService(DELAY_MILLIS, null, scheduledExecutorService),
        "mtMessageRetryHandler must not be null");
    AssertThrows.illegalArgumentException(() -> new MtRetrySchedulerService(DELAY_MILLIS, mtMessageRetryHandler, null),
        "scheduledExecutorService must not be null");
  }

  @Test
  void scheduleRetryTaskTest() {
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);
    ScheduledExecutorService scheduledExecutorService = Executors.newSingleThreadScheduledExecutor();

    new MtRetrySchedulerService(DELAY_MILLIS, mtMessageRetryHandler, scheduledExecutorService).scheduleRetryTasks(List.of(VPI_A, VPI_B, VPI_C));

    Mockito.verify(mtMessageRetryHandler, Mockito.timeout(100)).initiateRetryForWaitingMtMessages(VPI_A);
    Mockito.verify(mtMessageRetryHandler, Mockito.timeout(100)).initiateRetryForWaitingMtMessages(VPI_B);
    Mockito.verify(mtMessageRetryHandler, Mockito.timeout(100)).initiateRetryForWaitingMtMessages(VPI_C);

    Mockito.verifyNoMoreInteractions(mtMessageRetryHandler);
    scheduledExecutorService.shutdownNow();
  }
}
