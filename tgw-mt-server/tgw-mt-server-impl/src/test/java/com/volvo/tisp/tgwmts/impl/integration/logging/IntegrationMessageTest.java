package com.volvo.tisp.tgwmts.impl.integration.logging;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;

class IntegrationMessageTest {
  @Test
  void onDoorkeeperTest() {
    Assertions.assertEquals("MT/discardMessageDueToTooManyMessagesPerVehicle", IntegrationMessage.onDoorkeeper().toString());
  }

  @Test
  void onHandleAckTest() {
    Assertions.assertEquals("MT/CANCELED/5", IntegrationMessage.onHandleAck(ReceivedMtStatus.CANCELED, TestUtil.createJoinedActiveMtMessage()).toString());
  }

  @Test
  void onPersistFailTest() {
    Assertions.assertEquals("MT/Failed to persist", IntegrationMessage.onPersistFail().toString());
  }

  @Test
  void onPersistTest() {
    Assertions.assertEquals("MT/COMMON_LOW", IntegrationMessage.onPersist(TestUtil.SEND_SCHEMA_NAME).toString());
  }

  @Test
  void onSendTest() {
    Assertions.assertEquals("MT/Send/SMS/5", IntegrationMessage.onSend(SendSchemaStepType.SMS, TestUtil.createJoinedActiveMtMessage()).toString());
  }
}
