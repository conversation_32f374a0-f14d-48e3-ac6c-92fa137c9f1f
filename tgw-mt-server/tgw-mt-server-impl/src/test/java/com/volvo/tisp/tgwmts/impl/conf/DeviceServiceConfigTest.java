package com.volvo.tisp.tgwmts.impl.conf;

import java.time.Clock;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.cache.core.api.CacheDeviceInfoReader;
import com.volvo.tisp.tgw.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.volvo.tisp.tgw.device.info.cache.notify.metrics.NotifyMetricReporter;
import com.volvo.tisp.tgw.device.info.database.api.DatabaseEncryptionService;
import com.volvo.tisp.tgw.device.info.database.api.DatabaseSecurityParametersResolver;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;

import io.micrometer.core.instrument.MeterRegistry;

class DeviceServiceConfigTest {

  private static RowMapper<PersistedDeviceInfo> mockRowMapper() {
    return Mockito.mock(RowMapper.class);
  }

  @Test
  void createCacheDeviceInfoReaderFactoryTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createCacheDeviceInfoReaderFactory(Mockito.mock(CacheDeviceInfoReader.class)));
  }

  @Test
  void createCacheDeviceInfoReaderTest() {
    Assertions.assertNotNull(
        new DeviceServiceConfig().createCacheDeviceInfoReader(Mockito.mock(DeviceInfoReaderFactory.class), 10000L));
  }

  @Test
  void createCacheInvalidatorTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createCacheInvalidator(Mockito.mock(Jdbi.class), Mockito.mock(NotifyMetricReporter.class)));
  }

  @Test
  void createDeviceInfoReaderFactoryTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createDeviceInfoReaderFactory(Mockito.mock(Clock.class), Mockito.mock(DatabaseEncryptionService.class),
        Mockito.mock(DatabaseSecurityParametersResolver.class), Mockito.mock(Jdbi.class), mockRowMapper()));
  }

  @Test
  void createDeviceInfoWriterFactoryTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createDeviceInfoWriterFactory(Mockito.mock(Clock.class), Mockito.mock(DatabaseEncryptionService.class),
        Mockito.mock(DatabaseSecurityParametersResolver.class), Mockito.mock(Jdbi.class), mockRowMapper()));
  }

  @Test
  void createDeviceNotificationMessageListenerTest() {
    Assertions.assertNotNull(
        new DeviceServiceConfig().createDeviceNotificationMessageListener(Mockito.mock(DeviceServiceCacheManager.class), Mockito.mock(Jdbi.class),
            Mockito.mock(NotifyMetricReporter.class)));
  }

  @Test
  void createDeviceServiceCacheManagerTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createDeviceServiceCacheManager(Mockito.mock(CacheDeviceInfoReader.class)));
  }

  @Test
  void createNotifyMetricReporterTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createNotifyMetricReporter(Mockito.mock(MeterRegistry.class)));
  }

  @Test
  void createRowMapperTest() {
    Assertions.assertNotNull(new DeviceServiceConfig().createRowMapper(Mockito.mock(DatabaseEncryptionService.class)));
  }
}
