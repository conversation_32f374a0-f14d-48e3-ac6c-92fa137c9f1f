package com.volvo.tisp.tgwmts.impl.rest;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import com.volvo.tisp.protobuf.common.PlatformIdentifier;
import com.volvo.tisp.tce.proto.ConnectionEstablishedEvent;
import com.volvo.tisp.tgwmts.impl.services.MtMessageRetryHandler;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class ConnectionEstablishedEventRestControllerTest {
  static void verifyResponseEntity(ResponseEntity<String> responseEntity, HttpStatus expectedHttpStatus, String expectedBody) {
    Assertions.assertEquals(expectedHttpStatus, responseEntity.getStatusCode());
    Assertions.assertEquals(expectedBody, responseEntity.getBody());
  }

  @Test
  void postConnectionEstablishedEventFailureTest() {
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);
    ConnectionEstablishedEventRestController connectionEstablishedEventRestController = new ConnectionEstablishedEventRestController(mtMessageRetryHandler);

    AssertThrows.illegalArgumentException(() -> connectionEstablishedEventRestController.postConnectionEstablishedEvent(null),
        "connectionEstablishedEvent must not be null");

    ResponseEntity<String> responseEntity = connectionEstablishedEventRestController.postConnectionEstablishedEvent(
        ConnectionEstablishedEvent.newBuilder().build());

    verifyResponseEntity(responseEntity, HttpStatus.BAD_REQUEST, "The value for vpi field in the connectionEstablishedEvent is null");
    Mockito.verifyNoInteractions(mtMessageRetryHandler);
  }

  @Test
  void postConnectionEstablishedEventTest() {
    MtMessageRetryHandler mtMessageRetryHandler = Mockito.mock(MtMessageRetryHandler.class);
    ConnectionEstablishedEventRestController connectionEstablishedEventRestController = new ConnectionEstablishedEventRestController(mtMessageRetryHandler);
    ConnectionEstablishedEvent connectionEstablishedEvent = ConnectionEstablishedEvent.newBuilder()
        .setVpi(PlatformIdentifier.newBuilder().setValue(TestUtil.VPI.toString()).build())
        .build();

    ResponseEntity<String> responseEntity = connectionEstablishedEventRestController.postConnectionEstablishedEvent(connectionEstablishedEvent);

    verifyResponseEntity(responseEntity, HttpStatus.OK, null);
    Mockito.verify(mtMessageRetryHandler).initiateRetryForWaitingMtMessages(TestUtil.VPI);
    Mockito.verifyNoMoreInteractions(mtMessageRetryHandler);
  }
}
