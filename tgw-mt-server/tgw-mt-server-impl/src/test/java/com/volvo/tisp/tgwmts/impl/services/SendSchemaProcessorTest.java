package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.FilterableSendSchemaFetcher;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepFilterProducer;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.SendSchemaProcessorMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;

class SendSchemaProcessorTest {
  private static final Instant ZERO_INSTANT = Instant.ofEpochSecond(0);

  private static JoinedActiveMtMessage createSatPreppedJoinedActiveMtMessage() {
    PersistedMtMessage persistedMtMessage = TestUtil.createPersistedMtMessageBuilder()
        .setMtMessage(TestUtil.createMtMessageBuilder().setSendSchemaName(SendSchemaName.COMMON_HIGH).build()).build();
    PersistedActiveMtMessage persistedActiveMtMessage = TestUtil.createPersistedActiveMtMessageBuilder()
        .setActiveMtMessage(TestUtil.createActiveMtMessage(TestUtil.RETRY_ATTEMPT, SendSchemaStepId.ofInt(2)))
        .build();

    return new JoinedActiveMtMessage(persistedActiveMtMessage, persistedMtMessage, TestUtil.createPersistedVehicleLock());
  }

  private static SendSchemaProcessorContext createSendSchemaProcessorContext() {
    return createSendSchemaProcessorContext(Mockito.mock(TransmissionRouter.class));
  }

  private static SendSchemaProcessorContext createSendSchemaProcessorContext(TransmissionRouter transmissionRouter) {
    return new SendSchemaProcessorContext(
        new FilterableSendSchemaFetcher(TestUtil.createSendSchemaNameMappingFunction()),
        Mockito.mock(FinishedMtMessageHandler.class),
        Mockito.mock(SendSchemaProcessorMetricReporter.class),
        new SendSchemaStepFilterProducer(),
        transmissionRouter);
  }

  private static Clock mockClock() {
    return mockClock(ZERO_INSTANT, ZERO_INSTANT);
  }

  private static Clock mockClock(Instant globalComparisonInstant, Instant stepComparisonInstant) {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(globalComparisonInstant).thenReturn(stepComparisonInstant);
    return clock;
  }

  private static DeviceInfoReader mockDeviceInfoReader(Optional<PersistedDeviceInfo> optional) {
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    Mockito.when(deviceInfoReader.findDeviceInfoByVpi(TestUtil.VPI)).thenReturn(optional);
    return deviceInfoReader;
  }

  private static DeviceInfoReaderFactory mockDeviceInfoReaderFactory(DeviceInfoReader deviceInfoReader) {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    return deviceInfoReaderFactory;
  }

  private static void verifyUpdateActiveMtMessageParameter(UpdateActiveMtMessageParameter updateActiveMtMessageParameter) {
    Assertions.assertEquals(TestUtil.ACTIVE_MT_MESSAGE_ID, updateActiveMtMessageParameter.getActiveMtMessageId());
    Assertions.assertEquals(2, updateActiveMtMessageParameter.getSendSchemaStepId().toInt());
    Assertions.assertEquals(ZERO_INSTANT.plus(Duration.ofDays(7)), updateActiveMtMessageParameter.getTimeout());
  }

  @Test
  void deviceNotFoundTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.empty());
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();

    SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);

    TispContext.runInContext(() -> {
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage);

      Assertions.assertTrue(updateActiveMtMessageParameter.isEmpty());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onDeviceNotFound();
    Mockito.verify(sendSchemaProcessorContext.getFinishedMtMessageHandler()).onDeviceNotFound(activeMtMessageWriter, TestUtil.VPI);
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getTransmissionRouter());
  }

  /**
   * Use send schema with sat transition (COMMON HIGH). Set sendSchemaStepId to step prior to SAT and sat_enabled to false.
   * When processor executes next step it should skip SAT and go directly to WAIT.
   */
  @Test
  void executeNextSendSchemaStepSatDisabledTest() {
    Clock clock = mockClock();
    PersistedDeviceInfo persistedDeviceInfo = TestUtil.createPersistedDeviceInfoBuilder()
        .setDeviceInfo(TestUtil.createDeviceInfoBuilder().setSatEnabled(false).build())
        .build();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(persistedDeviceInfo));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    TransmissionRouter transmissionRouter = Mockito.mock(TransmissionRouter.class);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext(transmissionRouter);
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = createSatPreppedJoinedActiveMtMessage();

    ArgumentCaptor<ScheduledActiveMtMessage> argumentCaptor = ArgumentCaptor.forClass(ScheduledActiveMtMessage.class);

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      UpdateActiveMtMessageParameter updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage).orElseThrow();

      Assertions.assertEquals(TestUtil.ACTIVE_MT_MESSAGE_ID, updateActiveMtMessageParameter.getActiveMtMessageId());
      Assertions.assertEquals(3, updateActiveMtMessageParameter.getSendSchemaStepId().toInt());
      Assertions.assertEquals(ZERO_INSTANT.plus(Duration.ofDays(7)), updateActiveMtMessageParameter.getTimeout());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onSuccess();
    Mockito.verify(sendSchemaProcessorContext.getTransmissionRouter()).processScheduledActiveMtMessage(argumentCaptor.capture());

    Assertions.assertEquals(SendSchemaStepType.WAIT, argumentCaptor.getValue().sendSchemaStep().getSendSchemaStepType());
    Mockito.verifyNoMoreInteractions(transmissionRouter, sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter());
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getFinishedMtMessageHandler());
  }

  /**
   * Use send schema with sat transition (COMMON HIGH). Set sendSchemaStepId to step prior to SAT and sat_enabled to true.
   * When processor executes next step it should go to the SAT transition.
   */
  @Test
  void executeNextSendSchemaStepSatEnabledTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    TransmissionRouter transmissionRouter = Mockito.mock(TransmissionRouter.class);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext(transmissionRouter);
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = createSatPreppedJoinedActiveMtMessage();

    ArgumentCaptor<ScheduledActiveMtMessage> argumentCaptor = ArgumentCaptor.forClass(ScheduledActiveMtMessage.class);

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      UpdateActiveMtMessageParameter updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage).orElseThrow();

      Assertions.assertEquals(TestUtil.ACTIVE_MT_MESSAGE_ID, updateActiveMtMessageParameter.getActiveMtMessageId());
      Assertions.assertEquals(3, updateActiveMtMessageParameter.getSendSchemaStepId().toInt());
      Assertions.assertEquals(ZERO_INSTANT.plus(SendSchemaStep.DEFAULT_SAT_TIMEOUT), updateActiveMtMessageParameter.getTimeout());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onSuccess();
    Mockito.verify(sendSchemaProcessorContext.getTransmissionRouter()).processScheduledActiveMtMessage(argumentCaptor.capture());

    Assertions.assertEquals(SendSchemaStepType.SAT, argumentCaptor.getValue().sendSchemaStep().getSendSchemaStepType());
    Mockito.verifyNoMoreInteractions(transmissionRouter, sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter());
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getFinishedMtMessageHandler());
  }

  @Test
  void executeNextSendSchemaStepTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage);

      verifyUpdateActiveMtMessageParameter(updateActiveMtMessageParameter.orElseThrow());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onSuccess();
    Mockito.verify(sendSchemaProcessorContext.getTransmissionRouter()).processScheduledActiveMtMessage(ArgumentMatchers.any(ScheduledActiveMtMessage.class));
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getFinishedMtMessageHandler());
  }

  /**
   * Set wifiEnabled to true on device and verify COMMON_LOW tries on WIFI as the first step
   */
  @Test
  void executeNextSendSchemaStepWifiEnabledTest() {
    Clock clock = mockClock();
    PersistedDeviceInfo persistedDeviceInfo = TestUtil.createPersistedDeviceInfoBuilder()
        .setDeviceInfo(TestUtil.createDeviceInfoBuilder().setWifiEnabled(true).build())
        .build();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(persistedDeviceInfo));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    TransmissionRouter transmissionRouter = Mockito.mock(TransmissionRouter.class);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext(transmissionRouter);
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage(SendSchemaStepId.ofInt(0)); // COMMON_LOW

    ArgumentCaptor<ScheduledActiveMtMessage> argumentCaptor = ArgumentCaptor.forClass(ScheduledActiveMtMessage.class);

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      UpdateActiveMtMessageParameter updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage).orElseThrow();

      Assertions.assertEquals(TestUtil.ACTIVE_MT_MESSAGE_ID, updateActiveMtMessageParameter.getActiveMtMessageId());
      Assertions.assertEquals(1, updateActiveMtMessageParameter.getSendSchemaStepId().toInt());
      Assertions.assertEquals(ZERO_INSTANT.plus(SendSchemaStep.DEFAULT_UDP_TIMEOUT), updateActiveMtMessageParameter.getTimeout()); // next step is now UDP
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onSuccess();
    Mockito.verify(sendSchemaProcessorContext.getTransmissionRouter()).processScheduledActiveMtMessage(argumentCaptor.capture());

    Assertions.assertEquals(SendSchemaStepType.WIFI, argumentCaptor.getValue().sendSchemaStep().getSendSchemaStepType());
    Mockito.verifyNoMoreInteractions(transmissionRouter, sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter());
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getFinishedMtMessageHandler());
  }

  @Test
  void executeNextSendSchemaWithoutSurpassingGlobalTimeoutStepTest() {
    Clock clock = mockClock(ZERO_INSTANT, Instant.now());
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage);

      Assertions.assertEquals(TestUtil.INSTANT.plus(Duration.ofDays(7)), updateActiveMtMessageParameter.orElseThrow().getTimeout());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onSuccess();
    Mockito.verify(sendSchemaProcessorContext.getTransmissionRouter()).processScheduledActiveMtMessage(ArgumentMatchers.any(ScheduledActiveMtMessage.class));
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getFinishedMtMessageHandler());
  }

  @Test
  void globalTimeoutTest() {
    Clock clock = mockClock(Instant.now(), ZERO_INSTANT);
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage);

      Assertions.assertTrue(updateActiveMtMessageParameter.isEmpty());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onGlobalTimeout();
    Mockito.verify(sendSchemaProcessorContext.getFinishedMtMessageHandler()).onTimeout(activeMtMessageWriter, joinedActiveMtMessage);
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getTransmissionRouter());
  }

  @Test
  void globalTimeoutWithDoorkeeperDelayedActiveMtMessageTest() {
    Clock clock = mockClock(Instant.now(), ZERO_INSTANT);
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);

    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage(
        TestUtil.createPersistedActiveMtMessageBuilder()
            .setCreated(Instant.now().plus(Duration.ofHours(1)))
            .build());

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage);

      Assertions.assertTrue(updateActiveMtMessageParameter.isEmpty());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onGlobalTimeout();
    Mockito.verify(sendSchemaProcessorContext.getFinishedMtMessageHandler()).onTimeout(activeMtMessageWriter, joinedActiveMtMessage);
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getTransmissionRouter());
  }

  @Test
  void runtimeExceptionTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage();
    Mockito.doThrow(new RuntimeException("test"))
        .when(sendSchemaProcessorContext.getTransmissionRouter())
        .processScheduledActiveMtMessage(ArgumentMatchers.any(ScheduledActiveMtMessage.class));

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage);

      verifyUpdateActiveMtMessageParameter(updateActiveMtMessageParameter.get());
    });

    Mockito.verify(sendSchemaProcessorContext.getTransmissionRouter()).processScheduledActiveMtMessage(ArgumentMatchers.any(ScheduledActiveMtMessage.class));
    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onFailure();
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getFinishedMtMessageHandler());
  }

  @Test
  void stepsExhaustedTest() {
    Clock clock = mockClock();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(TestUtil.createPersistedDeviceInfo()));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);
    SendSchemaProcessorContext sendSchemaProcessorContext = createSendSchemaProcessorContext();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    JoinedActiveMtMessage joinedActiveMtMessage = TestUtil.createJoinedActiveMtMessage(SendSchemaStepId.ofInt(2));

    TispContext.runInContext(() -> {
      SendSchemaProcessor sendSchemaProcessor = new SendSchemaProcessor(clock, deviceInfoReaderFactory, sendSchemaProcessorContext);
      Optional<UpdateActiveMtMessageParameter> updateActiveMtMessageParameter = sendSchemaProcessor.executeNextSendSchemaStep(
          activeMtMessageWriter, joinedActiveMtMessage);

      Assertions.assertTrue(updateActiveMtMessageParameter.isEmpty());
    });

    Mockito.verify(sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter()).onStepsExhausted();
    Mockito.verify(sendSchemaProcessorContext.getFinishedMtMessageHandler()).onRejected(activeMtMessageWriter, joinedActiveMtMessage);
    Mockito.verifyNoInteractions(sendSchemaProcessorContext.getTransmissionRouter());
  }
}
