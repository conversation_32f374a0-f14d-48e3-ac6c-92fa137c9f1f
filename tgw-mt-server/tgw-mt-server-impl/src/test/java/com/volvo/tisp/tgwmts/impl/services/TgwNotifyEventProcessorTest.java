package com.volvo.tisp.tgwmts.impl.services;

import static org.mockito.Mockito.verify;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState.AssetCapability;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.TgwNotifyEventMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class TgwNotifyEventProcessorTest {
  private ActiveMtMessageWriter activeMtMessageWriter;
  private TgwNotifyEventMetricReporter tgwNotifyEventMetricReporter;
  private TgwNotifyEventProcessor tgwNotifyEventProcessor;

  @BeforeEach
  void setUp() {
    activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = Mockito.mock(ActiveMtMessageWriterFactory.class);
    Mockito.when(activeMtMessageWriterFactory.createReadCommitted()).thenReturn(activeMtMessageWriter);
    tgwNotifyEventMetricReporter = Mockito.mock(TgwNotifyEventMetricReporter.class);

    tgwNotifyEventProcessor = new TgwNotifyEventProcessor(activeMtMessageWriterFactory, tgwNotifyEventMetricReporter);
  }

  @Test
  void testProcess() {
    AssetCapabilityState assetCapabilityState = new AssetCapabilityState(TestUtil.VPI, AssetCapability.WIFI, true);
    Mockito.doNothing().when(activeMtMessageWriter).updateAssetCapabilityState(assetCapabilityState);

    tgwNotifyEventProcessor.process(assetCapabilityState);

    verify(tgwNotifyEventMetricReporter).onAssetStateChangeReceived(assetCapabilityState);
    verify(tgwNotifyEventMetricReporter).onAssetStateChangeProcessed(assetCapabilityState);
    verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    verify(activeMtMessageWriter).updateAssetCapabilityState(assetCapabilityState);
    verify(activeMtMessageWriter).commitTransaction();
    verify(activeMtMessageWriter).close();

    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, tgwNotifyEventMetricReporter);
  }

  @Test
  void testProcessFailure() {
    AssertThrows.illegalArgumentException(() -> tgwNotifyEventProcessor.process(null), "assetCapabilityState must not be null");

    AssetCapabilityState assetCapabilityState = new AssetCapabilityState(TestUtil.VPI, AssetCapability.WIFI, true);
    Mockito.doThrow(new RuntimeException("Database error")).when(activeMtMessageWriter).updateAssetCapabilityState(assetCapabilityState);

    tgwNotifyEventProcessor.process(assetCapabilityState);

    verify(tgwNotifyEventMetricReporter).onAssetStateChangeReceived(assetCapabilityState);
    verify(tgwNotifyEventMetricReporter).onAssetStateChangeProcessFailed(assetCapabilityState);
    verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    verify(activeMtMessageWriter).updateAssetCapabilityState(assetCapabilityState);
    verify(activeMtMessageWriter).close();

    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, tgwNotifyEventMetricReporter);
  }
}
