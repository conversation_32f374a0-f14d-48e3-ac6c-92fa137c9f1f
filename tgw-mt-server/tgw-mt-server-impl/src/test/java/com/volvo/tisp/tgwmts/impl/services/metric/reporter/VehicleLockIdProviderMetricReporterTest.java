package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

import io.micrometer.core.instrument.Tags;

class VehicleLockIdProviderMetricReporterTest {
  @Test
  void onVehicleLockInsertionFailureTest() {
    MetricsReporterTestUtils.initReporterAndTest(VehicleLockIdProviderMetricReporter::new, (meterRegistry, vehicleLockIdProviderMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> vehicleLockIdProviderMetricReporter.onVehicleLockInsertionFailure(null),
          "insertionFailureReason must not be null");

      vehicleLockIdProviderMetricReporter.onVehicleLockInsertionFailure(InsertionFailureReason.UNKNOWN);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "vehicle.lock.id.provider.insertion.failure", Tags.of("reason",
          InsertionFailureReason.UNKNOWN.name()), 1);

      vehicleLockIdProviderMetricReporter.onVehicleLockInsertionFailure(InsertionFailureReason.DUPLICATE_KEY);
      MetricsReporterTestUtils.checkCounter(meterRegistry, "vehicle.lock.id.provider.insertion.failure", Tags.of("reason",
          InsertionFailureReason.DUPLICATE_KEY.name()), 1);
    });
  }

  @Test
  void onVehicleLockInsertionSuccessTest() {
    MetricsReporterTestUtils.initReporterAndTest(VehicleLockIdProviderMetricReporter::new, (meterRegistry, vehicleLockIdProviderMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> vehicleLockIdProviderMetricReporter.onVehicleLockInsertionSuccess(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> vehicleLockIdProviderMetricReporter.onVehicleLockInsertionSuccess(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      vehicleLockIdProviderMetricReporter.onVehicleLockInsertionSuccess(Duration.ofSeconds(1));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "vehicle.lock.id.provider.insertion", Duration.ofSeconds(1), 1);
    });
  }

  @Test
  void onVpiLockTest() {
    MetricsReporterTestUtils.initReporterAndTest(VehicleLockIdProviderMetricReporter::new, (meterRegistry, vehicleLockIdProviderMetricReporter) -> {
      AssertThrows.illegalArgumentException(() -> vehicleLockIdProviderMetricReporter.onVpiLock(null), "duration must not be null");
      AssertThrows.illegalArgumentException(() -> vehicleLockIdProviderMetricReporter.onVpiLock(Duration.ofMillis(-1)),
          "duration must not be negative: PT-0.001S");

      vehicleLockIdProviderMetricReporter.onVpiLock(Duration.ofSeconds(1));
      MetricsReporterTestUtils.checkTimer(meterRegistry, "vpi.lock", Duration.ofSeconds(1), 1);
    });
  }
}
