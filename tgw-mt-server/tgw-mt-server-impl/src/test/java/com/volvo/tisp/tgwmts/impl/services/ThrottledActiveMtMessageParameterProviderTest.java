package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.impl.conf.properties.ThrottledStatusConfiguration;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;

class ThrottledActiveMtMessageParameterProviderTest {
  private static final Instant INSTANT = Instant.ofEpochSecond(1000);
  private static final Duration WAIT_TIMEOUT = Duration.ofSeconds(1);

  private static void assertParameterValues(UpdateActiveMtMessageParameter updateActiveMtMessageParameter, int expectedStepId) {
    Assertions.assertEquals(TestUtil.ACTIVE_MT_MESSAGE_ID, updateActiveMtMessageParameter.getActiveMtMessageId());
    Assertions.assertEquals(TestUtil.RETRY_ATTEMPT, updateActiveMtMessageParameter.getRetryAttempt());
    Assertions.assertEquals(expectedStepId, updateActiveMtMessageParameter.getSendSchemaStepId().toInt());
    Assertions.assertEquals(INSTANT.plus(WAIT_TIMEOUT), updateActiveMtMessageParameter.getTimeout());
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(INSTANT);
    return clock;
  }

  @Test
  void getTest() {
    Clock clock = mockClock();
    ThrottledStatusConfiguration properties = new ThrottledStatusConfiguration(WAIT_TIMEOUT);
    ActiveMtMessage activeMtMessage = TestUtil.createActiveMtMessage(TestUtil.RETRY_ATTEMPT, SendSchemaStepId.ofInt(3));
    PersistedActiveMtMessage persistedActiveMtMessage = TestUtil.createPersistedActiveMtMessageBuilder().setActiveMtMessage(activeMtMessage).build();

    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = new ThrottledActiveMtMessageParameterProvider(clock, properties);
    UpdateActiveMtMessageParameter updateActiveMtMessageParameter = throttledActiveMtMessageParameterProvider.get(persistedActiveMtMessage);

    assertParameterValues(updateActiveMtMessageParameter, 2);
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }

  @Test
  void nonZeroTest() {
    Clock clock = mockClock();
    ThrottledStatusConfiguration properties = new ThrottledStatusConfiguration(WAIT_TIMEOUT);
    ActiveMtMessage activeMtMessage = TestUtil.createActiveMtMessage(TestUtil.RETRY_ATTEMPT, SendSchemaStepId.ofInt(0));
    PersistedActiveMtMessage persistedActiveMtMessage = TestUtil.createPersistedActiveMtMessageBuilder().setActiveMtMessage(activeMtMessage).build();

    ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider = new ThrottledActiveMtMessageParameterProvider(clock, properties);
    UpdateActiveMtMessageParameter updateActiveMtMessageParameter = throttledActiveMtMessageParameterProvider.get(persistedActiveMtMessage);

    assertParameterValues(updateActiveMtMessageParameter, 0);
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(clock);
  }
}
