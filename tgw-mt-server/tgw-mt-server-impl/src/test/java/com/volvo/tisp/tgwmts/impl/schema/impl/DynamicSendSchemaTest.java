package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DynamicSendSchemaTest {
  private static final Duration ONE_HOUR = Duration.ofHours(1);
  private static final Duration ONE_MINUTE = Duration.ofMinutes(1);
  private static final Duration ONE_WEEK = Duration.ofDays(7);
  private static final SendSchemaName SEND_SCHEMA_NAME = new SendSchemaName("ONE_WEEK-NORMAL");

  public static SendSchema createDynamicSendSchema() {
    return DynamicSendSchema.create(ONE_WEEK, SEND_SCHEMA_NAME,
        List.of(
            SendSchemaStep.forUdp(SendSchemaStepId.ofInt(1), ONE_MINUTE),
            SendSchemaStep.forSms(SendSchemaStepId.ofInt(2), ONE_HOUR),
            SendSchemaStep.forWait(SendSchemaStepId.ofInt(3), ONE_WEEK.minus(ONE_MINUTE).minus(ONE_HOUR))));
  }

  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(ONE_WEEK, createDynamicSendSchema().getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(5, createDynamicSendSchema().getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> createDynamicSendSchema().getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SEND_SCHEMA_NAME, createDynamicSendSchema().getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    SendSchema dynamicSendSchema = createDynamicSendSchema();
    VerificationUtil.verifySendSchemaStepUdp(dynamicSendSchema, 1, ONE_MINUTE);
    VerificationUtil.verifySendSchemaStepSms(dynamicSendSchema, 2, ONE_HOUR);
    VerificationUtil.verifySendSchemaStepWait(dynamicSendSchema, 3, ONE_WEEK.minus(ONE_MINUTE).minus(ONE_HOUR));

    Assertions.assertTrue(dynamicSendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(4)).isEmpty());
    Assertions.assertTrue(dynamicSendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(0)).isEmpty());
  }
}