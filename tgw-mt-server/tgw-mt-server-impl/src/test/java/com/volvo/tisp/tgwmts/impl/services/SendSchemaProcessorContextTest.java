package com.volvo.tisp.tgwmts.impl.services;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.impl.schema.FilterableSendSchemaFetcher;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepFilterProducer;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.SendSchemaProcessorMetricReporter;

class SendSchemaProcessorContextTest {
  @Test
  void gettersTest() {
    FinishedMtMessageHandler finishedMtMessageHandler = Mockito.mock(FinishedMtMessageHandler.class);
    FilterableSendSchemaFetcher filterableSendSchemaFetcher = Mockito.mock(FilterableSendSchemaFetcher.class);
    SendSchemaProcessorMetricReporter sendSchemaProcessorMetricReporter = Mockito.mock(SendSchemaProcessorMetricReporter.class);
    SendSchemaStepFilterProducer sendSchemaStepFilterProducer = Mockito.mock(SendSchemaStepFilterProducer.class);
    TransmissionRouter transmissionRouter = Mockito.mock(TransmissionRouter.class);

    SendSchemaProcessorContext sendSchemaProcessorContext = new SendSchemaProcessorContext(filterableSendSchemaFetcher, finishedMtMessageHandler,
        sendSchemaProcessorMetricReporter, sendSchemaStepFilterProducer, transmissionRouter);

    Assertions.assertSame(finishedMtMessageHandler, sendSchemaProcessorContext.getFinishedMtMessageHandler());
    Assertions.assertSame(filterableSendSchemaFetcher, sendSchemaProcessorContext.getFilterableSendSchemaFetcher());
    Assertions.assertSame(sendSchemaProcessorMetricReporter, sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter());
    Assertions.assertSame(sendSchemaStepFilterProducer, sendSchemaProcessorContext.getSendSchemaStepFilterProducer());
    Assertions.assertSame(transmissionRouter, sendSchemaProcessorContext.getTransmissionRouter());
  }
}
