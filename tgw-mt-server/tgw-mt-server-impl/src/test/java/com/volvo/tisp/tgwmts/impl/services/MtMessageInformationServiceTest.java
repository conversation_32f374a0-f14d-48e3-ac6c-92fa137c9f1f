package com.volvo.tisp.tgwmts.impl.services;

import com.volvo.tisp.tgw.device.info.cache.core.api.CacheDeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.ActiveMtMessageWriterImpl;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.impl.model.MtMessageInformation;
import com.volvo.tisp.tgwmts.impl.schema.*;
import org.jetbrains.annotations.NotNull;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.*;

import static com.volvo.tisp.tgwmts.impl.util.TestUtil.*;
import static org.mockito.ArgumentMatchers.any;

class MtMessageInformationServiceTest {

    @Test
    void getMessageInformationByVpi() {
        MocksForMtMessageInformationService mocksForMtMessageInformationService = createMockedMtMessageInformationService();

        PersistedDeviceInfo persistedDeviceInfo = createPersistedDeviceInfo();
        JoinedMtMessage joinedMtMessage = createJoinedMtMessage();
        SendSchemaStepFilter sendSchemaStepFilter = SendSchemaStepFilter.of(Set.of(SEND_SCHEMA_STEP_TYPE));
        SendSchema sendSchema = Mockito.mock(SendSchema.class);
        SendSchemaStep sendSchemaStep = Mockito.mock(SendSchemaStep.class);

        Mockito.when(mocksForMtMessageInformationService.activeMtMessageWriterImpl.findMtMessageAndActiveMtMessages(VPI))
                .thenReturn(List.of(joinedMtMessage));
        Mockito.when(mocksForMtMessageInformationService.cacheDeviceInfoReader.findDeviceInfoByVpi(VPI))
                .thenReturn(Optional.ofNullable(persistedDeviceInfo));
        Mockito.when(mocksForMtMessageInformationService.sendSchemaStepFilterProducer.createSendSchemaStepFilter(persistedDeviceInfo.getDeviceInfo()))
                .thenReturn(sendSchemaStepFilter);
        Mockito.when(mocksForMtMessageInformationService.filterableSendSchemaFetcher.fetch(joinedMtMessage.persistedMtMessage().getMtMessage().getSendSchemaName(), sendSchemaStepFilter))
                .thenReturn(sendSchema);
        Mockito.when(sendSchema.getSendSchemaStep(any())).thenReturn(Optional.ofNullable(sendSchemaStep));
        Mockito.when(sendSchemaStep.getSendSchemaStepType()).thenReturn(SEND_SCHEMA_STEP_TYPE);

        MtMessageInformationService mtMessageInformationService = new MtMessageInformationService(
                mocksForMtMessageInformationService.activeMtMessageReaderFactory(),
                mocksForMtMessageInformationService.cacheDeviceInfoReader(),
                mocksForMtMessageInformationService.filterableSendSchemaFetcher(),
                mocksForMtMessageInformationService.sendSchemaStepFilterProducer()
        );

        Optional<MtMessageInformation> mtMessagesInfo = mtMessageInformationService.getMessageInformationByVpi(VPI);
        Assertions.assertEquals(VPI.toString(), mtMessagesInfo.get().vpi());
        Assertions.assertEquals(TID.toString(), mtMessagesInfo.get().mtMessageDetails().get(0).tid());
        Assertions.assertEquals(SEND_SCHEMA_STEP_TYPE, mtMessagesInfo.get().mtMessageDetails().get(0).activeMessageDetails().sendSchemaStep());

    }

    @Test
    void getMessageInformationByVpiNoActiveMessage() {
        MocksForMtMessageInformationService mocksForMtMessageInformationService = createMockedMtMessageInformationService();

        PersistedDeviceInfo persistedDeviceInfo = createPersistedDeviceInfo();
        PersistedVehicleLock persistedVehicleLock = createPersistedVehicleLock();
        PersistedMtMessage persistedMtMessage = createPersistedMtMessage(createSrpOption());

        JoinedMtMessage joinedMtMessage = new JoinedMtMessage(Optional.empty(), persistedMtMessage, persistedVehicleLock);
        SendSchemaStepFilter sendSchemaStepFilter = SendSchemaStepFilter.of(Set.of(SEND_SCHEMA_STEP_TYPE));
        SendSchema sendSchema = Mockito.mock(SendSchema.class);
        SendSchemaStep sendSchemaStep = Mockito.mock(SendSchemaStep.class);

        Mockito.when(mocksForMtMessageInformationService.activeMtMessageWriterImpl.findMtMessageAndActiveMtMessages(VPI))
                .thenReturn(List.of(joinedMtMessage));
        Mockito.when(mocksForMtMessageInformationService.cacheDeviceInfoReader.findDeviceInfoByVpi(VPI))
                .thenReturn(Optional.ofNullable(persistedDeviceInfo));
        Mockito.when(mocksForMtMessageInformationService.sendSchemaStepFilterProducer.createSendSchemaStepFilter(persistedDeviceInfo.getDeviceInfo()))
                .thenReturn(sendSchemaStepFilter);
        Mockito.when(mocksForMtMessageInformationService.filterableSendSchemaFetcher.fetch(joinedMtMessage.persistedMtMessage().getMtMessage().getSendSchemaName(), sendSchemaStepFilter))
                .thenReturn(sendSchema);
        Mockito.when(sendSchema.getSendSchemaStep(any())).thenReturn(Optional.ofNullable(sendSchemaStep));
        Mockito.when(sendSchemaStep.getSendSchemaStepType()).thenReturn(SEND_SCHEMA_STEP_TYPE);

        MtMessageInformationService mtMessageInformationService = new MtMessageInformationService(
                mocksForMtMessageInformationService.activeMtMessageReaderFactory(),
                mocksForMtMessageInformationService.cacheDeviceInfoReader(),
                mocksForMtMessageInformationService.filterableSendSchemaFetcher(),
                mocksForMtMessageInformationService.sendSchemaStepFilterProducer()
        );

        Optional<MtMessageInformation> mtMessagesInfo = mtMessageInformationService.getMessageInformationByVpi(VPI);
        Assertions.assertEquals(VPI.toString(), mtMessagesInfo.get().vpi());
        Assertions.assertEquals(TID.toString(), mtMessagesInfo.get().mtMessageDetails().get(0).tid());
        Assertions.assertNull(mtMessagesInfo.get().mtMessageDetails().get(0).activeMessageDetails());

    }

    @Test
    void getMessageInformationByVpiDeviceInfoNull() {
        MocksForMtMessageInformationService mocksForMtMessageInformationService = createMockedMtMessageInformationService();

        JoinedMtMessage joinedMtMessage = createJoinedMtMessage();

        Mockito.when(mocksForMtMessageInformationService.activeMtMessageWriterImpl.findMtMessageAndActiveMtMessages(VPI))
                .thenReturn(List.of(joinedMtMessage));
        Mockito.when(mocksForMtMessageInformationService.cacheDeviceInfoReader.findDeviceInfoByVpi(VPI))
                .thenReturn(Optional.empty());

        MtMessageInformationService mtMessageInformationService = new MtMessageInformationService(
                mocksForMtMessageInformationService.activeMtMessageReaderFactory(),
                mocksForMtMessageInformationService.cacheDeviceInfoReader(),
                mocksForMtMessageInformationService.filterableSendSchemaFetcher(),
                mocksForMtMessageInformationService.sendSchemaStepFilterProducer()
        );

        Assertions.assertThrows(NoSuchElementException.class, () -> mtMessageInformationService.getMessageInformationByVpi(VPI));
    }

    @Test
    void getMessageInformationByVpiNoMessages() {
        MocksForMtMessageInformationService mocksForMtMessageInformationService = createMockedMtMessageInformationService();

        Mockito.when(mocksForMtMessageInformationService.activeMtMessageWriterImpl.findMtMessageAndActiveMtMessages(VPI))
                .thenReturn(Collections.emptyList());
        Mockito.when(mocksForMtMessageInformationService.cacheDeviceInfoReader.findDeviceInfoByVpi(VPI))
                .thenReturn(Optional.empty());

        MtMessageInformationService mtMessageInformationService = new MtMessageInformationService(
                mocksForMtMessageInformationService.activeMtMessageReaderFactory(),
                mocksForMtMessageInformationService.cacheDeviceInfoReader(),
                mocksForMtMessageInformationService.filterableSendSchemaFetcher(),
                mocksForMtMessageInformationService.sendSchemaStepFilterProducer()
        );

        Optional<MtMessageInformation> mtMessagesInfo = mtMessageInformationService.getMessageInformationByVpi(VPI);
        Assertions.assertTrue(mtMessagesInfo.isEmpty());

    }

    @NotNull
    private static MocksForMtMessageInformationService createMockedMtMessageInformationService( ) {

        ActiveMtMessageReaderFactory activeMtMessageReaderFactory = Mockito.mock(ActiveMtMessageReaderFactory.class);
        ActiveMtMessageWriterImpl activeMtMessageWriterImpl = Mockito.mock(ActiveMtMessageWriterImpl.class);
        CacheDeviceInfoReader cacheDeviceInfoReader = Mockito.mock(CacheDeviceInfoReader.class);
        FilterableSendSchemaFetcher filterableSendSchemaFetcher = Mockito.mock(FilterableSendSchemaFetcher.class);
        SendSchemaStepFilterProducer sendSchemaStepFilterProducer = Mockito.mock(SendSchemaStepFilterProducer.class);


        Mockito.when(activeMtMessageReaderFactory.create()).thenReturn(activeMtMessageWriterImpl);

        return new MocksForMtMessageInformationService(activeMtMessageReaderFactory, cacheDeviceInfoReader,
                filterableSendSchemaFetcher, sendSchemaStepFilterProducer, activeMtMessageWriterImpl);
    }

    private record MocksForMtMessageInformationService(ActiveMtMessageReaderFactory activeMtMessageReaderFactory, CacheDeviceInfoReader cacheDeviceInfoReader,
                                                       FilterableSendSchemaFetcher filterableSendSchemaFetcher, SendSchemaStepFilterProducer sendSchemaStepFilterProducer,
                                                       ActiveMtMessageWriterImpl activeMtMessageWriterImpl) {
    }
}