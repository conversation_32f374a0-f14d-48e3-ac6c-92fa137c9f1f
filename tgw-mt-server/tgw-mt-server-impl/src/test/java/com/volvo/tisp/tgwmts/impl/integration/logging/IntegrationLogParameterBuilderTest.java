package com.volvo.tisp.tgwmts.impl.integration.logging;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.componentbase.logging.Logging;

class IntegrationLogParameterBuilderTest {
  private static final IntegrationMessage INTEGRATION_MESSAGE = IntegrationMessage.onDoorkeeper();
  private static final MetaData META_DATA = new MetaDataBuilder().build();
  private static final VehicleDetail VEHICLE_DETAIL = VehicleDetail.create(TestUtil.VPI);

  private static void compare(IntegrationLogParameterBuilder integrationLogParameterBuilder, IntegrationLogParameter integrationLogParameter) {
    Assertions.assertSame(integrationLogParameterBuilder.getDirection(), integrationLogParameter.getDirection());
    Assertions.assertSame(integrationLogParameterBuilder.getIntegrationMessage(), integrationLogParameter.getIntegrationMessage());
    Assertions.assertSame(integrationLogParameterBuilder.getMetaData(), integrationLogParameter.getMetaData());
    Assertions.assertSame(integrationLogParameterBuilder.getStatus(), integrationLogParameter.getStatus());
    Assertions.assertSame(integrationLogParameterBuilder.getVehicleDetail(), integrationLogParameter.getVehicleDetail());
  }

  @Test
  void buildTest() {
    IntegrationLogParameterBuilder integrationLogParameterBuilder = new IntegrationLogParameterBuilder();
    AssertThrows.illegalArgumentException(integrationLogParameterBuilder::build, "direction must not be null");

    integrationLogParameterBuilder.setDirection(Logging.Direction.SERVER_IN);
    AssertThrows.illegalArgumentException(integrationLogParameterBuilder::build, "integrationMessage must not be null");

    integrationLogParameterBuilder.setIntegrationMessage(INTEGRATION_MESSAGE);
    AssertThrows.illegalArgumentException(integrationLogParameterBuilder::build, "metaData must not be null");

    integrationLogParameterBuilder.setMetaData(META_DATA);
    AssertThrows.illegalArgumentException(integrationLogParameterBuilder::build, "status must not be null");

    integrationLogParameterBuilder.setStatus(Logging.Status.SUCCESS);
    AssertThrows.illegalArgumentException(integrationLogParameterBuilder::build, "vehicleDetail must not be null");

    integrationLogParameterBuilder.setVehicleDetail(VEHICLE_DETAIL);

    compare(integrationLogParameterBuilder, integrationLogParameterBuilder.build());
  }

  @Test
  void directionTest() {
    IntegrationLogParameterBuilder integrationLogParameterBuilder = new IntegrationLogParameterBuilder();
    Assertions.assertNull(integrationLogParameterBuilder.getDirection());

    integrationLogParameterBuilder.setDirection(Logging.Direction.SERVER_IN);
    Assertions.assertSame(Logging.Direction.SERVER_IN, integrationLogParameterBuilder.getDirection());

    AssertThrows.illegalArgumentException(() -> integrationLogParameterBuilder.setDirection(null), "direction must not be null");
    Assertions.assertSame(Logging.Direction.SERVER_IN, integrationLogParameterBuilder.getDirection());
  }

  @Test
  void integrationMessageTest() {
    IntegrationLogParameterBuilder integrationLogParameterBuilder = new IntegrationLogParameterBuilder();
    Assertions.assertNull(integrationLogParameterBuilder.getIntegrationMessage());

    integrationLogParameterBuilder.setIntegrationMessage(INTEGRATION_MESSAGE);
    Assertions.assertSame(INTEGRATION_MESSAGE, integrationLogParameterBuilder.getIntegrationMessage());

    AssertThrows.illegalArgumentException(() -> integrationLogParameterBuilder.setIntegrationMessage(null), "integrationMessage must not be null");
    Assertions.assertSame(INTEGRATION_MESSAGE, integrationLogParameterBuilder.getIntegrationMessage());
  }

  @Test
  void metaDataTest() {
    IntegrationLogParameterBuilder integrationLogParameterBuilder = new IntegrationLogParameterBuilder();
    Assertions.assertNull(integrationLogParameterBuilder.getMetaData());

    integrationLogParameterBuilder.setMetaData(META_DATA);
    Assertions.assertSame(META_DATA, integrationLogParameterBuilder.getMetaData());

    AssertThrows.illegalArgumentException(() -> integrationLogParameterBuilder.setMetaData(null), "metaData must not be null");
    Assertions.assertSame(META_DATA, integrationLogParameterBuilder.getMetaData());
  }

  @Test
  void statusTest() {
    IntegrationLogParameterBuilder integrationLogParameterBuilder = new IntegrationLogParameterBuilder();
    Assertions.assertNull(integrationLogParameterBuilder.getMetaData());

    integrationLogParameterBuilder.setStatus(Logging.Status.SUCCESS);
    Assertions.assertSame(Logging.Status.SUCCESS, integrationLogParameterBuilder.getStatus());

    AssertThrows.illegalArgumentException(() -> integrationLogParameterBuilder.setStatus(null), "status must not be null");
    Assertions.assertSame(Logging.Status.SUCCESS, integrationLogParameterBuilder.getStatus());
  }

  @Test
  void vehicleDetailTest() {
    IntegrationLogParameterBuilder integrationLogParameterBuilder = new IntegrationLogParameterBuilder();
    Assertions.assertNull(integrationLogParameterBuilder.getMetaData());

    integrationLogParameterBuilder.setVehicleDetail(VEHICLE_DETAIL);
    Assertions.assertSame(VEHICLE_DETAIL, integrationLogParameterBuilder.getVehicleDetail());

    AssertThrows.illegalArgumentException(() -> integrationLogParameterBuilder.setVehicleDetail(null), "vehicleDetail must not be null");
    Assertions.assertSame(VEHICLE_DETAIL, integrationLogParameterBuilder.getVehicleDetail());
  }
}
