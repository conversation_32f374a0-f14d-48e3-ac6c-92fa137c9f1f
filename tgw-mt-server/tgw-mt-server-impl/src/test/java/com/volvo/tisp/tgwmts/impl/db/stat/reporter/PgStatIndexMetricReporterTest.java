package com.volvo.tisp.tgwmts.impl.db.stat.reporter;


import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.stat.IndexName;
import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class PgStatIndexMetricReporterTest {
  private static final String INDEX_NAME = "INDEX_NAME";

  @Test
  void onDeletedPages() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatIndexMetricReporter::new, (meterRegistry, pgStatIndexMetricReporter) -> {
      pgStatIndexMetricReporter.onDeletedPages(IndexName.MT_MESSAGE_PKEY, 5);
      pgStatIndexMetricReporter.onDeletedPages(IndexName.ACTIVE_MT_MESSAGE_TIMEOUT, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "deleted-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "deleted-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_TIMEOUT.toString()), 20);

      pgStatIndexMetricReporter.onDeletedPages(IndexName.MT_MESSAGE_PKEY, 3);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "deleted-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 3);
    });
  }

  @Test
  void onEmptyPages() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatIndexMetricReporter::new, (meterRegistry, pgStatIndexMetricReporter) -> {
      pgStatIndexMetricReporter.onEmptyPages(IndexName.MT_MESSAGE_PKEY, 5);
      pgStatIndexMetricReporter.onEmptyPages(IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "empty-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "empty-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED.toString()), 20);

      pgStatIndexMetricReporter.onEmptyPages(IndexName.MT_MESSAGE_PKEY, 3);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "empty-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 3);
    });
  }

  @Test
  void onInternalPages() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatIndexMetricReporter::new, (meterRegistry, pgStatIndexMetricReporter) -> {
      pgStatIndexMetricReporter.onInternalPages(IndexName.MT_MESSAGE_PKEY, 5);
      pgStatIndexMetricReporter.onInternalPages(IndexName.ACTIVE_MT_MESSAGE_TIMEOUT, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "internal-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "internal-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_TIMEOUT.toString()), 20);

      pgStatIndexMetricReporter.onInternalPages(IndexName.MT_MESSAGE_PKEY, 3);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "internal-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 3);
    });
  }

  @Test
  void onLeafPages() {
    MetricsReporterTestUtils.initReporterAndTest(PgStatIndexMetricReporter::new, (meterRegistry, pgStatIndexMetricReporter) -> {
      pgStatIndexMetricReporter.onLeafPages(IndexName.MT_MESSAGE_PKEY, 5);
      pgStatIndexMetricReporter.onLeafPages(IndexName.ACTIVE_MT_MESSAGE_PKEY, 20);

      MetricsReporterTestUtils.checkGauge(meterRegistry, "leaf-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 5);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "leaf-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_PKEY.toString()), 20);

      pgStatIndexMetricReporter.onLeafPages(IndexName.MT_MESSAGE_PKEY, 3);
      MetricsReporterTestUtils.checkGauge(meterRegistry, "leaf-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), 3);
    });
  }
}