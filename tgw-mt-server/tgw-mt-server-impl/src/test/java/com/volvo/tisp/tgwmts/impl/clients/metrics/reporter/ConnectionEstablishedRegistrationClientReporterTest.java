package com.volvo.tisp.tgwmts.impl.clients.metrics.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

class ConnectionEstablishedRegistrationClientReporterTest {
  @Test
  void onReRegisterTest() {
    MetricsReporterTestUtils.initReporterAndTest(ConnectionEstablishedRegistrationReporter::new,
        (meterRegistry, connectionEstablishedRestClientReporter) -> {

          connectionEstablishedRestClientReporter.onReRegister();
          MetricsReporterTestUtils.checkCounter(meterRegistry, "re-register", 1);
        });
  }
}
