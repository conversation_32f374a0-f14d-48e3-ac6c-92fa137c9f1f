package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.net.URI;
import java.time.Duration;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class MtSoftCarReactiveClientPropertiesTest {
  @Test
  void invalidConstructorTest() {

    AssertThrows.illegalArgumentException(
        () -> new MtSoftCarReactiveClientProperties(URI.create("uri"), "/register", Duration.ofSeconds(-10)),
        "requestTimeout must not be negative");
  }
}