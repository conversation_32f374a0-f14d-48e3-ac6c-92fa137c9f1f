package com.volvo.tisp.tgwmts.impl.db.stat.reporter;

import java.util.Optional;

import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.stat.IndexName;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatIndex;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatTuple;
import com.volvo.tisp.tgwmts.database.model.stat.TableName;

class PgStatReporterTaskTest {
  private static ActiveMtMessageReader mockActiveMtMessageReader() {
    ActiveMtMessageReader activeMtMessageReader = Mockito.mock(ActiveMtMessageReader.class);
    Mockito.when(activeMtMessageReader.findPgStatIndex(ArgumentMatchers.any(IndexName.class)))
        .thenReturn(Optional.of(new PgStatIndex(1, 2, 3, 4)));
    Mockito.when(activeMtMessageReader.findPgStatTuple(ArgumentMatchers.any(TableName.class)))
        .thenReturn(Optional.of(new PgStatTuple(4, 3, 2, 1)));
    return activeMtMessageReader;
  }

  private static ActiveMtMessageReaderFactory mockActiveMtMessageReaderFactory(ActiveMtMessageReader activeMtMessageReader) {
    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = Mockito.mock(ActiveMtMessageReaderFactory.class);
    Mockito.when(activeMtMessageReaderFactory.create()).thenReturn(activeMtMessageReader);
    return activeMtMessageReaderFactory;
  }

  @Test
  void runTest() {
    ActiveMtMessageReader activeMtMessageReader = mockActiveMtMessageReader();
    ActiveMtMessageReaderFactory activeMtMessageReaderFactory = mockActiveMtMessageReaderFactory(activeMtMessageReader);
    PgStatIndexMetricReporter pgStatIndexMetricReporter = Mockito.mock(PgStatIndexMetricReporter.class);
    PgStatTupleMetricReporter pgStatTupleMetricReporter = Mockito.mock(PgStatTupleMetricReporter.class);

    PgStatReporterTask pgStatReporterTask = new PgStatReporterTask(activeMtMessageReaderFactory, pgStatIndexMetricReporter, pgStatTupleMetricReporter);
    pgStatReporterTask.run();

    Mockito.verify(activeMtMessageReaderFactory).create();

    Mockito.verify(activeMtMessageReader).findPgStatTuple(TableName.MT_MESSAGE);
    Mockito.verify(activeMtMessageReader).findPgStatTuple(TableName.ACTIVE_MT_MESSAGE);

    Mockito.verify(activeMtMessageReader).findPgStatIndex(IndexName.ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY);
    Mockito.verify(activeMtMessageReader).findPgStatIndex(IndexName.ACTIVE_MT_MESSAGE_PKEY);
    Mockito.verify(activeMtMessageReader).findPgStatIndex(IndexName.ACTIVE_MT_MESSAGE_TIMEOUT);
    Mockito.verify(activeMtMessageReader).findPgStatIndex(IndexName.MT_MESSAGE_PKEY);
    Mockito.verify(activeMtMessageReader).findPgStatIndex(IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED);

    Mockito.verify(pgStatTupleMetricReporter, Mockito.times(2)).onDeadTupleLen(ArgumentMatchers.any(TableName.class), ArgumentMatchers.eq(4));
    Mockito.verify(pgStatTupleMetricReporter, Mockito.times(2)).onFreeSpace(ArgumentMatchers.any(TableName.class), ArgumentMatchers.eq(3));
    Mockito.verify(pgStatTupleMetricReporter, Mockito.times(2)).onTableLen(ArgumentMatchers.any(TableName.class), ArgumentMatchers.eq(2));
    Mockito.verify(pgStatTupleMetricReporter, Mockito.times(2)).onTupleLen(ArgumentMatchers.any(TableName.class), ArgumentMatchers.eq(1));

    Mockito.verify(pgStatIndexMetricReporter, Mockito.times(5)).onDeletedPages(ArgumentMatchers.any(IndexName.class), ArgumentMatchers.eq(1));
    Mockito.verify(pgStatIndexMetricReporter, Mockito.times(5)).onEmptyPages(ArgumentMatchers.any(IndexName.class), ArgumentMatchers.eq(2));
    Mockito.verify(pgStatIndexMetricReporter, Mockito.times(5)).onInternalPages(ArgumentMatchers.any(IndexName.class), ArgumentMatchers.eq(3));
    Mockito.verify(pgStatIndexMetricReporter, Mockito.times(5)).onLeafPages(ArgumentMatchers.any(IndexName.class), ArgumentMatchers.eq(4));

    Mockito.verify(activeMtMessageReader).close();

    Mockito.verifyNoMoreInteractions(activeMtMessageReader, activeMtMessageReaderFactory,pgStatIndexMetricReporter, pgStatTupleMetricReporter);
  }
}