package com.volvo.tisp.tgwmts.impl.services;

import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class TgwIdentifierFunctionTest {
  private static DeviceInfoReader mockDeviceInfoReader(Optional<PersistedDeviceInfo> optional) {
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    Mockito.when(deviceInfoReader.findDeviceInfoByVpi(TestUtil.VPI)).thenReturn(optional);
    return deviceInfoReader;
  }

  private static DeviceInfoReader mockDeviceInfoReader(RuntimeException runtimeException) {
    DeviceInfoReader deviceInfoReader = Mockito.mock(DeviceInfoReader.class);
    Mockito.when(deviceInfoReader.findDeviceInfoByVpi(TestUtil.VPI)).thenThrow(runtimeException);
    return deviceInfoReader;
  }

  private static DeviceInfoReaderFactory mockDeviceInfoReaderFactory(DeviceInfoReader deviceInfoReader) {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    Mockito.when(deviceInfoReaderFactory.create()).thenReturn(deviceInfoReader);
    return deviceInfoReaderFactory;
  }

  @Test
  void applyEmptyTest() {
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.empty());
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);

    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    TgwIdentifierFunction tgwIdentifierFunction = new TgwIdentifierFunction(deviceInfoReaderFactory, mtMessageMetricReporter);

    Assertions.assertSame(Optional.empty(), tgwIdentifierFunction.apply(TestUtil.VPI));
    Mockito.verify(mtMessageMetricReporter).onUnidentifiedMtMessage();
  }

  @Test
  void applyExceptionTest() {
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(new RuntimeException("test"));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);

    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    TgwIdentifierFunction tgwIdentifierFunction = new TgwIdentifierFunction(deviceInfoReaderFactory, mtMessageMetricReporter);

    AssertThrows.exception(() -> tgwIdentifierFunction.apply(TestUtil.VPI), "test", RuntimeException.class);
    Mockito.verifyNoInteractions(mtMessageMetricReporter);
  }

  @Test
  void applyNullParameterTest() {
    DeviceInfoReaderFactory deviceInfoReaderFactory = Mockito.mock(DeviceInfoReaderFactory.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    TgwIdentifierFunction tgwIdentifierFunction = new TgwIdentifierFunction(deviceInfoReaderFactory, mtMessageMetricReporter);

    AssertThrows.illegalArgumentException(() -> tgwIdentifierFunction.apply(null), "vpi must not be null");
    Mockito.verifyNoInteractions(deviceInfoReaderFactory, mtMessageMetricReporter);
  }

  @Test
  void applyTest() {
    PersistedDeviceInfo persistedDeviceInfo = TestUtil.createPersistedDeviceInfo();
    DeviceInfoReader deviceInfoReader = mockDeviceInfoReader(Optional.of(persistedDeviceInfo));
    DeviceInfoReaderFactory deviceInfoReaderFactory = mockDeviceInfoReaderFactory(deviceInfoReader);

    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    TgwIdentifierFunction tgwIdentifierFunction = new TgwIdentifierFunction(deviceInfoReaderFactory, mtMessageMetricReporter);

    PersistedDeviceInfo returnedPersistedDeviceInfo = tgwIdentifierFunction.apply(TestUtil.VPI).get();
    Assertions.assertSame(persistedDeviceInfo, returnedPersistedDeviceInfo);
    Mockito.verifyNoInteractions(mtMessageMetricReporter);
  }
}
