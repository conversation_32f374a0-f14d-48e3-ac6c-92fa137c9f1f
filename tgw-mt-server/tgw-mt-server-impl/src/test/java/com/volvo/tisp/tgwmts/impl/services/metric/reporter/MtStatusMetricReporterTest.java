package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class MtStatusMetricReporterTest {
  private static final String CONVERSION_FAILURE = "CONVERSION-FAILURE";
  private static final String TYPE = "TYPE";

  @Test
  void onInvalidTgwMtStatusMessageTest() {
    MetricsReporterTestUtils.initReporterAndTest(MtStatusMetricReporter::new, (meterRegistry, mtRouterMessageMetricReporter) -> {
      mtRouterMessageMetricReporter.onInvalidMtStatus();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-status-received", Tags.of(TYPE, CONVERSION_FAILURE),
          1);

      mtRouterMessageMetricReporter.onInvalidMtStatus();
      MetricsReporterTestUtils.checkCounter(meterRegistry, "mt-status-received", Tags.of(TYPE, CONVERSION_FAILURE),
          2);
    });
  }
}