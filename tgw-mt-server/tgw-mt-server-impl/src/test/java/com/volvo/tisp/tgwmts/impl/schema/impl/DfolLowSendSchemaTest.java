package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DfolLowSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(7), DfolLowSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(10, DfolLowSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> DfolLowSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.DFOL_LOW, DfolLowSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(DfolLowSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(DfolLowSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepWait(DfolLowSendSchema.INSTANCE, 3, Duration.ofHours(168));

    Assertions.assertTrue(DfolLowSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(4)).isEmpty());
  }
}
