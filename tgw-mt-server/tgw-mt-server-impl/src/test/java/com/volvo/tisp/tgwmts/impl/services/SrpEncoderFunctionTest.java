package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Instant;
import java.time.ZoneId;
import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.SrpLevel;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.SrpEncoderMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.converter.SrpConverter;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v10.Srp10Wrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.Srp11Wrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.SrpHeaderWrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v12.Srp12Wrapper;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AdditionalAuthenticatedData;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.EncryptedPayloadWithoutMac;
import com.volvo.vc.crypto.symmetric.encryption.gcm.MessageAuthenticationCode;

class SrpEncoderFunctionTest {
  private static final SrpDestinationService ACTIVATION_SERVICE_ID = SrpDestinationService.ofInt(1);
  private static final EncryptedPayloadWithoutMac ENCRYPTED_PAYLOAD_WITHOUT_MAC = EncryptedPayloadWithoutMac.create(
      ImmutableByteArray.of(new byte[] {1, 2, 3}));
  private static final Instant INSTANT = Instant.ofEpochSecond(42);
  private static final MessageAuthenticationCode MESSAGE_AUTHENTICATION_CODE = MessageAuthenticationCode.create(ImmutableByteArray.of(new byte[16]));

  private static SrpOption createLegacySrpOption() {
    return TestUtil.createSrpOptionBuilder().setSrpLevel12(false).setSrpDestinationService(ACTIVATION_SERVICE_ID).build();
  }

  private static Consumer<EncodedActiveMtMessage> mockEncodedActiveMtMessageConsumer() {
    return Mockito.mock(Consumer.class);
  }

  private static EncryptionProcessor mockEncryptionProcessor() {
    EncryptionProcessor encryptionProcessor = Mockito.mock(EncryptionProcessor.class);
    Mockito.when(encryptionProcessor.encryptPayload(ArgumentMatchers.any(PersistedDeviceInfo.class), ArgumentMatchers.any(AdditionalAuthenticatedData.class),
            ArgumentMatchers.any(PlainTextPayload.class)))
        .thenReturn(AesGcmEncryptionResult.create(ENCRYPTED_PAYLOAD_WITHOUT_MAC, TestUtil.INITIALIZATION_VECTOR, MESSAGE_AUTHENTICATION_CODE));
    return encryptionProcessor;
  }

  private static void verifySrp10Wrapper(Srp10Wrapper srp10Wrapper, ObsAlias expectedObsAlias, SrpDestinationService expectedSrpDestinationService) {
    Assertions.assertEquals(expectedObsAlias, srp10Wrapper.getObsAlias());
    Assertions.assertEquals(expectedSrpDestinationService.toInt(), srp10Wrapper.getDestinationService().toInt());
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_VERSION.toShort(), srp10Wrapper.getDestinationVersion().toShort());
    Assertions.assertEquals(TestUtil.SRP_PAYLOAD.getImmutableByteArray(), srp10Wrapper.getPayload());
    Assertions.assertEquals(TestUtil.SRP_PRIORITY, srp10Wrapper.getPriority());
    Assertions.assertEquals(expectedSrpDestinationService.toInt(), srp10Wrapper.getSourceService().toInt());
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_VERSION.toShort(), srp10Wrapper.getSourceVersion().toShort());
    Assertions.assertEquals(INSTANT, srp10Wrapper.getVehicleTimestamp());
  }

  private static void verifySrp11Wrapper(Srp11Wrapper srp11Wrapper) {
    verifySrpHeaderWrapper(SrpConverter.decodeSrpHeaderWrapper(srp11Wrapper.getEncodedSrpHeader()).getRight());
    Assertions.assertEquals(ENCRYPTED_PAYLOAD_WITHOUT_MAC, srp11Wrapper.getEncryptedPayloadWithoutMac());
    Assertions.assertEquals(MESSAGE_AUTHENTICATION_CODE, srp11Wrapper.getMessageAuthenticationCode());
  }

  private static void verifySrp12Wrapper(Srp12Wrapper srp12Wrapper) {
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_VERSION.toShort(), srp12Wrapper.getDestinationVersion().toShort());
    Assertions.assertEquals(TestUtil.SRP_PAYLOAD.getImmutableByteArray(), srp12Wrapper.getPayload());
  }

  private static void verifySrpHeaderWrapper(SrpHeaderWrapper srpHeaderWrapper) {
    Assertions.assertEquals(TestUtil.OBS_ALIAS, srpHeaderWrapper.getObsAlias());
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_SERVICE.toInt(), srpHeaderWrapper.getDestinationService().toInt());
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_VERSION.toShort(), srpHeaderWrapper.getDestinationVersion().toShort());
    Assertions.assertEquals(TestUtil.SRP_PRIORITY, srpHeaderWrapper.getPriority());
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_SERVICE.toInt(), srpHeaderWrapper.getSourceService().toInt());
    Assertions.assertEquals(TestUtil.SRP_DESTINATION_VERSION.toShort(), srpHeaderWrapper.getSourceVersion().toShort());
    Assertions.assertEquals(INSTANT, srpHeaderWrapper.getVehicleTimestamp());
  }

  @Test
  void invalidCreateTest() {
    Clock clock = Mockito.mock(Clock.class);
    EncryptionProcessor encryptionProcessor = Mockito.mock(EncryptionProcessor.class);
    SrpEncoderMetricReporter srpEncoderMetricReporter = Mockito.mock(SrpEncoderMetricReporter.class);
    AssertThrows.illegalArgumentException(() -> SrpEncoderFunction.create(null, encryptionProcessor, TestUtil.SRP_PRIORITY, srpEncoderMetricReporter),
        "clock must not be null");
    AssertThrows.illegalArgumentException(
        () -> SrpEncoderFunction.create(clock, null, TestUtil.SRP_PRIORITY, srpEncoderMetricReporter), "encryptionProcessor must not be null");
    AssertThrows.illegalArgumentException(() -> SrpEncoderFunction.create(clock, encryptionProcessor, null, srpEncoderMetricReporter),
        "priority must not be null");
    AssertThrows.illegalArgumentException(() -> SrpEncoderFunction.create(clock, encryptionProcessor, TestUtil.SRP_PRIORITY, null),
        "srpEncoderMetricReporter must not be null");
  }

  @Test
  void legacyCommSetupTest() {
    Clock clock = Clock.fixed(INSTANT, ZoneId.systemDefault());
    Consumer<EncodedActiveMtMessage> encodedActiveMtMessageConsumer = mockEncodedActiveMtMessageConsumer();
    EncryptionProcessor encryptionProcessor = Mockito.mock(EncryptionProcessor.class);
    SrpEncoderMetricReporter srpEncoderMetricReporter = Mockito.mock(SrpEncoderMetricReporter.class);
    ScheduledActiveMtMessage scheduledActiveMtMessage = TestUtil.createScheduledActiveMtMessage(SrpLevel.SRP_10, createLegacySrpOption());
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> function = SrpEncoderFunction.create(clock, encryptionProcessor, TestUtil.SRP_PRIORITY,
        srpEncoderMetricReporter);

    EncodedActiveMtMessage encodedActiveMtMessage = function.apply(scheduledActiveMtMessage);

    Assertions.assertEquals(scheduledActiveMtMessage, encodedActiveMtMessage.scheduledActiveMtMessage());
    verifySrp10Wrapper(encodedActiveMtMessage.serviceRoutingPduWrapper().getEither3().getLeft().get(0), SrpEncoderFunction.DEFAULT_OBS_ALIAS,
        ACTIVATION_SERVICE_ID);

    Mockito.verify(srpEncoderMetricReporter).onMtSrp10Received();
    Mockito.verifyNoMoreInteractions(srpEncoderMetricReporter, encodedActiveMtMessageConsumer);
    Mockito.verifyNoInteractions(encryptionProcessor);
  }

  @Test
  void srp10Test() {
    Clock clock = Clock.fixed(INSTANT, ZoneId.systemDefault());
    EncryptionProcessor encryptionProcessor = Mockito.mock(EncryptionProcessor.class);
    SrpEncoderMetricReporter srpEncoderMetricReporter = Mockito.mock(SrpEncoderMetricReporter.class);
    ScheduledActiveMtMessage scheduledActiveMtMessage = TestUtil.createScheduledActiveMtMessage(SrpLevel.SRP_10,
        TestUtil.createSrpOptionBuilder().setSrpLevel12(false).build());
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> function = SrpEncoderFunction.create(clock, encryptionProcessor, TestUtil.SRP_PRIORITY,
        srpEncoderMetricReporter);

    EncodedActiveMtMessage encodedActiveMtMessage = function.apply(scheduledActiveMtMessage);

    Assertions.assertEquals(scheduledActiveMtMessage, encodedActiveMtMessage.scheduledActiveMtMessage());
    verifySrp10Wrapper(encodedActiveMtMessage.serviceRoutingPduWrapper().getEither3().getLeft().get(0), TestUtil.OBS_ALIAS,
        TestUtil.SRP_DESTINATION_SERVICE);

    Mockito.verify(srpEncoderMetricReporter).onMtSrp10Received();
    Mockito.verifyNoMoreInteractions(srpEncoderMetricReporter);
    Mockito.verifyNoInteractions(encryptionProcessor);
  }

  @Test
  void srp11Test() {
    Clock clock = Clock.fixed(INSTANT, ZoneId.systemDefault());
    EncryptionProcessor encryptionProcessor = mockEncryptionProcessor();
    SrpEncoderMetricReporter srpEncoderMetricReporter = Mockito.mock(SrpEncoderMetricReporter.class);
    ScheduledActiveMtMessage scheduledActiveMtMessage = TestUtil.createScheduledActiveMtMessage(SrpLevel.SRP_11,
        TestUtil.createSrpOptionBuilder().setSrpLevel12(false).build());
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> function = SrpEncoderFunction.create(clock, encryptionProcessor, TestUtil.SRP_PRIORITY,
        srpEncoderMetricReporter);

    EncodedActiveMtMessage encodedActiveMtMessage = function.apply(scheduledActiveMtMessage);

    Assertions.assertEquals(scheduledActiveMtMessage, encodedActiveMtMessage.scheduledActiveMtMessage());
    verifySrp11Wrapper(encodedActiveMtMessage.serviceRoutingPduWrapper().getEither3().getMiddle().get(0));

    Mockito.verify(encryptionProcessor)
        .encryptPayload(ArgumentMatchers.any(PersistedDeviceInfo.class), ArgumentMatchers.any(AdditionalAuthenticatedData.class),
            ArgumentMatchers.any(PlainTextPayload.class));
    Mockito.verify(srpEncoderMetricReporter).onMtSrp11Received();
    Mockito.verifyNoMoreInteractions(srpEncoderMetricReporter, encryptionProcessor);
  }

  @Test
  void srp12Test() {
    Clock clock = Clock.fixed(INSTANT, ZoneId.systemDefault());
    EncryptionProcessor encryptionProcessor = Mockito.mock(EncryptionProcessor.class);
    SrpEncoderMetricReporter srpEncoderMetricReporter = Mockito.mock(SrpEncoderMetricReporter.class);
    ScheduledActiveMtMessage scheduledActiveMtMessage = TestUtil.createScheduledActiveMtMessage(SrpLevel.SRP_12, TestUtil.createSrpOption());
    Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> function = SrpEncoderFunction.create(clock, encryptionProcessor, TestUtil.SRP_PRIORITY,
        srpEncoderMetricReporter);

    EncodedActiveMtMessage encodedActiveMtMessage = function.apply(scheduledActiveMtMessage);

    Assertions.assertEquals(scheduledActiveMtMessage, encodedActiveMtMessage.scheduledActiveMtMessage());
    verifySrp12Wrapper(encodedActiveMtMessage.serviceRoutingPduWrapper().getEither3().getRight());

    Mockito.verify(srpEncoderMetricReporter).onMtSrp12Received();
    Mockito.verifyNoMoreInteractions(srpEncoderMetricReporter);
    Mockito.verifyNoInteractions(encryptionProcessor);
  }
}
