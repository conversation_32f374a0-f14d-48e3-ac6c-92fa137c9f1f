package com.volvo.tisp.tgwmts.impl.conf;

import java.net.URI;
import java.time.Clock;
import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.web.reactive.function.client.WebClient;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;
import com.volvo.tisp.tgwmts.impl.clients.metrics.reporter.ConnectionEstablishedRegistrationReporter;
import com.volvo.tisp.tgwmts.impl.conf.properties.MoServerReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtRouterReactiveClientConfig;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtSoftCarReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.converters.MtStatusMessageOutputConverter;
import com.volvo.tisp.tgwmts.impl.db.queue.reporter.MtMessageQueueReporterTask;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.tgwmts.impl.scheduler.MtSchedulerMetricReporter;
import com.volvo.tisp.tgwmts.impl.scheduler.MtSchedulerRunnable;
import com.volvo.tisp.tgwmts.impl.scheduler.MtSchedulerWorker;
import com.volvo.tisp.tgwmts.impl.services.EncryptionProcessor;
import com.volvo.tisp.tgwmts.impl.services.MtMessageRetryHandler;
import com.volvo.tisp.tgwmts.impl.services.SendSchemaProcessor;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.SrpEncoderMetricReporter;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeperMetricReporter;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeperToggle;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.tgwmts.impl.utils.Throttler;

class AppConfigTest {

  @Test
  void connectionEstablishedRegistrationCacheTest() {
    Assertions.assertNotNull(new AppConfig().connectionEstablishedRegistrationCache(10, Duration.ofSeconds(1)));
  }

  @Test
  void createClockTest() {
    Assertions.assertNotNull(new AppConfig().createClock());
  }

  @Test
  void createConnectionEstablishedRegistrationReactiveClientTest() {
    MoServerReactiveClientProperties moServerReactiveClientProperties = new MoServerReactiveClientProperties(URI.create("http://mopers"),
        "/connectionEstablishedRegistrations", "/connectionEstablishedRegistration",
        Duration.ofSeconds(2), Duration.ofSeconds(2), true);

    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(TestUtil.INSTANT);
    ConnectionEstablishedCacheService connectionEstablishedCacheService = Mockito.mock(ConnectionEstablishedCacheService.class);

    WebClient.Builder webClientBuilder = Mockito.mock(WebClient.Builder.class);
    Mockito.when(webClientBuilder.baseUrl(moServerReactiveClientProperties.getBaseUrl().toString())).thenReturn(webClientBuilder);
    Mockito.when(webClientBuilder.clientConnector(ArgumentMatchers.any(ReactorClientHttpConnector.class))).thenReturn(webClientBuilder);
    Mockito.when(webClientBuilder.build()).thenReturn(Mockito.mock(WebClient.class));

    Assertions.assertNotNull(new AppConfig().createConnectionEstablishedRegistrationReactiveClient(clock, webClientBuilder,
        Mockito.mock(ConnectionEstablishedRegistrationReporter.class), connectionEstablishedCacheService, moServerReactiveClientProperties));
  }

  @Test
  void createEncodeServiceRoutingPduWrapperFunctionTest() {
    Assertions.assertNotNull(new AppConfig().createEncodeServiceRoutingPduWrapperFunction());
  }

  @Test
  void createEnqueueingTypeProcessorTest() {
    Assertions.assertNotNull(
        new AppConfig().createEnqueueingTypeProcessor(
            5,
            Mockito.mock(MtMessageMetricReporter.class),
            Mockito.mock(MtStatusPublisher.class)));
  }

  @Test
  void createInitializationVectorSupplierTest() {
    Assertions.assertNotNull(new AppConfig().createInitializationVectorSupplier());
  }

  @Test
  void createLoggingHelperTest() {
    Assertions.assertNotNull(new AppConfig().createLoggingHelper());
  }

  @Test
  void createMtDoorkeeperTest() {
    Assertions.assertNotNull(
        new AppConfig().createMtDoorkeeper(100, Mockito.mock(Clock.class), Mockito.mock(MtDoorkeeperMetricReporter.class),
            Mockito.mock(MtDoorkeeperToggle.class)));
  }

  @Test
  void createMtDoorkeeperToggleTest() {
    Assertions.assertNotNull(new AppConfig().createMtDoorkeeperToggle(Mockito.mock(MtDoorkeeperMetricReporter.class), true));
  }

  @Test
  void createMtMessageDefaultParametersTest() {
    Assertions.assertNotNull(new AppConfig().createMtMessageDefaultParameters(null, null, null));
  }

  @Test
  void createMtMessageInputConverterFunctionTest() {
    Assertions.assertNotNull(new AppConfig().createMtMessageInputConverterFunction(TestUtil.createMtMessageDefaultParameters()));
  }

  @Test
  void createMtMessageOutputConverterBiFunctionTest() {
    Assertions.assertNotNull(new AppConfig().createMtMessageOutputConverterBiFunction());
  }

  @Test
  void createMtMessageProtobufOutputConverterFunctionTest() {
    Assertions.assertNotNull(new AppConfig().createMtMessageProtobufOutputConverterFunction(TestUtil.mockFunction()));
  }

  @Test
  void createMtMessageQueueReporterTest() {
    Assertions.assertNotNull(new AppConfig().createMtMessageQueueReporter(Duration.ofSeconds(10), Mockito.mock(MtMessageQueueReporterTask.class)));
  }

  @Test
  void createMtRetrySchedulerServiceTest() {
    Assertions.assertNotNull(new AppConfig().createMtRetrySchedulerService(300, Mockito.mock(MtMessageRetryHandler.class)));
  }

  @Test
  void createMtRouterMessageOutputConverterFunctionTest() {
    Assertions.assertNotNull(new AppConfig().createMtRouterMessageOutputConverterFunction(TestUtil.mockFunction()));
  }

  @Test
  void createMtRouterMtStatusMessageInputConverterFunctionTest() {
    Assertions.assertNotNull(new AppConfig().createMtRouterMtStatusMessageInputConverterFunction());
  }

  @Test
  void createMtRouterReactiveClientTest() {
    MtRouterReactiveClientConfig mtRouterReactiveClientConfig = new MtRouterReactiveClientConfig(URI.create("http://localhost"), "/", Duration.ZERO);
    WebClient.Builder builder = Mockito.mock(WebClient.Builder.class);
    Mockito.when(builder.baseUrl(ArgumentMatchers.anyString())).thenReturn(builder);
    Mockito.when(builder.build()).thenReturn(Mockito.mock(WebClient.class));

    Assertions.assertNotNull(new AppConfig().createMtRouterReactiveClient(mtRouterReactiveClientConfig, builder));
  }

  @Test
  void createMtSchedulerContextTest() {
    Assertions.assertNotNull(new AppConfig().createMtSchedulerContext(4, Mockito.mock(MtSchedulerRunnable.class)));
  }

  @Test
  void createMtSchedulerRunnableTest() {
    Assertions.assertNotNull(new AppConfig().createMtSchedulerRunnable(Mockito.mock(MtSchedulerWorker.class), Mockito.mock(Throttler.class)));
  }

  @Test
  void createMtSchedulerWorkerTest() {
    Assertions.assertNotNull(new AppConfig().createMtSchedulerWorker(Mockito.mock(ActiveMtMessageWriterFactory.class), Mockito.mock(Clock.class),
        Mockito.mock(MtSchedulerMetricReporter.class), Mockito.mock(SendSchemaProcessor.class), 1));
  }

  @Test
  void createMtSoftcarRestClientTest() {
    MtSoftCarReactiveClientProperties mtSoftCarReactiveClientProperties = new MtSoftCarReactiveClientProperties(URI.create("http://localhost"), "/",
        Duration.ZERO);
    WebClient.Builder builder = Mockito.mock(WebClient.Builder.class);
    Mockito.when(builder.baseUrl(ArgumentMatchers.anyString())).thenReturn(builder);
    Mockito.when(builder.build()).thenReturn(Mockito.mock(WebClient.class));

    Assertions.assertNotNull(
        new AppConfig().createMtSoftcarRestClient(mtSoftCarReactiveClientProperties, builder, TestUtil.mockFunction()));
  }

  @Test
  void createMtStatusPublisherTest() {
    Assertions.assertNotNull(new AppConfig().createMtStatusPublisher(
        Mockito.mock(Clock.class),
        Mockito.mock(JmsTemplate.class),
        Mockito.mock(MtMessageMetricReporter.class),
        Mockito.mock(MtStatusMessageOutputConverter.class)
    ));
  }

  @Test
  void createSendSchemaCacheTest() {
    Assertions.assertNotNull(new AppConfig().createSendSchemaCache(42));
  }

  @Test
  void createSrpEncoderFunctionTest() {
    Assertions.assertNotNull(new AppConfig().createSrpEncoderFunction(Mockito.mock(Clock.class), Mockito.mock(EncryptionProcessor.class),
        Mockito.mock(SrpEncoderMetricReporter.class), 1));
  }

  @Test
  void createSymmetricAesGcmEncryptionServiceTest() {
    Assertions.assertNotNull(new AppConfig().createSymmetricAesGcmEncryptionService());
  }

  @Test
  void createThrottlerTest() {
    Assertions.assertNotNull(new AppConfig().createThrottler(Duration.ofSeconds(1), 1));
  }
}
