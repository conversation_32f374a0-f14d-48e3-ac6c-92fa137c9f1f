package com.volvo.tisp.tgwmts.impl.schema.dynamic.metric.reporter;

import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.impl.util.MetricsReporterTestUtils;

import io.micrometer.core.instrument.Tags;

class SendSchemaMetricReporterTest {
  private static final String ANOMALY = "ANOMALY";
  private static final String SEND_SCHEMA = "send-schema";

  @Test
  void onInvalidNameTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaMetricReporter::new, (meterRegistry, sendSchemaMetricReporter) -> {
      sendSchemaMetricReporter.onInvalidName();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA, Tags.of(ANOMALY, "INVALID-NAME"), 1);

      sendSchemaMetricReporter.onInvalidName();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA, Tags.of(ANOMALY, "INVALID-NAME"), 2);
    });
  }

  @Test
  void onTimeoutTooHighTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaMetricReporter::new, (meterRegistry, sendSchemaMetricReporter) -> {
      sendSchemaMetricReporter.onTimeoutTooHigh();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA, Tags.of(ANOMALY, "TIMEOUT-TOO-HIGH"), 1);

      sendSchemaMetricReporter.onTimeoutTooHigh();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA, Tags.of(ANOMALY, "TIMEOUT-TOO-HIGH"), 2);
    });
  }

  @Test
  void onTimeoutTooLowTest() {
    MetricsReporterTestUtils.initReporterAndTest(SendSchemaMetricReporter::new, (meterRegistry, sendSchemaMetricReporter) -> {
      sendSchemaMetricReporter.onTimeoutTooLow();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA, Tags.of(ANOMALY, "TIMEOUT-TOO-LOW"), 1);

      sendSchemaMetricReporter.onTimeoutTooLow();
      MetricsReporterTestUtils.checkCounter(meterRegistry, SEND_SCHEMA, Tags.of(ANOMALY, "TIMEOUT-TOO-LOW"), 2);
    });
  }
}
