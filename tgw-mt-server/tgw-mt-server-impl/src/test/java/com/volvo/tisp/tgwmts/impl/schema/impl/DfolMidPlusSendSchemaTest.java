package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DfolMidPlusSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofMinutes(5), DfolMidPlusSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(10, DfolMidPlusSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> DfolMidPlusSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.DFOL_MID_PLUS, DfolMidPlusSendSchema.INSTANCE.getSendSchemaName());
  }

  @Test
  void getSendSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(DfolMidPlusSendSchema.INSTANCE, 1, Duration.ofMinutes(2));
    VerificationUtil.verifySendSchemaStepUdp(DfolMidPlusSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepWait(DfolMidPlusSendSchema.INSTANCE, 3, Duration.ofMinutes(5));

    Assertions.assertTrue(DfolMidPlusSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(4)).isEmpty());
  }
}
