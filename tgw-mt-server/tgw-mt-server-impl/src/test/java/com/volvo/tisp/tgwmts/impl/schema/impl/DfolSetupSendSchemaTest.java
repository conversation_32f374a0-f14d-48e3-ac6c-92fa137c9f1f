package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.util.VerificationUtil;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;

class DfolSetupSendSchemaTest {
  @Test
  void getGlobalTimeoutTest() {
    Assertions.assertEquals(Duration.ofDays(14), DfolSetupSendSchema.INSTANCE.getGlobalTimeout());
  }

  @Test
  void getMaxRetryAttemptsTest() {
    Assertions.assertEquals(3, DfolSetupSendSchema.INSTANCE.getMaxRetryAttempts());
  }

  @Test
  void getSchemaStepInvalidParameterTest() {
    AssertThrows.illegalArgumentException(() -> DfolSetupSendSchema.INSTANCE.getSendSchemaStep(null), "sendSchemaStepId must not be null");
  }

  @Test
  void getSchemaStepTest() {
    VerificationUtil.verifySendSchemaStepWifi(DfolSetupSendSchema.INSTANCE, 1);
    VerificationUtil.verifySendSchemaStepUdp(DfolSetupSendSchema.INSTANCE, 2);
    VerificationUtil.verifySendSchemaStepSms(DfolSetupSendSchema.INSTANCE, 3);
    VerificationUtil.verifySendSchemaStepSat(DfolSetupSendSchema.INSTANCE, 4);
    VerificationUtil.verifySendSchemaStepSms(DfolSetupSendSchema.INSTANCE, 5);
    VerificationUtil.verifySendSchemaStepSms(DfolSetupSendSchema.INSTANCE, 6);
    VerificationUtil.verifySendSchemaStepWait(DfolSetupSendSchema.INSTANCE, 7, Duration.ofDays(14));

    Assertions.assertTrue(DfolSetupSendSchema.INSTANCE.getSendSchemaStep(SendSchemaStepId.ofInt(8)).isEmpty());
  }

  @Test
  void getSendSchemaNameTest() {
    Assertions.assertSame(SendSchemaName.DFOL_SETUP, DfolSetupSendSchema.INSTANCE.getSendSchemaName());
  }
}
