package com.volvo.tisp.tgwmts.impl.jms;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.tgwmts.impl.services.EnqueueingTypeProcessor;
import com.volvo.tisp.tgwmts.impl.services.MtPersister;
import com.volvo.tisp.tgwmts.impl.services.VehicleLockIdProvider;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeper;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.wirelesscar.componentbase.logging.Logging;

class ReceivedMtMessageConsumerTest {
  private static final ReceivedMtMessage RECEIVED_MT_MESSAGE = TestUtil.createReceivedMtMessage();
  private static final Set<Vpi> VPIS = Set.of(TestUtil.VPI);

  private static ActiveMtMessageWriterFactory mockActiveMtMessageWriterFactory(ActiveMtMessageWriter activeMtMessageWriter) {
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = Mockito.mock(ActiveMtMessageWriterFactory.class);
    Mockito.when(activeMtMessageWriterFactory.createReadCommitted()).thenReturn(activeMtMessageWriter);
    return activeMtMessageWriterFactory;
  }

  private static Clock mockClock() {
    Clock clock = Mockito.mock(Clock.class);
    Mockito.when(clock.instant()).thenReturn(Instant.ofEpochSecond(3), Instant.ofEpochSecond(5));
    return clock;
  }

  private static void mockEnqueueingTypeProcessor(EnqueueingTypeProcessor enqueueingTypeProcessor, ReceivedMtMessage receivedMtMessage,
      ActiveMtMessageWriter activeMtMessageWriter, boolean shouldIgnored, boolean shouldActivateMessageBeCreated) {
    Mockito.when(enqueueingTypeProcessor.shouldMessageBeIgnored(TestUtil.VEHICLE_LOCK_ID, receivedMtMessage, activeMtMessageWriter))
        .thenReturn(shouldIgnored);
    Mockito.when(enqueueingTypeProcessor.shouldActiveMtMessageBeCreated(TestUtil.VEHICLE_LOCK_ID, receivedMtMessage, activeMtMessageWriter))
        .thenReturn(shouldActivateMessageBeCreated);
  }

  private static Consumer<IntegrationLogParameter> mockLoggingHelper() {
    return Mockito.mock(Consumer.class);
  }

  private static void mockMtDoorkeeper(MtDoorkeeper mtDoorkeeper, ActiveMtMessageReader activeMtMessageReader, boolean shouldMessageBeDiscarded) {
    Mockito.when(mtDoorkeeper.shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageReader)).thenReturn(shouldMessageBeDiscarded);
  }

  private static MtMessagePersistStack mockMtMessagePersistStack(ReceivedMtMessage receivedMtMessage, ActiveMtMessageWriter activeMtMessageWriter) {
    MtMessagePersistStack mtMessagePersistStack = new MtMessagePersistStack(Mockito.mock(EnqueueingTypeProcessor.class), mockLoggingHelper(),
        Mockito.mock(MtDoorkeeper.class), Mockito.mock(MtPersister.class), TestUtil.mockFunction(), Mockito.mock(VehicleLockIdProvider.class));

    mockEnqueueingTypeProcessor(mtMessagePersistStack.getEnqueueingTypeProcessor(), receivedMtMessage, activeMtMessageWriter, false, true);
    mockMtDoorkeeper(mtMessagePersistStack.getMtDoorkeeper(), activeMtMessageWriter, false);
    mockTgwIdentifierFunction(mtMessagePersistStack.getTgwIdentifierFunction(), Optional.of(TestUtil.createPersistedDeviceInfo()));
    mockVehicleLockIdProvider(mtMessagePersistStack.getVehicleLockIdProvider(), activeMtMessageWriter, Either.left(TestUtil.VEHICLE_LOCK_ID));
    mockMtPersister(mtMessagePersistStack.getMtPersister(), receivedMtMessage, activeMtMessageWriter, Either.right(TestUtil.MT_MESSAGE_ID));

    return mtMessagePersistStack;
  }

  private static void mockMtPersister(MtPersister mtPersister, ReceivedMtMessage receivedMtMessage, ActiveMtMessageWriter activeMtMessageWriter,
      Either<RuntimeException, MtMessageId> either) {
    if (either.isLeft()) {
      Mockito.when(mtPersister.insertMtMessage(TestUtil.VEHICLE_LOCK_ID, receivedMtMessage, activeMtMessageWriter)).thenThrow(either.getLeft());
    } else {
      Mockito.when(mtPersister.insertMtMessage(TestUtil.VEHICLE_LOCK_ID, receivedMtMessage, activeMtMessageWriter)).thenReturn(either.getRight());
    }
  }

  private static void mockTgwIdentifierFunction(Function<Vpi, Optional<PersistedDeviceInfo>> function, Optional<PersistedDeviceInfo> optional) {
    Mockito.when(function.apply(TestUtil.VPI)).thenReturn(optional);
  }

  private static void mockVehicleLockIdProvider(VehicleLockIdProvider vehicleLockIdProvider, ActiveMtMessageWriter activeMtMessageWriter,
      Either<VehicleLockId, RuntimeException> vehicleLockIdEither) {
    Mockito.reset(vehicleLockIdProvider);

    if (vehicleLockIdEither.isLeft()) {
      Mockito.when(vehicleLockIdProvider.insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter))
          .thenReturn(List.of(vehicleLockIdEither.getLeft()));
    } else {
      Mockito.when(vehicleLockIdProvider.insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter))
          .thenThrow(vehicleLockIdEither.getRight());
    }
  }

  private static void verifyIntegrationLogging(Consumer<IntegrationLogParameter> loggingHelper) {
    ArgumentCaptor<IntegrationLogParameter> captor = ArgumentCaptor.forClass(IntegrationLogParameter.class);
    Mockito.verify(loggingHelper).accept(captor.capture());

    IntegrationLogParameter integrationLogParameter = captor.getValue();

    Assertions.assertEquals(Logging.Status.FAILED, integrationLogParameter.getStatus());
    Assertions.assertEquals("MT/discardMessageDueToTooManyMessagesPerVehicle", integrationLogParameter.getIntegrationMessage().toString());
  }

  private static void verifyMtMessageHasBeenPersisted(ActiveMtMessageWriter activeMtMessageWriter, ActiveMtMessageWriterFactory activeMtMessageWriterFactory,
      MtMessageMetricReporter mtMessageMetricReporter, MtStatusPublisher mtStatusPublisher, MtMessagePersistStack mtMessagePersistStack) {
    Mockito.verify(mtMessagePersistStack.getTgwIdentifierFunction()).apply(TestUtil.VPI);
    Mockito.verify(mtMessagePersistStack.getVehicleLockIdProvider()).insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getEnqueueingTypeProcessor())
        .shouldMessageBeIgnored(TestUtil.VEHICLE_LOCK_ID, RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    EnqueueingTypeProcessor enqueueingTypeProcessor = mtMessagePersistStack.getEnqueueingTypeProcessor();
    Mockito.verify(enqueueingTypeProcessor)
        .deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(enqueueingTypeProcessor).shouldActiveMtMessageBeCreated(TestUtil.VEHICLE_LOCK_ID, RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getMtDoorkeeper()).shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageWriter);
    MtPersister mtPersister = mtMessagePersistStack.getMtPersister();
    Mockito.verify(mtPersister).insertMtMessage(TestUtil.VEHICLE_LOCK_ID, RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).commitTransaction();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(mtMessageMetricReporter).onSuccess(Duration.ofSeconds(2));
    Mockito.verify(mtMessageMetricReporter).onEnqueueingType(EnqueueingType.NORMAL, TestUtil.SRP_DESTINATION_SERVICE);
    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, mtMessageMetricReporter, mtStatusPublisher);
  }

  private static void verifyNoMoreInteractions(MtMessagePersistStack mtMessagePersistStack) {
    Mockito.verifyNoMoreInteractions(mtMessagePersistStack.getEnqueueingTypeProcessor(), mtMessagePersistStack.getLoggingHelper(),
        mtMessagePersistStack.getMtDoorkeeper(), mtMessagePersistStack.getMtPersister(), mtMessagePersistStack.getTgwIdentifierFunction(),
        mtMessagePersistStack.getVehicleLockIdProvider());
  }

  @Test
  void activeMessageCreatedTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = mockClock();
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock, mtMessageMetricReporter,
        mtMessagePersistStack, mtStatusPublisher);
    receivedMtMessageConsumer.accept(RECEIVED_MT_MESSAGE);

    Mockito.verify(mtMessagePersistStack.getMtPersister()).insertActiveMtMessage(TestUtil.MT_MESSAGE_ID, activeMtMessageWriter);
    verifyMtMessageHasBeenPersisted(activeMtMessageWriter, activeMtMessageWriterFactory, mtMessageMetricReporter, mtStatusPublisher, mtMessagePersistStack);
  }

  @Test
  void activeMessageNotCreatedTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = mockClock();
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    mockEnqueueingTypeProcessor(mtMessagePersistStack.getEnqueueingTypeProcessor(), RECEIVED_MT_MESSAGE, activeMtMessageWriter, false, false);

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock, mtMessageMetricReporter,
        mtMessagePersistStack, mtStatusPublisher);
    receivedMtMessageConsumer.accept(RECEIVED_MT_MESSAGE);

    verifyMtMessageHasBeenPersisted(activeMtMessageWriter, activeMtMessageWriterFactory, mtMessageMetricReporter, mtStatusPublisher, mtMessagePersistStack);
  }

  @Test
  void discardedMtMessageTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    Clock clock = mockClock();
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    mockMtDoorkeeper(mtMessagePersistStack.getMtDoorkeeper(), activeMtMessageWriter, true);

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock, mtMessageMetricReporter,
        mtMessagePersistStack, mtStatusPublisher);
    receivedMtMessageConsumer.accept(RECEIVED_MT_MESSAGE);

    verifyIntegrationLogging(mtMessagePersistStack.getLoggingHelper());
    Mockito.verify(mtMessagePersistStack.getTgwIdentifierFunction()).apply(TestUtil.VPI);
    Mockito.verify(mtMessagePersistStack.getVehicleLockIdProvider()).insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getEnqueueingTypeProcessor())
        .shouldMessageBeIgnored(TestUtil.VEHICLE_LOCK_ID, RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getEnqueueingTypeProcessor())
        .deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getMtDoorkeeper()).shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageWriter);
    Mockito.verify(clock).instant();
    Mockito.verify(mtMessageMetricReporter).onDiscardedMtMessageWithReply();
    Mockito.verify(mtStatusPublisher)
        .publishMtStatus(MtStatus.FAILED, RECEIVED_MT_MESSAGE.getReplyOption().get(), TestUtil.VPI);
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).rollbackTransaction();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verify(mtMessageMetricReporter).onEnqueueingType(EnqueueingType.NORMAL, TestUtil.SRP_DESTINATION_SERVICE);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, clock, mtMessageMetricReporter, mtStatusPublisher);
  }

  @Test
  void discardedMtMessageWithoutReplyTest() {
    ReceivedMtMessage receivedMtMessage = TestUtil.createReceivedMtMessageWithoutReply();
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = mockClock();
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(receivedMtMessage, activeMtMessageWriter);
    mockMtDoorkeeper(mtMessagePersistStack.getMtDoorkeeper(), activeMtMessageWriter, true);

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock, mtMessageMetricReporter,
        mtMessagePersistStack, mtStatusPublisher);
    receivedMtMessageConsumer.accept(receivedMtMessage);

    verifyIntegrationLogging(mtMessagePersistStack.getLoggingHelper());
    Mockito.verify(mtMessagePersistStack.getTgwIdentifierFunction()).apply(TestUtil.VPI);
    Mockito.verify(mtMessagePersistStack.getVehicleLockIdProvider()).insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getEnqueueingTypeProcessor())
        .shouldMessageBeIgnored(TestUtil.VEHICLE_LOCK_ID, receivedMtMessage, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getEnqueueingTypeProcessor())
        .deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(receivedMtMessage, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getMtDoorkeeper()).shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageWriter);
    Mockito.verify(clock).instant();
    Mockito.verify(mtMessageMetricReporter).onDiscardedMtMessageWithoutReply();
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).rollbackTransaction();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verify(mtMessageMetricReporter).onEnqueueingType(EnqueueingType.NORMAL, TestUtil.SRP_DESTINATION_SERVICE);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, clock, mtMessageMetricReporter, mtStatusPublisher);
  }

  @Test
  void ignoredMtMessageTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = Mockito.mock(Clock.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    mockEnqueueingTypeProcessor(mtMessagePersistStack.getEnqueueingTypeProcessor(), RECEIVED_MT_MESSAGE, activeMtMessageWriter, true, false);

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock,
        mtMessageMetricReporter, mtMessagePersistStack, mtStatusPublisher);
    receivedMtMessageConsumer.accept(RECEIVED_MT_MESSAGE);

    Mockito.verify(mtMessagePersistStack.getTgwIdentifierFunction()).apply(TestUtil.VPI);
    Mockito.verify(mtMessagePersistStack.getVehicleLockIdProvider()).insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getEnqueueingTypeProcessor())
        .shouldMessageBeIgnored(TestUtil.VEHICLE_LOCK_ID, RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).rollbackTransaction();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(clock).instant();
    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verify(mtMessageMetricReporter).onEnqueueingType(EnqueueingType.NORMAL, TestUtil.SRP_DESTINATION_SERVICE);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, clock, mtMessageMetricReporter, mtStatusPublisher);
  }

  @Test
  void insertVehicleLockIfMissingAndAcquireVehicleLockIdExceptionTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = Mockito.mock(Clock.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    mockVehicleLockIdProvider(mtMessagePersistStack.getVehicleLockIdProvider(), activeMtMessageWriter, Either.right(new RuntimeException("test")));

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock, mtMessageMetricReporter,
        mtMessagePersistStack, mtStatusPublisher);
    AssertThrows.exception(() -> receivedMtMessageConsumer.accept(RECEIVED_MT_MESSAGE), "test", RuntimeException.class);

    Mockito.verify(mtMessagePersistStack.getTgwIdentifierFunction()).apply(TestUtil.VPI);
    Mockito.verify(mtMessagePersistStack.getVehicleLockIdProvider()).insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(clock).instant();
    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, clock, mtMessageMetricReporter, mtStatusPublisher);
  }

  @Test
  void mtMessagePersistExceptionTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = mockClock();
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    mockMtPersister(mtMessagePersistStack.getMtPersister(), RECEIVED_MT_MESSAGE, activeMtMessageWriter, Either.left(new RuntimeException("test")));

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock, mtMessageMetricReporter,
        mtMessagePersistStack, mtStatusPublisher);
    AssertThrows.exception(() -> receivedMtMessageConsumer.accept(RECEIVED_MT_MESSAGE), "test", RuntimeException.class);

    Mockito.verify(mtMessagePersistStack.getTgwIdentifierFunction()).apply(TestUtil.VPI);
    Mockito.verify(mtMessagePersistStack.getVehicleLockIdProvider()).insertVehicleLockIfMissingAndAcquireVehicleLockId(VPIS, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getEnqueueingTypeProcessor())
        .shouldMessageBeIgnored(TestUtil.VEHICLE_LOCK_ID, RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    EnqueueingTypeProcessor enqueueingTypeProcessor = mtMessagePersistStack.getEnqueueingTypeProcessor();
    Mockito.verify(enqueueingTypeProcessor)
        .deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(mtMessagePersistStack.getMtDoorkeeper()).shouldMessageBeDiscarded(TestUtil.VEHICLE_LOCK_ID, activeMtMessageWriter);
    MtPersister mtPersister = mtMessagePersistStack.getMtPersister();
    Mockito.verify(mtPersister).insertMtMessage(TestUtil.VEHICLE_LOCK_ID, RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    Mockito.verify(activeMtMessageWriter).startTransactionWithLockTimeout();
    Mockito.verify(activeMtMessageWriter).close();
    Mockito.verify(activeMtMessageWriterFactory).createReadCommitted();
    Mockito.verify(clock).instant();
    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verify(mtMessageMetricReporter).onEnqueueingType(EnqueueingType.NORMAL, TestUtil.SRP_DESTINATION_SERVICE);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, clock, mtMessageMetricReporter, mtStatusPublisher);
  }

  @Test
  void receiveMtMessageNullParameterTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = Mockito.mock(Clock.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock,
        mtMessageMetricReporter, mtMessagePersistStack, mtStatusPublisher);
    AssertThrows.illegalArgumentException(() -> receivedMtMessageConsumer.accept(null), "receivedMtMessage must not be null");

    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, clock, mtMessageMetricReporter, mtStatusPublisher);
  }

  @Test
  void unidentifiedMtMessageTest() {
    ActiveMtMessageWriter activeMtMessageWriter = Mockito.mock(ActiveMtMessageWriter.class);
    ActiveMtMessageWriterFactory activeMtMessageWriterFactory = mockActiveMtMessageWriterFactory(activeMtMessageWriter);
    Clock clock = Mockito.mock(Clock.class);
    MtMessageMetricReporter mtMessageMetricReporter = Mockito.mock(MtMessageMetricReporter.class);
    MtStatusPublisher mtStatusPublisher = Mockito.mock(MtStatusPublisher.class);
    MtMessagePersistStack mtMessagePersistStack = mockMtMessagePersistStack(RECEIVED_MT_MESSAGE, activeMtMessageWriter);
    mockTgwIdentifierFunction(mtMessagePersistStack.getTgwIdentifierFunction(), Optional.empty());

    ReceivedMtMessageConsumer receivedMtMessageConsumer = new ReceivedMtMessageConsumer(activeMtMessageWriterFactory, clock, mtMessageMetricReporter,
        mtMessagePersistStack, mtStatusPublisher);
    receivedMtMessageConsumer.accept(RECEIVED_MT_MESSAGE);

    Mockito.verify(mtMessagePersistStack.getTgwIdentifierFunction()).apply(TestUtil.VPI);
    verifyNoMoreInteractions(mtMessagePersistStack);
    Mockito.verify(clock).instant();
    Mockito.verifyNoMoreInteractions(activeMtMessageWriter, activeMtMessageWriterFactory, clock, mtMessageMetricReporter, mtStatusPublisher);
  }
}
