package com.volvo.tisp.tgwmts.impl.services;

import java.util.Optional;
import java.util.function.Supplier;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentMatcher;
import org.mockito.ArgumentMatchers;
import org.mockito.Mockito;

import com.volvo.tisp.tgw.device.info.database.model.AesKey;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.SrpLevel;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.EncryptionProcessorMetricReporter;
import com.volvo.tisp.tgwmts.impl.util.TestUtil;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.test.utils.lib.AssertThrows;
import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AdditionalAuthenticatedData;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.EncryptedPayloadWithoutMac;
import com.volvo.vc.crypto.symmetric.encryption.gcm.MessageAuthenticationCode;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionService;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;

class EncryptionProcessorTest {
  private static final AdditionalAuthenticatedData ADDITIONAL_AUTHENTICATED_DATA = AdditionalAuthenticatedData.create(ImmutableByteArray.of(new byte[15]));
  private static final AesGcmEncryptionResult AES_GCM_ENCRYPTION_RESULT = createAesGcmEncryptionResult();
  private static final PlainTextPayload PLAIN_TEXT_PAYLOAD = PlainTextPayload.create(ImmutableByteArray.of(new byte[72]));

  private static AesGcmEncryptionResult createAesGcmEncryptionResult() {
    EncryptedPayloadWithoutMac encryptedPayloadWithoutMac = EncryptedPayloadWithoutMac.create(ImmutableByteArray.of(new byte[5]));
    MessageAuthenticationCode messageAuthenticationCode = MessageAuthenticationCode.create(ImmutableByteArray.of(new byte[16]));

    return AesGcmEncryptionResult.create(encryptedPayloadWithoutMac, TestUtil.INITIALIZATION_VECTOR, messageAuthenticationCode);
  }

  private static ArgumentMatcher<SymmetricKey> createSymmetricKeyArgumentMatcher(AesKey aesKey) {
    return new ArgumentMatcher<>() {
      @Override
      public boolean matches(SymmetricKey symmetricKey) {
        return symmetricKey.getImmutableByteArray().equals(aesKey.getImmutableByteArray());
      }
    };
  }

  private static void encryptPayloadDeviceTest(PersistedDeviceInfo persistedDeviceInfo, AesKey aeskey) {
    EncryptionProcessorMetricReporter encryptionProcessorMetricReporter = Mockito.mock(EncryptionProcessorMetricReporter.class);
    SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService = mockSymmetricAesGcmEncryptionService(aeskey, Either.right(AES_GCM_ENCRYPTION_RESULT));
    Supplier<InitializationVector> initializationVectorSupplier = mockInitializationVectorSupplier();
    EncryptionProcessor encryptionProcessor = new EncryptionProcessor(encryptionProcessorMetricReporter, initializationVectorSupplier,
        symmetricAesGcmEncryptionService);

    AesGcmEncryptionResult returnedAesGcmEncryptionResult = encryptionProcessor.encryptPayload(persistedDeviceInfo, ADDITIONAL_AUTHENTICATED_DATA,
        PLAIN_TEXT_PAYLOAD);

    Assertions.assertSame(AES_GCM_ENCRYPTION_RESULT, returnedAesGcmEncryptionResult);
    Mockito.verify(encryptionProcessorMetricReporter).onEncryptSrp11Received();
    Mockito.verify(initializationVectorSupplier).get();
    Mockito.verify(symmetricAesGcmEncryptionService)
        .encryptWithGivenIv(ArgumentMatchers.any(), ArgumentMatchers.eq(PLAIN_TEXT_PAYLOAD), ArgumentMatchers.eq(TestUtil.INITIALIZATION_VECTOR),
            ArgumentMatchers.eq(ADDITIONAL_AUTHENTICATED_DATA));
    Mockito.verifyNoMoreInteractions(encryptionProcessorMetricReporter, symmetricAesGcmEncryptionService, initializationVectorSupplier);
  }

  private static Supplier<InitializationVector> mockInitializationVectorSupplier() {
    Supplier<InitializationVector> initializationVectorSupplier = Mockito.mock(Supplier.class);
    Mockito.when(initializationVectorSupplier.get()).thenReturn(TestUtil.INITIALIZATION_VECTOR);
    return initializationVectorSupplier;
  }

  private static SymmetricAesGcmEncryptionService mockSymmetricAesGcmEncryptionService(AesKey aesKey, Either<RuntimeException, AesGcmEncryptionResult> either) {
    SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService = Mockito.mock(SymmetricAesGcmEncryptionService.class);

    Mockito.when(symmetricAesGcmEncryptionService.encryptWithGivenIv(ArgumentMatchers.argThat(createSymmetricKeyArgumentMatcher(aesKey)),
        ArgumentMatchers.eq(PLAIN_TEXT_PAYLOAD),
        ArgumentMatchers.eq(TestUtil.INITIALIZATION_VECTOR),
        ArgumentMatchers.eq(ADDITIONAL_AUTHENTICATED_DATA))).thenReturn(either);

    return symmetricAesGcmEncryptionService;
  }

  @Test
  void encryptPayloadDeviceExceptionTest() {
    EncryptionProcessorMetricReporter encryptionProcessorMetricReporter = Mockito.mock(EncryptionProcessorMetricReporter.class);
    SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService = mockSymmetricAesGcmEncryptionService(TestUtil.CRYPTO_KEY_INFO_AES_KEY,
        Either.left(new RuntimeException("test")));
    Supplier<InitializationVector> initializationVectorSupplier = mockInitializationVectorSupplier();
    EncryptionProcessor encryptionProcessor = new EncryptionProcessor(encryptionProcessorMetricReporter, initializationVectorSupplier,
        symmetricAesGcmEncryptionService);

    AssertThrows.exception(
        () -> encryptionProcessor.encryptPayload(
            TestUtil.createPersistedDeviceInfo(Optional.of(TestUtil.createCryptoKeyInfo()), Optional.of(TestUtil.createPendingCryptoKeyInfo()),
                SrpLevel.SRP_10), ADDITIONAL_AUTHENTICATED_DATA, PLAIN_TEXT_PAYLOAD), "test", RuntimeException.class);

    Mockito.verify(encryptionProcessorMetricReporter).onEncryptSrp11Received();
    Mockito.verify(encryptionProcessorMetricReporter).onEncryptPayloadDeviceFail();
    Mockito.verify(initializationVectorSupplier).get();
    Mockito.verify(symmetricAesGcmEncryptionService)
        .encryptWithGivenIv(ArgumentMatchers.any(), ArgumentMatchers.eq(PLAIN_TEXT_PAYLOAD), ArgumentMatchers.eq(TestUtil.INITIALIZATION_VECTOR),
            ArgumentMatchers.eq(ADDITIONAL_AUTHENTICATED_DATA));
    Mockito.verifyNoMoreInteractions(encryptionProcessorMetricReporter, symmetricAesGcmEncryptionService, initializationVectorSupplier);
  }

  @Test
  void encryptPayloadDeviceWithCryptoKeyTest() {
    encryptPayloadDeviceTest(
        TestUtil.createPersistedDeviceInfo(Optional.of(TestUtil.createCryptoKeyInfo()), Optional.of(TestUtil.createPendingCryptoKeyInfo()), SrpLevel.SRP_10),
        TestUtil.CRYPTO_KEY_INFO_AES_KEY);
  }

  @Test
  void encryptPayloadDeviceWithPendingCryptoKeyTest() {
    encryptPayloadDeviceTest(TestUtil.createPersistedDeviceInfo(Optional.empty(), Optional.of(TestUtil.createPendingCryptoKeyInfo()), SrpLevel.SRP_10),
        TestUtil.PENDING_CRYPTO_KEY_INFO_AES_KEY);
  }

  @Test
  void encryptPayloadUnencryptedDeviceTest() {
    EncryptionProcessorMetricReporter encryptionProcessorMetricReporter = Mockito.mock(EncryptionProcessorMetricReporter.class);
    SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService = Mockito.mock(SymmetricAesGcmEncryptionService.class);
    Supplier<InitializationVector> initializationVectorSupplier = mockInitializationVectorSupplier();
    EncryptionProcessor encryptionProcessor = new EncryptionProcessor(encryptionProcessorMetricReporter, initializationVectorSupplier,
        symmetricAesGcmEncryptionService);

    AssertThrows.illegalStateException(
        () -> encryptionProcessor.encryptPayload(TestUtil.createPersistedDeviceInfo(), ADDITIONAL_AUTHENTICATED_DATA, PLAIN_TEXT_PAYLOAD),
        "device has neither current nor pending crypto key: cryptoKeyInfo=Optional.empty, pendingCryptoKeyInfo=Optional.empty, handle=12345, obsAlias=42, processingTags=Optional.empty, simInfo={ipv4Address=*******, ipv4Port=9052, mobileNetworkOperator=operator, msisdn=+3245678}, srpLevel=SRP_10, telematicUnitSubType=Optional.empty, vpi=12345678901234567890ABCDEFABCDEF, wtpVersion=VERSION_1, satEnabled=true, wifiEnabled=false");

    Mockito.verify(encryptionProcessorMetricReporter).onEncryptSrp11Received();
    Mockito.verifyNoMoreInteractions(encryptionProcessorMetricReporter, symmetricAesGcmEncryptionService, initializationVectorSupplier);
  }

  @Test
  void invalidParameterTest() {
    PersistedDeviceInfo persistedDeviceInfo = TestUtil.createPersistedDeviceInfo(Optional.of(TestUtil.createCryptoKeyInfo()),
        Optional.of(TestUtil.createPendingCryptoKeyInfo()), SrpLevel.SRP_10);
    EncryptionProcessorMetricReporter encryptionProcessorMetricReporter = Mockito.mock(EncryptionProcessorMetricReporter.class);
    SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService = Mockito.mock(SymmetricAesGcmEncryptionService.class);
    Supplier<InitializationVector> initializationVectorSupplier = mockInitializationVectorSupplier();
    EncryptionProcessor encryptionProcessor = new EncryptionProcessor(encryptionProcessorMetricReporter, initializationVectorSupplier,
        symmetricAesGcmEncryptionService);

    AssertThrows.illegalArgumentException(
        () -> encryptionProcessor.encryptPayload(null, ADDITIONAL_AUTHENTICATED_DATA, PLAIN_TEXT_PAYLOAD), "persistedDeviceInfo must not be null");
    AssertThrows.illegalArgumentException(
        () -> encryptionProcessor.encryptPayload(persistedDeviceInfo, null, PLAIN_TEXT_PAYLOAD), "additionalAuthenticatedData must not be null");
    AssertThrows.illegalArgumentException(
        () -> encryptionProcessor.encryptPayload(persistedDeviceInfo, ADDITIONAL_AUTHENTICATED_DATA, null), "plainTextPayload must not be null");

    Mockito.verifyNoMoreInteractions(encryptionProcessorMetricReporter, symmetricAesGcmEncryptionService, initializationVectorSupplier);
  }
}
