package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class RswdlLowSendSchema implements SendSchema {
  public static final SendSchema INSTANCE = new RswdlLowSendSchema();
  private static final Duration GLOBAL_TIMEOUT = Duration.ofDays(7);
  private static final SendSchemaName SEND_SCHEMA_NAME = SendSchemaName.RSWDL_LOW;

  private static final SendSchemaStep SEND_SCHEMA_STEP_1 = SendSchemaStep.forWifi(SendSchemaStepId.ofInt(1));
  private static final SendSchemaStep SEND_SCHEMA_STEP_2 = SendSchemaStep.forUdp(SendSchemaStepId.ofInt(2));
  private static final SendSchemaStep SEND_SCHEMA_STEP_3 = SendSchemaStep.forUdp(SendSchemaStepId.ofInt(3));
  private static final SendSchemaStep SEND_SCHEMA_STEP_4 = SendSchemaStep.forWait(SendSchemaStepId.ofInt(4), Duration.ofHours(1));
  private static final SendSchemaStep SEND_SCHEMA_STEP_5 = SendSchemaStep.forUdp(SendSchemaStepId.ofInt(5));
  private static final SendSchemaStep SEND_SCHEMA_STEP_6 = SendSchemaStep.forWait(SendSchemaStepId.ofInt(6), Duration.ofDays(7));

  private RswdlLowSendSchema() {
    // do nothing
  }

  @Override
  public Duration getGlobalTimeout() {
    return GLOBAL_TIMEOUT;
  }

  @Override
  public short getMaxRetryAttempts() {
    return 10;
  }

  @Override
  public SendSchemaName getSendSchemaName() {
    return SEND_SCHEMA_NAME;
  }

  @Override
  public Optional<SendSchemaStep> getSendSchemaStep(SendSchemaStepId sendSchemaStepId) {
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");

    return switch (sendSchemaStepId.toInt()) {
      case 1 -> Optional.of(SEND_SCHEMA_STEP_1);
      case 2 -> Optional.of(SEND_SCHEMA_STEP_2);
      case 3 -> Optional.of(SEND_SCHEMA_STEP_3);
      case 4 -> Optional.of(SEND_SCHEMA_STEP_4);
      case 5 -> Optional.of(SEND_SCHEMA_STEP_5);
      case 6 -> Optional.of(SEND_SCHEMA_STEP_6);
      default -> Optional.empty();
    };
  }
}