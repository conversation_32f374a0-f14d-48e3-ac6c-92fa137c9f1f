package com.volvo.tisp.tgwmts.impl.clients;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.connectivity.proto.MtMessage;
import com.volvo.tisp.tgwmts.impl.converters.MtRouterMessageOutputConverterFunction;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtRouterRestClientMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

@Component
public class MtRouterRestClient {
  private static final Logger logger = LoggerFactory.getLogger(MtRouterRestClient.class);

  private final MtRouterMessageOutputConverterFunction mtRouterMessageOutputConverterFunction;
  private final MtRouterReactiveClient mtRouterReactiveClient;
  private final MtRouterRestClientMetricReporter mtRouterRestClientMetricReporter;

  public MtRouterRestClient(MtRouterMessageOutputConverterFunction mtRouterMessageOutputConverterFunction,
      MtRouterRestClientMetricReporter mtRouterRestClientMetricReporter, MtRouterReactiveClient mtRouterReactiveClient) {
    this.mtRouterMessageOutputConverterFunction = mtRouterMessageOutputConverterFunction;
    this.mtRouterRestClientMetricReporter = mtRouterRestClientMetricReporter;
    this.mtRouterReactiveClient = mtRouterReactiveClient;
  }

  public void sendMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    Validate.notNull(encodedActiveMtMessage, "encodedActiveMtMessage");

    Either<RuntimeException, MtMessage> either = mtRouterMessageOutputConverterFunction.apply(encodedActiveMtMessage);

    if (either.isRight()) {
      MtMessage mtMessage = either.getRight();
      logger.debug("sending mtMessage to mt-router: {}", mtMessage);
      mtRouterReactiveClient.post(mtMessage).subscribe();
    } else {
      logger.warn("Error in message conversion", either.getLeft());
      mtRouterRestClientMetricReporter.onInvalidEncodedActiveMtMessage();
    }
  }
}
