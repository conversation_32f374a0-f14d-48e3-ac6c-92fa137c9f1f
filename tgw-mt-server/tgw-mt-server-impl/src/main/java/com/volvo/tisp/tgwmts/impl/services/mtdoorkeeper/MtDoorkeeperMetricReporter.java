package com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper;

import java.time.Duration;
import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

@Component
public class MtDoorkeeperMetricReporter {
  private final Timer mtDoorkeeperTimer;
  private final AtomicInteger mtDoorkeeperToggleGauge;

  public MtDoorkeeperMetricReporter(MeterRegistry meterRegistry) {
    mtDoorkeeperTimer = meterRegistry.timer("mt.doorkeeper");
    mtDoorkeeperToggleGauge = meterRegistry.gauge("mt.doorkeeper.toggle", new AtomicInteger(0));
  }

  public void onMtDoorkeeper(Duration duration) {
    Validate.notNegative(duration, "duration");

    mtDoorkeeperTimer.record(duration);
  }

  public void onMtDoorkeeperToggle(boolean value) {
    mtDoorkeeperToggleGauge.set(value ? 1 : 0);
  }
}
