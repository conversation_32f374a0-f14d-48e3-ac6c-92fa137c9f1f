package com.volvo.tisp.tgwmts.impl.services.statistics;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.springframework.stereotype.Component;

import com.google.common.collect.Queues;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtStatusStatisticsConfigProperties;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtStatusStatisticsMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.vcss.client.protobuf.MtStatusStatisticProtobuf.MtStatusStatistic;

@Component
public class MtStatusStatisticsHandler implements Runnable {
  private final BlockingQueue<MtStatusStatistic> mtStatusStatisticsBlockingQueue;
  private final MtStatusStatisticsConfigProperties mtStatusStatisticsConfigProperties;
  private final MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter;
  private final MtStatusStatisticsPublisher mtStatusStatisticsPublisher;

  public MtStatusStatisticsHandler(MtStatusStatisticsPublisher mtStatusStatisticsPublisher, MtStatusStatisticsConfigProperties mtStatusStatisticsConfigProperties,
      MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter) {
    this.mtStatusStatisticsPublisher = mtStatusStatisticsPublisher;
    this.mtStatusStatisticsBlockingQueue = new LinkedBlockingQueue<>(mtStatusStatisticsConfigProperties.maxQueueSize());
    this.mtStatusStatisticsConfigProperties = mtStatusStatisticsConfigProperties;
    this.mtStatusStatisticsMetricReporter = mtStatusStatisticsMetricReporter;
  }

  public void handle(MtStatusStatistic mtStatusStatistic) {
    Validate.notNull(mtStatusStatistic, "mtStatusStatistic");

    boolean offered = mtStatusStatisticsBlockingQueue.offer(mtStatusStatistic);
    if (!offered) {
      mtStatusStatisticsMetricReporter.onStatisticsHandleFailed();
    }
  }

  @Override
  public void run() {
    while (!Thread.currentThread().isInterrupted()) {
      TispContext.runInContext(this::publishMtStatusStatistics);
    }
  }

  private Collection<MtStatusStatistic> fetchDataToPublish() throws InterruptedException {
    List<MtStatusStatistic> dataToPublish = new ArrayList<>(mtStatusStatisticsConfigProperties.maxBatchSize());

    dataToPublish.add(mtStatusStatisticsBlockingQueue.take());
    Queues.drain(mtStatusStatisticsBlockingQueue, dataToPublish, mtStatusStatisticsConfigProperties.maxBatchSize() - 1,
        mtStatusStatisticsConfigProperties.maxWaitTimeForPublish());
    return dataToPublish;
  }

  @SuppressWarnings("FutureReturnValueIgnored")
  private void publishMtStatusStatistics() {
    try {
      mtStatusStatisticsPublisher.publish(fetchDataToPublish());
    } catch (InterruptedException e) {
      mtStatusStatisticsMetricReporter.onStatisticsPublishException();
    }
  }
}

