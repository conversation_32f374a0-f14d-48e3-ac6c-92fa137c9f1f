package com.volvo.tisp.tgwmts.impl.jms.publisher;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.jms.core.JmsTemplate;

import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.impl.converters.MtStatusMessageOutputConverter;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

public class MtStatusPublisher {
  private static final Logger logger = LoggerFactory.getLogger(MtStatusPublisher.class);

  private final Clock clock;
  private final ExecutorService executorService;
  private final JmsTemplate jmsTemplate;
  private final MtMessageMetricReporter mtMessageMetricReporter;
  private final MtStatusMessageOutputConverter mtStatusMessageOutputConverter;

  public MtStatusPublisher(
      Clock clock,
      ExecutorService executorService,
      JmsTemplate jmsTemplate,
      MtMessageMetricReporter mtMessageMetricReporter,
      MtStatusMessageOutputConverter mtStatusMessageOutputConverter) {
    this.clock = clock;
    this.executorService = executorService;
    this.jmsTemplate = jmsTemplate;
    this.mtMessageMetricReporter = mtMessageMetricReporter;
    this.mtStatusMessageOutputConverter = mtStatusMessageOutputConverter;
  }

  public void publishMtStatus(MtStatus mtStatus, ReplyOption replyOption, Vpi vpi) {
    Validate.notNull(mtStatus, "mtStatus");
    Validate.notNull(replyOption, "replyOption");
    Validate.notNull(vpi, "vpi");

    mtMessageMetricReporter.onMtStatus(mtStatus);
    publishAsync(mtStatusMessageOutputConverter.convert(mtStatus, replyOption, vpi), replyOption);
  }

  private Void onError(ReplyOption replyOption, String vpiString, Throwable throwable) {
    mtMessageMetricReporter.onMtStatusPublishError();
    logger.error("failed to publish message, correlationId: {}, replyTo: {}, vpi: {}",
        replyOption.getCorrelationId(), replyOption.getReplyTo(), vpiString, throwable);
    return null;
  }

  private void publish(ReplyOption replyOption, Supplier<Object> supplier) {
    Instant startTime = clock.instant();

    jmsTemplate.convertAndSend(replyOption.getReplyTo().toString(), supplier.get(), jmsMessage -> {
      jmsMessage.setJMSCorrelationID(replyOption.getCorrelationId().toString());

      jmsMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), MessageTypesJms.TCE_MTSTATUS_MESSAGE_TYPE);
      jmsMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), MessageTypesJms.VERSION_2_0);

      return jmsMessage;
    });

    mtMessageMetricReporter.onMtStatusPublishDuration(Duration.between(startTime, clock.instant()));
  }

  private void publishAsync(MtStatusMessage mtStatusMessage, ReplyOption replyOption) {
    CompletableFuture
        .runAsync(() -> publish(replyOption, () -> mtStatusMessage), executorService)
        .exceptionally(throwable -> onError(replyOption, mtStatusMessage.getVehiclePlatformId(), throwable));
  }
}
