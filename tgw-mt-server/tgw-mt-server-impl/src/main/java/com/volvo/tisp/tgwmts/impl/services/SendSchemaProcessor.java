package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Instant;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameterBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.model.IdentifiedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepFilter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class SendSchemaProcessor {
  private static final Logger logger = LoggerFactory.getLogger(SendSchemaProcessor.class);

  private final Clock clock;
  private final DeviceInfoReaderFactory deviceInfoReaderFactory;
  private final SendSchemaProcessorContext sendSchemaProcessorContext;

  public SendSchemaProcessor(Clock clock, DeviceInfoReaderFactory deviceInfoReaderFactory, SendSchemaProcessorContext sendSchemaProcessorContext) {
    this.clock = clock;
    this.deviceInfoReaderFactory = deviceInfoReaderFactory;
    this.sendSchemaProcessorContext = sendSchemaProcessorContext;
  }

  private static SendSchemaStepId createNextSendSchemaStepId(JoinedActiveMtMessage joinedActiveMtMessage) {
    return joinedActiveMtMessage.persistedActiveMtMessage().getActiveMtMessage().getSendSchemaStepId().plusOne();
  }

  private static TrackingIdentifier getTid(JoinedActiveMtMessage joinedActiveMtMessage) {
    return TrackingIdentifier.fromString(joinedActiveMtMessage.persistedMtMessage().getMtMessage().getTid().toString());
  }

  public Optional<UpdateActiveMtMessageParameter> executeNextSendSchemaStep(ActiveMtMessageWriter activeMtMessageWriter,
      JoinedActiveMtMessage joinedActiveMtMessage) {
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");
    Validate.notNull(joinedActiveMtMessage, "joinedActiveMtMessage");

    logger.debug("executing next send schema step: {}", joinedActiveMtMessage);

    try (final TispContext.Scope ignored = TispContext.current().newScope().tid(getTid(joinedActiveMtMessage)).activate()) {

      Vpi vpi = joinedActiveMtMessage.persistedVehicleLock().getVehicleLock().getVpi();
      Optional<PersistedDeviceInfo> persistedDeviceInfoOption = getPersistedDeviceInfo(vpi);

      if (persistedDeviceInfoOption.isEmpty()) {
        sendSchemaProcessorContext.getFinishedMtMessageHandler().onDeviceNotFound(activeMtMessageWriter, vpi);
        sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter().onDeviceNotFound();
        return Optional.empty();
      }

      SendSchemaName sendSchemaName = joinedActiveMtMessage.persistedMtMessage().getMtMessage().getSendSchemaName();
      PersistedDeviceInfo persistedDeviceInfo = persistedDeviceInfoOption.get();
      SendSchemaStepFilter sendSchemaStepFilter = sendSchemaProcessorContext.getSendSchemaStepFilterProducer()
          .createSendSchemaStepFilter(persistedDeviceInfo.getDeviceInfo());
      SendSchema sendSchema = sendSchemaProcessorContext.getFilterableSendSchemaFetcher().fetch(sendSchemaName, sendSchemaStepFilter);

      Instant globalTimeout = joinedActiveMtMessage.persistedMtMessage().getCreated().plus(sendSchema.getGlobalTimeout());

      if (clock.instant().isAfter(globalTimeout)) {
        sendSchemaProcessorContext.getFinishedMtMessageHandler().onTimeout(activeMtMessageWriter, joinedActiveMtMessage);
        sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter().onGlobalTimeout();
        return Optional.empty();
      }

      Optional<SendSchemaStep> sendSchemaStepOption = sendSchema.getSendSchemaStep(createNextSendSchemaStepId(joinedActiveMtMessage));

      if (sendSchemaStepOption.isEmpty()) {
        sendSchemaProcessorContext.getFinishedMtMessageHandler().onRejected(activeMtMessageWriter, joinedActiveMtMessage);
        sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter().onStepsExhausted();
        return Optional.empty();
      }

      return createAndSendScheduledActiveMtMessage(joinedActiveMtMessage, sendSchemaStepOption.get(), persistedDeviceInfo, globalTimeout);
    }
  }

  private Optional<UpdateActiveMtMessageParameter> createAndSendScheduledActiveMtMessage(
      JoinedActiveMtMessage joinedActiveMtMessage,
      SendSchemaStep sendSchemaStep,
      PersistedDeviceInfo persistedDeviceInfo,
      Instant globalTimeout) {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = new IdentifiedActiveMtMessage(joinedActiveMtMessage, persistedDeviceInfo);
    ScheduledActiveMtMessage scheduledActiveMtMessage = new ScheduledActiveMtMessage(identifiedActiveMtMessage, sendSchemaStep);
    sendScheduledActiveMtMessage(scheduledActiveMtMessage);
    return Optional.of(createUpdateActiveMtMessageParameter(joinedActiveMtMessage, sendSchemaStep, globalTimeout));
  }

  private UpdateActiveMtMessageParameter createUpdateActiveMtMessageParameter(
      JoinedActiveMtMessage joinedActiveMtMessage,
      SendSchemaStep sendSchemaStep,
      Instant globalTimeout) {
    PersistedActiveMtMessage persistedActiveMtMessage = joinedActiveMtMessage.persistedActiveMtMessage();

    Instant stepTimeout = clock.instant().plus(sendSchemaStep.getWaitDuration());

    return new UpdateActiveMtMessageParameterBuilder()
        .setActiveMtMessageId(persistedActiveMtMessage.getActiveMtMessageId())
        .setRetryAttempt(persistedActiveMtMessage.getActiveMtMessage().getRetryAttempt())
        .setSendSchemaStepId(sendSchemaStep.getSendSchemaStepId())
        .setTimeout(stepTimeout.isAfter(globalTimeout) ? globalTimeout : stepTimeout)
        .build();
  }

  private Optional<PersistedDeviceInfo> getPersistedDeviceInfo(Vpi vpi) {
    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      return deviceInfoReader.findDeviceInfoByVpi(vpi);
    }
  }

  private void sendScheduledActiveMtMessage(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    try {
      sendSchemaProcessorContext.getTransmissionRouter().processScheduledActiveMtMessage(scheduledActiveMtMessage);
      sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter().onSuccess();
    } catch (RuntimeException e) {
      logger.error("scheduledActiveMtMessage: {}", scheduledActiveMtMessage, e);
      sendSchemaProcessorContext.getSendSchemaProcessorMetricReporter().onFailure();
    }
  }
}
