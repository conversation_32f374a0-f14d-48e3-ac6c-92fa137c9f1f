package com.volvo.tisp.tgwmts.impl.model;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum MtStatusClient {
  SMS_GATE("smsgate"),
  SAT_GW("satgw"),
  TUCS("tucs"),
  VWTP_INIT("vwtpinit"),
  UNKNOWN("unknown");

  private static final Map<String, MtStatusClient> STRING_TO_ENUM_MAP = Stream.of(values()).collect(Collectors.toMap(Objects::toString, e -> e));

  private final String string;

  MtStatusClient(String string) {
    this.string = string;
  }

  public static Optional<MtStatusClient> fromString(String string) {
    return Optional.ofNullable(STRING_TO_ENUM_MAP.get(string));
  }

  @Override
  public String toString() {
    return string;
  }
}
