package com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtDoorkeeper {
  public static final int DEFAULT_THRESHOLD = 100;
  public static final boolean DEFAULT_TOGGLE_VALUE = true;

  private final Clock clock;
  private final MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter;
  private final MtDoorkeeperToggle mtDoorkeeperToggle;
  private final int threshold;

  public MtDoorkeeper(Clock clock, MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter, MtDoorkeeperToggle mtDoorkeeperToggle, int threshold) {
    Validate.notNull(clock, "clock");
    Validate.notNull(mtDoorkeeperMetricReporter, "mtDoorkeeperMetricReporter");
    Validate.notNull(mtDoorkeeperToggle, "mtDoorkeeperToggle");
    Validate.isPositive(threshold, "threshold");

    this.clock = clock;
    this.mtDoorkeeperMetricReporter = mtDoorkeeperMetricReporter;
    this.mtDoorkeeperToggle = mtDoorkeeperToggle;
    this.threshold = threshold;
  }

  public boolean shouldMessageBeDiscarded(VehicleLockId vehicleLockId, ActiveMtMessageReader activeMtMessageReader) {
    Validate.notNull(vehicleLockId, "vehicleLockId");
    Validate.notNull(activeMtMessageReader, "activeMtMessageReader");

    if (mtDoorkeeperToggle.isEnabled()) {
      Instant startTime = clock.instant();

      boolean shouldMessageBeDiscarded = activeMtMessageReader.countMtMessagesByVehicleLockId(vehicleLockId) >= threshold;

      mtDoorkeeperMetricReporter.onMtDoorkeeper(Duration.between(startTime, clock.instant()));
      return shouldMessageBeDiscarded;
    }

    return false;
  }
}
