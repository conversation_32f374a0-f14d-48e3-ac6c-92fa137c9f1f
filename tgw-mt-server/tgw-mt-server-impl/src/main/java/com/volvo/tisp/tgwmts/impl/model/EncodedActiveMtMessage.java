package com.volvo.tisp.tgwmts.impl.model;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;

public record EncodedActiveMtMessage(ScheduledActiveMtMessage scheduledActiveMtMessage, ServiceRoutingPduWrapper serviceRoutingPduWrapper) {
  public EncodedActiveMtMessage {
    Validate.notNull(scheduledActiveMtMessage, "scheduledActiveMtMessage");
    Validate.notNull(serviceRoutingPduWrapper, "serviceRoutingPduWrapper");

  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    EncodedActiveMtMessage other = (EncodedActiveMtMessage) object;
    if (!scheduledActiveMtMessage.equals(other.scheduledActiveMtMessage)) {
      return false;
    }

    return serviceRoutingPduWrapper.equals(other.serviceRoutingPduWrapper);
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("scheduledActiveMtMessage={")
        .append(scheduledActiveMtMessage)
        .append("}, serviceRoutingPduWrapper={")
        .append(serviceRoutingPduWrapper)
        .append("}")
        .toString();
  }
}
