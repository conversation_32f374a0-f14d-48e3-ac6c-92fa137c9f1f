package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.net.URI;
import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import software.amazon.awssdk.utils.Validate;

@Component
public class MoServerReactiveClientProperties {
  private final URI baseUrl;
  private final String connectionEstablishedRegistrationsMultiRequestPath;
  private final String connectionEstablishedRegistrationsSingleRequestPath;
  private final Duration connectionTimeout;
  private final boolean enableConnectionEstablishedRegistrationFailurePersist;
  private final Duration requestTimeout;

  public MoServerReactiveClientProperties(
      @Value("${moserver.base-url:http://tgwmos}") URI baseUrl,
      @Value("${moserver.connection-established.registration.multi-request-path:/api/v1/connectionEstablishedRegistration/multiple}")
      String connectionEstablishedRegistrationsMultiRequestPath,
      @Value("${moserver.connection-established.registration.single-request-path:/api/v1/connectionEstablishedRegistration}")
      String connectionEstablishedRegistrationsSingleRequestPath,
      @Value("${moserver.connection-timeout:5s}") Duration connectionTimeout,
      @Value("${moserver.request-timeout:5s}") Duration requestTimeout,
      @Value("${connection-established.registration.failure.persist.enabled:true}") boolean enableConnectionEstablishedRegistrationFailurePersist) {
    Validate.isNotNegative(connectionTimeout, "connectionTimeout");
    Validate.isNotNegative(requestTimeout, "requestTimeout");

    this.connectionEstablishedRegistrationsMultiRequestPath = connectionEstablishedRegistrationsMultiRequestPath;
    this.connectionEstablishedRegistrationsSingleRequestPath = connectionEstablishedRegistrationsSingleRequestPath;
    this.baseUrl = baseUrl;
    this.connectionTimeout = connectionTimeout;
    this.requestTimeout = requestTimeout;
    this.enableConnectionEstablishedRegistrationFailurePersist = enableConnectionEstablishedRegistrationFailurePersist;
  }

  public URI getBaseUrl() {
    return baseUrl;
  }

  public String getConnectionEstablishedRegistrationsMultiRequestPath() {
    return connectionEstablishedRegistrationsMultiRequestPath;
  }

  public String getConnectionEstablishedRegistrationsSingleRequestPath() {
    return connectionEstablishedRegistrationsSingleRequestPath;
  }

  public Duration getConnectionTimeout() {
    return connectionTimeout;
  }

  public Duration getRequestTimeout() {
    return requestTimeout;
  }

  public boolean isEnableConnectionEstablishedRegistrationFailurePersist() {
    return enableConnectionEstablishedRegistrationFailurePersist;
  }
}
