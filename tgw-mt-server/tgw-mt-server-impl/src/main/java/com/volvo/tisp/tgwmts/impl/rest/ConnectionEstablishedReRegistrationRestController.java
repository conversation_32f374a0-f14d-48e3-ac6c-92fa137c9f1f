package com.volvo.tisp.tgwmts.impl.rest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.tgwmts.impl.services.ConnectionEstablishedRegistrationRetryService;

@RestController
@RequestMapping(path = ConnectionEstablishedReRegistrationRestController.REQUEST_MAPPING_PATH)
@Authentication(required = false)
public class ConnectionEstablishedReRegistrationRestController {
  public static final String REQUEST_MAPPING_PATH = "/api/v1/connection-established-registration";
  private static final Logger logger = LoggerFactory.getLogger(ConnectionEstablishedReRegistrationRestController.class);
  private final ConnectionEstablishedRegistrationRetryService connectionEstablishedRegistrationRetryService;

  public ConnectionEstablishedReRegistrationRestController(ConnectionEstablishedRegistrationRetryService connectionEstablishedRegistrationRetryService) {
    this.connectionEstablishedRegistrationRetryService = connectionEstablishedRegistrationRetryService;
  }

  @PostMapping()
  public ResponseEntity<String> attemptResend() {
    try {
      connectionEstablishedRegistrationRetryService.resendConnectionEstablishedRegistrations();
    }  catch (RuntimeException e) {
      logger.error("", e);
      return ResponseEntity.internalServerError().body("Failed to resend connection established registrations");
    }

    return ResponseEntity.ok("Attempting to resend connection established registrations");
  }
}
