package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameterBuilder;
import com.volvo.tisp.tgwmts.impl.conf.properties.ThrottledStatusConfiguration;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class ThrottledActiveMtMessageParameterProvider {
  private final Clock clock;
  private final ThrottledStatusConfiguration throttledStatusConfiguration;

  public ThrottledActiveMtMessageParameterProvider(Clock clock, ThrottledStatusConfiguration throttledStatusConfiguration) {
    this.clock = clock;
    this.throttledStatusConfiguration = throttledStatusConfiguration;
  }

  private static SendSchemaStepId decrement(int sendSchemaStepIdInt) {
    return SendSchemaStepId.ofInt(Math.max(0, sendSchemaStepIdInt - 1));
  }

  public UpdateActiveMtMessageParameter get(PersistedActiveMtMessage persistedActiveMtMessage) {
    Validate.notNull(persistedActiveMtMessage, "persistedActiveMtMessage");

    return new UpdateActiveMtMessageParameterBuilder()
        .setActiveMtMessageId(persistedActiveMtMessage.getActiveMtMessageId())
        .setRetryAttempt(persistedActiveMtMessage.getActiveMtMessage().getRetryAttempt())
        .setSendSchemaStepId(decrement(persistedActiveMtMessage.getActiveMtMessage().getSendSchemaStepId().toInt()))
        .setTimeout(clock.instant().plus(throttledStatusConfiguration.getWaitTimeout()))
        .build();
  }
}
