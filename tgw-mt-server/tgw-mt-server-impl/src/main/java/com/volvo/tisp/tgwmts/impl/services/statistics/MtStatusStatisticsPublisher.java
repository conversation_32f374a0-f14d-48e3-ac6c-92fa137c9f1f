package com.volvo.tisp.tgwmts.impl.services.statistics;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.time.Duration;
import java.time.Instant;
import java.util.Collection;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import java.util.concurrent.ScheduledExecutorService;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtStatusStatisticsMetricReporter;
import com.volvo.tisp.vc.vcss.client.protobuf.MtStatusStatisticProtobuf.MtStatusStatistic;

import io.github.resilience4j.timelimiter.TimeLimiter;

public class MtStatusStatisticsPublisher {
  private static final Logger logger = LoggerFactory.getLogger(MtStatusStatisticsPublisher.class);

  private final MessagePublisher<ByteBuffer> messagePublisher;
  private final MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter;
  private final ScheduledExecutorService scheduledExecutorService;
  private final TimeLimiter timeLimiter;

  public MtStatusStatisticsPublisher(MessagePublisher<ByteBuffer> messagePublisher, MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter,
      TimeLimiter timeLimiter, ScheduledExecutorService scheduledExecutorService) {
    this.messagePublisher = messagePublisher;
    this.mtStatusStatisticsMetricReporter = mtStatusStatisticsMetricReporter;
    this.timeLimiter = timeLimiter;
    this.scheduledExecutorService = scheduledExecutorService;
  }

  public CompletableFuture<Void> publish(Collection<MtStatusStatistic> mtStatusStatistics) {
    return timeLimiter.decorateCompletionStage(scheduledExecutorService, (Supplier<CompletionStage<Void>>) () -> this.doPublish(mtStatusStatistics))
        .get()
        .toCompletableFuture();
  }

  private byte[] convertToBytes(Collection<MtStatusStatistic> mtStatusStatistics) throws IOException {
    try (ByteArrayOutputStream outputStream = new ByteArrayOutputStream()) {
      for (MtStatusStatistic mtStatusStatistic : mtStatusStatistics) {
        mtStatusStatistic.writeDelimitedTo(outputStream);
      }
      return outputStream.toByteArray();
    }
  }

  private CompletableFuture<Void> doPublish(Collection<MtStatusStatistic> mtStatusStatistics) {
    if (mtStatusStatistics.isEmpty()) {
      return CompletableFuture.completedFuture(null);
    }
    Instant beforePublish = Instant.now();

    try {
      return messagePublisher.newMessage()
          .publish(ByteBuffer.wrap(convertToBytes(mtStatusStatistics)))
          .thenAccept(numberOfSubscribers -> reportPublishResult(beforePublish, numberOfSubscribers))
          .exceptionally(this::reportPublishError);
    } catch (IOException e) {
      this.reportPublishError(e);
      return CompletableFuture.failedFuture(e);
    }
  }

  private Void reportPublishError(Throwable throwable) {
    mtStatusStatisticsMetricReporter.onStatisticsPublishException();
    logger.debug("Exception: In Mt Status Statistics", throwable);
    return null;
  }

  private void reportPublishResult(Instant beforePublish, int numberOfSubscribers) {
    if (numberOfSubscribers != 0) {
      Duration publishDuration = Duration.between(beforePublish, Instant.now());
      mtStatusStatisticsMetricReporter.onStatisticsPublishSuccess(publishDuration);
      logger.trace("publishDuration: {}, numberOfSubscribers: {}", publishDuration, numberOfSubscribers);
    } else {
      mtStatusStatisticsMetricReporter.onSubscriptionMissing();
      logger.debug("No subscribers for mt status statistics");
    }
  }
}
