package com.volvo.tisp.tgwmts.impl.services;

import java.util.Optional;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class TgwIdentifierFunction implements Function<Vpi, Optional<PersistedDeviceInfo>> {
  private static final Logger logger = LoggerFactory.getLogger(TgwIdentifierFunction.class);

  private final DeviceInfoReaderFactory deviceInfoReaderFactory;
  private final MtMessageMetricReporter mtMessageMetricReporter;

  public TgwIdentifierFunction(DeviceInfoReaderFactory deviceInfoReaderFactory, MtMessageMetricReporter mtMessageMetricReporter) {
    this.deviceInfoReaderFactory = deviceInfoReaderFactory;
    this.mtMessageMetricReporter = mtMessageMetricReporter;
  }

  @Override
  public Optional<PersistedDeviceInfo> apply(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      Optional<PersistedDeviceInfo> persistedDeviceInfoOptional = deviceInfoReader.findDeviceInfoByVpi(vpi);

      if (persistedDeviceInfoOptional.isEmpty()) {
        logger.debug("No device found for vpi: {}", vpi);
        mtMessageMetricReporter.onUnidentifiedMtMessage();
      }
      return persistedDeviceInfoOptional;
    }
  }
}
