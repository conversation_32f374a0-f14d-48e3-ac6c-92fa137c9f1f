package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

@Component
public class MtStatusStatisticsMetricReporter {
  static final String METRICS_FAILURE = "mt-status-statistics.failure";
  static final String METRICS_TIME_TO_PUBLISH_VEHICLE_STATS_SUCCESS = "mt-status-statistics.up.published.success.time";
  static final String STATUS = "status";
  private final Counter failureHandlingStatsCounter;
  private final Counter failurePublishVehicleStatsExceptionCounter;
  private final Counter failureSubscriptionMissingCounter;
  private final Timer timeToPublishVehicleStatsSuccessTimer;

  public MtStatusStatisticsMetricReporter(MeterRegistry meterRegistry) {
    Validate.notNull(meterRegistry, "meterRegistry");

    failurePublishVehicleStatsExceptionCounter = meterRegistry.counter(METRICS_FAILURE, STATUS, "published-exception");
    failureSubscriptionMissingCounter = meterRegistry.counter(METRICS_FAILURE, STATUS, "subscription-missing");
    timeToPublishVehicleStatsSuccessTimer = meterRegistry.timer(METRICS_TIME_TO_PUBLISH_VEHICLE_STATS_SUCCESS);
    failureHandlingStatsCounter = meterRegistry.counter(METRICS_FAILURE, STATUS, "handling-failed");
  }

  public void onStatisticsHandleFailed() {
    failureHandlingStatsCounter.increment();
  }

  public void onStatisticsPublishException() {
    failurePublishVehicleStatsExceptionCounter.increment();
  }

  public void onStatisticsPublishSuccess(Duration publishDuration) {
    Validate.notNegative(publishDuration, "publishDuration");

    timeToPublishVehicleStatsSuccessTimer.record(publishDuration);
  }

  public void onSubscriptionMissing() {
    failureSubscriptionMissingCounter.increment();
  }
}
