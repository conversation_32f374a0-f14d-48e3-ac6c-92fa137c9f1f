package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class DfolHighSendSchema implements SendSchema {
  public static final SendSchema INSTANCE = new DfolHighSendSchema();
  private static final Duration GLOBAL_TIMEOUT = Duration.ofDays(7);
  private static final SendSchemaName SEND_SCHEMA_NAME = SendSchemaName.DFOL_HIGH;

  private static final SendSchemaStep SEND_SCHEMA_STEP_1 = SendSchemaStep.forWifi(SendSchemaStepId.ofInt(1));
  private static final SendSchemaStep SEND_SCHEMA_STEP_2 = SendSchemaStep.forUdp(SendSchemaStepId.ofInt(2));
  private static final SendSchemaStep SEND_SCHEMA_STEP_3 = SendSchemaStep.forSms(SendSchemaStepId.ofInt(3));
  private static final SendSchemaStep SEND_SCHEMA_STEP_4 = SendSchemaStep.forSat(SendSchemaStepId.ofInt(4));
  private static final SendSchemaStep SEND_SCHEMA_STEP_5 = SendSchemaStep.forWait(SendSchemaStepId.ofInt(5), Duration.ofDays(7));

  private DfolHighSendSchema() {
    // do nothing
  }

  @Override
  public Duration getGlobalTimeout() {
    return GLOBAL_TIMEOUT;
  }

  @Override
  public short getMaxRetryAttempts() {
    return 5;
  }

  @Override
  public SendSchemaName getSendSchemaName() {
    return SEND_SCHEMA_NAME;
  }

  @Override
  public Optional<SendSchemaStep> getSendSchemaStep(SendSchemaStepId sendSchemaStepId) {
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");

    return switch (sendSchemaStepId.toInt()) {
      case 1 -> Optional.of(SEND_SCHEMA_STEP_1);
      case 2 -> Optional.of(SEND_SCHEMA_STEP_2);
      case 3 -> Optional.of(SEND_SCHEMA_STEP_3);
      case 4 -> Optional.of(SEND_SCHEMA_STEP_4);
      case 5 -> Optional.of(SEND_SCHEMA_STEP_5);
      default -> Optional.empty();
    };
  }
}
