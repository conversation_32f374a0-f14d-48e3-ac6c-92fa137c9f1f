package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class SqsQueueProperties {
  private final Optional<String> endpoint;
  private final String region;

  public SqsQueueProperties(@Value("${sqs.region}") String region,
      @Value("${sqs.endpoint:}") String endpoint) {
    Validate.notEmpty(region, "region");

    this.region = region;
    this.endpoint = Optional.ofNullable(endpoint).filter(StringUtils::isNotBlank);
  }

  public Optional<String> getEndpoint() {
    return endpoint;
  }

  public String getRegion() {
    return region;
  }
}
