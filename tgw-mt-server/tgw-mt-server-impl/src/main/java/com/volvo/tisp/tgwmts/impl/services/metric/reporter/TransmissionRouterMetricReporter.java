package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class TransmissionRouterMetricReporter {
  private static final String TRANSMISSION_ROUTER = "transmission.router";
  private static final String TYPE = "TYPE";

  private final Counter satCounter;
  private final Counter smsCounter;
  private final Counter softcarCounter;
  private final Counter udpCounter;
  private final Counter unimplementedCounter;
  private final Counter waitCounter;

  public TransmissionRouterMetricReporter(MeterRegistry meterRegistry) {
    satCounter = meterRegistry.counter(TRANSMISSION_ROUTER, TYPE, "SAT");
    smsCounter = meterRegistry.counter(TRANSMISSION_ROUTER, TYPE, "SMS");
    softcarCounter = meterRegistry.counter(TRANSMISSION_ROUTER, TYPE, "SOFTCAR");
    udpCounter = meterRegistry.counter(TRANSMISSION_ROUTER, TYPE, "UDP");
    unimplementedCounter = meterRegistry.counter(TRANSMISSION_ROUTER, TYPE, "UNIMPLEMENTED");
    waitCounter = meterRegistry.counter(TRANSMISSION_ROUTER, TYPE, "WAIT");
  }

  public void onRoute(SendSchemaStepType sendSchemaStepType) {
    switch (sendSchemaStepType) {
      case SMS -> onSms();
      case UDP -> onUdp();
      case SAT -> onSat();
      case WAIT -> onWait();
    }
  }

  public void onSat() {
    satCounter.increment();
  }

  public void onSms() {
    smsCounter.increment();
  }

  public void onSoftcar() {
    softcarCounter.increment();
  }

  public void onUdp() {
    udpCounter.increment();
  }

  public void onUnimplementedRoute() {
    unimplementedCounter.increment();
  }

  public void onWait() {
    waitCounter.increment();
  }
}
