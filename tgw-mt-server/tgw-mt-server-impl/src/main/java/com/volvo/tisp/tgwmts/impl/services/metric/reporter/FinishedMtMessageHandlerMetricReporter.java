package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class FinishedMtMessageHandlerMetricReporter {
  private static final String TYPE = "TYPE";

  private final Counter failedActiveMtMessageInsertionCounter;
  private final Counter failedMtMessageDeletionCounter;
  private final Counter failedVehicleLockDeletionCounter;

  public FinishedMtMessageHandlerMetricReporter(MeterRegistry meterRegistry) {
    failedMtMessageDeletionCounter = meterRegistry.counter("deletion.failure", Tags.of(TYPE, "MT_MESSAGE"));
    failedVehicleLockDeletionCounter = meterRegistry.counter("deletion.failure", Tags.of(TYPE, "VEHICLE_LOCK"));
    failedActiveMtMessageInsertionCounter = meterRegistry.counter("insertion.failure", Tags.of(TYPE, "ACTIVE_MT_MESSAGE"));
  }

  public void onFailedActiveMtMessageInsertion() {
    failedActiveMtMessageInsertionCounter.increment();
  }

  public void onFailedMtMessageDeletion() {
    failedMtMessageDeletionCounter.increment();
  }

  public void onFailedVehicleLockDeletion() {
    failedVehicleLockDeletionCounter.increment();
  }
}
