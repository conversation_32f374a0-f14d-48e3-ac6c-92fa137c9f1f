package com.volvo.tisp.tgwmts.impl.services;

import java.util.function.Consumer;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.impl.clients.ConnectionEstablishedRegistrationClient;
import com.volvo.tisp.tgwmts.impl.clients.MtRouterRestClient;
import com.volvo.tisp.tgwmts.impl.clients.MtSoftcarRestClient;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;

@Component
public class TransmissionRouterContext {
  private final ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient;
  private final Consumer<IntegrationLogParameter> loggingHelper;
  private final MtRouterRestClient mtRouterRestClient;
  private final MtSoftcarRestClient mtSoftcarRestClient;

  public TransmissionRouterContext(
      ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient,
      Consumer<IntegrationLogParameter> loggingHelper,
      MtRouterRestClient mtRouterRestClient,
      MtSoftcarRestClient mtSoftcarRestClient) {
    this.connectionEstablishedRegistrationClient = connectionEstablishedRegistrationClient;
    this.loggingHelper = loggingHelper;
    this.mtRouterRestClient = mtRouterRestClient;
    this.mtSoftcarRestClient = mtSoftcarRestClient;
  }

  public ConnectionEstablishedRegistrationClient getConnectionEstablishedWebClient() {
    return connectionEstablishedRegistrationClient;
  }

  public Consumer<IntegrationLogParameter> getLoggingHelper() {
    return loggingHelper;
  }

  public MtRouterRestClient getMtRouterRestClient() {
    return mtRouterRestClient;
  }

  public MtSoftcarRestClient getMtSoftcarRestClient() {
    return mtSoftcarRestClient;
  }
}
