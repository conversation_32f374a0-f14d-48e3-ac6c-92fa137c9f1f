package com.volvo.tisp.tgwmts.impl.converters;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.cache.core.api.CacheDeviceInfoReader;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

@Component
public class MtStatusMessageOutputConverter {
  private static final Logger logger = LoggerFactory.getLogger(MtStatusMessageOutputConverter.class);

  private final CacheDeviceInfoReader cacheDeviceInfoReader;

  public MtStatusMessageOutputConverter(CacheDeviceInfoReader cacheDeviceInfoReader) {
    this.cacheDeviceInfoReader = cacheDeviceInfoReader;
  }

  public MtStatusMessage convert(MtStatus mtStatus, ReplyOption replyOption, Vpi vpi) {
    Validate.notNull(mtStatus, "mtStatus");
    Validate.notNull(replyOption, "replyOption");
    Validate.notNull(vpi, "vpi");

    MtStatusMessage mtStatusMessage = new MtStatusMessage();

    mtStatusMessage.setCorrelationId(replyOption.getCorrelationId().toString());
    mtStatusMessage.setStatus(mtStatus.name());
    mtStatusMessage.setVehiclePlatformId(vpi.toString());

    cacheDeviceInfoReader.findDeviceInfoByVpi(vpi)
        .ifPresentOrElse(persistedDeviceInfo -> mtStatusMessage.setHandle(persistedDeviceInfo.getDeviceInfo().getHandle().toString()),
            () -> logger.warn("device not found to populate handle for V2 mt status message, vpi {}", vpi));

    return mtStatusMessage;
  }
}
