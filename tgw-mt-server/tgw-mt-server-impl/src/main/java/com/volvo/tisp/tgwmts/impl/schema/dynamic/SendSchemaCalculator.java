package com.volvo.tisp.tgwmts.impl.schema.dynamic;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.conf.properties.SendSchemaProperties;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.metric.reporter.SendSchemaMetricReporter;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonNormalSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DynamicSendSchema;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class SendSchemaCalculator {
  private static final Logger logger = LoggerFactory.getLogger(SendSchemaCalculator.class);

  private final SendSchemaMetricReporter sendSchemaMetricReporter;
  private final SendSchemaNameParameterExtractor sendSchemaNameParameterExtractor;
  private final SendSchemaProperties sendSchemaProperties;

  public SendSchemaCalculator(SendSchemaMetricReporter sendSchemaMetricReporter, SendSchemaNameParameterExtractor sendSchemaNameParameterExtractor,
      SendSchemaProperties sendSchemaProperties) {
    this.sendSchemaMetricReporter = sendSchemaMetricReporter;
    this.sendSchemaNameParameterExtractor = sendSchemaNameParameterExtractor;
    this.sendSchemaProperties = sendSchemaProperties;
  }

  private static Duration addSatStep(List<SendSchemaStep> steps, Duration remainingDuration) {
    if (remainingDuration.compareTo(SendSchemaProperties.MIN_TIMEOUT) < 0) {
      return Duration.ZERO;
    }

    SendSchemaStepId sendSchemaStepId = SendSchemaStepId.ofInt(steps.size() + 1);
    Duration stepDuration = Duration.ofSeconds(Math.min(SendSchemaStep.DEFAULT_SAT_TIMEOUT.getSeconds(), remainingDuration.getSeconds()));
    steps.add(SendSchemaStep.forSat(sendSchemaStepId, stepDuration));
    return remainingDuration.minus(stepDuration);
  }

  private static Duration addSmsStep(List<SendSchemaStep> steps, Duration remainingDuration) {
    if (remainingDuration.compareTo(SendSchemaProperties.MIN_TIMEOUT) < 0) {
      return Duration.ZERO;
    }

    SendSchemaStepId sendSchemaStepId = SendSchemaStepId.ofInt(steps.size() + 1);
    Duration stepDuration = Duration.ofSeconds(Math.min(SendSchemaStep.DEFAULT_SMS_TIMEOUT.getSeconds(), remainingDuration.getSeconds()));
    steps.add(SendSchemaStep.forSms(sendSchemaStepId, stepDuration));
    return remainingDuration.minus(stepDuration);
  }

  private static Duration addUdpStep(List<SendSchemaStep> steps, Duration remainingDuration) {
    if (remainingDuration.compareTo(SendSchemaProperties.MIN_TIMEOUT) < 0) {
      return Duration.ZERO;
    }

    SendSchemaStepId sendSchemaStepId = SendSchemaStepId.ofInt(steps.size() + 1);
    Duration stepDuration = Duration.ofSeconds(Math.min(SendSchemaStep.DEFAULT_UDP_TIMEOUT.getSeconds(), remainingDuration.getSeconds()));
    steps.add(SendSchemaStep.forUdp(sendSchemaStepId, stepDuration));
    return remainingDuration.minus(stepDuration);
  }

  private static void addWaitStep(List<SendSchemaStep> steps, Duration remainingDuration) {
    if (remainingDuration.compareTo(SendSchemaProperties.MIN_TIMEOUT) < 0) {
      return;
    }

    SendSchemaStepId sendSchemaStepId = SendSchemaStepId.ofInt(steps.size() + 1);
    steps.add(SendSchemaStep.forWait(sendSchemaStepId, remainingDuration));
  }

  private static Duration addWifiStep(List<SendSchemaStep> steps, Duration remainingDuration) {
    if (remainingDuration.compareTo(SendSchemaProperties.MIN_TIMEOUT) < 0) {
      return Duration.ZERO;
    }

    SendSchemaStepId sendSchemaStepId = SendSchemaStepId.ofInt(steps.size() + 1);
    Duration stepDuration = Duration.ofSeconds(Math.min(SendSchemaStep.DEFAULT_WIFI_TIMEOUT.getSeconds(), remainingDuration.getSeconds()));
    steps.add(SendSchemaStep.forWifi(sendSchemaStepId, stepDuration));
    return remainingDuration.minus(stepDuration);
  }

  /**
   * If ttl is less than or equal to five minutes it does not matter which acceptedCost is used. We will try on WIFI and/or UDP
   */
  private static SendSchema createLowTtlSchema(SendSchemaName sendSchemaName, Duration ttl) {
    return DynamicSendSchema.create(ttl, sendSchemaName, List.of(
        SendSchemaStep.forWifi(SendSchemaStepId.ofInt(1)),
        SendSchemaStep.forUdp(SendSchemaStepId.ofInt(2))));
  }

  /**
   * Tries on UDP first, followed by SMS, SAT and WAIT if TTL is long enough
   */
  private static SendSchema createSendSchemaHigh(SendSchemaName sendSchemaName, Duration ttl) {
    return DynamicSendSchema.create(ttl, sendSchemaName, new StepBuilder(ttl).addWifiStep().addUdpStep().addSmsStep().addSatStep().build());
  }

  /**
   * Tries on UDP first and then optionally goes to WAIT if TTL is long enough
   */
  private static SendSchema createSendSchemaLow(SendSchemaName sendSchemaName, Duration ttl) {
    return DynamicSendSchema.create(ttl, sendSchemaName, new StepBuilder(ttl).addWifiStep().addUdpStep().build());
  }

  /**
   * Tries on UDP first, followed by SMS and WAIT if TTL is long enough
   */
  private static SendSchema createSendSchemaMid(SendSchemaName sendSchemaName, Duration ttl) {
    return DynamicSendSchema.create(ttl, sendSchemaName, new StepBuilder(ttl).addWifiStep().addUdpStep().addSmsStep().build());
  }

  public SendSchema calculate(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    Optional<TtlAcceptedCostHolder> optional = sendSchemaNameParameterExtractor.extract(sendSchemaName);

    if (optional.isEmpty()) {
      logger.warn("invalid sendSchemaName: {}, defaulting to {}", sendSchemaName, CommonNormalSendSchema.INSTANCE.getSendSchemaName());
      sendSchemaMetricReporter.onInvalidName();
      return CommonNormalSendSchema.INSTANCE;
    }

    TtlAcceptedCostHolder ttlAcceptedCostHolder = optional.get();
    Duration ttl = validateTtl(ttlAcceptedCostHolder.duration());

    if (ttl.compareTo(Duration.ofMinutes(5)) <= 0) {
      return createLowTtlSchema(sendSchemaName, ttl);
    }

    return switch (ttlAcceptedCostHolder.acceptedCost()) {
      case LOW -> createSendSchemaLow(sendSchemaName, ttl);
      case NORMAL -> createSendSchemaMid(sendSchemaName, ttl);
      case HIGH, EXTREME -> createSendSchemaHigh(sendSchemaName, ttl);
    };
  }

  private Duration validateTtl(Duration ttl) {
    Duration maxTimeout = sendSchemaProperties.getMaxTimeout();

    if (ttl.compareTo(maxTimeout) > 0) {
      logger.warn("sendSchema timeout too high, defaulting to {}, was {}", maxTimeout, ttl);
      sendSchemaMetricReporter.onTimeoutTooHigh();
      return maxTimeout;
    }

    if (ttl.compareTo(SendSchemaProperties.MIN_TIMEOUT) < 0) {
      logger.warn("sendSchema timeout too low, defaulting to {}, was {}", SendSchemaProperties.MIN_TIMEOUT, ttl);
      sendSchemaMetricReporter.onTimeoutTooLow();
      return SendSchemaProperties.MIN_TIMEOUT;
    }

    return ttl;
  }

  private static class StepBuilder {
    private Duration duration;
    private final List<SendSchemaStep> steps;

    StepBuilder(Duration duration) {
      this.steps = new ArrayList<>();
      this.duration = duration;
    }

    StepBuilder addSatStep() {
      this.duration = SendSchemaCalculator.addSatStep(steps, duration);
      return this;
    }

    StepBuilder addSmsStep() {
      this.duration = SendSchemaCalculator.addSmsStep(steps, duration);
      return this;
    }

    StepBuilder addUdpStep() {
      this.duration = SendSchemaCalculator.addUdpStep(steps, duration);
      return this;
    }

    StepBuilder addWifiStep() {
      this.duration = SendSchemaCalculator.addWifiStep(steps, duration);
      return this;
    }

    List<SendSchemaStep> build() {
      SendSchemaCalculator.addWaitStep(steps, duration);
      return List.copyOf(steps);
    }
  }
}