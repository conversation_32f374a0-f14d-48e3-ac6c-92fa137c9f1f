package com.volvo.tisp.tgwmts.impl.services;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.TgwNotifyEventMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class TgwNotifyEventProcessor {
  private static final Logger logger = LoggerFactory.getLogger(TgwNotifyEventProcessor.class);
  private final ActiveMtMessageWriterFactory activeMtMessageWriterFactory;
  private final TgwNotifyEventMetricReporter tgwNotifyEventMetricReporter;

  public TgwNotifyEventProcessor(ActiveMtMessageWriterFactory activeMtMessageWriterFactory,
      TgwNotifyEventMetricReporter tgwNotifyEventMetricReporter) {
    this.activeMtMessageWriterFactory = activeMtMessageWriterFactory;
    this.tgwNotifyEventMetricReporter = tgwNotifyEventMetricReporter;
  }

  public void process(AssetCapabilityState assetCapabilityState) {
    Validate.notNull(assetCapabilityState, "assetCapabilityState");

    tgwNotifyEventMetricReporter.onAssetStateChangeReceived(assetCapabilityState);

    try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
      activeMtMessageWriter.startTransactionWithLockTimeout();

      activeMtMessageWriter.updateAssetCapabilityState(assetCapabilityState);

      activeMtMessageWriter.commitTransaction();
      tgwNotifyEventMetricReporter.onAssetStateChangeProcessed(assetCapabilityState);
    } catch (RuntimeException e) {
      logger.error("Error processing assetCapabilityState: {}", assetCapabilityState, e);
      tgwNotifyEventMetricReporter.onAssetStateChangeProcessFailed(assetCapabilityState);
    }
  }
}
