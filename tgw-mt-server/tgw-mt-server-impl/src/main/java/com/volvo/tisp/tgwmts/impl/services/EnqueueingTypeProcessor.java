package com.volvo.tisp.tgwmts.impl.services;

import java.time.Duration;
import java.time.Instant;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

public class EnqueueingTypeProcessor {
  private static final Logger logger = LoggerFactory.getLogger(EnqueueingTypeProcessor.class);

  private final int maxNumberOfInFlightMessagesPerVehicle;
  private final MtMessageMetricReporter mtMessageMetricReporter;
  private final MtStatusPublisher mtStatusPublisher;

  public EnqueueingTypeProcessor(int maxNumberOfInFlightMessagesPerVehicle, MtMessageMetricReporter mtMessageMetricReporter,
      MtStatusPublisher mtStatusPublisher) {
    Validate.isPositive(maxNumberOfInFlightMessagesPerVehicle, "maxNumberOfInFlightMessagesPerVehicle");
    Validate.notNull(mtMessageMetricReporter, "mtMessageMetricReporter");
    Validate.notNull(mtStatusPublisher, "mtStatusPublisher");

    this.maxNumberOfInFlightMessagesPerVehicle = maxNumberOfInFlightMessagesPerVehicle;
    this.mtMessageMetricReporter = mtMessageMetricReporter;
    this.mtStatusPublisher = mtStatusPublisher;
  }

  private static ActiveMtMessage createActiveMtMessage(PersistedMtMessage persistedMtMessage) {
    return new ActiveMtMessageBuilder()
        .setMtMessageId(persistedMtMessage.getMtMessageId())
        .setSendSchemaStepId(SendSchemaStepId.ofInt(0))
        .setRetryAttempt(RetryAttempt.ofShort((short) 0))
        .setTimeout(Instant.now())
        .build();
  }

  public void deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(ReceivedMtMessage receivedMtMessage, ActiveMtMessageWriter activeMtMessageWriter) {
    Validate.notNull(receivedMtMessage, "receivedMtMessage");
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");

    if (receivedMtMessage.getEnqueueingType() == EnqueueingType.OVERRIDE || receivedMtMessage.getEnqueueingType() == EnqueueingType.OVERRIDE_IMMEDIATE) {
      Vpi vpi = receivedMtMessage.getVpi();
      List<PersistedMtMessage> mtMessages = activeMtMessageWriter.findMtMessagesByVpiAndQueueId(vpi, receivedMtMessage.getQueueId());

      if (mtMessages.isEmpty()) {
        return;
      }

      activeMtMessageWriter.deleteMtMessageByIds(mtMessages.stream().map(PersistedMtMessage::getMtMessageId).toList());
      activateQueuedMtMessages(activeMtMessageWriter, vpi);

      mtMessages.stream()
          .map(PersistedMtMessage::getMtMessage)
          .filter(mtMessage -> mtMessage.getReplyOption().isPresent())
          .forEach(mtMessage -> mtStatusPublisher.publishMtStatus(MtStatus.OVERRIDDEN, mtMessage.getReplyOption().get(), vpi));
    }
  }

  public boolean shouldActiveMtMessageBeCreated(VehicleLockId vehicleLockId, ReceivedMtMessage receivedMtMessage, ActiveMtMessageWriter activeMtMessageWriter) {
    Validate.notNull(vehicleLockId, "vehicleLockId");
    Validate.notNull(receivedMtMessage, "receivedMtMessage");
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");

    return switch (receivedMtMessage.getEnqueueingType()) {
      case IMMEDIATE, OVERRIDE_IMMEDIATE -> true;
      default -> activeMtMessageWriter.countActiveMtMessagesByVehicleLockId(vehicleLockId) < maxNumberOfInFlightMessagesPerVehicle;
    };
  }

  public boolean shouldMessageBeIgnored(VehicleLockId vehicleLockId, ReceivedMtMessage receivedMtMessage, ActiveMtMessageWriter activeMtMessageWriter) {
    Validate.notNull(vehicleLockId, "vehicleLockId");
    Validate.notNull(receivedMtMessage, "receivedMtMessage");
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");

    if (receivedMtMessage.getEnqueueingType() == EnqueueingType.IGNORE) {
      return shouldMessageBeIgnoredForEnqueueingTypeIgnore(vehicleLockId, receivedMtMessage, activeMtMessageWriter);
    }

    return false;
  }

  private void activateQueuedMtMessages(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi) {
    int activeMessageCount = activeMtMessageWriter.countActiveMtMessages();
    int availableSlots = Math.max(maxNumberOfInFlightMessagesPerVehicle - activeMessageCount, 0);

    if (availableSlots > 0) {
      activeMtMessageWriter
          .findOldestNonActiveMtMessages(vpi, availableSlots)
          .forEach(persistedMtMessage -> insertActiveMtMessage(activeMtMessageWriter, persistedMtMessage));
    }
  }

  private void insertActiveMtMessage(ActiveMtMessageWriter activeMtMessageWriter, PersistedMtMessage persistedMtMessage) {
    Instant start = Instant.now();
    Either<InsertionFailure, ActiveMtMessageId> either = activeMtMessageWriter.insertActiveMtMessage(createActiveMtMessage(persistedMtMessage));
    Instant end = Instant.now();

    if (either.isLeft()) {
      logger.warn("failed to insert active_mt_message for mt_message: {} {}", persistedMtMessage, either.getLeft());
      mtMessageMetricReporter.onMtInsertFailure(Duration.between(start, end));
      return;
    }

    mtMessageMetricReporter.onActiveMtInsertSuccess(Duration.between(start, end));
  }

  private boolean shouldMessageBeIgnoredForEnqueueingTypeIgnore(VehicleLockId vehicleLockId, ReceivedMtMessage receivedMtMessage,
      ActiveMtMessageWriter activeMtMessageWriter) {
    int count = activeMtMessageWriter.countMtMessagesByVehicleLockIdAndQueueId(vehicleLockId, receivedMtMessage.getQueueId());

    if (count > 0) {
      logger.info("mtMessage ({}) will be ignored since there are {} mt-messages in the MT queue for this vehicle", receivedMtMessage, count);
      mtMessageMetricReporter.onIgnoredMtMessage();
      return true;
    }

    return false;
  }
}
