package com.volvo.tisp.tgwmts.impl.services;

import java.util.Optional;
import java.util.function.Supplier;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.database.model.AesKey;
import com.volvo.tisp.tgw.device.info.database.model.CryptoKeyInfo;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.PendingCryptoKeyInfo;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.EncryptionProcessorMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AdditionalAuthenticatedData;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionService;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;

@Component
public class EncryptionProcessor {
  private final EncryptionProcessorMetricReporter encryptionProcessorMetricReporter;
  private final Supplier<InitializationVector> initializationVectorSupplier;
  private final SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService;

  public EncryptionProcessor(EncryptionProcessorMetricReporter encryptionProcessorMetricReporter,
      Supplier<InitializationVector> initializationVectorSupplier, SymmetricAesGcmEncryptionService symmetricAesGcmEncryptionService) {
    this.encryptionProcessorMetricReporter = encryptionProcessorMetricReporter;
    this.initializationVectorSupplier = initializationVectorSupplier;
    this.symmetricAesGcmEncryptionService = symmetricAesGcmEncryptionService;
  }

  private static SymmetricKey createSymmetricKey(AesKey aesKey) {
    return SymmetricKey.create(aesKey.getImmutableByteArray());
  }

  public AesGcmEncryptionResult encryptPayload(PersistedDeviceInfo persistedDeviceInfo, AdditionalAuthenticatedData additionalAuthenticatedData,
      PlainTextPayload plainTextPayload) {
    Validate.notNull(persistedDeviceInfo, "persistedDeviceInfo");
    Validate.notNull(additionalAuthenticatedData, "additionalAuthenticatedData");
    Validate.notNull(plainTextPayload, "plainTextPayload");

    encryptionProcessorMetricReporter.onEncryptSrp11Received();

    DeviceInfo deviceInfo = persistedDeviceInfo.getDeviceInfo();
    Either<RuntimeException, AesGcmEncryptionResult> either = encrypt(additionalAuthenticatedData, plainTextPayload, deviceInfo);

    if (either.isRight()) {
      return either.getRight();
    }

    encryptionProcessorMetricReporter.onEncryptPayloadDeviceFail();
    throw either.getLeft();
  }

  private Either<RuntimeException, AesGcmEncryptionResult> encrypt(AdditionalAuthenticatedData additionalAuthenticatedData, PlainTextPayload plainTextPayload,
      DeviceInfo deviceInfo) {
    Optional<CryptoKeyInfo> cryptoKeyInfo = deviceInfo.getCryptoKeyInfo();

    if (cryptoKeyInfo.isPresent()) {
      return symmetricAesGcmEncryptionService.encryptWithGivenIv(createSymmetricKey(cryptoKeyInfo.get().aesKey()), plainTextPayload,
          initializationVectorSupplier.get(), additionalAuthenticatedData);
    }

    Optional<PendingCryptoKeyInfo> pendingCryptoKeyInfo = deviceInfo.getPendingCryptoKeyInfo();

    if (pendingCryptoKeyInfo.isPresent()) {
      AesKey aesKey = pendingCryptoKeyInfo.get().aesKey();

      return symmetricAesGcmEncryptionService.encryptWithGivenIv(createSymmetricKey(aesKey), plainTextPayload, initializationVectorSupplier.get(),
          additionalAuthenticatedData);
    }

    throw new IllegalStateException("device has neither current nor pending crypto key: " + deviceInfo);
  }
}
