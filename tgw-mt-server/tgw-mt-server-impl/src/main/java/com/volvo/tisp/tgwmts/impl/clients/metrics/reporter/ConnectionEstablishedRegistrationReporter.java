package com.volvo.tisp.tgwmts.impl.clients.metrics.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class ConnectionEstablishedRegistrationReporter {
  private final Counter connectionEstablishReRegisterCounter;

  public ConnectionEstablishedRegistrationReporter(MeterRegistry meterRegistry) {
    connectionEstablishReRegisterCounter = meterRegistry.counter("re-register");
  }

  public void onReRegister() {
    connectionEstablishReRegisterCounter.increment();
  }
}
