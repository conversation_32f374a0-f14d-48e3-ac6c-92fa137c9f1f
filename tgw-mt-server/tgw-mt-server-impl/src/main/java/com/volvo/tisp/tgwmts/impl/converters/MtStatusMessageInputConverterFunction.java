package com.volvo.tisp.tgwmts.impl.converters;

import java.util.function.BiFunction;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.Status;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.impl.model.MtStatusClient;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatusMessage;
import com.volvo.tisp.tgwmts.impl.model.TransportType;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

public final class MtStatusMessageInputConverterFunction
    implements BiFunction<MtStatus, MtStatusClient, Either<IllegalArgumentException, ReceivedMtStatusMessage>> {
  public static final MtStatusMessageInputConverterFunction INSTANCE = new MtStatusMessageInputConverterFunction();
  private static final Logger logger = LoggerFactory.getLogger(MtStatusMessageInputConverterFunction.class);

  private MtStatusMessageInputConverterFunction() {
    // do nothing
  }

  private static ActiveMtMessageId convertMessageId(String activeMtMessageId) {
    try {
      return ActiveMtMessageId.ofLong(Long.parseLong(activeMtMessageId));
    } catch (NumberFormatException e) {
      throw new IllegalArgumentException("invalid activeMtMessageId: " + activeMtMessageId, e);
    }
  }

  private static ReceivedMtStatus convertMtStatus(Status status) {
    return switch (status) {
      case CANCELED -> ReceivedMtStatus.CANCELED;
      case DELIVERED -> ReceivedMtStatus.DELIVERED;
      case REJECTED -> ReceivedMtStatus.REJECTED;
      case THROTTLED -> ReceivedMtStatus.THROTTLED;
      case SERVICE_UNSUPPORTED -> ReceivedMtStatus.SERVICE_UNSUPPORTED;
      default -> {
        logger.warn("Unknown status: {}, defaulting to REJECTED", status);
        yield ReceivedMtStatus.REJECTED;
      }
    };
  }

  private static TransportType convertTransportType(MtStatus mtStatus, MtStatusClient mtStatusClient) {
    if (mtStatusClient == MtStatusClient.TUCS) {
      return TransportType.SOFTCAR;
    }

    return switch (mtStatus.getTransport()) {
      case UDP -> TransportType.UDP;
      case SMS -> TransportType.SMS;
      case SAT -> TransportType.SAT;
      case VPN -> TransportType.WIFI;
      case UNRECOGNIZED -> {
        logger.warn("unknown transportType: {}", mtStatus);
        yield TransportType.UNKNOWN;
      }
    };
  }

  @Override
  public Either<IllegalArgumentException, ReceivedMtStatusMessage> apply(MtStatus mtStatus, MtStatusClient mtStatusClient) {
    Validate.notNull(mtStatus, "mtStatus");
    Validate.notNull(mtStatusClient, "mtStatusClient");

    try {
      return Either.right(
          new ReceivedMtStatusMessage(
              convertMessageId(mtStatus.getMessageId()),
              convertMtStatus(mtStatus.getStatus()),
              convertTransportType(mtStatus, mtStatusClient)));
    } catch (IllegalArgumentException e) {
      return Either.left(e);
    }
  }
}
