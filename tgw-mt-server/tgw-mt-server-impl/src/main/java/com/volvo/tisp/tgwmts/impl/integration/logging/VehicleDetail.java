package com.volvo.tisp.tgwmts.impl.integration.logging;

import java.util.Optional;

import com.volvo.tisp.tgw.device.info.database.model.Handle;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class VehicleDetail {
  private final Handle handle;
  private final Ipv4Address ipv4Address;
  private final Msisdn msisdn;
  private final Vpi vpi;

  private VehicleDetail(Handle handle, Ipv4Address ipv4Address, Msisdn msisdn, Vpi vpi) {
    this.handle = handle;
    this.ipv4Address = ipv4Address;
    this.msisdn = msisdn;
    this.vpi = vpi;
  }

  public static VehicleDetail create(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    return new VehicleDetail(null, null, null, vpi);
  }

  public static VehicleDetail create(Handle handle, Ipv4Address ipv4Address, Msisdn msisdn, Vpi vpi) {
    Validate.notNull(handle, "handle");
    Validate.notNull(ipv4Address, "ipv4Address");
    Validate.notNull(msisdn, "msisdn");
    Validate.notNull(vpi, "vpi");

    return new VehicleDetail(handle, ipv4Address, msisdn, vpi);
  }

  public Optional<Handle> getHandle() {
    return Optional.ofNullable(handle);
  }

  public Optional<Ipv4Address> getIpv4Address() {
    return Optional.ofNullable(ipv4Address);
  }

  public Optional<Msisdn> getMsisdn() {
    return Optional.ofNullable(msisdn);
  }

  public Vpi getVpi() {
    return vpi;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("handle=")
        .append(handle)
        .append(", ipv4Address={")
        .append(ipv4Address)
        .append(", msisdn=")
        .append(msisdn)
        .append(", vpi=")
        .append(vpi)
        .toString();
  }
}
