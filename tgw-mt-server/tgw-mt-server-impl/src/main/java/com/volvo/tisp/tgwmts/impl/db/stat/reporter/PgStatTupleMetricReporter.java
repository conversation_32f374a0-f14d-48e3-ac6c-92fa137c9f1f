package com.volvo.tisp.tgwmts.impl.db.stat.reporter;

import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.stat.TableName;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class PgStatTupleMetricReporter {
  public static final String TABLE_NAME = "TABLE_NAME";

  private final AtomicInteger amtDeadTupleLenGauge;
  private final AtomicInteger mtDeadTupleLenGauge;
  private final AtomicInteger amtFreeSpaceGauge;
  private final AtomicInteger mtFreeSpaceGauge;
  private final AtomicInteger amtTableLenGauge;
  private final AtomicInteger mtTableLenGauge;
  private final AtomicInteger amtTupleLenGauge;
  private final AtomicInteger mtTupleLenGauge;

  public PgStatTupleMetricReporter(MeterRegistry meterRegistry) {
    this.amtDeadTupleLenGauge = meterRegistry.gauge("dead-tuple-len", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), new AtomicInteger());
    this.mtDeadTupleLenGauge = meterRegistry.gauge("dead-tuple-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), new AtomicInteger());
    this.amtFreeSpaceGauge = meterRegistry.gauge("free-space", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), new AtomicInteger());
    this.mtFreeSpaceGauge = meterRegistry.gauge("free-space", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), new AtomicInteger());
    this.amtTableLenGauge = meterRegistry.gauge("table-len", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), new AtomicInteger());
    this.mtTableLenGauge = meterRegistry.gauge("table-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), new AtomicInteger());
    this.amtTupleLenGauge = meterRegistry.gauge("tuple-len", Tags.of(TABLE_NAME, TableName.ACTIVE_MT_MESSAGE.toString()), new AtomicInteger());
    this.mtTupleLenGauge = meterRegistry.gauge("tuple-len", Tags.of(TABLE_NAME, TableName.MT_MESSAGE.toString()), new AtomicInteger());;
  }

  public void onDeadTupleLen(TableName tableName, int value) {
    Validate.notNull(tableName, "tableName");

    switch (tableName) {
      case ACTIVE_MT_MESSAGE -> amtDeadTupleLenGauge.set(value);
      case MT_MESSAGE -> mtDeadTupleLenGauge.set(value);
    }
  }

  public void onFreeSpace(TableName tableName, int value) {
    Validate.notNull(tableName, "tableName");

    switch (tableName) {
      case ACTIVE_MT_MESSAGE -> amtFreeSpaceGauge.set(value);
      case MT_MESSAGE -> mtFreeSpaceGauge.set(value);
    }
  }

  public void onTableLen(TableName tableName, int value) {
    Validate.notNull(tableName, "tableName");

    switch (tableName) {
      case ACTIVE_MT_MESSAGE -> amtTableLenGauge.set(value);
      case MT_MESSAGE -> mtTableLenGauge.set(value);
    }
  }

  public void onTupleLen(TableName tableName, int value) {
    Validate.notNull(tableName, "tableName");

    switch (tableName) {
      case ACTIVE_MT_MESSAGE -> amtTupleLenGauge.set(value);
      case MT_MESSAGE -> mtTupleLenGauge.set(value);
    }
  }
}
