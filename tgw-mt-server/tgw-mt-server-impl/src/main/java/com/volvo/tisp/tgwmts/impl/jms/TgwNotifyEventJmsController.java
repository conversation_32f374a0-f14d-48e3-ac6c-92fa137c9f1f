package com.volvo.tisp.tgwmts.impl.jms;

import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.connectivity.cms.events.model.TgwNotifyEvent;
import com.volvo.connectivity.cms.events.model.Wifi;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;
import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState.AssetCapability;
import com.volvo.tisp.tgwmts.impl.services.TgwNotifyEventProcessor;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.TgwNotifyEventMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@JmsController(destination = TgwNotifyEventJmsController.TGW_NOTIFY_QUEUE_NAME)
public class TgwNotifyEventJmsController {
  public static final String MESSAGE_TYPE = "TGW_NOTIFY_MESSAGE";
  public static final String MESSAGE_VERSION = "1.0";
  public static final String TGW_NOTIFY_QUEUE_NAME = "TGW-NOTIFY-IN";
  private static final Logger logger = LoggerFactory.getLogger(TgwNotifyEventJmsController.class);

  private final TgwNotifyEventMetricReporter tgwNotifyEventMetricReporter;
  private final TgwNotifyEventProcessor tgwNotifyEventProcessor;

  public TgwNotifyEventJmsController(TgwNotifyEventProcessor tgwNotifyEventProcessor,
      TgwNotifyEventMetricReporter tgwNotifyEventMetricReporter) {
    this.tgwNotifyEventMetricReporter = tgwNotifyEventMetricReporter;
    this.tgwNotifyEventProcessor = tgwNotifyEventProcessor;
  }

  @JmsMessageMapping(consumesType = MESSAGE_TYPE, consumesVersion = MESSAGE_VERSION)
  public void receiveTgwNotifyEvent(JmsMessage<TgwNotifyEvent> jmsMessage) {
    Validate.notNull(jmsMessage, "jmsMessage");

    logger.debug("received TgwNotifyEvent: {}", jmsMessage);
    TgwNotifyEvent tgwNotifyEvent = jmsMessage.payload();

    Optional<AssetCapabilityState> receivedAssetCapabilityState = createReceivedAssetCapabilityState(tgwNotifyEvent);

    receivedAssetCapabilityState.ifPresentOrElse(tgwNotifyEventProcessor::process,
        () -> tgwNotifyEventMetricReporter.onIgnoredNotifyEvent());
  }

  private Optional<AssetCapabilityState> createReceivedAssetCapabilityState(TgwNotifyEvent tgwNotifyEvent) {
    Wifi wifi = tgwNotifyEvent.getConnectivityCapability().getWifi();
    if (wifi != null) {

      Vpi vpi = Vpi.ofString(tgwNotifyEvent.getAsset().getVpi());
      return getAssetWifiCapabilityState(vpi, wifi);
    }

    logger.debug("tgwNotifyEvent: {} is not for wifi capability", tgwNotifyEvent);
    return Optional.empty();
  }

  private Optional<AssetCapabilityState> getAssetWifiCapabilityState(Vpi vpi, Wifi wifi) {
    return switch (wifi.getState()) {
      case AVAILABLE -> Optional.of(new AssetCapabilityState(vpi, AssetCapability.WIFI, true));
      case UNAVAILABLE -> Optional.of(new AssetCapabilityState(vpi, AssetCapability.WIFI, false));
      default -> Optional.empty();
    };
  }
}
