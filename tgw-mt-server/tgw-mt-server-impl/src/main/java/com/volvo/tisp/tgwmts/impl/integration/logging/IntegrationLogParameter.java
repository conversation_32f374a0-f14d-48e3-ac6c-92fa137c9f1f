package com.volvo.tisp.tgwmts.impl.integration.logging;

import com.wirelesscar.componentbase.logging.Logging;

public final class IntegrationLogParameter {
  private final Logging.Direction direction;
  private final IntegrationMessage integrationMessage;
  private final MetaData metaData;
  private final Logging.Status status;
  private final VehicleDetail vehicleDetail;

  IntegrationLogParameter(IntegrationLogParameterBuilder integrationLogParameterBuilder) {
    this.direction = integrationLogParameterBuilder.getDirection();
    this.integrationMessage = integrationLogParameterBuilder.getIntegrationMessage();
    this.metaData = integrationLogParameterBuilder.getMetaData();
    this.status = integrationLogParameterBuilder.getStatus();
    this.vehicleDetail = integrationLogParameterBuilder.getVehicleDetail();
  }

  public Logging.Direction getDirection() {
    return direction;
  }

  public IntegrationMessage getIntegrationMessage() {
    return integrationMessage;
  }

  public MetaData getMetaData() {
    return metaData;
  }

  public Logging.Status getStatus() {
    return status;
  }

  public VehicleDetail getVehicleDetail() {
    return vehicleDetail;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("status=")
        .append(status)
        .append(", vehicleDetail={")
        .append(vehicleDetail)
        .append("}, integrationMessage=")
        .append(integrationMessage)
        .append(", metadata=")
        .append(metaData)
        .toString();
  }
}
