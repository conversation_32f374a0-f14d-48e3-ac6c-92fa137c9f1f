package com.volvo.tisp.tgwmts.impl.jms;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;

@Component
public class MtMessageMetricReporter {
  private static final String ENQUEUEING_TYPE = "enqueueing-type";
  private static final String INVALID = "invalid";
  private static final String METRIC_MT_MESSAGE = "mt";
  private static final String METRIC_MT_PERSIST = "mt.persist";
  private static final String METRIC_MT_STATUS_MESSAGE = "mt.status";
  private static final String STATUS_DISCARDED = "discarded";
  private static final String TAG_MT_STATUS_TYPE = "type";
  private static final String TAG_STATUS = "status";
  private static final String TAG_STATUS_FAILURE = "failure";
  private static final String TAG_STATUS_SUCCESS = "success";
  private static final String TAG_TYPE = "type";
  private static final String TAG_TYPE_ACTIVE_MT_MESSAGE = "active-mt-message";
  private static final String TAG_TYPE_MT_MESSAGE = "mt-message";
  private static final String TAG_WITH_REPLY = "reply";
  private static final String VERSION = "version";

  private final Timer activeMtInsertFailureTimer;
  private final Timer activeMtInsertSuccessTimer;
  private final Counter discardedMtMessageWithReplyCounter;
  private final Counter discardedMtMessageWithoutReplyCounter;
  private final Counter ignoredMtMessageCounter;
  private final Counter invalidMtMessageReceivedV2Counter;
  private final MeterRegistry meterRegistry;
  private final Timer mtInsertFailureTimer;
  private final Timer mtInsertSuccessTimer;
  private final Counter mtStatusPublishErrorCounter;
  private final Timer mtStatusPublishedTimer;
  private final Timer mtTimer;
  private final Counter unidentifiedMtMessageCounter;

  public MtMessageMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    activeMtInsertFailureTimer = meterRegistry.timer(METRIC_MT_PERSIST, Tags.of(TAG_TYPE, TAG_TYPE_ACTIVE_MT_MESSAGE, TAG_STATUS, TAG_STATUS_FAILURE));
    activeMtInsertSuccessTimer = meterRegistry.timer(METRIC_MT_PERSIST, Tags.of(TAG_TYPE, TAG_TYPE_ACTIVE_MT_MESSAGE, TAG_STATUS, TAG_STATUS_SUCCESS));
    discardedMtMessageWithoutReplyCounter = meterRegistry.counter(METRIC_MT_MESSAGE, Tags.of(TAG_STATUS, STATUS_DISCARDED, TAG_WITH_REPLY, "false"));
    discardedMtMessageWithReplyCounter = meterRegistry.counter(METRIC_MT_MESSAGE, Tags.of(TAG_STATUS, STATUS_DISCARDED, TAG_WITH_REPLY, "true"));
    ignoredMtMessageCounter = meterRegistry.counter(METRIC_MT_MESSAGE, Tags.of(TAG_STATUS, "ignored"));
    invalidMtMessageReceivedV2Counter = meterRegistry.counter("api.jms.message.received", Tags.of(VERSION, "v2", TAG_STATUS, INVALID));
    mtInsertFailureTimer = meterRegistry.timer(METRIC_MT_PERSIST, Tags.of(TAG_TYPE, TAG_TYPE_MT_MESSAGE, TAG_STATUS, TAG_STATUS_FAILURE));
    mtInsertSuccessTimer = meterRegistry.timer(METRIC_MT_PERSIST, Tags.of(TAG_TYPE, TAG_TYPE_MT_MESSAGE, TAG_STATUS, TAG_STATUS_SUCCESS));
    mtStatusPublishErrorCounter = meterRegistry.counter("mt.status.publish.error");
    mtStatusPublishedTimer = meterRegistry.timer("mt.status.publish");
    mtTimer = meterRegistry.timer(METRIC_MT_MESSAGE, Tags.of(TAG_STATUS, TAG_STATUS_SUCCESS));
    unidentifiedMtMessageCounter = meterRegistry.counter(METRIC_MT_MESSAGE, Tags.of(TAG_STATUS, "unidentified"));
  }

  public void onActiveMtInsertFailure(Duration duration) {
    Validate.notNegative(duration, "duration");

    activeMtInsertFailureTimer.record(duration);
  }

  public void onActiveMtInsertSuccess(Duration duration) {
    Validate.notNegative(duration, "duration");

    activeMtInsertSuccessTimer.record(duration);
  }

  public void onDiscardedMtMessageWithReply() {
    discardedMtMessageWithReplyCounter.increment();
  }

  public void onDiscardedMtMessageWithoutReply() {
    discardedMtMessageWithoutReplyCounter.increment();
  }

  public void onEnqueueingType(EnqueueingType enqueueingType, SrpDestinationService srpDestinationService) {
    meterRegistry.counter(ENQUEUEING_TYPE, Tags.of(TAG_TYPE, enqueueingType.name(), "service", srpDestinationService.toString())).increment();
  }

  public void onIgnoredMtMessage() {
    ignoredMtMessageCounter.increment();
  }

  public void onMtInsertFailure(Duration duration) {
    Validate.notNegative(duration, "duration");

    mtInsertFailureTimer.record(duration);
  }

  public void onMtInsertSuccess(Duration duration) {
    Validate.notNegative(duration, "duration");

    mtInsertSuccessTimer.record(duration);
  }

  public void onMtStatus(MtStatus mtStatus) {
    Validate.notNull(mtStatus, "mtStatus");

    meterRegistry.counter(METRIC_MT_STATUS_MESSAGE, Tags.of(TAG_MT_STATUS_TYPE, mtStatus.name())).increment();
  }

  public void onMtStatusPublishDuration(Duration duration) {
    Validate.notNegative(duration, "duration");

    mtStatusPublishedTimer.record(duration);
  }

  public void onMtStatusPublishError() {
    mtStatusPublishErrorCounter.increment();
  }

  public void onMtV2Invalid() {
    invalidMtMessageReceivedV2Counter.increment();
  }

  public void onSuccess(Duration duration) {
    Validate.notNegative(duration, "duration");

    mtTimer.record(duration);
  }

  public void onUnidentifiedMtMessage() {
    unidentifiedMtMessageCounter.increment();
  }
}
