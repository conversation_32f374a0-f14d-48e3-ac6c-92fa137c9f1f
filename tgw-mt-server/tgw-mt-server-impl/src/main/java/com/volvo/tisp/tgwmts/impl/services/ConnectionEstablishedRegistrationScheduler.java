package com.volvo.tisp.tgwmts.impl.services;

import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;
import com.volvo.tisp.tgwmts.impl.clients.ConnectionEstablishedRegistrationClient;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

import net.javacrumbs.shedlock.spring.annotation.SchedulerLock;

@Component
public class ConnectionEstablishedRegistrationScheduler {
  private final ConnectionEstablishedCacheService connectionEstablishedCacheService;
  private final ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient;
  private final int pollLimit;

  public ConnectionEstablishedRegistrationScheduler(
      ConnectionEstablishedRegistrationClient connectionEstablishedRegistrationClient,
      ConnectionEstablishedCacheService connectionEstablishedCacheService,
      @Value("${connection-established.registration.retry.poll-count:500}") int pollLimit) {
    this.connectionEstablishedRegistrationClient = connectionEstablishedRegistrationClient;
    this.connectionEstablishedCacheService = connectionEstablishedCacheService;
    this.pollLimit = pollLimit;
  }

  @Scheduled(fixedDelayString = "${connection-established.registration.retry.interval-in-sec:30}", timeUnit = TimeUnit.SECONDS)
  @SchedulerLock(name = "connection_established_registration_retry_task",
      lockAtLeastFor = "${scheduler.connection-established.registration.retry.lockAtLeastFor:10s}")
  public void retryConnectionEstablishedRegistration() {
    List<Map.Entry<Vpi, Instant>> connectionEstablishedRegistrations = connectionEstablishedCacheService.getEntries(pollLimit);

    if (!connectionEstablishedRegistrations.isEmpty()) {
      connectionEstablishedRegistrationClient.reRegister(connectionEstablishedRegistrations)
          .subscribe(isSuccess -> {
            if (isSuccess) {
              invalidateCache(connectionEstablishedRegistrations);
            }
            connectionEstablishedCacheService.reportCacheSizeMetric();
          });
    }
  }

  private void invalidateCache(List<Map.Entry<Vpi, Instant>> connectionEstablishedRegistrations) {
    connectionEstablishedRegistrations.forEach(vpiInstantEntry -> connectionEstablishedCacheService.remove(vpiInstantEntry.getKey()));
  }
}
