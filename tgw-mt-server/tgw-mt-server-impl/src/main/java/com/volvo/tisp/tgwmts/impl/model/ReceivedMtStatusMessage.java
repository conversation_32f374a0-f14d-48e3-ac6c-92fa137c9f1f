package com.volvo.tisp.tgwmts.impl.model;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ReceivedMtStatusMessage(ActiveMtMessageId activeMtMessageId, ReceivedMtStatus receivedMtStatus, TransportType transportType) {
  public ReceivedMtStatusMessage {
    Validate.notNull(activeMtMessageId, "activeMtMessageId");
    Validate.notNull(receivedMtStatus, "receivedMtStatus");
    Validate.notNull(transportType, "transportType");

  }
}
