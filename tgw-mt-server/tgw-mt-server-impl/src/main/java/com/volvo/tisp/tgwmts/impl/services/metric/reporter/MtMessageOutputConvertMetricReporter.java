package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

@Component
public class MtMessageOutputConvertMetricReporter {
  private static final String TYPE = "TYPE";
  private static final String UDP_REST_CLIENT = "udp-rest-client";

  private final Counter udpInvalidMessageCounter;

  public MtMessageOutputConvertMetricReporter(MeterRegistry meterRegistry) {
    udpInvalidMessageCounter = meterRegistry.counter(UDP_REST_CLIENT, Tags.of(Tag.of(TYPE, "INVALID_MT_MESSAGE")));
  }

  public void onUdpInvalidMessage() {
    udpInvalidMessageCounter.increment();
  }
}
