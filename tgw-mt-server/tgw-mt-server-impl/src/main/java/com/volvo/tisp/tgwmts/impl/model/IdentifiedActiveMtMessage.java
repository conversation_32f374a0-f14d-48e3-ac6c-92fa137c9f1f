package com.volvo.tisp.tgwmts.impl.model;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record IdentifiedActiveMtMessage(JoinedActiveMtMessage joinedActiveMtMessage, PersistedDeviceInfo persistedDeviceInfo) {
  public IdentifiedActiveMtMessage {
    Validate.notNull(joinedActiveMtMessage, "joinedActiveMtMessage");
    Validate.notNull(persistedDeviceInfo, "persistedDeviceInfo");

  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    IdentifiedActiveMtMessage other = (IdentifiedActiveMtMessage) object;
    if (!joinedActiveMtMessage.equals(other.joinedActiveMtMessage)) {
      return false;
    }

    return persistedDeviceInfo.equals(other.persistedDeviceInfo);
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("joinedActiveMtMessage={")
        .append(joinedActiveMtMessage)
        .append("}, persistedDeviceInfo={")
        .append(persistedDeviceInfo)
        .append("}")
        .toString();
  }
}
