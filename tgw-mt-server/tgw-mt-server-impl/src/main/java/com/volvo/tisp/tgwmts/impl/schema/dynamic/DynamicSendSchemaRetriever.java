package com.volvo.tisp.tgwmts.impl.schema.dynamic;

import org.springframework.stereotype.Component;

import com.github.benmanes.caffeine.cache.Cache;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class DynamicSendSchemaRetriever {
  private final Cache<SendSchemaName, SendSchema> sendSchemaCache;
  private final SendSchemaCalculator sendSchemaCalculator;

  public DynamicSendSchemaRetriever(Cache<SendSchemaName, SendSchema> sendSchemaCache, SendSchemaCalculator sendSchemaCalculator) {
    this.sendSchemaCache = sendSchemaCache;
    this.sendSchemaCalculator = sendSchemaCalculator;
  }

  public final SendSchema getSendSchema(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    return sendSchemaCache.get(sendSchemaName, this::calculateSendSchema);
  }

  private SendSchema calculateSendSchema(SendSchemaName sendSchemaName) {
    return sendSchemaCalculator.calculate(sendSchemaName);
  }
}
