package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class MtRouterRestClientMetricReporter {
  private static final String CONVERSION_FAILURE = "CONVERSION-FAILURE";
  private static final String TYPE = "TYPE";

  private final Counter invalidEncodedActiveMtMessageCounter;

  public MtRouterRestClientMetricReporter(MeterRegistry meterRegistry) {
    invalidEncodedActiveMtMessageCounter = meterRegistry.counter("mt-router-client", Tags.of(TYPE, CONVERSION_FAILURE));
  }

  public void onInvalidEncodedActiveMtMessage() {
    invalidEncodedActiveMtMessageCounter.increment();
  }
}
