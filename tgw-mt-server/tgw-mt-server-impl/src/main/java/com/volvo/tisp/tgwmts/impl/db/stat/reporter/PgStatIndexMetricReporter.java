package com.volvo.tisp.tgwmts.impl.db.stat.reporter;

import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.stat.IndexName;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class PgStatIndexMetricReporter {
  private static final String INDEX_NAME = "INDEX_NAME";

  private final AtomicInteger amtMtIdDeletedPagesGauge;
  private final AtomicInteger amtPkeyDeletedPagesGauge;
  private final AtomicInteger amtTimeoutDeletedPagesGauge;
  private final AtomicInteger mtPkeyDeletedPagesGauge;
  private final AtomicInteger mtVlockIdDeletedPagesGauge;
  private final AtomicInteger amtMtIdEmptyPagesGauge;
  private final AtomicInteger amtPkeyEmptyPagesGauge;
  private final AtomicInteger amtTimeoutEmptyPagesGauge;
  private final AtomicInteger mtPkeyEmptyPagesGauge;
  private final AtomicInteger mtVlockIdEmptyPagesGauge;
  private final AtomicInteger amtMtIdInternalPagesGauge;
  private final AtomicInteger amtPkeyInternalPagesGauge;
  private final AtomicInteger amtTimeoutInternalPagesGauge;
  private final AtomicInteger mtPkeyInternalPagesGauge;
  private final AtomicInteger mtVlockIdInternalPagesGauge;
  private final AtomicInteger amtMtIdLeafPagesGauge;
  private final AtomicInteger amtPkeyLeafPagesGauge;
  private final AtomicInteger amtTimeoutLeafPagesGauge;
  private final AtomicInteger mtPkeyLeafPagesGauge;
  private final AtomicInteger mtVlockIdLeafPagesGauge;

  public PgStatIndexMetricReporter(MeterRegistry meterRegistry) {
    this.amtMtIdDeletedPagesGauge =
        meterRegistry.gauge("deleted-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY.toString()), new AtomicInteger());
    this.amtPkeyDeletedPagesGauge =
        meterRegistry.gauge("deleted-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.amtTimeoutDeletedPagesGauge =
        meterRegistry.gauge("deleted-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_TIMEOUT.toString()), new AtomicInteger());
    this.mtPkeyDeletedPagesGauge =
        meterRegistry.gauge("deleted-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.mtVlockIdDeletedPagesGauge =
        meterRegistry.gauge("deleted-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED.toString()), new AtomicInteger());

    this.amtMtIdEmptyPagesGauge =
        meterRegistry.gauge("empty-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY.toString()), new AtomicInteger());
    this.amtPkeyEmptyPagesGauge =
        meterRegistry.gauge("empty-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.amtTimeoutEmptyPagesGauge =
        meterRegistry.gauge("empty-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_TIMEOUT.toString()), new AtomicInteger());
    this.mtPkeyEmptyPagesGauge =
        meterRegistry.gauge("empty-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.mtVlockIdEmptyPagesGauge =
        meterRegistry.gauge("empty-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED.toString()), new AtomicInteger());

    this.amtMtIdInternalPagesGauge =
        meterRegistry.gauge("internal-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY.toString()), new AtomicInteger());
    this.amtPkeyInternalPagesGauge =
        meterRegistry.gauge("internal-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.amtTimeoutInternalPagesGauge =
        meterRegistry.gauge("internal-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_TIMEOUT.toString()), new AtomicInteger());
    this.mtPkeyInternalPagesGauge =
        meterRegistry.gauge("internal-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.mtVlockIdInternalPagesGauge =
        meterRegistry.gauge("internal-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED.toString()), new AtomicInteger());

    this.amtMtIdLeafPagesGauge =
        meterRegistry.gauge("leaf-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY.toString()), new AtomicInteger());
    this.amtPkeyLeafPagesGauge =
        meterRegistry.gauge("leaf-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.amtTimeoutLeafPagesGauge =
        meterRegistry.gauge("leaf-pages", Tags.of(INDEX_NAME, IndexName.ACTIVE_MT_MESSAGE_TIMEOUT.toString()), new AtomicInteger());
    this.mtPkeyLeafPagesGauge =
        meterRegistry.gauge("leaf-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_PKEY.toString()), new AtomicInteger());
    this.mtVlockIdLeafPagesGauge =
        meterRegistry.gauge("leaf-pages", Tags.of(INDEX_NAME, IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED.toString()), new AtomicInteger());
  }

  public void onDeletedPages(IndexName indexName, int value) {
    Validate.notNull(indexName, "indexName");

    switch (indexName) {
      case ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY -> amtMtIdDeletedPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_PKEY -> amtPkeyDeletedPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_TIMEOUT -> amtTimeoutDeletedPagesGauge.set(value);
      case MT_MESSAGE_PKEY -> mtPkeyDeletedPagesGauge.set(value);
      case MT_MESSAGE_VEHICLE_LOCK_ID_CREATED -> mtVlockIdDeletedPagesGauge.set(value);
    }
  }

  public void onEmptyPages(IndexName indexName, int value) {
    Validate.notNull(indexName, "indexName");

    switch (indexName) {
      case ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY -> amtMtIdEmptyPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_PKEY -> amtPkeyEmptyPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_TIMEOUT -> amtTimeoutEmptyPagesGauge.set(value);
      case MT_MESSAGE_PKEY -> mtPkeyEmptyPagesGauge.set(value);
      case MT_MESSAGE_VEHICLE_LOCK_ID_CREATED -> mtVlockIdEmptyPagesGauge.set(value);
    }
  }

  public void onInternalPages(IndexName indexName, int value) {
    Validate.notNull(indexName, "indexName");

    switch (indexName) {
      case ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY -> amtMtIdInternalPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_PKEY -> amtPkeyInternalPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_TIMEOUT -> amtTimeoutInternalPagesGauge.set(value);
      case MT_MESSAGE_PKEY -> mtPkeyInternalPagesGauge.set(value);
      case MT_MESSAGE_VEHICLE_LOCK_ID_CREATED -> mtVlockIdInternalPagesGauge.set(value);
    }
  }

  public void onLeafPages(IndexName indexName, int value) {
    Validate.notNull(indexName, "indexName");

    switch (indexName) {
      case ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY -> amtMtIdLeafPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_PKEY -> amtPkeyLeafPagesGauge.set(value);
      case ACTIVE_MT_MESSAGE_TIMEOUT -> amtTimeoutLeafPagesGauge.set(value);
      case MT_MESSAGE_PKEY -> mtPkeyLeafPagesGauge.set(value);
      case MT_MESSAGE_VEHICLE_LOCK_ID_CREATED -> mtVlockIdLeafPagesGauge.set(value);
    }
  }
}
