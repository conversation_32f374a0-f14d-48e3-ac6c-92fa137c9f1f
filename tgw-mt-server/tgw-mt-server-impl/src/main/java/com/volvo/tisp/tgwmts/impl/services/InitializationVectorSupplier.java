package com.volvo.tisp.tgwmts.impl.services;

import java.security.SecureRandom;
import java.util.function.Supplier;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.crypto.common.entity.InitializationVector;

public final class InitializationVectorSupplier implements Supplier<InitializationVector> {
  private final SecureRandom secureRandom;

  private InitializationVectorSupplier(SecureRandom secureRandom) {
    this.secureRandom = secureRandom;
  }

  public static Supplier<InitializationVector> of(SecureRandom secureRandom) {
    Validate.notNull(secureRandom, "secureRandom");

    return new InitializationVectorSupplier(secureRandom);
  }

  @Override
  public InitializationVector get() {
    byte[] nonce = new byte[InitializationVector.IV_LENGTH_IN_BYTES];
    secureRandom.nextBytes(nonce);
    return InitializationVector.create(ImmutableByteArray.of(nonce));
  }
}
