package com.volvo.tisp.tgwmts.impl.conf;

import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record MtMessageDefaultParameters(EnqueueingType enqueueingType, QueueId queueId, SendSchemaName sendSchemaName) {
  public static final EnqueueingType DEFAULT_ENQUEUEING_TYPE = EnqueueingType.NORMAL;
  public static final QueueId DEFAULT_QUEUE_ID = QueueId.ofString("QUEUE_ID_NOT_SET");
  public static final SendSchemaName DEFAULT_SEND_SCHEMA_NAME = SendSchemaName.COMMON_LOW;

  public MtMessageDefaultParameters {
    Validate.notNull(enqueueingType, "enqueueingType");
    Validate.notNull(queueId, "queueId");
    Validate.notNull(sendSchemaName, "sendSchemaName");

  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("enqueueingType=")
        .append(enqueueingType)
        .append(", queueId=")
        .append(queueId)
        .append(", sendSchemaName=")
        .append(sendSchemaName)
        .toString();
  }
}
