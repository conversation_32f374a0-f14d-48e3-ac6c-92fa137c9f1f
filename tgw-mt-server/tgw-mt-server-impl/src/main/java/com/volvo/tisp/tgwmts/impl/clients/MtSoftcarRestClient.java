package com.volvo.tisp.tgwmts.impl.clients;

import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;

import com.volvo.tisp.tce.proto.MtMessage;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtSoftCarReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import reactor.core.publisher.Mono;

public class MtSoftcarRestClient {
  private static final Logger logger = LoggerFactory.getLogger(MtSoftcarRestClient.class);

  private final Function<EncodedActiveMtMessage, MtMessage> mtMessageProtobufOutputConverterFunction;
  private final MtSoftCarReactiveClientProperties mtSoftCarReactiveClientProperties;
  private final WebClient mtSoftcarWebClient;

  public MtSoftcarRestClient(MtSoftCarReactiveClientProperties mtSoftCarReactiveClientProperties, WebClient mtSoftcarWebClient,
      Function<EncodedActiveMtMessage, MtMessage> mtMessageProtobufOutputConverterFunction) {

    this.mtSoftCarReactiveClientProperties = mtSoftCarReactiveClientProperties;
    this.mtSoftcarWebClient = mtSoftcarWebClient;
    this.mtMessageProtobufOutputConverterFunction = mtMessageProtobufOutputConverterFunction;
  }

  private static Mono<Boolean> handleError(Throwable throwable) {
    logger.error("", throwable);
    return Mono.just(false);
  }

  private static Mono<Boolean> handleResponse(ClientResponse clientResponse) {
    if (clientResponse.statusCode().is2xxSuccessful()) {
      return Mono.just(true);
    } else {
      logger.warn("request error: {}", clientResponse.statusCode().value());
      return Mono.just(false);
    }
  }

  public Mono<Boolean> sendMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    Validate.notNull(encodedActiveMtMessage, "encodedActiveMtMessage");

    MtMessage mtMessage = mtMessageProtobufOutputConverterFunction.apply(encodedActiveMtMessage);

    return mtSoftcarWebClient.post()
        .uri(mtSoftCarReactiveClientProperties.getMtSoftCarRequestPath())
        .contentType(MediaType.APPLICATION_PROTOBUF)
        .bodyValue(mtMessage)
        .exchangeToMono(MtSoftcarRestClient::handleResponse)
        .timeout(mtSoftCarReactiveClientProperties.getRequestTimeout())
        .onErrorResume(MtSoftcarRestClient::handleError);
  }
}
