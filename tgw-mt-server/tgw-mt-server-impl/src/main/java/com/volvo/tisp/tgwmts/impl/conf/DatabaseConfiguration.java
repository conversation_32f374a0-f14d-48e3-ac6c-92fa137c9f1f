package com.volvo.tisp.tgwmts.impl.conf;

import java.time.Clock;
import java.time.Duration;

import javax.sql.DataSource;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.ActiveMtMessageReaderFactoryImpl;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.ActiveMtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.ActiveMtMessageWriterFactoryImpl;
import com.volvo.tisp.tgwmts.database.db.activemtmessage.JoinedActiveMtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.mtmessage.JoinedMtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.mtmessage.MtMessageRowMapper;
import com.volvo.tisp.tgwmts.database.db.vehiclelock.VehicleLockRowMapper;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;

@Configuration
class DatabaseConfiguration {
  private static final Logger logger = LoggerFactory.getLogger(DatabaseConfiguration.class);

  @Bean
  ActiveMtMessageReaderFactory createActiveMtMessageReaderFactory(Clock clock, Jdbi jdbi, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration databaseLockTimeout) {
    return ActiveMtMessageReaderFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, databaseLockTimeout);
  }

  @Bean
  ActiveMtMessageWriterFactory createActiveMtMessageWriterFactory(Clock clock, Jdbi jdbi, RowMapper<JoinedActiveMtMessage> joinedActiveMtMessageRowMapper,
      RowMapper<JoinedMtMessage> joinedMtMessageRowMapper, Duration databaseLockTimeout) {
    return ActiveMtMessageWriterFactoryImpl.create(clock, jdbi, joinedActiveMtMessageRowMapper, joinedMtMessageRowMapper, databaseLockTimeout);
  }

  @Bean
  DataSource createDataSource(DatasourceConfigProperties datasourceConfigProperties) {
    return DataSourceFactory.createDataSource(datasourceConfigProperties);
  }

  @Bean
  Duration createDatabaseLockTimeout(@Value("${db.lock-timeout:PT5S}") Duration databaseLockTimeout) {
    logger.info("databaseLockTimeout: {}", databaseLockTimeout);

    return databaseLockTimeout;
  }

  @Bean
  Jdbi createJdbi(DataSource dataSource) {
    return Jdbi.create(dataSource);
  }

  @Bean
  RowMapper<JoinedActiveMtMessage> createJoinedActiveMtMessageRowMapper() {
    return JoinedActiveMtMessageRowMapper.create(ActiveMtMessageRowMapper.INSTANCE, MtMessageRowMapper.INSTANCE, VehicleLockRowMapper.INSTANCE);
  }

  @Bean
  RowMapper<JoinedMtMessage> createJoinedMtMessageRowMapper() {
    return JoinedMtMessageRowMapper.create(ActiveMtMessageRowMapper.INSTANCE, MtMessageRowMapper.INSTANCE, VehicleLockRowMapper.INSTANCE);
  }
}
