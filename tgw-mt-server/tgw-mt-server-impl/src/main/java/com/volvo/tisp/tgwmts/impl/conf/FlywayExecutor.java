package com.volvo.tisp.tgwmts.impl.conf;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.database.database.DatabaseVendor;
import com.volvo.tisp.tgw.device.info.database.database.DeviceServiceFlywayExecutor;
import com.volvo.tisp.tgwmts.database.db.MtDatabaseFlywayExecutor;

@Component
class FlywayExecutor {
  private static final Logger logger = LoggerFactory.getLogger(FlywayExecutor.class);

  private final DataSource dataSource;

  FlywayExecutor(DataSource dataSource) {
    this.dataSource = dataSource;
  }

  @EventListener(ApplicationStartedEvent.class)
  void onApplicationReadyEvent() {
    logger.info("Running flyway database migration");

    DeviceServiceFlywayExecutor.performDatabaseMigration(dataSource, DatabaseVendor.POSTGRESQL);
    MtDatabaseFlywayExecutor.performDatabaseMigration(dataSource);
  }
}
