package com.volvo.tisp.tgwmts.impl.schema.dynamic;

import java.time.Duration;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class SendSchemaNameParameterExtractor {
  public static final Pattern NAME_PATTERN = Pattern.compile(
      "^(ONE|TWO|THREE|FOUR|FIVE|SIX|SEVEN|EIGHT|NINE|TEN|ELEVEN|TWELVE|THIRTY)_(MINUTE|HOUR|DAY|WEEK)S?-(LOW|NORMAL|HIGH|EXTREME)$");
  private static final Logger logger = LoggerFactory.getLogger(SendSchemaNameParameterExtractor.class);

  private static AcceptedCost getAcceptedCost(String acceptedCostString) {
    return switch (acceptedCostString) {
      case "LOW" -> AcceptedCost.LOW;
      case "NORMAL" -> AcceptedCost.NORMAL;
      case "HIGH" -> AcceptedCost.HIGH;
      case "EXTREME" -> AcceptedCost.EXTREME;
      default -> throw new IllegalArgumentException("acceptedCostString not valid: " + acceptedCostString);
    };
  }

  private static Duration getDuration(String numberString, String timeUnitString) {
    int value = getTimeUnitValue(numberString);

    return switch (timeUnitString) {
      case "MINUTE" -> Duration.ofMinutes(value);
      case "HOUR" -> Duration.ofHours(value);
      case "DAY" -> Duration.ofDays(value);
      case "WEEK" -> Duration.ofDays(value * 7L);
      default -> throw new IllegalArgumentException("timeUnitString not valid: " + timeUnitString);
    };
  }

  private static int getTimeUnitValue(String valueString) {
    return switch (valueString) {
      case "ONE" -> 1;
      case "TWO" -> 2;
      case "THREE" -> 3;
      case "FOUR" -> 4;
      case "FIVE" -> 5;
      case "SIX" -> 6;
      case "SEVEN" -> 7;
      case "EIGHT" -> 8;
      case "NINE" -> 9;
      case "TEN" -> 10;
      case "ELEVEN" -> 11;
      case "TWELVE" -> 12;
      case "THIRTY" -> 30;
      default -> throw new IllegalArgumentException("valueString not valid: " + valueString);
    };
  }

  private static Optional<TtlAcceptedCostHolder> getTtlPriorityHolder(Matcher matcher) {
    try {
      return Optional.of(new TtlAcceptedCostHolder(getDuration(matcher.group(1), matcher.group(2)), getAcceptedCost(matcher.group(3))));
    } catch (IllegalArgumentException e) {
      logger.warn("", e);
      return Optional.empty();
    }
  }

  public Optional<TtlAcceptedCostHolder> extract(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    Matcher matcher = NAME_PATTERN.matcher(sendSchemaName.value());

    if (matcher.find()) {
      return getTtlPriorityHolder(matcher);
    }

    return Optional.empty();
  }
}
