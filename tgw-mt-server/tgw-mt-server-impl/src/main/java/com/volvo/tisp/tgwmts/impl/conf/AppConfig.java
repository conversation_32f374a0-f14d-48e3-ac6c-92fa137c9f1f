package com.volvo.tisp.tgwmts.impl.conf;

import java.security.SecureRandom;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.function.BiFunction;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Import;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.web.reactive.function.client.WebClient;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.Scheduler;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;
import com.volvo.tisp.tgwmts.impl.clients.ConnectionEstablishedRegistrationClient;
import com.volvo.tisp.tgwmts.impl.clients.MtRouterReactiveClient;
import com.volvo.tisp.tgwmts.impl.clients.MtSoftcarRestClient;
import com.volvo.tisp.tgwmts.impl.clients.metrics.reporter.ConnectionEstablishedRegistrationReporter;
import com.volvo.tisp.tgwmts.impl.conf.properties.MoServerReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtRouterReactiveClientConfig;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtSoftCarReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.converters.MtMessageInputConverterFunction;
import com.volvo.tisp.tgwmts.impl.converters.MtMessageOutputConverterBiFunction;
import com.volvo.tisp.tgwmts.impl.converters.MtMessageProtobufOutputConverterFunction;
import com.volvo.tisp.tgwmts.impl.converters.MtRouterMessageOutputConverterFunction;
import com.volvo.tisp.tgwmts.impl.converters.MtStatusMessageInputConverterFunction;
import com.volvo.tisp.tgwmts.impl.converters.MtStatusMessageOutputConverter;
import com.volvo.tisp.tgwmts.impl.converters.SchedulerHintConverter;
import com.volvo.tisp.tgwmts.impl.db.queue.reporter.MtMessageQueueReporter;
import com.volvo.tisp.tgwmts.impl.db.queue.reporter.MtMessageQueueReporterTask;
import com.volvo.tisp.tgwmts.impl.db.stat.reporter.PgStatReporter;
import com.volvo.tisp.tgwmts.impl.db.stat.reporter.PgStatReporterTask;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.integration.logging.LoggingHelper;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.scheduler.MtSchedulerContext;
import com.volvo.tisp.tgwmts.impl.scheduler.MtSchedulerMetricReporter;
import com.volvo.tisp.tgwmts.impl.scheduler.MtSchedulerRunnable;
import com.volvo.tisp.tgwmts.impl.scheduler.MtSchedulerWorker;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.services.EncryptionProcessor;
import com.volvo.tisp.tgwmts.impl.services.EnqueueingTypeProcessor;
import com.volvo.tisp.tgwmts.impl.services.InitializationVectorSupplier;
import com.volvo.tisp.tgwmts.impl.services.MtMessageRetryHandler;
import com.volvo.tisp.tgwmts.impl.services.MtRetrySchedulerService;
import com.volvo.tisp.tgwmts.impl.services.SendSchemaProcessor;
import com.volvo.tisp.tgwmts.impl.services.SrpEncoderFunction;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.SrpEncoderMetricReporter;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeper;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeperMetricReporter;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeperToggle;
import com.volvo.tisp.tgwmts.impl.utils.Throttler;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.converter.SrpConverter;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.Priority;
import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionService;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;
import com.wirelesscar.tce.api.v2.MtMessage;

import io.netty.channel.ChannelOption;
import io.netty.handler.timeout.ReadTimeoutHandler;
import io.netty.handler.timeout.WriteTimeoutHandler;
import nl.talsmasoftware.context.executors.ContextAwareExecutorService;
import reactor.netty.Connection;
import reactor.netty.http.client.HttpClient;
import reactor.util.Loggers;

@EnableScheduling
@SpringBootApplication
@Import({
    com.volvo.tisp.vc.device.bulk.sync.config.AppConfig.class
})
@ComponentScan(basePackages = {"com.volvo.tisp.tgwmts",
                               "com.volvo.tisp.tgw.device.info.conrepo.activation.notify",
                               "com.volvo.tisp.vc.uncaught.exception.handler",
                               "com.volvo.tisp.vc.device.bulk.sync"})
public class AppConfig {
  private static final Logger logger = LoggerFactory.getLogger(AppConfig.class);

  public static void main(String[] args) {
    SpringApplication.run(AppConfig.class);
  }

  private static Consumer<Connection> addHandlers(MoServerReactiveClientProperties moServerReactiveClientProperties) {
    return conn -> conn
        .addHandlerFirst(new ReadTimeoutHandler(moServerReactiveClientProperties.getRequestTimeout().toMillis(), TimeUnit.MILLISECONDS))
        .addHandlerLast(new WriteTimeoutHandler(moServerReactiveClientProperties.getRequestTimeout().toMillis(), TimeUnit.MILLISECONDS));
  }

  private static WebClient createMoServerWebClient(WebClient.Builder webClientBuilder, MoServerReactiveClientProperties moServerReactiveClientProperties) {
    logger.info("clientConfigProperties: {}", moServerReactiveClientProperties);

    HttpClient httpClient = HttpClient.create()
        .keepAlive(true)
        .responseTimeout(moServerReactiveClientProperties.getRequestTimeout())
        .option(ChannelOption.CONNECT_TIMEOUT_MILLIS, (int) moServerReactiveClientProperties.getConnectionTimeout().toMillis())
        .doOnConnected(addHandlers(moServerReactiveClientProperties))
        .wiretap(Loggers.getLogger(HttpClient.class.getPackageName()).isDebugEnabled());

    return webClientBuilder
        .baseUrl(moServerReactiveClientProperties.getBaseUrl().toString())
        .clientConnector(new ReactorClientHttpConnector(httpClient))
        .build();
  }

  @Bean
  public Cache<Vpi, Instant> connectionEstablishedRegistrationCache(
      @Value("${cache.connection-established-registration.max-size:100000}") int maxSize,
      @Value("${cache.connection-established-registration.expire-timeout:PT10M}") Duration expiryTimeout) {

    return Caffeine.newBuilder()
        .expireAfterWrite(expiryTimeout)
        .scheduler(Scheduler.systemScheduler())
        .maximumSize(maxSize)
        .build();
  }

  @Bean
  Clock createClock() {
    return Clock.systemUTC();
  }

  @Bean
  ConnectionEstablishedRegistrationClient createConnectionEstablishedRegistrationReactiveClient(Clock clock, WebClient.Builder webClientBuilder,
      ConnectionEstablishedRegistrationReporter connectionEstablishedRegistrationReporter,
      ConnectionEstablishedCacheService connectionEstablishedCacheService,
      MoServerReactiveClientProperties moServerReactiveClientProperties) {
    logger.info("moServerReactiveClientProperties: {}", moServerReactiveClientProperties);

    return new ConnectionEstablishedRegistrationClient(clock, createMoServerWebClient(webClientBuilder, moServerReactiveClientProperties),
        connectionEstablishedRegistrationReporter, moServerReactiveClientProperties, connectionEstablishedCacheService);
  }

  @Bean
  Function<ServiceRoutingPduWrapper, ImmutableByteArray> createEncodeServiceRoutingPduWrapperFunction() {
    return SrpConverter::encodeServiceRoutingPduWrapper;
  }

  @Bean
  EnqueueingTypeProcessor createEnqueueingTypeProcessor(@Value("${mt.max-in-flight-messages-per-vehicle:5}") int maxNumberOfInFlightMessagesPerVehicle,
      MtMessageMetricReporter mtMessageMetricReporter, MtStatusPublisher mtStatusPublisher) {

    logger.info("maxNumberOfInFlightMessagesPerVehicle: {}", maxNumberOfInFlightMessagesPerVehicle);
    return new EnqueueingTypeProcessor(maxNumberOfInFlightMessagesPerVehicle, mtMessageMetricReporter, mtStatusPublisher);
  }

  @Bean
  Supplier<InitializationVector> createInitializationVectorSupplier() {
    return InitializationVectorSupplier.of(new SecureRandom());
  }

  @Bean
  Consumer<IntegrationLogParameter> createLoggingHelper() {
    return LoggingHelper.INSTANCE;
  }

  @Bean
  MtDoorkeeper createMtDoorkeeper(@Value("${mt.doorkeeper.threshold:100}") int mtDoorkeeperThreshold, Clock clock,
      MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter, MtDoorkeeperToggle mtDoorkeeperToggle) {

    logger.info("threshold for mtDoorkeeper: {}", mtDoorkeeperThreshold);
    return new MtDoorkeeper(clock, mtDoorkeeperMetricReporter, mtDoorkeeperToggle, mtDoorkeeperThreshold);
  }

  @Bean
  MtDoorkeeperToggle createMtDoorkeeperToggle(MtDoorkeeperMetricReporter mtDoorkeeperMetricReporter,
      @Value("${mt.doorkeeper.toggle-value:true}") boolean mtDoorkeeperToggleValue) {
    logger.info("mtDoorkeeperToggleValue: {}", mtDoorkeeperToggleValue);

    return MtDoorkeeperToggle.create(mtDoorkeeperMetricReporter::onMtDoorkeeperToggle, mtDoorkeeperToggleValue);
  }

  @Bean
  MtMessageDefaultParameters createMtMessageDefaultParameters(@Value("${mt.default-enqueueing-type:}") String enqueueingTypeString,
      @Value("${mt.default-queue-id:}") String queueIdString, @Value("${mt.default-send-schema-name:}") String sendSchemaNameString) {

    MtMessageDefaultParameters mtMessageDefaultParameters = new MtMessageDefaultParameters(getDefaultEnqueueingType(enqueueingTypeString),
        getDefaultQueueId(queueIdString), getDefaultSendSchemaName(sendSchemaNameString));

    logger.info("mtMessageDefaultParameters: {}", mtMessageDefaultParameters);
    return mtMessageDefaultParameters;
  }

  private static SendSchemaName getDefaultSendSchemaName(String sendSchemaNameString) {
    if (sendSchemaNameString != null && !sendSchemaNameString.isEmpty())
      return new SendSchemaName(sendSchemaNameString);
    return MtMessageDefaultParameters.DEFAULT_SEND_SCHEMA_NAME;
  }

  private static QueueId getDefaultQueueId(String queueIdString) {
    if (queueIdString != null && !queueIdString.isEmpty())
      return QueueId.ofString(queueIdString);
    return MtMessageDefaultParameters.DEFAULT_QUEUE_ID;
  }

  private static EnqueueingType getDefaultEnqueueingType(String enqueueingTypeString) {
    if (enqueueingTypeString != null && !enqueueingTypeString.isEmpty())
      return EnqueueingType.valueOf(enqueueingTypeString);
    return EnqueueingType.NORMAL;
  }

  @Bean
  Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> createMtMessageInputConverterFunction(
      MtMessageDefaultParameters mtMessageDefaultParameters) {
    return MtMessageInputConverterFunction.create(SchedulerHintConverter.INSTANCE, mtMessageDefaultParameters);
  }

  @Bean
  BiFunction<ReceivedMtMessage, VehicleLockId, com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage> createMtMessageOutputConverterBiFunction() {
    return MtMessageOutputConverterBiFunction.INSTANCE;
  }

  @Bean
  Function<EncodedActiveMtMessage, com.volvo.tisp.tce.proto.MtMessage> createMtMessageProtobufOutputConverterFunction(
      Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction) {
    return MtMessageProtobufOutputConverterFunction.create(encodeServiceRoutingPduWrapperFunction);
  }

  @Bean
  MtMessageQueueReporter createMtMessageQueueReporter(@Value("${mt.message.db.report-interval:PT10S}") Duration reportDuration,
      MtMessageQueueReporterTask mtMessageQueueReporterTask) {
    logger.info("reportDuration for db queue is: {}", reportDuration);
    return new MtMessageQueueReporter(mtMessageQueueReporterTask, reportDuration);
  }

  @Bean
  MtRetrySchedulerService createMtRetrySchedulerService(@Value("${mt-retry-scheduler-delay-millis:300}") long delayMillis,
      MtMessageRetryHandler mtMessageRetryHandler) {
    return new MtRetrySchedulerService(delayMillis, mtMessageRetryHandler, Executors.newSingleThreadScheduledExecutor());
  }

  @Bean
  MtRouterMessageOutputConverterFunction createMtRouterMessageOutputConverterFunction(
      Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction) {
    return MtRouterMessageOutputConverterFunction.create(encodeServiceRoutingPduWrapperFunction);
  }

  @Bean
  MtStatusMessageInputConverterFunction createMtRouterMtStatusMessageInputConverterFunction() {
    return MtStatusMessageInputConverterFunction.INSTANCE;
  }

  @Bean
  MtRouterReactiveClient createMtRouterReactiveClient(MtRouterReactiveClientConfig mtRouterReactiveClientConfig, WebClient.Builder webClientBuilder) {
    logger.info("mtRouterReactiveClientConfig: {}", mtRouterReactiveClientConfig);

    return new MtRouterReactiveClient(mtRouterReactiveClientConfig, webClientBuilder.baseUrl(mtRouterReactiveClientConfig.getBaseUrl().toString()).build());
  }

  @Bean
  MtSchedulerContext createMtSchedulerContext(@Value("${scheduler.number-of-threads:4}") int schedulerNumberOfThreads,
      MtSchedulerRunnable mtSchedulerRunnable) {
    logger.info("scheduler numberOfThreads: {}", schedulerNumberOfThreads);

    Collection<Thread> threads = new ArrayList<>(schedulerNumberOfThreads);

    for (int i = 0; i < schedulerNumberOfThreads; ++i) {
      Thread thread = new Thread(mtSchedulerRunnable);
      thread.setName("MtSchedulerRunnable-" + i);
      threads.add(thread);
    }

    return new MtSchedulerContext(List.copyOf(threads));
  }

  @Bean
  MtSchedulerRunnable createMtSchedulerRunnable(MtSchedulerWorker mtSchedulerWorker, Throttler throttler) {

    return new MtSchedulerRunnable(mtSchedulerWorker, throttler);
  }

  @Bean
  MtSchedulerWorker createMtSchedulerWorker(ActiveMtMessageWriterFactory activeMtMessageWriterFactory, Clock clock,
      MtSchedulerMetricReporter mtSchedulerMetricReporter, SendSchemaProcessor sendSchemaProcessor,
      @Value("${scheduler.batch-size:1}") int schedulerBatchSize) {
    logger.info("scheduler batchSize: {}", schedulerBatchSize);

    return new MtSchedulerWorker(activeMtMessageWriterFactory, schedulerBatchSize, clock, mtSchedulerMetricReporter, sendSchemaProcessor);
  }

  @Bean
  MtSoftcarRestClient createMtSoftcarRestClient(MtSoftCarReactiveClientProperties mtSoftCarReactiveClientProperties, WebClient.Builder webClientBuilder,
      Function<EncodedActiveMtMessage, com.volvo.tisp.tce.proto.MtMessage> mtMessageProtobufOutputConverterFunction) {
    logger.info("mtSoftCarReactiveClientProperties: {}", mtSoftCarReactiveClientProperties);

    return new MtSoftcarRestClient(mtSoftCarReactiveClientProperties,
        webClientBuilder.baseUrl(mtSoftCarReactiveClientProperties.getBaseUrl().toString()).build(), mtMessageProtobufOutputConverterFunction);
  }

  @Bean
  MtStatusPublisher createMtStatusPublisher(
      Clock clock,
      JmsTemplate jmsTemplate,
      MtMessageMetricReporter mtMessageMetricReporter,
      MtStatusMessageOutputConverter mtStatusMessageOutputConverter) {
    ExecutorService executorService = new ContextAwareExecutorService(Executors.newSingleThreadExecutor());
    return new MtStatusPublisher(clock, executorService, jmsTemplate, mtMessageMetricReporter, mtStatusMessageOutputConverter);
  }

  @Bean
  @ConditionalOnProperty(name = "pg-stat-reporting.enabled:false")
  PgStatReporter createPgStatReporter(@Value("${pg-stat-report-interval:PT1H}") Duration reportInterval, PgStatReporterTask pgStatReporterTask) {
    logger.info("pg-stat-reporting enabled with reportInterval {}", reportInterval);

    return new PgStatReporter(reportInterval, pgStatReporterTask);
  }

  @Bean
  Cache<SendSchemaName, SendSchema> createSendSchemaCache(@Value("${send-schema-cache.max-size:500}") int maxSize) {
    logger.info("sendSchemaCacheMaxSize: {}", maxSize);

    return Caffeine.newBuilder()
        .maximumSize(maxSize)
        .build();
  }

  @Bean
  Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> createSrpEncoderFunction(Clock clock, EncryptionProcessor encryptionProcessor,
      SrpEncoderMetricReporter srpEncoderMetricReporter, @Value("${mt.default-srp-priority:1}") Integer mtDefaultSrpPriority) {
    Priority defaultSrpPriority = Priority.ofShort(mtDefaultSrpPriority.shortValue());
    logger.info("defaultSrpPriority: {}", defaultSrpPriority);

    return SrpEncoderFunction.create(clock, encryptionProcessor, defaultSrpPriority, srpEncoderMetricReporter);
  }

  @Bean
  SymmetricAesGcmEncryptionService createSymmetricAesGcmEncryptionService() {
    return SymmetricAesGcmEncryptionServiceImpl.INSTANCE;
  }

  @Bean
  Throttler createThrottler(@Value("${scheduler.throttler.duration:PT0.1S}") Duration duration, @Value("${scheduler.throttler.limit:1}") int limit) {
    logger.info("duration: {}, limit: {}", duration, limit);

    return Throttler.create(duration, limit);
  }
}
