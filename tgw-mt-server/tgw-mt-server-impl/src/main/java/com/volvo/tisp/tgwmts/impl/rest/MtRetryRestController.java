package com.volvo.tisp.tgwmts.impl.rest;

import java.util.List;
import java.util.Locale;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.tgwmts.impl.services.MtRetrySchedulerService;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@RestController
@RequestMapping(path = MtRetryRestController.REQUEST_MAPPING_PATH)
@Authentication(required = false)
public class MtRetryRestController {
  public static final String REQUEST_MAPPING_PATH = "/api/v1/mt-retry";

  private final MtRetrySchedulerService mtRetrySchedulerService;

  public MtRetryRestController(MtRetrySchedulerService mtRetrySchedulerService) {
    this.mtRetrySchedulerService = mtRetrySchedulerService;
  }

  @PostMapping
  public ResponseEntity<String> attemptRetry(@RequestBody List<String> vpiStrings) {
    Validate.notEmpty(vpiStrings, "vpiStrings must not be null");

    List<Vpi> vpis = vpiStrings.stream().map(Vpi::ofString).distinct().toList();
    mtRetrySchedulerService.scheduleRetryTasks(vpis);

    return ResponseEntity.ok(String.format(Locale.ROOT, "Attempting to resend MT-messages in WAIT state for %d vehicle(s)", vpis.size()));
  }
}
