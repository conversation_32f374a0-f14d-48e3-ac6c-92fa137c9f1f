package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class SatOnlySendSchema implements SendSchema {
  public static final SendSchema INSTANCE = new SatOnlySendSchema();
  private static final Duration GLOBAL_TIMEOUT = Duration.ofHours(1);
  private static final SendSchemaName SEND_SCHEMA_NAME = SendSchemaName.SAT_ONLY;

  private static final SendSchemaStep SEND_SCHEMA_STEP_1 = SendSchemaStep.forSat(SendSchemaStepId.ofInt(1));

  @Override
  public Duration getGlobalTimeout() {
    return GLOBAL_TIMEOUT;
  }

  @Override
  public short getMaxRetryAttempts() {
    return 1;
  }

  @Override
  public SendSchemaName getSendSchemaName() {
    return SEND_SCHEMA_NAME;
  }

  @Override
  public Optional<SendSchemaStep> getSendSchemaStep(SendSchemaStepId sendSchemaStepId) {
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");

    return switch (sendSchemaStepId.toInt()) {
      case 1 -> Optional.of(SEND_SCHEMA_STEP_1);
      default -> Optional.empty();
    };
  }
}
