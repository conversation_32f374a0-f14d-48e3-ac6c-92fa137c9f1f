package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class SendSchemaProcessorMetricReporter {
  private static final String SEND_SCHEMA_PROCESSOR = "send-schema-processor";
  private static final String TYPE = "type";

  private final Counter deviceNotFoundCounter;
  private final Counter failureCounter;
  private final Counter globalTimeoutCounter;
  private final Counter stepsExhaustedCounter;
  private final Counter successCounter;
  private final Counter timeoutCounter;

  public SendSchemaProcessorMetricReporter(MeterRegistry meterRegistry) {
    deviceNotFoundCounter = meterRegistry.counter(SEND_SCHEMA_PROCESSOR, TYPE, "DEVICE_NOT_FOUND");
    failureCounter = meterRegistry.counter(SEND_SCHEMA_PROCESSOR, TYPE, "FAILURE");
    globalTimeoutCounter = meterRegistry.counter(SEND_SCHEMA_PROCESSOR, TYPE, "GLOBAL_TIMEOUT");
    stepsExhaustedCounter = meterRegistry.counter(SEND_SCHEMA_PROCESSOR, TYPE, "STEPS_EXHAUSTED");
    successCounter = meterRegistry.counter(SEND_SCHEMA_PROCESSOR, TYPE, "SUCCESS");
    timeoutCounter = meterRegistry.counter(SEND_SCHEMA_PROCESSOR, TYPE, "TIMEOUT");
  }

  public void onDeviceNotFound() {
    deviceNotFoundCounter.increment();
  }

  public void onFailure() {
    failureCounter.increment();
  }

  public void onGlobalTimeout() {
    globalTimeoutCounter.increment();
  }

  public void onStepsExhausted() {
    stepsExhaustedCounter.increment();
  }

  public void onSuccess() {
    successCounter.increment();
  }

  public void onTimeout() {
    timeoutCounter.increment();
  }
}
