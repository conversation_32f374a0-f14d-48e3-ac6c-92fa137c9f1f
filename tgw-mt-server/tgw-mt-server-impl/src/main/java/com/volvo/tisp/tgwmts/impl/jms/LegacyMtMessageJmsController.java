package com.volvo.tisp.tgwmts.impl.jms;

import java.util.function.Consumer;
import java.util.function.Function;

import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;

import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.tce.api.v2.MtMessage;

@JmsController(destination = "${legacy.jms.mt.queue.name}")
@ConditionalOnExpression(value = "${mtschd.mt.queue.reuse:false}")
public class LegacyMtMessageJmsController extends MtMessageJmsController {
  public LegacyMtMessageJmsController(Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction,
      MtMessageMetricReporter mtMessageMetricReporter, Consumer<ReceivedMtMessage> receivedMtMessageConsumer) {
    super(mtMessageInputConverterFunction, mtMessageMetricReporter, receivedMtMessageConsumer);
  }
}
