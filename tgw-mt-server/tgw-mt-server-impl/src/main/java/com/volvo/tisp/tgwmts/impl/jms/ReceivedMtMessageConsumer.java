package com.volvo.tisp.tgwmts.impl.jms;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameterBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationMessage;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaData;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaDataBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.VehicleDetail;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.tgwmts.impl.services.EnqueueingTypeProcessor;
import com.volvo.tisp.tgwmts.impl.services.MtPersister;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.componentbase.logging.Logging;

@Component
public class ReceivedMtMessageConsumer implements Consumer<ReceivedMtMessage> {
  private static final Logger logger = LoggerFactory.getLogger(ReceivedMtMessageConsumer.class);

  private final ActiveMtMessageWriterFactory activeMtMessageWriterFactory;
  private final Clock clock;
  private final MtMessageMetricReporter mtMessageMetricReporter;
  private final MtMessagePersistStack mtMessagePersistStack;
  private final MtStatusPublisher mtStatusPublisher;

  public ReceivedMtMessageConsumer(ActiveMtMessageWriterFactory activeMtMessageWriterFactory, Clock clock,
      MtMessageMetricReporter mtMessageMetricReporter, MtMessagePersistStack mtMessagePersistStack, MtStatusPublisher mtStatusPublisher) {
    this.clock = clock;
    this.activeMtMessageWriterFactory = activeMtMessageWriterFactory;
    this.mtMessageMetricReporter = mtMessageMetricReporter;
    this.mtMessagePersistStack = mtMessagePersistStack;
    this.mtStatusPublisher = mtStatusPublisher;
  }

  private static IntegrationLogParameter createIntegrationLogParameter(ReceivedMtMessage receivedMtMessage) {
    return new IntegrationLogParameterBuilder()
        .setDirection(Logging.Direction.CLIENT_IN)
        .setIntegrationMessage(IntegrationMessage.onDoorkeeper())
        .setMetaData(createMetaData(receivedMtMessage))
        .setStatus(Logging.Status.FAILED)
        .setVehicleDetail(VehicleDetail.create(receivedMtMessage.getVpi()))
        .build();
  }

  private static MetaData createMetaData(ReceivedMtMessage receivedMtMessage) {
    SrpOption srpOption = receivedMtMessage.getSrpOption();

    return new MetaDataBuilder()
        .setMobileDirection("MT")
        .setSendSchemaName(receivedMtMessage.getSendSchemaName())
        .setSrpDestinationService(srpOption.getSrpDestinationService())
        .setSrpDestinationVersion(srpOption.getSrpDestinationVersion())
        .build();
  }

  @Override
  public void accept(ReceivedMtMessage receivedMtMessage) {
    Validate.notNull(receivedMtMessage, "receivedMtMessage");

    Instant startTime = clock.instant();
    mtMessagePersistStack.getTgwIdentifierFunction()
        .apply(receivedMtMessage.getVpi())
        .ifPresent(persistedDeviceInfo -> processIdentifiedMtMessage(receivedMtMessage, startTime));
  }

  private VehicleLockId getVehicleLockId(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi) {
    return mtMessagePersistStack.getVehicleLockIdProvider().insertVehicleLockIfMissingAndAcquireVehicleLockId(Set.of(vpi), activeMtMessageWriter).get(0);
  }

  private void persistMtMessageAndActiveMtMessageIfNeeded(ReceivedMtMessage receivedMtMessage, ActiveMtMessageWriter activeMtMessageWriter,
      VehicleLockId vehicleLockId) {
    MtPersister mtPersister = mtMessagePersistStack.getMtPersister();
    MtMessageId mtMessageId = mtPersister.insertMtMessage(vehicleLockId, receivedMtMessage, activeMtMessageWriter);

    if (mtMessagePersistStack.getEnqueueingTypeProcessor().shouldActiveMtMessageBeCreated(vehicleLockId, receivedMtMessage, activeMtMessageWriter)) {
      mtPersister.insertActiveMtMessage(mtMessageId, activeMtMessageWriter);
    }
  }

  private void processDiscardedMessage(ReceivedMtMessage receivedMtMessage) {
    Optional<ReplyOption> optional = receivedMtMessage.getReplyOption();

    mtMessagePersistStack.getLoggingHelper().accept(createIntegrationLogParameter(receivedMtMessage));

    if (optional.isPresent()) {
      mtStatusPublisher.publishMtStatus(MtStatus.FAILED, optional.get(), receivedMtMessage.getVpi());
      mtMessageMetricReporter.onDiscardedMtMessageWithReply();
    } else {
      logger.debug("The mt message {} is discarded and no mt status is sent out since no reply option is configured in the message", receivedMtMessage);
      mtMessageMetricReporter.onDiscardedMtMessageWithoutReply();
    }
  }

  private void processIdentifiedMtMessage(ReceivedMtMessage receivedMtMessage, Instant startTime) {
    try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
      activeMtMessageWriter.startTransactionWithLockTimeout();

      VehicleLockId vehicleLockId = getVehicleLockId(activeMtMessageWriter, receivedMtMessage.getVpi());
      EnqueueingTypeProcessor enqueueingTypeProcessor = mtMessagePersistStack.getEnqueueingTypeProcessor();
      mtMessageMetricReporter.onEnqueueingType(receivedMtMessage.getEnqueueingType(), receivedMtMessage.getSrpOption().getSrpDestinationService());

      if (enqueueingTypeProcessor.shouldMessageBeIgnored(vehicleLockId, receivedMtMessage, activeMtMessageWriter)) {
        activeMtMessageWriter.rollbackTransaction();
        return;
      }

      enqueueingTypeProcessor.deleteMtMessagesInTheQueueForEnqueueingTypeContainsOverride(receivedMtMessage, activeMtMessageWriter);

      if (mtMessagePersistStack.getMtDoorkeeper().shouldMessageBeDiscarded(vehicleLockId, activeMtMessageWriter)) {
        processDiscardedMessage(receivedMtMessage);
        activeMtMessageWriter.rollbackTransaction();
        return;
      }

      persistMtMessageAndActiveMtMessageIfNeeded(receivedMtMessage, activeMtMessageWriter, vehicleLockId);
      mtMessageMetricReporter.onSuccess(Duration.between(startTime, clock.instant()));
      activeMtMessageWriter.commitTransaction();
    }
  }
}
