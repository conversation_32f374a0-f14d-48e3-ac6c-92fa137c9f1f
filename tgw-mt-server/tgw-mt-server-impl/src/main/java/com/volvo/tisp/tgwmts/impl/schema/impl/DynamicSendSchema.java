package com.volvo.tisp.tgwmts.impl.schema.impl;

import java.time.Duration;
import java.util.List;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class DynamicSendSchema implements SendSchema {
  private static final short DEFAULT_MAX_RETRY_ATTEMPTS = 5;

  private final Duration globalTimeout;
  private final short maxRetryAttempts;
  private final SendSchemaName sendSchemaName;
  private final List<SendSchemaStep> sendSchemaSteps;

  private DynamicSendSchema(Duration globalTimeout, SendSchemaName sendSchemaName, List<SendSchemaStep> sendSchemaSteps, short maxRetryAttempts) {
    Validate.isPositive(globalTimeout, "globalTimeout");
    Validate.notNull(sendSchemaName, "sendSchemaName");
    Validate.notNull(sendSchemaSteps, "sendSchemaSteps");
    Validate.isPositive(maxRetryAttempts, "maxRetryAttempts");

    this.globalTimeout = globalTimeout;
    this.sendSchemaName = sendSchemaName;
    this.sendSchemaSteps = sendSchemaSteps;
    this.maxRetryAttempts = maxRetryAttempts;
  }

  public static SendSchema create(Duration globalTimeout, SendSchemaName sendSchemaName, List<SendSchemaStep> sendSchemaSteps) {
    return new DynamicSendSchema(globalTimeout, sendSchemaName, sendSchemaSteps, DEFAULT_MAX_RETRY_ATTEMPTS);
  }

  public static SendSchema create(Duration globalTimeout, SendSchemaName sendSchemaName, List<SendSchemaStep> sendSchemaSteps, short maxRetryAttempts) {
    return new DynamicSendSchema(globalTimeout, sendSchemaName, sendSchemaSteps, maxRetryAttempts);
  }

  @Override
  public Duration getGlobalTimeout() {
    return globalTimeout;
  }

  @Override
  public short getMaxRetryAttempts() {
    return maxRetryAttempts;
  }

  @Override
  public SendSchemaName getSendSchemaName() {
    return sendSchemaName;
  }

  @Override
  public Optional<SendSchemaStep> getSendSchemaStep(SendSchemaStepId sendSchemaStepId) {
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");

    int currentIndex = sendSchemaStepId.toInt() - 1;
    if (currentIndex < sendSchemaSteps.size() && currentIndex >= 0) {
      return Optional.of(sendSchemaSteps.get(currentIndex));
    }

    return Optional.empty();
  }

  @Override
  public String toString() {
    StringBuilder stringBuilder = new StringBuilder(300)
        .append("globalTimeout=")
        .append(globalTimeout)
        .append(", maxRetryAttempts=")
        .append(maxRetryAttempts)
        .append(", sendSchemaName: ")
        .append(sendSchemaName)
        .append(", sendSchemaSteps:")
        .append(System.lineSeparator());

    sendSchemaSteps.forEach(step -> stringBuilder.append(step).append(System.lineSeparator()));
    return stringBuilder.toString();
  }
}