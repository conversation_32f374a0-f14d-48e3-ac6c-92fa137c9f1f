package com.volvo.tisp.tgwmts.impl.db.queue.reporter;

import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class MtMessageQueueMetricReporter {
  private static final String DB_QUEUE_SIZE = "db.queue.size";
  private static final String TAG_TABLE = "table";

  private final AtomicInteger activeMtDbQueueSizeGauge;
  private final AtomicInteger mtDbQueueSizeGauge;

  public MtMessageQueueMetricReporter(MeterRegistry meterRegistry) {
    this.mtDbQueueSizeGauge = meterRegistry.gauge(DB_QUEUE_SIZE, Tags.of(TAG_TABLE, "MT-MESSAGE"), new AtomicInteger(0));
    this.activeMtDbQueueSizeGauge = meterRegistry.gauge(DB_QUEUE_SIZE, Tags.of(TAG_TABLE, "ACTIVE-MT-MESSAGE"), new AtomicInteger(0));
  }

  public void activeMtDbQueueSize(int queueSize) {
    Validate.notNegative(queueSize, "queueSize");

    activeMtDbQueueSizeGauge.set(queueSize);
  }

  public void mtDbQueueSize(int queueSize) {
    Validate.notNegative(queueSize, "queueSize");

    mtDbQueueSizeGauge.set(queueSize);
  }
}
