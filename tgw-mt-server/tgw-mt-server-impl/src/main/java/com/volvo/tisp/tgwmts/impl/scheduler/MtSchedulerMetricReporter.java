package com.volvo.tisp.tgwmts.impl.scheduler;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;

@Component
public class MtSchedulerMetricReporter {
  private final Timer timedOutMessagesSelectedTimer;
  private final Timer timedOutMessagesTotalTimer;

  public MtSchedulerMetricReporter(MeterRegistry meterRegistry) {
    timedOutMessagesSelectedTimer = meterRegistry.timer("scheduler.timed-out-messages-selected");
    timedOutMessagesTotalTimer = meterRegistry.timer("scheduler.timed-out-messages-total");
  }

  public void onTimedOutMessagesSelected(Duration duration) {
    Validate.notNegative(duration, "duration");

    timedOutMessagesSelectedTimer.record(duration);
  }

  public void onTimedOutMessagesTotal(Duration duration) {
    Validate.notNegative(duration, "duration");

    timedOutMessagesTotalTimer.record(duration);
  }
}
