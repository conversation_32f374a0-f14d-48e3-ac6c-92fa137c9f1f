package com.volvo.tisp.tgwmts.impl.conf;

import java.time.Duration;
import java.util.Objects;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class DatasourceConfigProperties {
  private final String dbUrl;
  private final String dbUsername;
  private final String dbPassword;
  private final int dbMaxPoolSize;
  private final int dbMinPoolSize;
  private final Duration dbLeakDetectionThresholdDuration;

  public DatasourceConfigProperties(
      @Value("${db.url}") String dbUrl,
      @Value("${db.user}") String dbUsername,
      @Value("${db.pw:}") String dbPassword,
      @Value("${db.maxpoolsize:50}") int dbMaxPoolSize,
      @Value("${db.minpoolsize:15}") int dbMinPoolSize,
      @Value("${db.leak-detection-threshold-duration:PT0S}") Duration dbLeakDetectionThresholdDuration) {
    Validate.notNull(dbUrl, "dbUrl");
    Validate.notNull(dbUsername, "dbUsername");
    Validate.notNull(dbPassword, "dbPassword");
    Validate.isPositive(dbMaxPoolSize, "dbMaxPoolSize");
    Validate.isPositive(dbMinPoolSize, "dbMinPoolSize");
    Validate.notNull(dbLeakDetectionThresholdDuration, "dbLeakDetectionThresholdDuration");

    this.dbUrl = dbUrl;
    this.dbUsername = dbUsername;
    this.dbPassword = dbPassword;
    this.dbMaxPoolSize = dbMaxPoolSize;
    this.dbMinPoolSize = dbMinPoolSize;
    this.dbLeakDetectionThresholdDuration = dbLeakDetectionThresholdDuration;
  }

  public String getDbUrl() {
    return dbUrl;
  }

  public String getDbUsername() {
    return dbUsername;
  }

  public String getDbPassword() {
    return dbPassword;
  }

  public int getDbMaxPoolSize() {
    return dbMaxPoolSize;
  }

  public int getDbMinPoolSize() {
    return dbMinPoolSize;
  }

  public Duration getDbLeakDetectionThresholdDuration() {
    return dbLeakDetectionThresholdDuration;
  }

  @Override
  public boolean equals(Object obj) {
    if (obj == this)
      return true;
    if (obj == null || obj.getClass() != this.getClass())
      return false;
    var that = (DatasourceConfigProperties) obj;
    return Objects.equals(this.dbUrl, that.dbUrl) &&
        Objects.equals(this.dbUsername, that.dbUsername) &&
        Objects.equals(this.dbPassword, that.dbPassword) &&
        this.dbMaxPoolSize == that.dbMaxPoolSize &&
        this.dbMinPoolSize == that.dbMinPoolSize &&
        Objects.equals(this.dbLeakDetectionThresholdDuration, that.dbLeakDetectionThresholdDuration);
  }

  @Override
  public int hashCode() {
    return Objects.hash(dbUrl, dbUsername, dbPassword, dbMaxPoolSize, dbMinPoolSize, dbLeakDetectionThresholdDuration);
  }

  @Override
  public String toString() {
    return "DatasourceConfigProperties[" +
        "dbUrl=" + dbUrl + ", " +
        "dbUsername=" + dbUsername + ", " +
        "dbPassword=" + dbPassword + ", " +
        "dbMaxPoolSize=" + dbMaxPoolSize + ", " +
        "dbMinPoolSize=" + dbMinPoolSize + ", " +
        "dbLeakDetectionThresholdDuration=" + dbLeakDetectionThresholdDuration + ']';
  }
}
