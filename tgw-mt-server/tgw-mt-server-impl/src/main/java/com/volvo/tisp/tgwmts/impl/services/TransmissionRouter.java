package com.volvo.tisp.tgwmts.impl.services;

import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.MobileNetworkOperator;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.SimInfo;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameterBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationMessage;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaData;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaDataBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.VehicleDetail;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.IdentifiedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.TransmissionRouterMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.componentbase.logging.Logging;

@Component
public class TransmissionRouter {
  public static final MobileNetworkOperator SOFTCAR_MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("softcar");
  private static final Logger logger = LoggerFactory.getLogger(TransmissionRouter.class);

  private final Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction;
  private final TransmissionRouterContext transmissionRouterContext;
  private final TransmissionRouterMetricReporter transmissionRouterMetricReporter;

  public TransmissionRouter(Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> srpEncoderFunction,
      TransmissionRouterContext transmissionRouterContext, TransmissionRouterMetricReporter transmissionRouterMetricReporter) {
    this.srpEncoderFunction = srpEncoderFunction;
    this.transmissionRouterContext = transmissionRouterContext;
    this.transmissionRouterMetricReporter = transmissionRouterMetricReporter;
  }

  private static MetaData createMetaData(PersistedMtMessage persistedMtMessage, SendSchemaStepType sendSchemaStepType) {
    MtMessage mtMessage = persistedMtMessage.getMtMessage();
    SrpOption srpOption = mtMessage.getSrpOption();

    return new MetaDataBuilder()
        .setMobileDirection("MT")
        .setSendSchemaName(mtMessage.getSendSchemaName())
        .setSendSchemaStepType(sendSchemaStepType)
        .setSrpDestinationService(srpOption.getSrpDestinationService())
        .setSrpDestinationVersion(srpOption.getSrpDestinationVersion())
        .build();
  }

  private static VehicleDetail createVehicleDetail(DeviceInfo deviceInfo) {
    SimInfo simInfo = deviceInfo.getSimInfo();
    return VehicleDetail.create(deviceInfo.getHandle(), simInfo.getIpv4Address(), simInfo.getMsisdn(), deviceInfo.getVpi());
  }

  private static boolean isSoftCar(PersistedDeviceInfo persistedDeviceInfo) {
    return SOFTCAR_MOBILE_NETWORK_OPERATOR.equals(persistedDeviceInfo.getDeviceInfo().getSimInfo().getMobileNetworkOperator());
  }

  public void processScheduledActiveMtMessage(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    Validate.notNull(scheduledActiveMtMessage, "scheduledActiveMtMessage");

    SendSchemaStepType sendSchemaStepType = scheduledActiveMtMessage.sendSchemaStep().getSendSchemaStepType();
    IdentifiedActiveMtMessage identifiedActiveMtMessage = scheduledActiveMtMessage.identifiedActiveMtMessage();

    logger.debug("processing scheduledActiveMtMessage, sendSchemaStepType: {}", sendSchemaStepType);

    if (isSoftCar(identifiedActiveMtMessage.persistedDeviceInfo())) {
      transmissionRouterContext.getMtSoftcarRestClient().sendMtMessage(encode(scheduledActiveMtMessage)).subscribe();
      transmissionRouterMetricReporter.onSoftcar();
      return;
    }

    routeMessage(scheduledActiveMtMessage, sendSchemaStepType);
    logIntegrationMessage(identifiedActiveMtMessage, sendSchemaStepType);
  }

  private EncodedActiveMtMessage encode(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    EncodedActiveMtMessage encodedActiveMtMessage = srpEncoderFunction.apply(scheduledActiveMtMessage);

    logger.debug("successfully encoded message: {}", encodedActiveMtMessage);

    return encodedActiveMtMessage;
  }

  private void logIntegrationMessage(IdentifiedActiveMtMessage identifiedActiveMtMessage, SendSchemaStepType sendSchemaStepType) {
    JoinedActiveMtMessage joinedActiveMtMessage = identifiedActiveMtMessage.joinedActiveMtMessage();
    IntegrationLogParameter integrationLogParameter = new IntegrationLogParameterBuilder()
        .setDirection(Logging.Direction.CLIENT_OUT)
        .setIntegrationMessage(IntegrationMessage.onSend(sendSchemaStepType, joinedActiveMtMessage))
        .setMetaData(createMetaData(joinedActiveMtMessage.persistedMtMessage(), sendSchemaStepType))
        .setStatus(Logging.Status.SUCCESS)
        .setVehicleDetail(createVehicleDetail(identifiedActiveMtMessage.persistedDeviceInfo().getDeviceInfo()))
        .build();

    transmissionRouterContext.getLoggingHelper().accept(integrationLogParameter);
  }

  private void registerConnectionEstablished(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    transmissionRouterContext
        .getConnectionEstablishedWebClient()
        .registerConnectionEstablished(scheduledActiveMtMessage);
    transmissionRouterMetricReporter.onWait();
  }

  private void routeMessage(ScheduledActiveMtMessage scheduledActiveMtMessage, SendSchemaStepType sendSchemaStepType) {
    switch (sendSchemaStepType) {
      case WAIT -> registerConnectionEstablished(scheduledActiveMtMessage);
      case SMS, UDP, SAT, WIFI -> sendEncodedActiveMtMessage(encode(scheduledActiveMtMessage), sendSchemaStepType);
      default -> {
        transmissionRouterMetricReporter.onUnimplementedRoute();
        throw new IllegalStateException("sendSchemaStepType not implemented: " + sendSchemaStepType);
      }
    }
  }

  private void sendEncodedActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage, SendSchemaStepType sendSchemaStepType) {
    transmissionRouterContext.getMtRouterRestClient().sendMtMessage(encodedActiveMtMessage);
    transmissionRouterMetricReporter.onRoute(sendSchemaStepType);
  }
}
