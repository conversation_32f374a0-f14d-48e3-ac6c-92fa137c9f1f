package com.volvo.tisp.tgwmts.impl.schema.dynamic.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class SendSchemaMetricReporter {
  private static final String ANOMALY = "ANOMALY";
  private static final String SEND_SCHEMA = "send-schema";

  private final Counter invalidNameCounter;
  private final Counter timeoutTooHighCounter;
  private final Counter timeoutTooLowCounter;

  public SendSchemaMetricReporter(MeterRegistry meterRegistry) {
    this.invalidNameCounter = meterRegistry.counter(SEND_SCHEMA, Tags.of(ANOMALY, "INVALID-NAME"));
    this.timeoutTooHighCounter = meterRegistry.counter(SEND_SCHEMA, Tags.of(ANOMALY, "TIMEOUT-TOO-HIGH"));
    this.timeoutTooLowCounter = meterRegistry.counter(SEND_SCHEMA, Tags.of(ANOMALY, "TIMEOUT-TOO-LOW"));
  }

  public void onInvalidName() {
    invalidNameCounter.increment();
  }

  public void onTimeoutTooHigh() {
    timeoutTooHighCounter.increment();
  }

  public void onTimeoutTooLow() {
    timeoutTooLowCounter.increment();
  }
}
