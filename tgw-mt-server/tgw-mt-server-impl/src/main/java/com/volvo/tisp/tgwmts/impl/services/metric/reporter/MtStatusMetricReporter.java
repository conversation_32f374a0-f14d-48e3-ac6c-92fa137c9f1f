package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class MtStatusMetricReporter {
  private static final String CONVERSION_FAILURE = "CONVERSION-FAILURE";
  private static final String TYPE = "TYPE";

  private final Counter invalidMtStatusCounter;

  public MtStatusMetricReporter(MeterRegistry meterRegistry) {
    invalidMtStatusCounter = meterRegistry.counter("mt-status-received", Tags.of(TYPE, CONVERSION_FAILURE));
  }

  public void onInvalidMtStatus() {
    invalidMtStatusCounter.increment();
  }
}
