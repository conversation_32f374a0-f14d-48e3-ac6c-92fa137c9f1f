package com.volvo.tisp.tgwmts.impl.scheduler;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.impl.services.SendSchemaProcessor;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtSchedulerWorker {
  private final ActiveMtMessageWriterFactory activeMtMessageWriterFactory;
  private final int batchSize;
  private final Clock clock;
  private final MtSchedulerMetricReporter mtSchedulerMetricReporter;
  private final SendSchemaProcessor sendSchemaProcessor;

  public MtSchedulerWorker(ActiveMtMessageWriterFactory activeMtMessageWriterFactory, int batchSize, Clock clock,
      MtSchedulerMetricReporter mtSchedulerMetricReporter, SendSchemaProcessor sendSchemaProcessor) {
    Validate.notNull(activeMtMessageWriterFactory, "activeMtMessageWriterFactory");
    Validate.isPositive(batchSize, "batchSize");
    Validate.notNull(clock, "clock");
    Validate.notNull(mtSchedulerMetricReporter, "mtSchedulerMetricReporter");
    Validate.notNull(sendSchemaProcessor, "sendSchemaProcessor");

    this.activeMtMessageWriterFactory = activeMtMessageWriterFactory;
    this.batchSize = batchSize;
    this.clock = clock;
    this.mtSchedulerMetricReporter = mtSchedulerMetricReporter;
    this.sendSchemaProcessor = sendSchemaProcessor;
  }

  /**
   * @return the number of processed MT messages.
   */
  public int doWorkOnce() {
    try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
      activeMtMessageWriter.startTransaction();

      Instant startTime = clock.instant();
      List<JoinedActiveMtMessage> joinedActiveMtMessages = activeMtMessageWriter.findTimedOutActiveMtMessagesWithVpiLock(batchSize);
      mtSchedulerMetricReporter.onTimedOutMessagesSelected(Duration.between(startTime, clock.instant()));

      List<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters = joinedActiveMtMessages.stream()
          .map(joinedActiveMtMessage -> sendSchemaProcessor.executeNextSendSchemaStep(activeMtMessageWriter, joinedActiveMtMessage))
          .filter(Optional::isPresent)
          .map(Optional::get)
          .toList();

      if (!updateActiveMtMessageParameters.isEmpty()) {
        activeMtMessageWriter.updateActiveMtMessages(updateActiveMtMessageParameters);
      }

      activeMtMessageWriter.commitTransaction();

      mtSchedulerMetricReporter.onTimedOutMessagesTotal(Duration.between(startTime, clock.instant()));
      return joinedActiveMtMessages.size();
    }
  }
}
