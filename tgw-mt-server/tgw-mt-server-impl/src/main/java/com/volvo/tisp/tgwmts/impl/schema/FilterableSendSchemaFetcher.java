package com.volvo.tisp.tgwmts.impl.schema;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.IntStream;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.impl.DynamicSendSchema;
import com.volvo.tisp.vc.main.utils.lib.Validate;

/**
 * This component takes a send schema name and uses the mappingFunction to fetch the raw send schema.
 * Then it uses zero or more filters remove any {@link SendSchemaStep} in the raw send schema which is not also part
 * of the filter
 */
@Component
public final class FilterableSendSchemaFetcher {
  private final Function<SendSchemaName, SendSchema> mappingFunction;

  public FilterableSendSchemaFetcher(Function<SendSchemaName, SendSchema> mappingFunction) {
    this.mappingFunction = mappingFunction;
  }

  private static SendSchema filter(SendSchema sendSchema, Set<SendSchemaStepType> sendSchemaStepTypes) {
    List<SendSchemaStep> filteredSteps = IntStream.iterate(1, i -> i + 1)
        .mapToObj(i -> sendSchema.getSendSchemaStep(SendSchemaStepId.ofInt(i)))
        .takeWhile(Optional::isPresent)
        .map(Optional::orElseThrow)
        .filter(step -> shouldInclude(sendSchemaStepTypes, step.getSendSchemaStepType()))
        .toList();

    return DynamicSendSchema.create(
        sendSchema.getGlobalTimeout(),
        sendSchema.getSendSchemaName(),
        IntStream.range(0, filteredSteps.size()).mapToObj(i -> reNormalizeStep(filteredSteps.get(i), i + 1)).toList(),
        sendSchema.getMaxRetryAttempts());
  }

  private static SendSchemaStep reNormalizeStep(SendSchemaStep sendSchemaStep, int i) {
    return SendSchemaStep.of(SendSchemaStepId.ofInt(i), sendSchemaStep.getSendSchemaStepType(), sendSchemaStep.getWaitDuration());
  }

  private static SendSchema recursivelyApplyFilters(SendSchema sendSchema, SendSchemaStepFilter[] sendSchemaStepFilters, int i) {
    if (sendSchemaStepFilters.length == i) {
      return sendSchema;
    }

    return recursivelyApplyFilters(filter(sendSchema, sendSchemaStepFilters[i].stepTypes()), sendSchemaStepFilters, i + 1);
  }

  private static boolean shouldInclude(Set<SendSchemaStepType> sendSchemaStepTypes, SendSchemaStepType stepType) {
    return sendSchemaStepTypes.contains(stepType) || stepType == SendSchemaStepType.WAIT;
  }

  public SendSchema fetch(SendSchemaName sendSchemaName, SendSchemaStepFilter... sendSchemaStepFilters) {
    Validate.notNull(sendSchemaName, "sendSchemaName");
    Validate.notNull(sendSchemaStepFilters, "sendSchemaStepFilters");

    SendSchema baseSchema = mappingFunction.apply(sendSchemaName);
    return recursivelyApplyFilters(baseSchema, sendSchemaStepFilters, 0);
  }
}
