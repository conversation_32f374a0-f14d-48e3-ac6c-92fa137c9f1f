package com.volvo.tisp.tgwmts.impl.converters;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.DynamicSendSchemaRetriever;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonLongSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonLowSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonNormalSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonRswdlSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonSetupSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonShortSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.CommonVeryHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DarfConfigSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolLowSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolMidPlusSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DfolSetupSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.DrutVeryHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.RdnsMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.RswdlLowSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.RswdlMidSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.SatOnlySendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.SmsOnlySendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.UptimeVeryHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.VLinkHighSendSchema;
import com.volvo.tisp.tgwmts.impl.schema.impl.VLinkLowSendSchema;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class SendSchemaNameMappingFunction implements Function<SendSchemaName, SendSchema> {
  private static final Map<SendSchemaName, SendSchema> SEND_SCHEMA_MAP = Stream.of(
          CommonHighSendSchema.INSTANCE, CommonLongSendSchema.INSTANCE, CommonLowSendSchema.INSTANCE, CommonMidSendSchema.INSTANCE,
          CommonNormalSendSchema.INSTANCE, CommonRswdlSendSchema.INSTANCE, CommonSetupSendSchema.INSTANCE, CommonShortSendSchema.INSTANCE,
          CommonVeryHighSendSchema.INSTANCE, DarfConfigSendSchema.INSTANCE, DfolHighSendSchema.INSTANCE, DfolLowSendSchema.INSTANCE, DfolMidSendSchema.INSTANCE,
          DfolMidPlusSendSchema.INSTANCE, DfolSetupSendSchema.INSTANCE, DrutVeryHighSendSchema.INSTANCE,
          RdnsMidSendSchema.INSTANCE, RswdlLowSendSchema.INSTANCE, RswdlMidSendSchema.INSTANCE,
          SatOnlySendSchema.INSTANCE, SmsOnlySendSchema.INSTANCE,
          UptimeVeryHighSendSchema.INSTANCE, VLinkHighSendSchema.INSTANCE, VLinkLowSendSchema.INSTANCE)
      .collect(Collectors.toMap(SendSchema::getSendSchemaName, Function.identity()));

  private final DynamicSendSchemaRetriever dynamicSendSchemaRetriever;

  public SendSchemaNameMappingFunction(DynamicSendSchemaRetriever dynamicSendSchemaRetriever) {
    this.dynamicSendSchemaRetriever = dynamicSendSchemaRetriever;
  }

  @Override
  public SendSchema apply(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    if (SEND_SCHEMA_MAP.containsKey(sendSchemaName)) {
      return SEND_SCHEMA_MAP.get(sendSchemaName);
    }

    return dynamicSendSchemaRetriever.getSendSchema(sendSchemaName);
  }

  public SendSchema apply(SendSchemaName sendSchemaName, DeviceInfo deviceInfo) {
    Validate.notNull(sendSchemaName, "sendSchemaName");
    Validate.notNull(deviceInfo, "deviceInfo");

    if (SEND_SCHEMA_MAP.containsKey(sendSchemaName)) {
      return SEND_SCHEMA_MAP.get(sendSchemaName);
    }

    return dynamicSendSchemaRetriever.getSendSchema(sendSchemaName, deviceInfo);
  }
}
