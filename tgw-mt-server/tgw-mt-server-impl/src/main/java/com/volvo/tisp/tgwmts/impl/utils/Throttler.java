package com.volvo.tisp.tgwmts.impl.utils;

import java.time.Duration;
import java.util.concurrent.TimeUnit;

import org.apache.commons.lang3.concurrent.TimedSemaphore;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class Throttler {
  private static final Logger logger = LoggerFactory.getLogger(Throttler.class);

  private final TimedSemaphore timedSemaphore;

  private Throttler(TimedSemaphore timedSemaphore) {
    Validate.notNull(timedSemaphore, "timedSemaphore");

    this.timedSemaphore = timedSemaphore;
  }

  public static Throttler create(Duration duration, int limit) {
    Validate.isPositive(duration, "duration");
    Validate.isPositive(limit, "limit");

    logger.info("duration: {}, limit: {}", duration, limit);

    return new Throttler(new TimedSemaphore(duration.toNanos(), TimeUnit.NANOSECONDS, limit));
  }

  public void acquire() throws InterruptedException {
    timedSemaphore.acquire();
  }

  public Duration getDuration() {
    return Duration.of(timedSemaphore.getPeriod(), timedSemaphore.getUnit().toChronoUnit());
  }

  public int getLimit() {
    return timedSemaphore.getLimit();
  }

  public void setLimit(int limit) {
    Validate.isPositive(limit, "limit");

    logger.info("limit: {}", limit);

    timedSemaphore.setLimit(limit);
  }

  @Override
  public String toString() {
    return new StringBuilder(30)
        .append("duration: ")
        .append(getDuration())
        .append(", limit: ")
        .append(getLimit())
        .toString();
  }
}
