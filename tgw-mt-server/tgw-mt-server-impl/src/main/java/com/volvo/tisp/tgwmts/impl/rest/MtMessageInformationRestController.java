package com.volvo.tisp.tgwmts.impl.rest;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.tgwmts.impl.model.MtMessageInformation;
import com.volvo.tisp.tgwmts.impl.services.MtMessageInformationService;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Optional;

@RestController
@RequestMapping(path = MtMessageInformationRestController.REQUEST_MAPPING_PATH)
@Authentication(required = false)
public class MtMessageInformationRestController {
    public static final String REQUEST_MAPPING_PATH = "/api/v1/message-information";
    private final MtMessageInformationService mtMessageInformationService;

    public MtMessageInformationRestController(MtMessageInformationService mtMessageInformationService) {
        this.mtMessageInformationService = mtMessageInformationService;
    }

    @GetMapping
    public ResponseEntity<MtMessageInformation> getMessageInformationByVpi(@RequestParam("vpi") String vpi) {
        Validate.notEmpty(vpi, "vpi must not be null");

        Optional<MtMessageInformation> mtMessageInformationOption = mtMessageInformationService.getMessageInformationByVpi(Vpi.ofString(vpi));
        return mtMessageInformationOption.map(mtMessageItems -> ResponseEntity.ok().body(mtMessageItems))
                .orElseGet(() -> ResponseEntity.status(HttpStatus.NOT_FOUND).build());
    }
}
