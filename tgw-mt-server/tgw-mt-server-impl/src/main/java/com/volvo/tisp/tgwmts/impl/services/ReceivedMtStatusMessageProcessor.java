package com.volvo.tisp.tgwmts.impl.services;

import static com.volvo.tisp.vc.vcss.client.protobuf.MtStatusStatisticProtobuf.MtStatusStatistic;
import static com.volvo.tisp.vc.vcss.client.protobuf.MtStatusStatisticProtobuf.Status;
import static com.volvo.tisp.vc.vcss.client.protobuf.common.TransportTypeProtobuf.TransportType;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.function.Consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameterBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationMessage;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaData;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaDataBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.VehicleDetail;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatusMessage;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.ReceivedMtStatusMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.services.statistics.MtStatusStatisticsHandler;
import com.volvo.tisp.tgwmts.impl.utils.ProtobufUtils;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.componentbase.logging.Logging;

@Component
public class ReceivedMtStatusMessageProcessor {
  private static final Logger logger = LoggerFactory.getLogger(ReceivedMtStatusMessageProcessor.class);

  private final ActiveMtMessageWriterFactory activeMtMessageWriterFactory;
  private final Clock clock;
  private final FinishedMtMessageHandler finishedMtMessageHandler;
  private final Consumer<IntegrationLogParameter> loggingHelper;
  private final MtStatusStatisticsHandler mtStatusStatisticsHandler;
  private final ReceivedMtStatusMessageMetricReporter receivedMtStatusMessageMetricReporter;
  private final SendSchemaProcessor sendSchemaProcessor;

  public ReceivedMtStatusMessageProcessor(
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory,
      Clock clock,
      FinishedMtMessageHandler finishedMtMessageHandler,
      Consumer<IntegrationLogParameter> loggingHelper,
      ReceivedMtStatusMessageMetricReporter receivedMtStatusMessageMetricReporter,
      SendSchemaProcessor sendSchemaProcessor, MtStatusStatisticsHandler mtStatusStatisticsHandler) {
    this.activeMtMessageWriterFactory = activeMtMessageWriterFactory;
    this.clock = clock;
    this.finishedMtMessageHandler = finishedMtMessageHandler;
    this.loggingHelper = loggingHelper;
    this.receivedMtStatusMessageMetricReporter = receivedMtStatusMessageMetricReporter;
    this.sendSchemaProcessor = sendSchemaProcessor;
    this.mtStatusStatisticsHandler = mtStatusStatisticsHandler;
  }

  private static IntegrationLogParameter createIntegrationLogParameter(JoinedActiveMtMessage joinedActiveMtMessage, ReceivedMtStatus receivedMtStatus) {
    return new IntegrationLogParameterBuilder()
        .setDirection(Logging.Direction.CLIENT_OUT)
        .setMetaData(createMetaData(joinedActiveMtMessage.persistedMtMessage().getMtMessage()))
        .setIntegrationMessage(IntegrationMessage.onHandleAck(receivedMtStatus, joinedActiveMtMessage))
        .setStatus(Logging.Status.SUCCESS)
        .setVehicleDetail(VehicleDetail.create(joinedActiveMtMessage.persistedVehicleLock().getVehicleLock().getVpi()))
        .build();
  }

  private static MetaData createMetaData(MtMessage mtMessage) {
    SrpOption srpOption = mtMessage.getSrpOption();

    return new MetaDataBuilder()
        .setMobileDirection("MT")
        .setSendSchemaName(mtMessage.getSendSchemaName())
        .setSrpDestinationService(srpOption.getSrpDestinationService())
        .setSrpDestinationVersion(srpOption.getSrpDestinationVersion())
        .build();
  }

  private static TrackingIdentifier getTid(JoinedActiveMtMessage joinedActiveMtMessage) {
    return TrackingIdentifier.fromString(joinedActiveMtMessage.persistedMtMessage().getMtMessage().getTid().toString());
  }

  public void processMtStatusMessage(ReceivedMtStatusMessage receivedMtStatusMessage) {
    Validate.notNull(receivedMtStatusMessage, "receivedMtStatusMessage");

    try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
      activeMtMessageWriter.startTransactionWithLockTimeout();

      Instant startTime = clock.instant();
      Optional<JoinedActiveMtMessage> optional = activeMtMessageWriter.findActiveMtMessageWithVpiLock(receivedMtStatusMessage.activeMtMessageId());
      receivedMtStatusMessageMetricReporter.onFindActiveMtMessageWithVpiLock(Duration.between(startTime, clock.instant()));

      if (optional.isPresent()) {
        JoinedActiveMtMessage joinedActiveMtMessage = optional.get();
        ReceivedMtStatus receivedMtStatus = receivedMtStatusMessage.receivedMtStatus();
        handleReceivedMtStatus(receivedMtStatus, activeMtMessageWriter, joinedActiveMtMessage, startTime);
        publishMtStatusStatistic(joinedActiveMtMessage.persistedVehicleLock(), receivedMtStatusMessage);
      } else {
        logger.debug("Could not find receivedMtStatusMessage in database: {}", receivedMtStatusMessage);
        receivedMtStatusMessageMetricReporter.onNotFound();
      }

      activeMtMessageWriter.commitTransaction();
    } catch (RuntimeException e) {
      logger.error("", e);
      receivedMtStatusMessageMetricReporter.onFailedToProcess();
    }
  }

  public void reportMtStatusStatistics(MtStatusStatistic mtStatusStatistic) {
    mtStatusStatisticsHandler.handle(mtStatusStatistic);
    receivedMtStatusMessageMetricReporter.onMtStatusStatisticTransportType(mtStatusStatistic.getTransportType());
  }

  private Status convertToStatisticStatus(ReceivedMtStatus receivedMtStatus) {
    return switch (receivedMtStatus) {
      case CANCELED -> Status.CANCELED;
      case DELIVERED -> Status.DELIVERED;
      case REJECTED -> Status.REJECTED;
      case THROTTLED -> Status.THROTTLED;
      case SERVICE_UNSUPPORTED -> Status.SERVICE_UNSUPPORTED;
    };
  }

  private MtStatusStatistic.Builder createMtStatusStatisticBuilder(ReceivedMtStatus receivedMtStatus, Vpi vpi) {
    return MtStatusStatistic.newBuilder()
        .setVpi(vpi.toString())
        .setTimestamp(ProtobufUtils.createTimestamp(clock.instant()))
        .setStatus(convertToStatisticStatus(receivedMtStatus))
        .setCount(1);
  }

  private void executeNextSendSchemaStep(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {
    sendSchemaProcessor.executeNextSendSchemaStep(activeMtMessageWriter, joinedActiveMtMessage)
        .ifPresent(updateActiveMtMessageParameter -> activeMtMessageWriter.updateActiveMtMessages(List.of(updateActiveMtMessageParameter)));
  }

  private void handleReceivedMtStatus(ReceivedMtStatus receivedMtStatus, ActiveMtMessageWriter activeMtMessageWriter,
      JoinedActiveMtMessage joinedActiveMtMessage, Instant startTime) {
    try (final TispContext.Scope ignored = TispContext.current().newScope().tid(getTid(joinedActiveMtMessage)).activate()) {
      loggingHelper.accept(createIntegrationLogParameter(joinedActiveMtMessage, receivedMtStatus));

      switch (receivedMtStatus) {
        case CANCELED -> onCanceled(activeMtMessageWriter, joinedActiveMtMessage);
        case DELIVERED -> finishedMtMessageHandler.onDelivered(activeMtMessageWriter, joinedActiveMtMessage);
        case REJECTED -> finishedMtMessageHandler.onRejected(activeMtMessageWriter, joinedActiveMtMessage);
        case THROTTLED -> finishedMtMessageHandler.onThrottled(activeMtMessageWriter, joinedActiveMtMessage);
        case SERVICE_UNSUPPORTED -> finishedMtMessageHandler.onServiceUnsupported(activeMtMessageWriter, joinedActiveMtMessage);
        default -> logger.debug("mtStatus received but no action configured for it, {} {}", receivedMtStatus, joinedActiveMtMessage);
      }

      receivedMtStatusMessageMetricReporter.onReceivedMtStatus(receivedMtStatus, Duration.between(startTime, clock.instant()));
    }
  }

  private void onCanceled(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {
    receivedMtStatusMessageMetricReporter.onCanceled();
    executeNextSendSchemaStep(activeMtMessageWriter, joinedActiveMtMessage);
  }

  private void publishMtStatusStatistic(PersistedVehicleLock persistedVehicleLock, ReceivedMtStatusMessage receivedMtStatusMessage) {
    MtStatusStatistic.Builder builder = createMtStatusStatisticBuilder(receivedMtStatusMessage.receivedMtStatus(),
        persistedVehicleLock.getVehicleLock().getVpi());

    switch (receivedMtStatusMessage.transportType()) {
      case UDP -> reportMtStatusStatistics(builder.setTransportType(TransportType.UDP).build());
      case SMS -> reportMtStatusStatistics(builder.setTransportType(TransportType.SMS).build());
      case SAT -> reportMtStatusStatistics(builder.setTransportType(TransportType.SATELLITE).build());
      case WIFI -> reportMtStatusStatistics(builder.setTransportType(TransportType.WIFI).build());
      case SOFTCAR -> logger.debug("skipping softcar mt-status-statistic publishing");
      default -> {
        logger.warn("unknown transportType, {}", receivedMtStatusMessage);
        receivedMtStatusMessageMetricReporter.onUnknownTransportType();
      }
    }
  }
}
