package com.volvo.tisp.tgwmts.impl.schema;

import java.util.EnumSet;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;

@Component
public class SendSchemaStepFilterProducer {
  public static final Set<SendSchemaStepType> DEFAULT_SEND_SCHEMA_STEPS_TYPES = EnumSet.of(SendSchemaStepType.UDP, SendSchemaStepType.SMS);

  public SendSchemaStepFilter createSendSchemaStepFilter(DeviceInfo deviceInfo) {
    Set<SendSchemaStepType> sendSchemaStepTypes = EnumSet.copyOf(DEFAULT_SEND_SCHEMA_STEPS_TYPES);

    if (deviceInfo.isSatEnabled()) {
      sendSchemaStepTypes.add(SendSchemaStepType.SAT);
    }

    if (deviceInfo.isWifiEnabled()) {
      sendSchemaStepTypes.add(SendSchemaStepType.WIFI);
    }

    return SendSchemaStepFilter.of(sendSchemaStepTypes);
  }
}
