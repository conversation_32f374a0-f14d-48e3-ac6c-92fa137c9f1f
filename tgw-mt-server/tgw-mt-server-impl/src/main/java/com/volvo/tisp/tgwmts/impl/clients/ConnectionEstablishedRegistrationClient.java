package com.volvo.tisp.tgwmts.impl.clients;

import java.time.Clock;
import java.time.Instant;
import java.util.List;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;

import com.google.protobuf.Timestamp;
import com.volvo.tisp.protobuf.common.PlatformIdentifier;
import com.volvo.tisp.tce.proto.ConnectionEstablishedRegistration;
import com.volvo.tisp.tce.proto.ConnectionEstablishedRegistrations;
import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;
import com.volvo.tisp.tgwmts.impl.clients.metrics.reporter.ConnectionEstablishedRegistrationReporter;
import com.volvo.tisp.tgwmts.impl.conf.properties.MoServerReactiveClientProperties;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.utils.ProtobufUtils;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import reactor.core.publisher.Mono;

public class ConnectionEstablishedRegistrationClient {
  private static final Logger logger = LoggerFactory.getLogger(ConnectionEstablishedRegistrationClient.class);
  private final Clock clock;
  private final ConnectionEstablishedCacheService connectionEstablishedCacheService;
  private final ConnectionEstablishedRegistrationReporter connectionEstablishedRegistrationReporter;
  private final MoServerReactiveClientProperties moServerReactiveClientProperties;
  private final WebClient webClient;

  public ConnectionEstablishedRegistrationClient(Clock clock, WebClient webClient,
      ConnectionEstablishedRegistrationReporter connectionEstablishedRegistrationReporter,
      MoServerReactiveClientProperties moServerReactiveClientProperties, ConnectionEstablishedCacheService connectionEstablishedCacheService) {

    this.moServerReactiveClientProperties = moServerReactiveClientProperties;
    this.webClient = webClient;
    this.connectionEstablishedRegistrationReporter = connectionEstablishedRegistrationReporter;
    this.connectionEstablishedCacheService = connectionEstablishedCacheService;
    this.clock = clock;
  }

  private static Timestamp computeExpirationTimestamp(SendSchemaStep sendSchemaStep, Instant now) {
    return ProtobufUtils.createTimestamp(now.plus(sendSchemaStep.getWaitDuration()));
  }

  private static ConnectionEstablishedRegistration createConnectionEstablishedRegistration(Vpi vpi, Instant instant) {
    return ConnectionEstablishedRegistration.newBuilder()
        .setVpi(createPlatformIdentifier(vpi))
        .setExpires(ProtobufUtils.createTimestamp(instant))
        .build();
  }

  private static PlatformIdentifier createPlatformIdentifier(Vpi vpi) {
    return PlatformIdentifier.newBuilder()
        .setValue(vpi.toString())
        .build();
  }

  private static Mono<Boolean> handleResponse(ClientResponse clientResponse) {
    if (clientResponse.statusCode().is2xxSuccessful()) {
      return Mono.just(true);
    } else {
      logger.warn("request error: {}", clientResponse.statusCode().value());
      return Mono.just(false);
    }
  }

  private static void saveConnectionEstablishRegistrationFailure(ConnectionEstablishedRegistration connectionEstablishedRegistration,
      ConnectionEstablishedCacheService connectionEstablishedCacheService) {
    logger.debug("connectionEstablishedRegistration failed for connectionEstablishedRegistration: {}", connectionEstablishedRegistration);

    Vpi vpi = Vpi.ofString(connectionEstablishedRegistration.getVpi().getValue());
    Instant expires = Instant.ofEpochSecond(connectionEstablishedRegistration.getExpires().getSeconds());

    connectionEstablishedCacheService.add(vpi, expires);
  }

  public Mono<Boolean> reRegister(List<Map.Entry<Vpi, Instant>> connectionEstablishedRegistrations) {
    Validate.notEmpty(connectionEstablishedRegistrations, "connectionEstablishedRegistrations");
    connectionEstablishedRegistrationReporter.onReRegister();

    return webClient.post()
        .uri(moServerReactiveClientProperties.getConnectionEstablishedRegistrationsMultiRequestPath())
        .contentType(MediaType.APPLICATION_PROTOBUF)
        .bodyValue(createConnectionEstablishedRegistrations(connectionEstablishedRegistrations))
        .exchangeToMono(ConnectionEstablishedRegistrationClient::handleResponse);
  }

  public void registerConnectionEstablished(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    Validate.notNull(scheduledActiveMtMessage, "scheduledActiveMtMessage");

    ConnectionEstablishedRegistration connectionEstablishedRegistration = createConnectionEstablishedRegistration(scheduledActiveMtMessage);
    logger.debug("sending connectionEstablishedRegistration to mo-connection-established-service: {}", connectionEstablishedRegistration);

    webClient.post()
        .uri(moServerReactiveClientProperties.getConnectionEstablishedRegistrationsSingleRequestPath())
        .contentType(MediaType.APPLICATION_PROTOBUF)
        .bodyValue(connectionEstablishedRegistration)
        .exchangeToMono(ConnectionEstablishedRegistrationClient::handleResponse)
        .subscribe(isSuccess -> {
          if (moServerReactiveClientProperties.isEnableConnectionEstablishedRegistrationFailurePersist() && !isSuccess) {
            saveConnectionEstablishRegistrationFailure(connectionEstablishedRegistration, connectionEstablishedCacheService);
          }
        });
  }

  private ConnectionEstablishedRegistration createConnectionEstablishedRegistration(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    return ConnectionEstablishedRegistration.newBuilder()
        .setVpi(createPlatformIdentifier(scheduledActiveMtMessage.identifiedActiveMtMessage().persistedDeviceInfo().getDeviceInfo().getVpi()))
        .setExpires(computeExpirationTimestamp(scheduledActiveMtMessage.sendSchemaStep(), clock.instant()))
        .build();
  }

  private ConnectionEstablishedRegistrations createConnectionEstablishedRegistrations(List<Map.Entry<Vpi, Instant>> connectionEstablishedRegistrations) {
    ConnectionEstablishedRegistrations.Builder builder = ConnectionEstablishedRegistrations.newBuilder();
    connectionEstablishedRegistrations.forEach(vpiInstantEntry -> builder.addConnectionEstablishedRegistrations(
        createConnectionEstablishedRegistration(vpiInstantEntry.getKey(), vpiInstantEntry.getValue())));
    return builder.build();
  }
}
