package com.volvo.tisp.tgwmts.impl.conf;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.tisp.servicemonitoring.ServiceMonitoring;

import software.amazon.awssdk.services.sqs.SqsClient;

@Configuration
public class ServiceMonitoringConfig {
  @Bean
  public ServiceMonitoring createServiceMonitoring(SqsClient sqsClient, ObjectMapper objectMapper, Environment environment) {
    return new ServiceMonitoring(sqsClient, objectMapper, environment, null);
  }
}
