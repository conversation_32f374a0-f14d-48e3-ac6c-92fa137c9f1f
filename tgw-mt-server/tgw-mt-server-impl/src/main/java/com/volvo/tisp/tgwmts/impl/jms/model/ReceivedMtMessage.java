package com.volvo.tisp.tgwmts.impl.jms.model;

import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ReceivedMtMessage {
  private final EnqueueingType enqueueingType;
  private final QueueId queueId;
  private final Optional<ReplyOption> replyOption;
  private final SendSchemaName sendSchemaName;
  private final SrpOption srpOption;
  private final Tid tid;
  private final Vpi vpi;

  ReceivedMtMessage(ReceivedMtMessageBuilder receivedMtMessageBuilder) {
    Validate.notNull(receivedMtMessageBuilder, "receivedMtMessageBuilder");

    this.enqueueingType = receivedMtMessageBuilder.getEnqueueingType();
    this.queueId = receivedMtMessageBuilder.getQueueId();
    this.replyOption = receivedMtMessageBuilder.getReplyOption();
    this.sendSchemaName = receivedMtMessageBuilder.getSendSchemaName();
    this.srpOption = receivedMtMessageBuilder.getSrpOption();
    this.tid = receivedMtMessageBuilder.getTid();
    this.vpi = receivedMtMessageBuilder.getVpi();
  }

  public EnqueueingType getEnqueueingType() {
    return enqueueingType;
  }

  public QueueId getQueueId() {
    return queueId;
  }

  public Optional<ReplyOption> getReplyOption() {
    return replyOption;
  }

  public SendSchemaName getSendSchemaName() {
    return sendSchemaName;
  }

  public SrpOption getSrpOption() {
    return srpOption;
  }

  public Tid getTid() {
    return tid;
  }

  public Vpi getVpi() {
    return vpi;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("enqueueingType=")
        .append(enqueueingType)
        .append(", queueId=")
        .append(queueId)
        .append(", replyOption={")
        .append(replyOption)
        .append("}, sendSchemaName=")
        .append(sendSchemaName)
        .append(", srpOption={")
        .append(srpOption)
        .append("}, tid=")
        .append(tid)
        .append(", vpi=")
        .append(vpi)
        .toString();
  }
}
