package com.volvo.tisp.tgwmts.impl.schema;

import java.time.Duration;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SendSchemaStep {
  public static final Duration DEFAULT_SAT_TIMEOUT = Duration.ofHours(1);
  public static final Duration DEFAULT_SMS_TIMEOUT = Duration.ofMinutes(65);
  public static final Duration SAT_ENABLED_SMS_TIMEOUT = Duration.ofMinutes(5);
  public static final Duration DEFAULT_UDP_TIMEOUT = Duration.ofMinutes(3);
  public static final Duration DEFAULT_WIFI_TIMEOUT = Duration.ofMinutes(3);

  private final SendSchemaStepId sendSchemaStepId;
  private final SendSchemaStepType sendSchemaStepType;
  private final Duration waitDuration;

  private SendSchemaStep(SendSchemaStepId sendSchemaStepId, SendSchemaStepType sendSchemaStepType, Duration waitDuration) {
    Validate.notNull(sendSchemaStepId, "sendSchemaStepId");
    Validate.notNull(sendSchemaStepType, "sendSchemaStepType");
    Validate.isPositive(waitDuration, "waitDuration");

    this.sendSchemaStepId = sendSchemaStepId;
    this.sendSchemaStepType = sendSchemaStepType;
    this.waitDuration = waitDuration;
  }

  public static SendSchemaStep forSat(SendSchemaStepId sendSchemaStepId, Duration waitDuration) {
    return new SendSchemaStep(sendSchemaStepId, SendSchemaStepType.SAT, waitDuration);
  }

  public static SendSchemaStep forSat(SendSchemaStepId sendSchemaStepId) {
    return forSat(sendSchemaStepId, DEFAULT_SAT_TIMEOUT);
  }

  public static SendSchemaStep forSms(SendSchemaStepId sendSchemaStepId, Duration waitDuration) {
    return new SendSchemaStep(sendSchemaStepId, SendSchemaStepType.SMS, waitDuration);
  }

  public static SendSchemaStep forSms(SendSchemaStepId sendSchemaStepId) {
    return forSms(sendSchemaStepId, DEFAULT_SMS_TIMEOUT);
  }

  public static SendSchemaStep forUdp(SendSchemaStepId sendSchemaStepId, Duration waitDuration) {
    return new SendSchemaStep(sendSchemaStepId, SendSchemaStepType.UDP, waitDuration);
  }

  public static SendSchemaStep forUdp(SendSchemaStepId sendSchemaStepId) {
    return forUdp(sendSchemaStepId, DEFAULT_UDP_TIMEOUT);
  }

  public static SendSchemaStep forWait(SendSchemaStepId sendSchemaStepId, Duration waitDuration) {
    return new SendSchemaStep(sendSchemaStepId, SendSchemaStepType.WAIT, waitDuration);
  }

  public static SendSchemaStep forWifi(SendSchemaStepId sendSchemaStepId, Duration waitDuration) {
    return new SendSchemaStep(sendSchemaStepId, SendSchemaStepType.WIFI, waitDuration);
  }

  public static SendSchemaStep forWifi(SendSchemaStepId sendSchemaStepId) {
    return new SendSchemaStep(sendSchemaStepId, SendSchemaStepType.WIFI, DEFAULT_WIFI_TIMEOUT);
  }

  public static SendSchemaStep of(SendSchemaStepId sendSchemaStepId, SendSchemaStepType sendSchemaStepType, Duration waitDuration) {
    return new SendSchemaStep(sendSchemaStepId, sendSchemaStepType, waitDuration);
  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    SendSchemaStep other = (SendSchemaStep) object;
    if (!sendSchemaStepId.equals(other.sendSchemaStepId)) {
      return false;
    } else if (sendSchemaStepType != other.sendSchemaStepType) {
      return false;
    } else {
      return waitDuration.equals(other.waitDuration);
    }
  }

  /**
   * @return the identifier of this {@code SendSchemaStep}, must be unique within one {@link SendSchema}.
   */
  public SendSchemaStepId getSendSchemaStepId() {
    return sendSchemaStepId;
  }

  /**
   * @return the transmission type of this {@code SendSchemaStep}.
   */
  public SendSchemaStepType getSendSchemaStepType() {
    return sendSchemaStepType;
  }

  /**
   * @return the time between this and the next {@code SendSchemaStep} to wait for an MT ACK message from the vehicle.
   */
  public Duration getWaitDuration() {
    return waitDuration;
  }

  @Override
  public int hashCode() {
    final int prime = 31;
    int result = 1;
    result = prime * result + sendSchemaStepId.hashCode();
    result = prime * result + sendSchemaStepType.hashCode();
    result = prime * result + waitDuration.hashCode();
    return result;
  }

  @Override
  public String toString() {
    return new StringBuilder(200)
        .append("sendSchemaStepId=")
        .append(sendSchemaStepId)
        .append(", sendSchemaStepType=")
        .append(sendSchemaStepType)
        .append(", waitDuration=")
        .append(waitDuration)
        .toString();
  }
}
