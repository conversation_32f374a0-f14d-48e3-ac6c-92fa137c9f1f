package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;

@Component
public class MtMessageRetryHandlerMetricReporter {
  private static final String TYPE = "TYPE";
  private final Timer findActiveMtMessagesByVpiWithVpiLockTimer;
  private final Counter missingDeviceCounter;
  private final Counter mtMessageRetryProcessCounter;
  private final Timer mtMessageRetryProcessTimer;
  private final Counter noActiveMtMessageCounter;

  public MtMessageRetryHandlerMetricReporter(MeterRegistry meterRegistry) {
    findActiveMtMessagesByVpiWithVpiLockTimer = meterRegistry.timer("mt-retry", Tags.of(TYPE, "FIND_ACTIVE_MT_MESSAGE"));
    missingDeviceCounter = meterRegistry.counter("mt-retry", Tags.of(TYPE, "MISSING_DEVICE"));
    mtMessageRetryProcessCounter = meterRegistry.counter("mt-retry", Tags.of(TYPE, "PROCESSED"));
    mtMessageRetryProcessTimer = meterRegistry.timer("mt-retry", Tags.of(TYPE, "DURATION"));
    noActiveMtMessageCounter = meterRegistry.counter("mt-retry", Tags.of(TYPE, "NO_ACTIVE_MT_MESSAGE"));
  }

  public void onFindActiveMtMessagesByVpiWithVpiLock(Duration duration) {
    Validate.notNegative(duration, "duration");

    findActiveMtMessagesByVpiWithVpiLockTimer.record(duration);
  }

  public void onMissingDeviceCounter() {
    missingDeviceCounter.increment();
  }

  public void onNoActiveMtMessageFound() {
    noActiveMtMessageCounter.increment();
  }

  public void onRetryInitiated(int count, Duration duration) {
    Validate.isPositive(count, "count");
    Validate.notNegative(duration, "duration");

    mtMessageRetryProcessCounter.increment(count);
    mtMessageRetryProcessTimer.record(duration);
  }
}
