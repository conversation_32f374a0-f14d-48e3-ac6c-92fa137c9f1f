package com.volvo.tisp.tgwmts.impl.jms.model;

public enum EnqueueingType {
  /**
   * Drop the MT message if there are any other MT messages for the same vehicle with the same queue ID in the queue already, else add it like in
   * {@link #NORMAL} mode.
   */
  IGNORE,

  /** Add the MT message to the queue and also start processing it, regardless of whether the capping threshold for the vehicle has already been reached. */
  IMMEDIATE,

  /** Add the MT message at the end of the queue, but only start to process it if the capping threshold for the vehicle has not yet been reached. */
  NORMAL,

  /**
   * Remove all MT messages for the same vehicle with the same queue ID from the queue (including all that are currently being processed, if any), then add it
   * like in {@link #NORMAL} mode.
   */
  OVERRIDE,

  /** Combination of {@link #OVERRIDE} and {@link #IMMEDIATE} mode. */
  OVERRIDE_IMMEDIATE
}
