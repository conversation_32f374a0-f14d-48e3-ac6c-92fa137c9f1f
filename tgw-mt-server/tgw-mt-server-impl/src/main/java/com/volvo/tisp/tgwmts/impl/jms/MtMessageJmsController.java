package com.volvo.tisp.tgwmts.impl.jms;

import java.util.function.Consumer;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.context.Vehicle;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

@JmsController(destination = MtMessageJmsController.MT_MESSAGE_IN_QUEUE_NAME)
public class MtMessageJmsController {
  public static final String MT_MESSAGE_IN_QUEUE_NAME = "MT-MESSAGES";
  private static final Logger logger = LoggerFactory.getLogger(MtMessageJmsController.class);

  private final Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction;
  private final MtMessageMetricReporter mtMessageMetricReporter;
  private final Consumer<ReceivedMtMessage> receivedMtMessageConsumer;

  public MtMessageJmsController(Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> mtMessageInputConverterFunction,
      MtMessageMetricReporter mtMessageMetricReporter, Consumer<ReceivedMtMessage> receivedMtMessageConsumer) {
    this.mtMessageInputConverterFunction = mtMessageInputConverterFunction;
    this.mtMessageMetricReporter = mtMessageMetricReporter;
    this.receivedMtMessageConsumer = receivedMtMessageConsumer;
  }

  @JmsMessageMapping(consumesType = MessageTypesJms.TCE_MT_MESSAGE_TYPE, consumesVersion = MessageTypesJms.VERSION_2_0)
  public void receiveMtMessage(JmsMessage<MtMessage> jmsMessage) {
    Validate.notNull(jmsMessage, "jmsMessage");

    Either<RuntimeException, ReceivedMtMessage> either = mtMessageInputConverterFunction.apply(jmsMessage.payload());

    if (either.isLeft()) {
      logger.warn("Failed to convert received MtMessage version 2", either.getLeft());
      mtMessageMetricReporter.onMtV2Invalid();
      return;
    }

    buildVehicleAndAcceptReceivedMtMessage(either.getRight());
  }

  private void buildVehicleAndAcceptReceivedMtMessage(ReceivedMtMessage receivedMtMessage) {
    Vehicle vehicle = Vehicle.builder().vpi(VehicleIdentifier.fromString(receivedMtMessage.getVpi().toString())).build();

    try (final TispContext.Scope ignored = TispContext.current().newScope().vehicle(vehicle).activate()) {
      receivedMtMessageConsumer.accept(receivedMtMessage);
    }
  }
}
