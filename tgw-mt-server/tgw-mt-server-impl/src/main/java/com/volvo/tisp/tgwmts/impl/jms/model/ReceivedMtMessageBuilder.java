package com.volvo.tisp.tgwmts.impl.jms.model;

import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class ReceivedMtMessageBuilder {
  static final Optional<ReplyOption> DEFAULT_REPLY_OPTION = Optional.empty();

  private EnqueueingType enqueueingType;
  private QueueId queueId;
  private Optional<ReplyOption> replyOption = DEFAULT_REPLY_OPTION;
  private SendSchemaName sendSchemaName;
  private SrpOption srpOption;
  private Tid tid;
  private Vpi vpi;

  public ReceivedMtMessage build() {
    Validate.notNull(enqueueingType, "enqueueingType");
    Validate.notNull(queueId, "queueId");
    Validate.notNull(replyOption, "replyOption");
    Validate.notNull(sendSchemaName, "sendSchemaName");
    Validate.notNull(srpOption, "srpOption");
    Validate.notNull(tid, "tid");
    Validate.notNull(vpi, "vpi");

    return new ReceivedMtMessage(this);
  }

  public EnqueueingType getEnqueueingType() {
    return enqueueingType;
  }

  public QueueId getQueueId() {
    return queueId;
  }

  public Optional<ReplyOption> getReplyOption() {
    return replyOption;
  }

  public SendSchemaName getSendSchemaName() {
    return sendSchemaName;
  }

  public SrpOption getSrpOption() {
    return srpOption;
  }

  public Tid getTid() {
    return tid;
  }

  public Vpi getVpi() {
    return vpi;
  }

  public ReceivedMtMessageBuilder setEnqueueingType(EnqueueingType enqueueingType) {
    Validate.notNull(enqueueingType, "enqueueingType");

    this.enqueueingType = enqueueingType;
    return this;
  }

  public ReceivedMtMessageBuilder setQueueId(QueueId queueId) {
    Validate.notNull(queueId, "queueId");

    this.queueId = queueId;
    return this;
  }

  public ReceivedMtMessageBuilder setReplyOption(Optional<ReplyOption> replyOption) {
    Validate.notNull(replyOption, "replyOption");

    this.replyOption = replyOption;
    return this;
  }

  public ReceivedMtMessageBuilder setSendSchemaName(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    this.sendSchemaName = sendSchemaName;
    return this;
  }

  public ReceivedMtMessageBuilder setSrpOption(SrpOption srpOption) {
    Validate.notNull(srpOption, "srpOption");

    this.srpOption = srpOption;
    return this;
  }

  public ReceivedMtMessageBuilder setTid(Tid tid) {
    Validate.notNull(tid, "tid");

    this.tid = tid;
    return this;
  }

  public ReceivedMtMessageBuilder setVpi(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    this.vpi = vpi;
    return this;
  }
}
