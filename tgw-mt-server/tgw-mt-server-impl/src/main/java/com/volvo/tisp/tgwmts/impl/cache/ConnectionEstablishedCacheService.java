package com.volvo.tisp.tgwmts.impl.cache;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import com.github.benmanes.caffeine.cache.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class ConnectionEstablishedCacheService {
  private static final Logger logger = LoggerFactory.getLogger(ConnectionEstablishedCacheService.class);

  private final Clock clock;
  private final Cache<Vpi, Instant> connectionEstablishedRegistrationCache;
  private final ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter;

  public ConnectionEstablishedCacheService(Clock clock, Cache<Vpi, Instant> connectionEstablishedRegistrationCache,
      ConnectionEstablishedRegistrationCacheMetricReporter connectionEstablishedRegistrationCacheMetricReporter) {
    this.clock = clock;
    this.connectionEstablishedRegistrationCache = connectionEstablishedRegistrationCache;
    this.connectionEstablishedRegistrationCacheMetricReporter = connectionEstablishedRegistrationCacheMetricReporter;
  }

  private static boolean isPositive(Duration timeToLive) {
    return !timeToLive.isZero() && !timeToLive.isNegative();
  }

  public void add(Vpi vpi, Instant expiryInstant) {
    Validate.notNull(vpi, "vpi");
    Validate.notNull(expiryInstant, "expiryInstant");

    logger.debug("try adding vpi :{}, instant: {}", vpi, expiryInstant);

    Instant existingVpiExpiry = connectionEstablishedRegistrationCache.getIfPresent(vpi);
    if (existingVpiExpiry != null && existingVpiExpiry.isAfter(expiryInstant)) {
      logger.debug("no need to update the existing VPI in the cache");
      return;
    }

    Duration timeToLive = Duration.between(clock.instant(), expiryInstant);
    if (isPositive(timeToLive)) {
      connectionEstablishedRegistrationCache.put(vpi, expiryInstant);
    } else {
      connectionEstablishedRegistrationCacheMetricReporter.onInvalidExpiryTime();
      logger.warn("connection established registration cache update failed for vpi: {} timeToLive: {}", vpi, timeToLive);
    }
  }

  public List<Map.Entry<Vpi, Instant>> getEntries(long limit) {
    Validate.isPositive(limit, "limit");

    connectionEstablishedRegistrationCache.cleanUp();
    return connectionEstablishedRegistrationCache.asMap().entrySet().stream()
            .limit(limit)
            .toList();
  }

  public Optional<Instant> get(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    return Optional.ofNullable(connectionEstablishedRegistrationCache.getIfPresent(vpi));
  }

  public void remove(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    connectionEstablishedRegistrationCache.invalidate(vpi);
  }

  public void reportCacheSizeMetric() {
    connectionEstablishedRegistrationCacheMetricReporter.onCacheSizeChanged(connectionEstablishedRegistrationCache.asMap().size());
  }
}
