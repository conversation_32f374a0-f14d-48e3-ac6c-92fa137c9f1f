package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class SrpEncoderMetricReporter {
  private static final String SERVICE_ENCODE = "service.encode";
  private static final String SRP_LEVEL = "srp-level";

  private final Counter srp10Counter;
  private final Counter srp11Counter;
  private final Counter srp12Counter;

  public SrpEncoderMetricReporter(MeterRegistry meterRegistry) {
    srp10Counter = meterRegistry.counter(SERVICE_ENCODE, SRP_LEVEL, "srp10");
    srp11Counter = meterRegistry.counter(SERVICE_ENCODE, SRP_LEVEL, "srp11");
    srp12Counter = meterRegistry.counter(SERVICE_ENCODE, SRP_LEVEL, "srp12");
  }

  public void onMtSrp10Received() {
    srp10Counter.increment();
  }

  public void onMtSrp11Received() {
    srp11Counter.increment();
  }

  public void onMtSrp12Received() {
    srp12Counter.increment();
  }
}
