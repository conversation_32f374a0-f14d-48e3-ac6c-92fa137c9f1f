package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.util.List;
import java.util.function.Function;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.SrpLevel;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.IdentifiedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.SrpEncoderMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.service.routing.protocol.lib.converter.SrpConverter;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.DestinationService;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.DestinationVersion;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.Priority;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.SourceService;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.SourceVersion;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v10.Srp10Wrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v10.Srp10WrapperBuilder;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.EncodedSrpHeader;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.Srp11Wrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.Srp11WrapperBuilder;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.SrpHeaderWrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.SrpHeaderWrapperBuilder;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v12.Srp12Wrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v12.Srp12WrapperBuilder;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AdditionalAuthenticatedData;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;

public final class SrpEncoderFunction implements Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> {
  public static final ObsAlias DEFAULT_OBS_ALIAS = ObsAlias.ofLong(4294967295L);
  private static final SrpDestinationService ACTIVATION_SERVICE_ID = SrpDestinationService.ofInt(1);

  private final Clock clock;
  private final EncryptionProcessor encryptionProcessor;
  private final Priority priority;
  private final SrpEncoderMetricReporter srpEncoderMetricReporter;

  private SrpEncoderFunction(Clock clock, EncryptionProcessor encryptionProcessor, Priority priority, SrpEncoderMetricReporter srpEncoderMetricReporter) {
    this.clock = clock;
    this.encryptionProcessor = encryptionProcessor;
    this.priority = priority;
    this.srpEncoderMetricReporter = srpEncoderMetricReporter;
  }

  public static Function<ScheduledActiveMtMessage, EncodedActiveMtMessage> create(Clock clock, EncryptionProcessor encryptionProcessor, Priority priority,
      SrpEncoderMetricReporter srpEncoderMetricReporter) {
    Validate.notNull(clock, "clock");
    Validate.notNull(encryptionProcessor, "encryptionProcessor");
    Validate.notNull(priority, "priority");
    Validate.notNull(srpEncoderMetricReporter, "srpEncoderMetricReporter");

    return new SrpEncoderFunction(clock, encryptionProcessor, priority, srpEncoderMetricReporter);
  }

  private static Srp11Wrapper createSrp11Wrapper(EncodedSrpHeader encodedSrpHeader, AesGcmEncryptionResult aesGcmEncryptionResult) {
    return new Srp11WrapperBuilder()
        .setEncodedSrpHeader(encodedSrpHeader)
        .setEncryptedPayloadWithoutMac(aesGcmEncryptionResult.getEncryptedPayloadWithoutMac())
        .setMessageAuthenticationCode(aesGcmEncryptionResult.getMessageAuthenticationCode())
        .setNonce(aesGcmEncryptionResult.getInitializationVector())
        .build();
  }

  private static ServiceRoutingPduWrapper createSrp12ServiceRoutingPduWrapper(SrpOption srpOption) {
    return ServiceRoutingPduWrapper.ofSrp12Wrapper(createSrp12Wrapper(srpOption));
  }

  private static Srp12Wrapper createSrp12Wrapper(SrpOption srpOption) {
    return new Srp12WrapperBuilder()
        .setDestinationVersion(DestinationVersion.ofShort(srpOption.getSrpDestinationVersion().toShort()))
        .setPayload(srpOption.getSrpPayload().getImmutableByteArray())
        .build();
  }

  /**
   * Return ObsAlias of this vehicle. If encrypted swap is not enabled on the vehicle side, the com-setup request will not be srp12 and the obsAlias must be the
   * default obsAlias
   */
  private static ObsAlias getObsAlias(SrpDestinationService srpDestinationService, PersistedDeviceInfo persistedDeviceInfo) {
    if (ACTIVATION_SERVICE_ID.equals(srpDestinationService)) {
      return DEFAULT_OBS_ALIAS;
    }

    return persistedDeviceInfo.getDeviceInfo().getObsAlias();
  }

  private static SrpLevel getSrpLevel(PersistedDeviceInfo persistedDeviceInfo) {
    return persistedDeviceInfo.getDeviceInfo().getSrpLevel();
  }

  @Override
  public EncodedActiveMtMessage apply(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    Validate.notNull(scheduledActiveMtMessage, "scheduledActiveMtMessage");

    return encodeAsSrpAsn1(scheduledActiveMtMessage);
  }

  private ServiceRoutingPduWrapper createSrp10ServiceRoutingPduWrapper(SrpOption srpOption, PersistedDeviceInfo persistedDeviceInfo) {
    return ServiceRoutingPduWrapper.ofSrp10Wrappers(List.of(createSrp10Wrapper(srpOption, persistedDeviceInfo)));
  }

  private Srp10Wrapper createSrp10Wrapper(SrpOption srpOption, PersistedDeviceInfo persistedDeviceInfo) {
    short srpVersion = srpOption.getSrpDestinationVersion().toShort();
    SrpDestinationService srpDestinationService = srpOption.getSrpDestinationService();
    int srpService = srpDestinationService.toInt();

    return new Srp10WrapperBuilder()
        .setDestinationService(DestinationService.ofInt(srpService))
        .setDestinationVersion(DestinationVersion.ofShort(srpVersion))
        .setObsAlias(getObsAlias(srpDestinationService, persistedDeviceInfo))
        .setPriority(priority)
        .setSourceService(SourceService.ofInt(srpService))
        .setSourceVersion(SourceVersion.ofShort(srpVersion))
        .setVehicleTimestamp(clock.instant())
        .setPayload(srpOption.getSrpPayload().getImmutableByteArray())
        .build();
  }

  private ServiceRoutingPduWrapper createSrp11ServiceRoutingPduWrapper(SrpOption srpOption, PersistedDeviceInfo persistedDeviceInfo) {
    EncodedSrpHeader encodedSrpHeader = SrpConverter.encodeSrpHeaderWrapper(createSrpHeaderWrapper(srpOption, persistedDeviceInfo));
    AdditionalAuthenticatedData additionalAuthenticatedData = AdditionalAuthenticatedData.create(encodedSrpHeader.getImmutableByteArray());

    AesGcmEncryptionResult aesGcmEncryptionResult = encryptionProcessor.encryptPayload(persistedDeviceInfo, additionalAuthenticatedData,
        PlainTextPayload.create(srpOption.getSrpPayload().getImmutableByteArray()));

    return ServiceRoutingPduWrapper.ofSrp11Wrappers(List.of(createSrp11Wrapper(encodedSrpHeader, aesGcmEncryptionResult)));
  }

  private SrpHeaderWrapper createSrpHeaderWrapper(SrpOption srpOption, PersistedDeviceInfo persistedDeviceInfo) {
    return new SrpHeaderWrapperBuilder()
        .setDestinationService(DestinationService.ofInt(srpOption.getSrpDestinationService().toInt()))
        .setDestinationVersion(DestinationVersion.ofShort(srpOption.getSrpDestinationVersion().toShort()))
        .setObsAlias(persistedDeviceInfo.getDeviceInfo().getObsAlias())
        .setPriority(priority)
        .setSourceService(SourceService.ofInt(srpOption.getSrpDestinationService().toInt()))
        .setSourceVersion(SourceVersion.ofShort(srpOption.getSrpDestinationVersion().toShort()))
        .setVehicleTimestamp(clock.instant())
        .build();
  }

  private EncodedActiveMtMessage encodeAsSrpAsn1(ScheduledActiveMtMessage scheduledActiveMtMessage) {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = scheduledActiveMtMessage.identifiedActiveMtMessage();
    SrpOption srpOption = identifiedActiveMtMessage.joinedActiveMtMessage().persistedMtMessage().getMtMessage().getSrpOption();

    if (srpOption.isSrpLevel12()) {
      srpEncoderMetricReporter.onMtSrp12Received();
      return new EncodedActiveMtMessage(scheduledActiveMtMessage, createSrp12ServiceRoutingPduWrapper(srpOption));
    }

    PersistedDeviceInfo persistedDeviceInfo = identifiedActiveMtMessage.persistedDeviceInfo();
    if (getSrpLevel(persistedDeviceInfo) == SrpLevel.SRP_11) {
      srpEncoderMetricReporter.onMtSrp11Received();
      return new EncodedActiveMtMessage(scheduledActiveMtMessage, createSrp11ServiceRoutingPduWrapper(srpOption, persistedDeviceInfo));
    }

    srpEncoderMetricReporter.onMtSrp10Received();
    return new EncodedActiveMtMessage(scheduledActiveMtMessage, createSrp10ServiceRoutingPduWrapper(srpOption, persistedDeviceInfo));
  }
}
