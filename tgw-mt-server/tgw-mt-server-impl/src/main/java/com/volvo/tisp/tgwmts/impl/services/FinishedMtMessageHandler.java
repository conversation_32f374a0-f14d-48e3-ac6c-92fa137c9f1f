package com.volvo.tisp.tgwmts.impl.services;

import java.time.Duration;
import java.time.Instant;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.servicemonitoring.ServiceMonitoring;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.impl.jms.publisher.MtStatusPublisher;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.FinishedMtMessageHandlerMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

@Component
public class FinishedMtMessageHandler {
  private static final int LIMIT = 1;
  private static final Logger logger = LoggerFactory.getLogger(FinishedMtMessageHandler.class);
  private final boolean enableRetryWaitingOnSuccessMtDelivery;
  private final FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter;
  private final MtMessageRetryHandler mtMessageRetryHandler;
  private final MtStatusPublisher mtStatusPublisher;
  private final ServiceMonitoring serviceMonitoring;
  private final ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider;

  public FinishedMtMessageHandler(MtMessageRetryHandler mtMessageRetryHandler, FinishedMtMessageHandlerMetricReporter finishedMtMessageHandlerMetricReporter,
      MtStatusPublisher mtStatusPublisher, ThrottledActiveMtMessageParameterProvider throttledActiveMtMessageParameterProvider,
      @Value("${retry-waiting-messages-on-success-mt-status.enabled:true}") boolean enableRetryWaitingOnSuccessMtDelivery,
      ServiceMonitoring serviceMonitoring) {
    this.mtMessageRetryHandler = mtMessageRetryHandler;
    this.finishedMtMessageHandlerMetricReporter = finishedMtMessageHandlerMetricReporter;
    this.mtStatusPublisher = mtStatusPublisher;
    this.throttledActiveMtMessageParameterProvider = throttledActiveMtMessageParameterProvider;
    this.enableRetryWaitingOnSuccessMtDelivery = enableRetryWaitingOnSuccessMtDelivery;
    this.serviceMonitoring = serviceMonitoring;
  }

  private static ActiveMtMessage createActiveMtMessage(PersistedMtMessage persistedMtMessage) {
    return new ActiveMtMessageBuilder()
        .setMtMessageId(persistedMtMessage.getMtMessageId())
        .setSendSchemaStepId(SendSchemaStepId.ofInt(0))
        .setRetryAttempt(RetryAttempt.ofShort((short) 0))
        .setTimeout(Instant.now())
        .build();
  }

  private static void validateParameters(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");
    Validate.notNull(joinedActiveMtMessage, "joinedActiveMtMessage");
  }

  public void onDelivered(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {

    validateParameters(activeMtMessageWriter, joinedActiveMtMessage);

    publishMtStatusAndProceedWithNextActiveMtMessage(activeMtMessageWriter, joinedActiveMtMessage, MtStatus.DELIVERED);

    Vpi vpi = joinedActiveMtMessage.persistedVehicleLock().getVehicleLock().getVpi();
    if (enableRetryWaitingOnSuccessMtDelivery) {
      logger.debug("retrying waiting messages for vpi: {}", vpi);
      mtMessageRetryHandler.initiateRetryForWaitingMtMessages(activeMtMessageWriter, vpi);
    }

    logServiceMonitoring(joinedActiveMtMessage, vpi);
  }

  public void onDeviceNotFound(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi) {
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");
    Validate.notNull(vpi, "vpi");

    List<PersistedMtMessage> persistedMtMessages = activeMtMessageWriter.findMtMessagesByVpi(vpi);

    publishFailedMtStatuses(vpi, persistedMtMessages);

    deleteVpiLock(activeMtMessageWriter, vpi);
  }

  public void onRejected(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {
    validateParameters(activeMtMessageWriter, joinedActiveMtMessage);

    publishMtStatusAndProceedWithNextActiveMtMessage(activeMtMessageWriter, joinedActiveMtMessage, MtStatus.FAILED);
  }

  public void onServiceUnsupported(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {
    validateParameters(activeMtMessageWriter, joinedActiveMtMessage);

    publishMtStatusAndProceedWithNextActiveMtMessage(activeMtMessageWriter, joinedActiveMtMessage, MtStatus.SERVICE_UNSUPPORTED);
  }

  /**
   * Throttling will decrement the current {@code sendSchemaStepId} by one and persist it with a configured timeout. When the message is
   * processed by the {@link SendSchemaProcessor} the next time, it will increment the step id to the same value again,
   * thus retrying the same {@link SendSchemaStep} which is the intention of the throttling status
   */
  public void onThrottled(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {
    validateParameters(activeMtMessageWriter, joinedActiveMtMessage);

    logger.debug("throttled status received for vpi: {}", joinedActiveMtMessage.persistedVehicleLock().getVehicleLock().getVpi());
    activeMtMessageWriter.updateActiveMtMessages(
        List.of(throttledActiveMtMessageParameterProvider.get(joinedActiveMtMessage.persistedActiveMtMessage())));
  }

  public void onTimeout(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage) {
    validateParameters(activeMtMessageWriter, joinedActiveMtMessage);

    publishMtStatusAndProceedWithNextActiveMtMessage(activeMtMessageWriter, joinedActiveMtMessage, MtStatus.TIMEOUT);
  }

  private void deleteMtMessage(ActiveMtMessageWriter activeMtMessageWriter, PersistedMtMessage persistedMtMessage) {
    int numberOfDeletedRows = activeMtMessageWriter.deleteMtMessageById(persistedMtMessage.getMtMessageId());

    if (numberOfDeletedRows == 0) {
      finishedMtMessageHandlerMetricReporter.onFailedMtMessageDeletion();
      logger.warn("failed to delete mt_message: {}", persistedMtMessage);
    }
  }

  private void deleteVpiLock(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi) {
    int numberOfDeletedRows = activeMtMessageWriter.deleteVehicleLockByVpi(vpi);

    if (numberOfDeletedRows == 0) {
      finishedMtMessageHandlerMetricReporter.onFailedVehicleLockDeletion();
      logger.warn("failed to delete vehicle_lock for VPI: {}", vpi);
    }
  }

  private void findAndInsertNextActiveMtMessage(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi) {
    activeMtMessageWriter
        .findOldestNonActiveMtMessages(vpi, LIMIT)
        .forEach(persistedMtMessage -> insertActiveMtMessage(activeMtMessageWriter, persistedMtMessage));
  }

  private void insertActiveMtMessage(ActiveMtMessageWriter activeMtMessageWriter, PersistedMtMessage persistedMtMessage) {
    Either<InsertionFailure, ActiveMtMessageId> either = activeMtMessageWriter.insertActiveMtMessage(createActiveMtMessage(persistedMtMessage));

    if (either.isLeft()) {
      logger.warn("failed to insert active_mt_message for mt_message: {} {}", persistedMtMessage, either.getLeft());
      finishedMtMessageHandlerMetricReporter.onFailedActiveMtMessageInsertion();
    }
  }

  private void logServiceMonitoring(JoinedActiveMtMessage joinedActiveMtMessage, Vpi vpi) {
    Instant now = Instant.now();
    VehicleIdentifier vehicleIdentifier = VehicleIdentifier.fromString(vpi.toString());
    Instant receivedTime = joinedActiveMtMessage.persistedMtMessage().getCreated();
    Duration messageTotalLatency = Duration.between(receivedTime, now);
    serviceMonitoring.ifEnabled(vehicleIdentifier, receivedTime, receivedTime, messageTotalLatency);
  }

  private void publishFailedMtStatuses(Vpi vpi, List<PersistedMtMessage> persistedMtMessages) {
    for (PersistedMtMessage persistedMtMessage : persistedMtMessages) {
      publishMtStatus(persistedMtMessage, vpi, MtStatus.FAILED);
    }
  }

  private void publishMtStatus(PersistedMtMessage persistedMtMessage, Vpi vpi, MtStatus mtStatus) {
    persistedMtMessage
        .getMtMessage()
        .getReplyOption()
        .ifPresent(replyOption -> mtStatusPublisher.publishMtStatus(mtStatus, replyOption, vpi));
  }

  private void publishMtStatusAndProceedWithNextActiveMtMessage(ActiveMtMessageWriter activeMtMessageWriter, JoinedActiveMtMessage joinedActiveMtMessage,
      MtStatus mtStatus) {
    Vpi vpi = joinedActiveMtMessage.persistedVehicleLock().getVehicleLock().getVpi();
    PersistedMtMessage persistedMtMessage = joinedActiveMtMessage.persistedMtMessage();
    publishMtStatus(persistedMtMessage, vpi, mtStatus);

    deleteMtMessage(activeMtMessageWriter, persistedMtMessage);

    findAndInsertNextActiveMtMessage(activeMtMessageWriter, vpi);
  }
}
