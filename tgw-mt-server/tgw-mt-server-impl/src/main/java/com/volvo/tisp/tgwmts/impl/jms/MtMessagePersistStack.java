package com.volvo.tisp.tgwmts.impl.jms;

import java.util.Optional;
import java.util.function.Consumer;
import java.util.function.Function;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.services.EnqueueingTypeProcessor;
import com.volvo.tisp.tgwmts.impl.services.MtPersister;
import com.volvo.tisp.tgwmts.impl.services.VehicleLockIdProvider;
import com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper.MtDoorkeeper;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;

@Component
public final class MtMessagePersistStack {
  private final EnqueueingTypeProcessor enqueueingTypeProcessor;
  private final Consumer<IntegrationLogParameter> loggingHelper;
  private final MtDoorkeeper mtDoorkeeper;
  private final MtPersister mtPersister;
  private final Function<Vpi, Optional<PersistedDeviceInfo>> tgwIdentifierFunction;
  private final VehicleLockIdProvider vehicleLockIdProvider;

  public MtMessagePersistStack(EnqueueingTypeProcessor enqueueingTypeProcessor, Consumer<IntegrationLogParameter> loggingHelper, MtDoorkeeper mtDoorkeeper,
      MtPersister mtPersister, Function<Vpi, Optional<PersistedDeviceInfo>> tgwIdentifierFunction, VehicleLockIdProvider vehicleLockIdProvider) {
    this.enqueueingTypeProcessor = enqueueingTypeProcessor;
    this.loggingHelper = loggingHelper;
    this.mtDoorkeeper = mtDoorkeeper;
    this.mtPersister = mtPersister;
    this.tgwIdentifierFunction = tgwIdentifierFunction;
    this.vehicleLockIdProvider = vehicleLockIdProvider;
  }

  public EnqueueingTypeProcessor getEnqueueingTypeProcessor() {
    return enqueueingTypeProcessor;
  }

  public Consumer<IntegrationLogParameter> getLoggingHelper() {
    return loggingHelper;
  }

  public MtDoorkeeper getMtDoorkeeper() {
    return mtDoorkeeper;
  }

  public MtPersister getMtPersister() {
    return mtPersister;
  }

  public Function<Vpi, Optional<PersistedDeviceInfo>> getTgwIdentifierFunction() {
    return tgwIdentifierFunction;
  }

  public VehicleLockIdProvider getVehicleLockIdProvider() {
    return vehicleLockIdProvider;
  }
}
