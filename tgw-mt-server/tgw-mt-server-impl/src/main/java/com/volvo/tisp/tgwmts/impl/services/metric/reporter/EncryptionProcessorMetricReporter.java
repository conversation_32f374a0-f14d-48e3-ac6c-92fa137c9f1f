package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class EncryptionProcessorMetricReporter {
  private final Counter encryptPayloadDeviceFailCounter;
  private final Counter encryptSrp11ReceivedCounter;

  public EncryptionProcessorMetricReporter(MeterRegistry meterRegistry) {
    encryptPayloadDeviceFailCounter = meterRegistry.counter("service.encryption.encrypt.fail.encryptpayloadfail.deviceaeskey");
    encryptSrp11ReceivedCounter = meterRegistry.counter("service.encryption.encrypt.srp11.received");
  }

  public void onEncryptPayloadDeviceFail() {
    encryptPayloadDeviceFailCounter.increment();
  }

  public void onEncryptSrp11Received() {
    encryptSrp11ReceivedCounter.increment();
  }
}
