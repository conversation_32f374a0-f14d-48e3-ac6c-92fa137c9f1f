package com.volvo.tisp.tgwmts.impl.db.queue.reporter;

import java.io.Closeable;
import java.time.Duration;
import java.util.Timer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.EventListener;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtMessageQueueReporter implements Closeable {
  public static final Duration DEFAULT_REPORT_DURATION = Duration.ofSeconds(10);
  private static final Logger logger = LoggerFactory.getLogger(MtMessageQueueReporter.class);
  private static final String TIMER_NAME = "mtMessageReporter";

  private final MtMessageQueueReporterTask mtMessageQueueReporterTask;
  private final Duration reportDuration;
  private final Timer timer = new Timer(TIMER_NAME);

  public MtMessageQueueReporter(MtMessageQueueReporterTask mtMessageQueueReporterTask, Duration reportDuration) {
    Validate.notNull(mtMessageQueueReporterTask, "mtMessageQueueReporterTask");
    Validate.isPositive(reportDuration, "reportDuration");

    this.mtMessageQueueReporterTask = mtMessageQueueReporterTask;
    this.reportDuration = reportDuration;
  }

  @Override
  public void close() {
    timer.cancel();
    logger.info("Stopped timer: {}", TIMER_NAME);
  }

  @EventListener(ApplicationStartedEvent.class)
  public void start() {
    timer.scheduleAtFixedRate(mtMessageQueueReporterTask, 0, reportDuration.toMillis());
  }
}
