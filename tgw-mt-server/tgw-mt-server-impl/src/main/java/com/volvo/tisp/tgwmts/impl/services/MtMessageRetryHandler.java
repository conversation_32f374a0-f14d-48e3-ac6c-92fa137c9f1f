package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriterFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameter;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.UpdateActiveMtMessageParameterBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.FilterableSendSchemaFetcher;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepFilterProducer;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtMessageRetryHandlerMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class MtMessageRetryHandler {
  private static final Logger logger = LoggerFactory.getLogger(MtMessageRetryHandler.class);

  private final ActiveMtMessageWriterFactory activeMtMessageWriterFactory;
  private final Clock clock;
  private final DeviceInfoReaderFactory deviceInfoReaderFactory;
  private final FilterableSendSchemaFetcher filterableSendSchemaFetcher;
  private final MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter;
  private final SendSchemaStepFilterProducer sendSchemaStepFilterProducer;

  public MtMessageRetryHandler(
      ActiveMtMessageWriterFactory activeMtMessageWriterFactory,
      Clock clock,
      DeviceInfoReaderFactory deviceInfoReaderFactory,
      FilterableSendSchemaFetcher filterableSendSchemaFetcher,
      MtMessageRetryHandlerMetricReporter mtMessageRetryHandlerMetricReporter,
      SendSchemaStepFilterProducer sendSchemaStepFilterProducer) {

    this.activeMtMessageWriterFactory = activeMtMessageWriterFactory;
    this.clock = clock;
    this.deviceInfoReaderFactory = deviceInfoReaderFactory;
    this.filterableSendSchemaFetcher = filterableSendSchemaFetcher;
    this.mtMessageRetryHandlerMetricReporter = mtMessageRetryHandlerMetricReporter;
    this.sendSchemaStepFilterProducer = sendSchemaStepFilterProducer;
  }

  private static SendSchemaStepId getSendSchemaStepId(JoinedActiveMtMessage joinedActiveMtMessage, PersistedActiveMtMessage persistedActiveMtMessage,
      SendSchema sendSchema) {
    if (isRetryAllowed(joinedActiveMtMessage, sendSchema)) {
      return SendSchemaStepId.ofInt(0);
    }
    return persistedActiveMtMessage.getActiveMtMessage().getSendSchemaStepId();
  }

  private static boolean isInWaitSendSchemaStep(JoinedActiveMtMessage joinedActiveMtMessage, SendSchema sendSchema) {
    SendSchemaStepId sendSchemaStepId = joinedActiveMtMessage.persistedActiveMtMessage().getActiveMtMessage().getSendSchemaStepId();
    logger.debug("sendSchemaName: {}, sendSchemaStepId: {}", sendSchema.getSendSchemaName(), sendSchemaStepId);

    return sendSchema.getSendSchemaStep(sendSchemaStepId)
        .map(SendSchemaStep::getSendSchemaStepType)
        .filter(sendSchemaStepType -> sendSchemaStepType == SendSchemaStepType.WAIT)
        .isPresent();
  }

  private static boolean isRetryAllowed(JoinedActiveMtMessage joinedActiveMtMessage, SendSchema sendSchema) {
    RetryAttempt retryAttempt = joinedActiveMtMessage.persistedActiveMtMessage().getActiveMtMessage().getRetryAttempt();
    logger.debug("ActiveMtMessage retryAttempt: {}", retryAttempt);

    return sendSchema.getMaxRetryAttempts() > retryAttempt.toShort();
  }

  public void initiateRetryForWaitingMtMessages(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi) {
    Instant startTime = clock.instant();
    List<JoinedActiveMtMessage> joinedActiveMtMessages = activeMtMessageWriter.findActiveMtMessagesByVpiWithVpiLock(vpi);
    mtMessageRetryHandlerMetricReporter.onFindActiveMtMessagesByVpiWithVpiLock(Duration.between(startTime, clock.instant()));

    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      Optional<PersistedDeviceInfo> deviceOption = deviceInfoReader.findDeviceInfoByVpi(vpi);

      if (deviceOption.isPresent()) {
        updateActiveMtMessageParameters(activeMtMessageWriter, vpi, startTime, joinedActiveMtMessages, deviceOption.get().getDeviceInfo());
      } else {
        logger.warn("missing device when trying to perform send schema restart");
        mtMessageRetryHandlerMetricReporter.onMissingDeviceCounter();
      }
    }
  }

  public void initiateRetryForWaitingMtMessages(Vpi vpi) {
    Validate.notNull(vpi, "vpi");

    logger.debug("Processing MT message retry for vpi: {}", vpi);
    try (ActiveMtMessageWriter activeMtMessageWriter = activeMtMessageWriterFactory.createReadCommitted()) {
      activeMtMessageWriter.startTransactionWithLockTimeout();

      initiateRetryForWaitingMtMessages(activeMtMessageWriter, vpi);

      activeMtMessageWriter.commitTransaction();
    }
  }

  private List<UpdateActiveMtMessageParameter> createUpdateActiveMtMessageParameters(List<JoinedActiveMtMessage> joinedActiveMtMessages,
      DeviceInfo deviceInfo) {
    List<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters = new ArrayList<>();

    for (JoinedActiveMtMessage joinedActiveMtMessage : joinedActiveMtMessages) {
      SendSchema sendSchema = getSendSchema(joinedActiveMtMessage, deviceInfo);

      if (isInWaitSendSchemaStep(joinedActiveMtMessage, sendSchema)) {
        PersistedActiveMtMessage persistedActiveMtMessage = joinedActiveMtMessage.persistedActiveMtMessage();
        updateActiveMtMessageParameters.add(new UpdateActiveMtMessageParameterBuilder()
            .setActiveMtMessageId(persistedActiveMtMessage.getActiveMtMessageId())
            .setRetryAttempt(RetryAttempt.ofShort((short) (persistedActiveMtMessage.getActiveMtMessage().getRetryAttempt().toShort() + 1)))
            .setSendSchemaStepId(getSendSchemaStepId(joinedActiveMtMessage, persistedActiveMtMessage, sendSchema))
            .setTimeout(clock.instant())
            .build());
      }
    }

    return updateActiveMtMessageParameters;
  }

  private SendSchema getSendSchema(JoinedActiveMtMessage joinedActiveMtMessage, DeviceInfo deviceInfo) {
    SendSchemaName sendSchemaName = joinedActiveMtMessage.persistedMtMessage().getMtMessage().getSendSchemaName();
    return filterableSendSchemaFetcher.fetch(sendSchemaName, sendSchemaStepFilterProducer.createSendSchemaStepFilter(deviceInfo));
  }

  private void updateActiveMtMessageParameters(ActiveMtMessageWriter activeMtMessageWriter, Vpi vpi, Instant startTime,
      List<JoinedActiveMtMessage> joinedActiveMtMessages, DeviceInfo deviceInfo) {
    List<UpdateActiveMtMessageParameter> updateActiveMtMessageParameters = createUpdateActiveMtMessageParameters(joinedActiveMtMessages, deviceInfo);

    if (updateActiveMtMessageParameters.isEmpty()) {
      logger.debug("No active mt message in wait state, vpi: {}", vpi);
      mtMessageRetryHandlerMetricReporter.onNoActiveMtMessageFound();
    } else {
      activeMtMessageWriter.updateActiveMtMessages(updateActiveMtMessageParameters);
      logger.debug("MT message retry initiated for {} messages with vpi: {}", updateActiveMtMessageParameters.size(), vpi);

      mtMessageRetryHandlerMetricReporter.onRetryInitiated(updateActiveMtMessageParameters.size(), Duration.between(startTime, clock.instant()));
    }
  }
}
