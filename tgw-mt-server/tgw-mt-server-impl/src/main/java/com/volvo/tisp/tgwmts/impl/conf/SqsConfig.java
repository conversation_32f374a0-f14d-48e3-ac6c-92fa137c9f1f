package com.volvo.tisp.tgwmts.impl.conf;

import java.net.URI;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.volvo.tisp.tgwmts.impl.conf.properties.SqsQueueProperties;

import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.SqsClientBuilder;

@Configuration
public class SqsConfig {
  @Bean
  SqsClient createSqsClient(SqsQueueProperties sqsQueueProperties) {
    SqsClientBuilder sqsClientBuilder = SqsClient.builder();
    sqsQueueProperties.getEndpoint().ifPresent(endpoint -> sqsClientBuilder.endpointOverride(URI.create(endpoint)));
    sqsClientBuilder.region(Region.of(sqsQueueProperties.getRegion()));
    return sqsClientBuilder.build();
  }
}
