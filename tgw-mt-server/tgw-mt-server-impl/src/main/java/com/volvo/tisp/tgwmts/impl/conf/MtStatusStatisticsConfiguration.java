package com.volvo.tisp.tgwmts.impl.conf;

import java.nio.ByteBuffer;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.function.Function;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtStatusStatisticsConfigProperties;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtStatusStatisticsMetricReporter;
import com.volvo.tisp.tgwmts.impl.services.statistics.MtStatusStatisticsPublisher;
import com.volvo.tisp.vc.vcss.client.protobuf.MessageTypes;

import io.github.resilience4j.timelimiter.TimeLimiter;

@Configuration
class MtStatusStatisticsConfiguration {
  private static ScheduledExecutorService createScheduledExecutorService(MtStatusStatisticsConfigProperties mtStatusStatisticsConfigProperties) {
    ScheduledThreadPoolExecutor scheduledThreadPoolExecutor = new ScheduledThreadPoolExecutor(1);
    scheduledThreadPoolExecutor.setMaximumPoolSize(mtStatusStatisticsConfigProperties.maximumPublisherThreads());
    return scheduledThreadPoolExecutor;
  }

  private static TimeLimiter createTimeLimiter(MtStatusStatisticsConfigProperties mtStatusStatisticsConfigProperties) {
    return TimeLimiter.of(mtStatusStatisticsConfigProperties.maxWaitTimeForPublish());
  }

  @Bean
  MessagePublisher<ByteBuffer> createMessagePublisher(MessagePublisher.Builder builder) {
    return builder
        .messageType(MessageTypes.VCSS_MT_STATUS_STATISTIC, ByteBuffer.class)
        .version(MessageTypes.VERSION_1_0, Function.identity())
        .build();
  }

  @Bean
  MtStatusStatisticsPublisher createMtStatusStatisticsPublisher(
      MessagePublisher<ByteBuffer> messagePublisher,
      MtStatusStatisticsMetricReporter mtStatusStatisticsMetricReporter,
      MtStatusStatisticsConfigProperties mtStatusStatisticsConfigProperties) {
    return new MtStatusStatisticsPublisher(
        messagePublisher,
        mtStatusStatisticsMetricReporter,
        createTimeLimiter(mtStatusStatisticsConfigProperties),
        createScheduledExecutorService(mtStatusStatisticsConfigProperties));
  }
}
