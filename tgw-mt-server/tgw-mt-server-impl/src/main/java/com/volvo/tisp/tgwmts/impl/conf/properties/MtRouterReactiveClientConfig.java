package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.net.URI;
import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import software.amazon.awssdk.utils.Validate;

@Configuration
public class MtRouterReactiveClientConfig {
  private final URI baseUrl;
  private final String mtRouterRequestPath;
  private final Duration requestTimeout;

  public MtRouterReactiveClientConfig(
      @Value("${mtrouter.base-url:http://mtrouter}") URI baseUrl,
      @Value("${mtrouter.request-path:/api/v1/mt/message}") String mtRouterRequestPath,
      @Value("${mtrouter.request-timeout:PT5S}") Duration requestTimeout) {
    Validate.isNotNegative(requestTimeout, "requestTimeout");

    this.mtRouterRequestPath = mtRouterRequestPath;
    this.baseUrl = baseUrl;
    this.requestTimeout = requestTimeout;
  }

  public URI getBaseUrl() {
    return baseUrl;
  }

  public String getMtRouterRequestPath() {
    return mtRouterRequestPath;
  }

  public Duration getRequestTimeout() {
    return requestTimeout;
  }
}
