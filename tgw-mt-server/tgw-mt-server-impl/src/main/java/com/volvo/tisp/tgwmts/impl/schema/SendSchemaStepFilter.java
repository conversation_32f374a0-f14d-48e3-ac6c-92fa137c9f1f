package com.volvo.tisp.tgwmts.impl.schema;

import java.util.Set;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public record SendSchemaStepFilter(Set<SendSchemaStepType> stepTypes) {
  public SendSchemaStepFilter {
    Validate.notNull(stepTypes, "stepTypes");
  }

  public static SendSchemaStepFilter of(Set<SendSchemaStepType> stepTypes) {
    return new SendSchemaStepFilter(stepTypes);
  }
}
