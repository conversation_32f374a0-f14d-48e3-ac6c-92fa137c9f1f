package com.volvo.tisp.tgwmts.impl.services;

import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.stream.IntStream;

import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtRetrySchedulerService {
  private final long delayMillis;
  private final MtMessageRetryHandler mtMessageRetryHandler;
  private final ScheduledExecutorService scheduledExecutorService;

  public MtRetrySchedulerService(long delayMillis, MtMessageRetryHandler mtMessageRetryHandler, ScheduledExecutorService scheduledExecutorService) {
    Validate.isPositive(delayMillis, "delayMillis");
    Validate.notNull(mtMessageRetryHandler, "mtMessageRetryHandler");
    Validate.notNull(scheduledExecutorService, "scheduledExecutorService");

    this.delayMillis = delayMillis;
    this.mtMessageRetryHandler = mtMessageRetryHandler;
    this.scheduledExecutorService = scheduledExecutorService;
  }

  @SuppressWarnings("FutureReturnValueIgnored")
  public void scheduleRetryTasks(List<Vpi> vpis) {
    Validate.notEmpty(vpis, "vpis");

    IntStream.range(0, vpis.size()).forEach(i -> scheduledExecutorService.schedule(retryMessagesInWait(vpis.get(i)), delayMillis * i, TimeUnit.MILLISECONDS));
  }

  private Runnable retryMessagesInWait(Vpi vpi) {
    return () -> mtMessageRetryHandler.initiateRetryForWaitingMtMessages(vpi);
  }
}
