package com.volvo.tisp.tgwmts.impl.scheduler;

import java.io.Closeable;
import java.util.Collection;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.tgwmts.impl.utils.ThreadUtils;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtSchedulerContext implements Closeable {
  private static final Logger logger = LoggerFactory.getLogger(MtSchedulerContext.class);

  private final Collection<Thread> threads;

  public MtSchedulerContext(Collection<Thread> threads) {
    Validate.notEmpty(threads, "threads");

    this.threads = threads;
  }

  @Override
  public void close() {
    ThreadUtils.interrupt(threads);
    ThreadUtils.join(threads);

    logger.info("MtSchedulerContext closed");
  }

  public void start() {
    ThreadUtils.startThreads(threads);

    logger.info("MtSchedulerContext started");
  }
}
