package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;

@Component
public class VehicleLockIdProviderMetricReporter {
  private final MeterRegistry meterRegistry;
  private final Timer vehicleLockInsertionTimer;
  private final Timer vpiLockTimer;

  public VehicleLockIdProviderMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    vehicleLockInsertionTimer = meterRegistry.timer("vehicle.lock.id.provider.insertion");
    vpiLockTimer = meterRegistry.timer("vpi.lock");
  }

  public void onVehicleLockInsertionFailure(InsertionFailureReason insertionFailureReason) {
    Validate.notNull(insertionFailureReason, "insertionFailureReason");

    meterRegistry.counter("vehicle.lock.id.provider.insertion.failure", Tags.of("reason", insertionFailureReason.name())).increment();
  }

  public void onVehicleLockInsertionSuccess(Duration duration) {
    Validate.notNegative(duration, "duration");

    vehicleLockInsertionTimer.record(duration);
  }

  public void onVpiLock(Duration duration) {
    Validate.notNegative(duration, "duration");

    vpiLockTimer.record(duration);
  }
}
