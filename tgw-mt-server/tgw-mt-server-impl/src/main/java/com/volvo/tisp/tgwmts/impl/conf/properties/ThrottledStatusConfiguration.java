package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Configuration
public class ThrottledStatusConfiguration {
  private final Duration waitTimeout;

  public ThrottledStatusConfiguration(@Value("${throttled-wait-timeout:PT1M}") Duration waitTimeout) {
    Validate.notNegative(waitTimeout, "waitTimeout");

    this.waitTimeout = waitTimeout;
  }

  public Duration getWaitTimeout() {
    return waitTimeout;
  }
}
