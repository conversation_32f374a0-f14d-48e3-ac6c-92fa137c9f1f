package com.volvo.tisp.tgwmts.impl.conf;

import java.time.Duration;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.zaxxer.hikari.HikariDataSource;

final class DataSourceFactory {
  private static final Logger logger = LoggerFactory.getLogger(DataSourceFactory.class);

  private DataSourceFactory() {
    throw new IllegalStateException();
  }

  static DataSource createDataSource(DatasourceConfigProperties datasourceConfigProperties) {
    Validate.notNull(datasourceConfigProperties, "datasourceConfigProperties");

    final String jdbcUrl = datasourceConfigProperties.getDbUrl();
    final String username = datasourceConfigProperties.getDbUsername();
    final String password = datasourceConfigProperties.getDbPassword();
    final int maxPoolSize = datasourceConfigProperties.getDbMaxPoolSize();
    final int minPoolSize = datasourceConfigProperties.getDbMinPoolSize();
    final Duration leakDetectionThreshold = datasourceConfigProperties.getDbLeakDetectionThresholdDuration();

    logger.info("Creating DataSource: jdbcUrl={}, username={}, password={}, leakDetectionThreshold={}", jdbcUrl, username, password, leakDetectionThreshold);

    HikariDataSource hikariDataSource = new HikariDataSource();

    hikariDataSource.setJdbcUrl(jdbcUrl);
    hikariDataSource.setUsername(username);
    hikariDataSource.setPassword(password);
    hikariDataSource.setMaximumPoolSize(maxPoolSize);
    hikariDataSource.setMinimumIdle(minPoolSize);
    hikariDataSource.setLeakDetectionThreshold(leakDetectionThreshold.toMillis());

    return hikariDataSource;
  }
}
