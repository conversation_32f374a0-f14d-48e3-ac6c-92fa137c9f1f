package com.volvo.tisp.tgwmts.impl.rest;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.tce.proto.ConnectionEstablishedEvent;
import com.volvo.tisp.tgwmts.impl.services.MtMessageRetryHandler;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

@RestController
@RequestMapping(path = ConnectionEstablishedEventRestController.REQUEST_MAPPING_PATH)
@Authentication(required = false)
public class ConnectionEstablishedEventRestController {
  public static final String REQUEST_MAPPING_PATH = "/api/v1/connectionEstablishedEvent";
  private final MtMessageRetryHandler mtMessageRetryHandler;

  public ConnectionEstablishedEventRestController(MtMessageRetryHandler mtMessageRetryHandler) {
    this.mtMessageRetryHandler = mtMessageRetryHandler;
  }

  private static Either<IllegalArgumentException, Vpi> toVpi(ConnectionEstablishedEvent connectionEstablishedEvent) {
    try {
      if (!connectionEstablishedEvent.hasVpi()) {
        return Either.left(new IllegalArgumentException("The value for vpi field in the connectionEstablishedEvent is null"));
      }

      return Either.right(Vpi.ofString(connectionEstablishedEvent.getVpi().getValue()));
    } catch (RuntimeException e) {
      return Either.left(new IllegalArgumentException("Error in getting VPI from connectionEstablishedEvent", e));
    }
  }

  @PostMapping(consumes = "application/x-protobuf")
  public ResponseEntity<String> postConnectionEstablishedEvent(@RequestBody ConnectionEstablishedEvent connectionEstablishedEvent) {
    Validate.notNull(connectionEstablishedEvent, "connectionEstablishedEvent");

    Either<IllegalArgumentException, Vpi> either = toVpi(connectionEstablishedEvent);

    if (either.isLeft()) {
      return ResponseEntity.badRequest().body(either.getLeft().getMessage());
    }

    mtMessageRetryHandler.initiateRetryForWaitingMtMessages(either.getRight());

    return ResponseEntity.ok().build();
  }
}
