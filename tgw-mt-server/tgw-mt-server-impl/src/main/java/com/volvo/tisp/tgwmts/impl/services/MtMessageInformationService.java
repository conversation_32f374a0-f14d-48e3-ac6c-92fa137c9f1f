package com.volvo.tisp.tgwmts.impl.services;

import com.volvo.tisp.tgw.device.info.cache.core.api.CacheDeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.model.ActiveMessageDetails;
import com.volvo.tisp.tgwmts.impl.model.MtMessageDetails;
import com.volvo.tisp.tgwmts.impl.model.MtMessageInformation;
import com.volvo.tisp.tgwmts.impl.schema.*;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class MtMessageInformationService {
    private final ActiveMtMessageReaderFactory activeMtMessageReaderFactory;
    private final CacheDeviceInfoReader cacheDeviceInfoReader;
    private final FilterableSendSchemaFetcher filterableSendSchemaFetcher;
    private final SendSchemaStepFilterProducer sendSchemaStepFilterProducer;

    public MtMessageInformationService(ActiveMtMessageReaderFactory activeMtMessageReaderFactory,
                                       CacheDeviceInfoReader cacheDeviceInfoReader,
                                       FilterableSendSchemaFetcher filterableSendSchemaFetcher,
                                       SendSchemaStepFilterProducer sendSchemaStepFilterProducer) {
        this.activeMtMessageReaderFactory = activeMtMessageReaderFactory;
        this.cacheDeviceInfoReader = cacheDeviceInfoReader;
        this.filterableSendSchemaFetcher = filterableSendSchemaFetcher;
        this.sendSchemaStepFilterProducer = sendSchemaStepFilterProducer;
    }

    public Optional<MtMessageInformation> getMessageInformationByVpi(Vpi vpi) {
        try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
            List<JoinedMtMessage> joinedMtMessages = activeMtMessageReader.findMtMessageAndActiveMtMessages(vpi);
            if (joinedMtMessages.isEmpty()) return Optional.empty();
            else {
                List<MtMessageDetails> mtMessageDetails = getMtMessages(vpi, joinedMtMessages);

                MtMessageInformation mtMessageInformation = new MtMessageInformation(
                        vpi.toString(),
                        mtMessageDetails
                );
                return Optional.of(mtMessageInformation);
            }
        }
    }

    private List<MtMessageDetails> getMtMessages(Vpi vpi, List<JoinedMtMessage> joinedMtMessages) {
        Optional<PersistedDeviceInfo> deviceInfoByVpi = cacheDeviceInfoReader.findDeviceInfoByVpi(vpi);
        return joinedMtMessages.stream()
                .map(joinedMtMessage -> createMtMessage(deviceInfoByVpi, joinedMtMessage))
                .toList();
    }

    private MtMessageDetails createMtMessage(Optional<PersistedDeviceInfo> deviceInfo, JoinedMtMessage joinedMtMessage) {
        return new MtMessageDetails(joinedMtMessage.persistedMtMessage().getMtMessage().getTid().toString(),
                joinedMtMessage.persistedMtMessage().getMtMessage().getSendSchemaName().value(),
                joinedMtMessage.persistedMtMessage().getCreated(),
                joinedMtMessage.persistedMtMessage().getMtMessage().getQueueId().toString(),
                joinedMtMessage.persistedMtMessage().getMtMessage().getSrpOption().getSrpDestinationService().toInt(),
                joinedMtMessage.persistedMtMessage().getMtMessage().getSrpOption().getSrpDestinationVersion().toShort(),
                joinedMtMessage.persistedMtMessage().getMtMessage().getReplyOption().flatMap(replyOption -> Optional.of(replyOption.getReplyTo().toString())),
                getActiveMessageDetail(deviceInfo, joinedMtMessage));
    }

    private ActiveMessageDetails getActiveMessageDetail(Optional<PersistedDeviceInfo> deviceInfoByVpi, JoinedMtMessage joinedMtMessage) {
        return joinedMtMessage.persistedActiveMtMessage()
                .map(persistedActiveMtMessage ->
                        createActiveMtMessageItem(persistedActiveMtMessage,
                                deviceInfoByVpi.flatMap(info -> getSendSchemaStep(joinedMtMessage, info))
                        )
                ).orElse(null);
    }

    private static ActiveMessageDetails createActiveMtMessageItem(PersistedActiveMtMessage persistedActiveMtMessage, Optional<SendSchemaStep> sendSchemaStep) {
        return new ActiveMessageDetails(persistedActiveMtMessage.getActiveMtMessageId().toLong(),
                sendSchemaStep.map(SendSchemaStep::getSendSchemaStepType).orElseThrow(),
                persistedActiveMtMessage.getActiveMtMessage().getRetryAttempt().toShort(),
                persistedActiveMtMessage.getCreated(),
                persistedActiveMtMessage.getUpdated(),
                persistedActiveMtMessage.getActiveMtMessage().getTimeout()
        );
    }

    private Optional<SendSchemaStep> getSendSchemaStep(JoinedMtMessage joinedMtMessage, PersistedDeviceInfo deviceInfoByVpi) {
        SendSchemaName sendSchemaName = joinedMtMessage.persistedMtMessage().getMtMessage().getSendSchemaName();
        SendSchemaStepFilter sendSchemaStepFilter = sendSchemaStepFilterProducer.createSendSchemaStepFilter(deviceInfoByVpi.getDeviceInfo());
        SendSchema sendSchema = filterableSendSchemaFetcher.fetch(sendSchemaName, sendSchemaStepFilter);

        return joinedMtMessage.persistedActiveMtMessage()
                .map(activeMessage -> activeMessage.getActiveMtMessage().getSendSchemaStepId())
                .flatMap(sendSchema::getSendSchemaStep);
    }
}
