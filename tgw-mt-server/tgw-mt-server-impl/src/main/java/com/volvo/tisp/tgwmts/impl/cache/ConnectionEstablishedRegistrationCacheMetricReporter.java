package com.volvo.tisp.tgwmts.impl.cache;

import java.util.concurrent.atomic.AtomicInteger;

import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;

@Component
public class ConnectionEstablishedRegistrationCacheMetricReporter {
  private final AtomicInteger connectionEstablishedRegistrationCacheSize;
  private final Counter invalidExpiryTimeCounter;

  public ConnectionEstablishedRegistrationCacheMetricReporter(MeterRegistry meterRegistry) {
    connectionEstablishedRegistrationCacheSize = meterRegistry.gauge("conn-est-reg-cache-size", new AtomicInteger(0));
    invalidExpiryTimeCounter = meterRegistry.counter("conn-est-invalid-expiry-time");
  }

  public void onCacheSizeChanged(int size) {
    Validate.notNegative(size, "size");

    connectionEstablishedRegistrationCacheSize.set(size);
  }

  public void onInvalidExpiryTime() {
    invalidExpiryTimeCounter.increment();
  }
}
