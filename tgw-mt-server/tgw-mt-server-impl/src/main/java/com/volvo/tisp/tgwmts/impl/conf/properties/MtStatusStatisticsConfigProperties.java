package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public record MtStatusStatisticsConfigProperties(
    @Value("${mt-status-statistics.max-wait-time-for-publish:PT1S}") Duration maxWaitTimeForPublish,
    @Value("${mt-status-statistics.max-queue-size:100000}") Integer maxQueueSize,
    @Value("${mt-status-statistics.max-batch-size:1000}") Integer maxBatchSize,
    @Value("${mt-status-statistics.max-publisher-threads:20}") Integer maximumPublisherThreads) {
  public MtStatusStatisticsConfigProperties {
    Validate.isPositive(maxWaitTimeForPublish, "maxWaitTimeForPublish");
    Validate.isPositive(maxQueueSize, "maxQueueSize");
    Validate.isPositive(maxBatchSize, "maxBatchSize");
    Validate.isPositive(maximumPublisherThreads, "maximumPublisherThreads");
  }
}
