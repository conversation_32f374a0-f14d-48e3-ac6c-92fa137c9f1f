package com.volvo.tisp.tgwmts.impl.converters;

import java.util.function.BiFunction;

import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class MtMessageOutputConverterBiFunction implements BiFunction<ReceivedMtMessage, VehicleLockId, MtMessage> {
  public static final BiFunction<ReceivedMtMessage, VehicleLockId, MtMessage> INSTANCE = new MtMessageOutputConverterBiFunction();

  private MtMessageOutputConverterBiFunction() {
    // do nothing
  }

  @Override
  public MtMessage apply(ReceivedMtMessage receivedMtMessage, VehicleLockId vehicleLockId) {
    Validate.notNull(receivedMtMessage, "receivedMtMessage");
    Validate.notNull(vehicleLockId, "vehicleLockId");

    return new MtMessageBuilder()
        .setQueueId(receivedMtMessage.getQueueId())
        .setReplyOption(receivedMtMessage.getReplyOption())
        .setSendSchemaName(receivedMtMessage.getSendSchemaName())
        .setSrpOption(receivedMtMessage.getSrpOption())
        .setTid(receivedMtMessage.getTid())
        .setVehicleLockId(vehicleLockId)
        .build();
  }
}
