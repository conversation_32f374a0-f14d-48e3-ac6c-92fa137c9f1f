package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.model.AssetCapabilityState;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import io.micrometer.core.instrument.Tags;

@Component
public class TgwNotifyEventMetricReporter {
  private static final String ASSET_NOTIFICATION = "asset-notification";
  private static final String CAPABILITY = "capability";
  private static final String PROCESS_STATUS = "process-status";
  private static final String STATE = "state";
  private final Counter assetNotificationIgnoredCounter;
  private final MeterRegistry meterRegistry;

  public TgwNotifyEventMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    this.assetNotificationIgnoredCounter = meterRegistry.counter("asset-notification-ignored");
  }

  public void onAssetStateChangeProcessFailed(AssetCapabilityState assetCapabilityState) {
    meterRegistry.counter(ASSET_NOTIFICATION,
        Tags.of(Tag.of(CAPABILITY, assetCapabilityState.assetCapability().name()),
            Tag.of(STATE, String.valueOf(assetCapabilityState.isEnabled())),
            Tag.of(PROCESS_STATUS, "failure"))).increment();
  }

  public void onAssetStateChangeProcessed(AssetCapabilityState assetCapabilityState) {
    meterRegistry.counter(ASSET_NOTIFICATION,
        Tags.of(Tag.of(CAPABILITY, assetCapabilityState.assetCapability().name()),
            Tag.of(STATE, String.valueOf(assetCapabilityState.isEnabled())),
            Tag.of(PROCESS_STATUS, "success"))).increment();
  }

  public void onAssetStateChangeReceived(AssetCapabilityState assetCapabilityState) {
    meterRegistry.counter(ASSET_NOTIFICATION,
        Tags.of(Tag.of(CAPABILITY, assetCapabilityState.assetCapability().name()),
            Tag.of(STATE, String.valueOf(assetCapabilityState.isEnabled())),
            Tag.of(PROCESS_STATUS, "received"))).increment();
  }

  public void onIgnoredNotifyEvent() {
    assetNotificationIgnoredCounter.increment();
  }
}
