package com.volvo.tisp.tgwmts.impl.influx;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.volvo.tisp.framework.servicediscovery.ServiceActivatedEvent;
import com.volvo.tisp.framework.servicediscovery.ServiceDeactivatedEvent;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;

@Component
public class ApplicationEventReporter {
  private static final String APP_EVENT = "app-event";
  private static final Logger logger = LoggerFactory.getLogger(ApplicationEventReporter.class);

  private final Counter activatedCounter;
  private final Counter deactivatedCounter;
  private final Counter startedCounter;
  private final Counter stoppedCounter;

  public ApplicationEventReporter(MeterRegistry meterRegistry) {
    this.startedCounter = meterRegistry.counter(APP_EVENT, Tags.of("type", "STARTED"));
    this.stoppedCounter = meterRegistry.counter(APP_EVENT, Tags.of("type", "STOPPED"));
    this.activatedCounter = meterRegistry.counter(APP_EVENT, Tags.of("type", "ACTIVATED"));
    this.deactivatedCounter = meterRegistry.counter(APP_EVENT, Tags.of("type", "DEACTIVATED"));
  }

  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReadyEvent() {
    logger.info("Received ApplicationReadyEvent");

    startedCounter.increment();
  }

  @EventListener(ContextClosedEvent.class)
  public void onContextClosed() {
    logger.info("Received ContextClosedEvent");

    stoppedCounter.increment();
  }

  @EventListener(ServiceActivatedEvent.class)
  public void onServiceActivatedEvent() {
    logger.info("Received ServiceActivatedEvent");

    activatedCounter.increment();
  }

  @EventListener(ServiceDeactivatedEvent.class)
  public void onServiceDeactivatedEvent() {
    logger.info("Received ServiceDeactivatedEvent");

    deactivatedCounter.increment();
  }
}
