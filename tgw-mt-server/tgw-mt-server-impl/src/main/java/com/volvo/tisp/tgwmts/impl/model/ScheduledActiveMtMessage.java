package com.volvo.tisp.tgwmts.impl.model;

import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public record ScheduledActiveMtMessage(IdentifiedActiveMtMessage identifiedActiveMtMessage, SendSchemaStep sendSchemaStep) {
  public ScheduledActiveMtMessage {
    Validate.notNull(identifiedActiveMtMessage, "identifiedActiveMtMessage");
    Validate.notNull(sendSchemaStep, "sendSchemaStep");

  }

  @Override
  public boolean equals(Object object) {
    if (this == object) {
      return true;
    } else if (object == null) {
      return false;
    } else if (getClass() != object.getClass()) {
      return false;
    }

    ScheduledActiveMtMessage other = (ScheduledActiveMtMessage) object;
    if (!identifiedActiveMtMessage.equals(other.identifiedActiveMtMessage)) {
      return false;
    }

    return sendSchemaStep.equals(other.sendSchemaStep);
  }

  @Override
  public String toString() {
    return new StringBuilder(100)
        .append("identifiedActiveMtMessage={")
        .append(identifiedActiveMtMessage)
        .append("}, sendSchemaStep={")
        .append(sendSchemaStep)
        .append("}")
        .toString();
  }
}
