package com.volvo.tisp.tgwmts.impl.db.stat.reporter;

import java.util.TimerTask;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.stat.IndexName;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatIndex;
import com.volvo.tisp.tgwmts.database.model.stat.PgStatTuple;
import com.volvo.tisp.tgwmts.database.model.stat.TableName;

@Component
public class PgStatReporterTask extends TimerTask {
  private static final Logger logger = LoggerFactory.getLogger(PgStatReporterTask.class);

  private final ActiveMtMessageReaderFactory activeMtMessageReaderFactory;
  private final PgStatIndexMetricReporter pgStatIndexMetricReporter;
  private final PgStatTupleMetricReporter pgStatTupleMetricReporter;

  public PgStatReporterTask(ActiveMtMessageReaderFactory activeMtMessageReaderFactory, PgStatIndexMetricReporter pgStatIndexMetricReporter,
      PgStatTupleMetricReporter pgStatTupleMetricReporter) {
    this.activeMtMessageReaderFactory = activeMtMessageReaderFactory;
    this.pgStatIndexMetricReporter = pgStatIndexMetricReporter;
    this.pgStatTupleMetricReporter = pgStatTupleMetricReporter;
  }

  @Override
  public void run() {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      activeMtMessageReader.findPgStatTuple(TableName.MT_MESSAGE)
          .ifPresent(pgStatTuple -> reportPgStatTuple(pgStatTuple, TableName.MT_MESSAGE));
      activeMtMessageReader.findPgStatTuple(TableName.ACTIVE_MT_MESSAGE)
          .ifPresent(pgStatTuple -> reportPgStatTuple(pgStatTuple, TableName.ACTIVE_MT_MESSAGE));

      activeMtMessageReader.findPgStatIndex(IndexName.ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY)
          .ifPresent(pgStatIndex -> reportPgStatIndex(pgStatIndex, IndexName.ACTIVE_MT_MESSAGE_MT_MESSAGE_ID_KEY));
      activeMtMessageReader.findPgStatIndex(IndexName.ACTIVE_MT_MESSAGE_PKEY)
          .ifPresent(pgStatIndex -> reportPgStatIndex(pgStatIndex, IndexName.ACTIVE_MT_MESSAGE_PKEY));
      activeMtMessageReader.findPgStatIndex(IndexName.ACTIVE_MT_MESSAGE_TIMEOUT)
          .ifPresent(pgStatIndex -> reportPgStatIndex(pgStatIndex, IndexName.ACTIVE_MT_MESSAGE_TIMEOUT));
      activeMtMessageReader.findPgStatIndex(IndexName.MT_MESSAGE_PKEY)
          .ifPresent(pgStatIndex -> reportPgStatIndex(pgStatIndex, IndexName.MT_MESSAGE_PKEY));
      activeMtMessageReader.findPgStatIndex(IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED)
          .ifPresent(pgStatIndex -> reportPgStatIndex(pgStatIndex, IndexName.MT_MESSAGE_VEHICLE_LOCK_ID_CREATED));
    }
  }

  private void reportPgStatTuple(PgStatTuple pgStatTuple, TableName tableName) {
    logger.info("reporting pgStatTuple for table {}: {}", tableName, pgStatTuple);

    pgStatTupleMetricReporter.onDeadTupleLen(tableName, pgStatTuple.deadTupleLen());
    pgStatTupleMetricReporter.onFreeSpace(tableName, pgStatTuple.freeSpace());
    pgStatTupleMetricReporter.onTableLen(tableName, pgStatTuple.tableLen());
    pgStatTupleMetricReporter.onTupleLen(tableName, pgStatTuple.tupleLen());
  }

  private void reportPgStatIndex(PgStatIndex pgStatIndex, IndexName indexName) {
    logger.info("reporting pgStatIndex for index {}: {}", indexName, pgStatIndex);

    pgStatIndexMetricReporter.onDeletedPages(indexName, pgStatIndex.deletedPages());
    pgStatIndexMetricReporter.onEmptyPages(indexName, pgStatIndex.emptyPages());
    pgStatIndexMetricReporter.onInternalPages(indexName, pgStatIndex.internalPages());
    pgStatIndexMetricReporter.onLeafPages(indexName, pgStatIndex.leafPages());
  }
}
