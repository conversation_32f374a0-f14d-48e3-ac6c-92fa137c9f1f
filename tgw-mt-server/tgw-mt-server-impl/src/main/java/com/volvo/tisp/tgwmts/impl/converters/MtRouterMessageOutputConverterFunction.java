package com.volvo.tisp.tgwmts.impl.converters;

import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.IdentifiedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;

public final class MtRouterMessageOutputConverterFunction implements Function<EncodedActiveMtMessage, Either<RuntimeException, MtMessage>> {
  private static final char PADDING_CHAR = '0';
  private static final Logger logger = LoggerFactory.getLogger(MtRouterMessageOutputConverterFunction.class);

  private final Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction;

  private MtRouterMessageOutputConverterFunction(Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction) {
    this.encodeServiceRoutingPduWrapperFunction = encodeServiceRoutingPduWrapperFunction;
  }

  public static MtRouterMessageOutputConverterFunction create(Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction) {
    Validate.notNull(encodeServiceRoutingPduWrapperFunction, "encodeServiceRoutingPduWrapperFunction");

    return new MtRouterMessageOutputConverterFunction(encodeServiceRoutingPduWrapperFunction);
  }

  private static ActiveMtMessageId getActiveMtMessageId(EncodedActiveMtMessage encodedActiveMtMessage) {
    PersistedActiveMtMessage persistedActiveMtMessage = getPersistedActiveMtMessage(encodedActiveMtMessage);

    return persistedActiveMtMessage.getActiveMtMessageId();
  }

  private static CorrelationId getCorrelationId(EncodedActiveMtMessage encodedActiveMtMessage) {

    return CorrelationId.ofString(StringUtils.leftPad(getActiveMtMessageId(encodedActiveMtMessage).toString(), 32, PADDING_CHAR));
  }

  private static DeviceInfo getDeviceInfo(EncodedActiveMtMessage encodedActiveMtMessage) {
    PersistedDeviceInfo persistedDeviceInfo = getPersistedDeviceInfo(encodedActiveMtMessage);

    return persistedDeviceInfo.getDeviceInfo();
  }

  private static IdentifiedActiveMtMessage getIdentifiedActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    ScheduledActiveMtMessage scheduledActiveMtMessage = getScheduledActiveMtMessage(encodedActiveMtMessage);

    return scheduledActiveMtMessage.identifiedActiveMtMessage();
  }

  private static JoinedActiveMtMessage getJoinedActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = getIdentifiedActiveMtMessage(encodedActiveMtMessage);

    return identifiedActiveMtMessage.joinedActiveMtMessage();
  }

  private static PersistedActiveMtMessage getPersistedActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    JoinedActiveMtMessage joinedActiveMtMessage = getJoinedActiveMtMessage(encodedActiveMtMessage);

    return joinedActiveMtMessage.persistedActiveMtMessage();
  }

  private static PersistedDeviceInfo getPersistedDeviceInfo(EncodedActiveMtMessage encodedActiveMtMessage) {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = getIdentifiedActiveMtMessage(encodedActiveMtMessage);

    return identifiedActiveMtMessage.persistedDeviceInfo();
  }

  private static ScheduledActiveMtMessage getScheduledActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    return encodedActiveMtMessage.scheduledActiveMtMessage();
  }

  private static SendSchemaStep getSendSchemaStep(EncodedActiveMtMessage encodedActiveMtMessage) {
    return getScheduledActiveMtMessage(encodedActiveMtMessage).sendSchemaStep();
  }

  private static Transport getTransport(EncodedActiveMtMessage encodedActiveMtMessage) {
    SendSchemaStepType sendSchemaStepType = getSendSchemaStep(encodedActiveMtMessage).getSendSchemaStepType();

    return switch (sendSchemaStepType) {
      case SMS -> Transport.SMS;
      case UDP -> Transport.UDP;
      case SAT -> Transport.SAT;
      case WIFI -> Transport.VPN;
      default -> throw new IllegalStateException("illegal transport type" + sendSchemaStepType);
    };
  }

  private static Vpi getVpi(EncodedActiveMtMessage encodedActiveMtMessage) {
    DeviceInfo deviceInfo = getDeviceInfo(encodedActiveMtMessage);

    return deviceInfo.getVpi();
  }

  @Override
  public Either<RuntimeException, MtMessage> apply(EncodedActiveMtMessage encodedActiveMtMessage) {
    Validate.notNull(encodedActiveMtMessage, "encodedActiveMtMessage");

    try {
      CorrelationId correlationId = getCorrelationId(encodedActiveMtMessage);
      if (logger.isDebugEnabled()) {
        logger.debug("Received message with ID: {}", getIdentifiedActiveMtMessage(encodedActiveMtMessage));
      }

      return Either.right(MtMessage.newBuilder()
          .setMessageId(correlationId.toString())
          .setPayload(getByteStringPayload(encodedActiveMtMessage.serviceRoutingPduWrapper()))
          .setTransport(getTransport(encodedActiveMtMessage))
          .setVpi(getVpi(encodedActiveMtMessage).toString())
          .build());
    } catch (RuntimeException e) {
      return Either.left(e);
    }
  }

  private ByteString getByteStringPayload(ServiceRoutingPduWrapper serviceRoutingPduWrapper) {
    ImmutableByteArray immutableByteArray = encodeServiceRoutingPduWrapperFunction.apply(serviceRoutingPduWrapper);
    return ByteString.copyFrom(immutableByteArray.toByteArray());
  }
}
