package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.volvo.tisp.vc.main.utils.lib.Validate;

@Component
public class SendSchemaProperties {
  public static final Duration MIN_TIMEOUT = Duration.ofMinutes(1);

  private final Duration maxTimeout;

  public SendSchemaProperties(@Value("${send-schema-max-timeout:P14D}") Duration maxTimeout) {
    Validate.notNull(maxTimeout, "maxTimeout");
    validateMaxTimeout(maxTimeout);

    this.maxTimeout = maxTimeout;
  }

  private static void validateMaxTimeout(Duration maxTimeout) {
    if (maxTimeout.compareTo(MIN_TIMEOUT) < 0) {
      throw new IllegalArgumentException("maxTimeout cannot be less than 1 min, was: " + maxTimeout);
    }
  }

  public Duration getMaxTimeout() {
    return maxTimeout;
  }
}
