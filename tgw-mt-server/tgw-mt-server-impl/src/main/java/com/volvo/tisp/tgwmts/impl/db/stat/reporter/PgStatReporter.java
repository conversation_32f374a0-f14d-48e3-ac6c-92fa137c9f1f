package com.volvo.tisp.tgwmts.impl.db.stat.reporter;

import java.io.Closeable;
import java.io.IOException;
import java.time.Duration;
import java.util.Timer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.EventListener;

public final class PgStatReporter implements Closeable {
  private static final Logger logger = LoggerFactory.getLogger(PgStatReporter.class);
  public static final String TIMER_NAME = "pgStatReporter";

  private final Duration reportInterval;
  private final PgStatReporterTask pgStatReporterTask;
  private final Timer timer = new Timer(TIMER_NAME);

  public PgStatReporter(Duration reportInterval, PgStatReporterTask pgStatReporterTask) {
    this.reportInterval = reportInterval;
    this.pgStatReporterTask = pgStatReporterTask;
  }

  @Override
  public void close() throws IOException {
    timer.cancel();
    logger.info("Stopped timer: {}", TIMER_NAME);
  }

  @EventListener(ApplicationStartedEvent.class)
  public void start() {
    logger.info("Starting timer: {}", TIMER_NAME);
    timer.scheduleAtFixedRate(pgStatReporterTask, 0, reportInterval.toMillis());
  }
}
