package com.volvo.tisp.tgwmts.impl.services;

import org.springframework.stereotype.Service;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;

@Service
public class ConnectionEstablishedRegistrationRetryService {
  private final ActiveMtMessageReaderFactory activeMtMessageReaderFactory;
  private final ConnectionEstablishedCacheService connectionEstablishedCacheService;

  public ConnectionEstablishedRegistrationRetryService(ActiveMtMessageReaderFactory activeMtMessageReaderFactory,
      ConnectionEstablishedCacheService connectionEstablishedCacheService) {
    this.activeMtMessageReaderFactory = activeMtMessageReaderFactory;
    this.connectionEstablishedCacheService = connectionEstablishedCacheService;
  }

  public void resendConnectionEstablishedRegistrations() {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      activeMtMessageReader.findGreatestActiveMtMessageTimeoutGroupByVpi()
          .forEach(vpiToTimeout -> connectionEstablishedCacheService.add(vpiToTimeout.vpi(), vpiToTimeout.timeout()));
    }
  }
}
