package com.volvo.tisp.tgwmts.impl.integration.logging;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum MetaDataKey {
  SERVICE_ID("serviceId"),
  SERVICE_VERSION("serviceVersion"),
  STACK_NAME("stackName"),
  SCHEDULE_NAME("scheduleName"),
  MOBILE_DIRECTION("mobileDirection");

  private static final Map<String, MetaDataKey> STRING_TO_ENUM_MAP = Stream.of(values()).collect(Collectors.toMap(Objects::toString, e -> e));

  private final String string;

  MetaDataKey(String string) {
    this.string = string;
  }

  public static Optional<MetaDataKey> fromString(String string) {
    return Optional.ofNullable(STRING_TO_ENUM_MAP.get(string));
  }

  @Override
  public String toString() {
    return string;
  }
}
