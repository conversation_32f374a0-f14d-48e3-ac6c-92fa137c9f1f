package com.volvo.tisp.tgwmts.impl.integration.logging;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.time.Instant;
import java.util.function.Consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.context.Vehicle;
import com.volvo.tisp.identifier.VehicleIdentifier;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.componentbase.Headers;
import com.wirelesscar.componentbase.logging.Logging;

public final class LoggingHelper implements Consumer<IntegrationLogParameter> {
  public static final Consumer<IntegrationLogParameter> INSTANCE = new LoggingHelper();
  private static final Logger logger = LoggerFactory.getLogger(LoggingHelper.class);

  private LoggingHelper() {
    // do nothing
  }

  private static Vehicle createVehicle(VehicleDetail vehicleDetail) {
    Vehicle.Builder builder = Vehicle.builder();

    builder.vpi(VehicleIdentifier.fromString(vehicleDetail.getVpi().toString()));
    vehicleDetail.getHandle().ifPresent(handle -> builder.deviceHandle(handle.toString()));
    vehicleDetail.getMsisdn().ifPresent(msisdn -> builder.msisdn(msisdn.toString()));
    vehicleDetail.getIpv4Address().ifPresent(ipv4Address -> setInetAddress(builder, ipv4Address));

    return builder.build();
  }

  private static void setInetAddress(Vehicle.Builder builder, Ipv4Address ipv4Address) {
    try {
      builder.ipAddress(InetAddress.getByName(ipv4Address.toString()));
    } catch (UnknownHostException e) {
      logger.warn("unable to set InetAddress", e);
    }
  }

  @Override
  public void accept(IntegrationLogParameter integrationLogParameter) {
    Validate.notNull(integrationLogParameter, "integrationLogParameter");

    Vehicle vehicle = createVehicle(integrationLogParameter.getVehicleDetail());

    try (TispContext.Scope ignored = TispContext.current().newScope().vehicle(vehicle).activate()) {
      Logging.integration(Logging.Type.JMS,
          integrationLogParameter.getDirection(),
          integrationLogParameter.getStatus(),
          integrationLogParameter.getStatus().name(),
          integrationLogParameter.getIntegrationMessage().toString(),
          "",
          integrationLogParameter.getMetaData().getMapAsOptional(),
          Headers.of(),
          null,
          Instant.now());
    }
  }
}
