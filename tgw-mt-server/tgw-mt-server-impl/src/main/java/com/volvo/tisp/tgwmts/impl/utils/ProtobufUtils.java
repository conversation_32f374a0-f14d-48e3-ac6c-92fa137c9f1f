package com.volvo.tisp.tgwmts.impl.utils;

import java.time.Instant;

import com.google.protobuf.Timestamp;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ProtobufUtils {
  private ProtobufUtils() {
    throw new IllegalStateException();
  }

  public static Instant createInstant(Timestamp timestamp) {
    Validate.notNull(timestamp, "timestamp");

    return Instant.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos());
  }

  public static Timestamp createTimestamp(Instant instant) {
    Validate.notNull(instant, "instant");

    return Timestamp.newBuilder()
        .setNanos(instant.getNano())
        .setSeconds(instant.getEpochSecond())
        .build();
  }
}
