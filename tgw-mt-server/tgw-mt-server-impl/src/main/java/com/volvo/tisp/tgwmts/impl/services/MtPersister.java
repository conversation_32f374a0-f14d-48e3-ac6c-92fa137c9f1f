package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.function.BiFunction;
import java.util.function.Consumer;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageBuilder;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameter;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationLogParameterBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.IntegrationMessage;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaData;
import com.volvo.tisp.tgwmts.impl.integration.logging.MetaDataBuilder;
import com.volvo.tisp.tgwmts.impl.integration.logging.VehicleDetail;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageMetricReporter;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.wirelesscar.componentbase.logging.Logging;

@Component
public class MtPersister {
  private static final RetryAttempt START_RETRY_ATTEMPT = RetryAttempt.ofShort((short) 0);
  private static final Logger logger = LoggerFactory.getLogger(MtPersister.class);

  private final Clock clock;
  private final Consumer<IntegrationLogParameter> loggingHelper;
  private final MtMessageMetricReporter mtMessageMetricReporter;
  private final BiFunction<ReceivedMtMessage, VehicleLockId, MtMessage> mtMessageOutputConverterBiFunction;

  public MtPersister(Clock clock, Consumer<IntegrationLogParameter> loggingHelper, MtMessageMetricReporter mtMessageMetricReporter,
      BiFunction<ReceivedMtMessage, VehicleLockId, MtMessage> mtMessageOutputConverterBiFunction) {
    this.clock = clock;
    this.loggingHelper = loggingHelper;
    this.mtMessageMetricReporter = mtMessageMetricReporter;
    this.mtMessageOutputConverterBiFunction = mtMessageOutputConverterBiFunction;
  }

  private static IntegrationLogParameterBuilder createIntegrationLogParameterBuilder(MtMessage mtMessage, Vpi vpi) {
    return new IntegrationLogParameterBuilder()
        .setDirection(Logging.Direction.CLIENT_OUT)
        .setMetaData(createMetaData(mtMessage))
        .setVehicleDetail(VehicleDetail.create(vpi));
  }

  private static MetaData createMetaData(MtMessage mtMessage) {
    SrpOption srpOption = mtMessage.getSrpOption();

    return new MetaDataBuilder()
        .setMobileDirection("MT")
        .setSendSchemaName(mtMessage.getSendSchemaName())
        .setSrpDestinationService(srpOption.getSrpDestinationService())
        .setSrpDestinationVersion(srpOption.getSrpDestinationVersion())
        .build();
  }

  public void insertActiveMtMessage(MtMessageId mtMessageId, ActiveMtMessageWriter activeMtMessageWriter) {
    Validate.notNull(mtMessageId, "mtMessageId");
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");

    ActiveMtMessage activeMtMessage = createActiveMtMessage(mtMessageId);

    Instant beforeInsert = clock.instant();
    Either<InsertionFailure, ActiveMtMessageId> either = activeMtMessageWriter.insertActiveMtMessage(activeMtMessage);
    Duration duration = Duration.between(beforeInsert, clock.instant());

    if (either.isLeft()) {
      mtMessageMetricReporter.onActiveMtInsertFailure(duration);
      throw either.getLeft().runtimeException();
    }

    mtMessageMetricReporter.onActiveMtInsertSuccess(duration);

    ActiveMtMessageId activeMtMessageId = either.getRight();
    logger.debug("Inserted activeMtMessage: {}, activeMtMessageId: {}", activeMtMessage, activeMtMessageId);
  }

  public MtMessageId insertMtMessage(VehicleLockId vehicleLockId, ReceivedMtMessage receivedMtMessage, ActiveMtMessageWriter activeMtMessageWriter) {
    Validate.notNull(vehicleLockId, "vehicleLockId");
    Validate.notNull(receivedMtMessage, "receivedMtMessage");
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");

    MtMessage mtMessage = mtMessageOutputConverterBiFunction.apply(receivedMtMessage, vehicleLockId);
    Vpi vpi = receivedMtMessage.getVpi();

    Instant beforeInsert = clock.instant();
    Either<InsertionFailure, MtMessageId> either = activeMtMessageWriter.insertMtMessage(mtMessage);
    Duration duration = Duration.between(beforeInsert, clock.instant());

    if (either.isLeft()) {
      logPersistFailedIntegrationMessage(mtMessage, vpi);
      mtMessageMetricReporter.onMtInsertFailure(duration);
      throw either.getLeft().runtimeException();
    }

    logPersistSuccessIntegrationMessage(mtMessage, vpi);
    mtMessageMetricReporter.onMtInsertSuccess(duration);

    MtMessageId mtMessageId = either.getRight();
    logger.debug("Inserted mtMessage: {}, mtMessageId: {}", mtMessage, mtMessageId);

    return mtMessageId;
  }

  private ActiveMtMessage createActiveMtMessage(MtMessageId mtMessageId) {
    return new ActiveMtMessageBuilder()
        .setMtMessageId(mtMessageId)
        .setRetryAttempt(START_RETRY_ATTEMPT)
        .setSendSchemaStepId(SendSchemaStepId.ofInt(0))
        .setTimeout(clock.instant())
        .build();
  }

  private void logPersistFailedIntegrationMessage(MtMessage mtMessage, Vpi vpi) {
    loggingHelper.accept(createIntegrationLogParameterBuilder(mtMessage, vpi)
        .setIntegrationMessage(IntegrationMessage.onPersistFail())
        .setStatus(Logging.Status.FAILED).build());
  }

  private void logPersistSuccessIntegrationMessage(MtMessage mtMessage, Vpi vpi) {
    loggingHelper.accept(createIntegrationLogParameterBuilder(mtMessage, vpi)
        .setIntegrationMessage(IntegrationMessage.onPersist(mtMessage.getSendSchemaName()))
        .setStatus(Logging.Status.SUCCESS).build());
  }
}
