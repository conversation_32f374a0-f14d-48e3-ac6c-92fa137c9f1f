package com.volvo.tisp.tgwmts.impl.conf;

import java.io.Closeable;
import java.util.concurrent.Executors;

import com.volvo.tisp.tgw.device.info.cache.notify.impl.pg.PgCacheInvalidationListener;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.impl.services.statistics.MtStatusStatisticsHandler;

@Component
public class ThreadLifecycleManager implements Closeable {
  private static final Logger logger = LoggerFactory.getLogger(ThreadLifecycleManager.class);
  private final Thread mtStatusStatisticsHandlerThread;
  private final Thread pgCacheInvalidationListenerThread;

  public ThreadLifecycleManager(MtStatusStatisticsHandler mtStatusStatisticsHandler, PgCacheInvalidationListener pgCacheInvalidationListener) {
    mtStatusStatisticsHandlerThread = Executors.defaultThreadFactory().newThread(mtStatusStatisticsHandler);
    pgCacheInvalidationListenerThread = Executors.defaultThreadFactory().newThread(pgCacheInvalidationListener);

  }

  @Override
  public void close() {
    logger.info("stopping managed threads");
    mtStatusStatisticsHandlerThread.interrupt();
    pgCacheInvalidationListenerThread.interrupt();
  }

  @EventListener(ApplicationStartedEvent.class)
  public void startThreads() {
    logger.info("starting managed threads");
    mtStatusStatisticsHandlerThread.start();
    pgCacheInvalidationListenerThread.start();
  }
}
