package com.volvo.tisp.tgwmts.impl.services;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.impl.schema.FilterableSendSchemaFetcher;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepFilterProducer;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.SendSchemaProcessorMetricReporter;

@Component
public class SendSchemaProcessorContext {
  private final FilterableSendSchemaFetcher filterableSendSchemaFetcher;
  private final FinishedMtMessageHandler finishedMtMessageHandler;
  private final SendSchemaProcessorMetricReporter sendSchemaProcessorMetricReporter;
  private final SendSchemaStepFilterProducer sendSchemaStepFilterProducer;
  private final TransmissionRouter transmissionRouter;

  public SendSchemaProcessorContext(
      FilterableSendSchemaFetcher filterableSendSchemaFetcher,
      FinishedMtMessageHand<PERSON> finishedMtMessageHandler,
      SendSchemaProcessorMetricReporter sendSchemaProcessorMetricReporter,
      SendSchemaStepFilterProducer sendSchemaStepFilterProducer,
      TransmissionRouter transmissionRouter) {
    this.filterableSendSchemaFetcher = filterableSendSchemaFetcher;
    this.finishedMtMessageHandler = finishedMtMessageHandler;
    this.sendSchemaProcessorMetricReporter = sendSchemaProcessorMetricReporter;
    this.sendSchemaStepFilterProducer = sendSchemaStepFilterProducer;
    this.transmissionRouter = transmissionRouter;
  }

  public FilterableSendSchemaFetcher getFilterableSendSchemaFetcher() {
    return filterableSendSchemaFetcher;
  }

  public FinishedMtMessageHandler getFinishedMtMessageHandler() {
    return finishedMtMessageHandler;
  }

  public SendSchemaProcessorMetricReporter getSendSchemaProcessorMetricReporter() {
    return sendSchemaProcessorMetricReporter;
  }

  public SendSchemaStepFilterProducer getSendSchemaStepFilterProducer() {
    return sendSchemaStepFilterProducer;
  }

  public TransmissionRouter getTransmissionRouter() {
    return transmissionRouter;
  }
}
