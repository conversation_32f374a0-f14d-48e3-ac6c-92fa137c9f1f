package com.volvo.tisp.tgwmts.impl.converters;

import java.util.Locale;
import java.util.Optional;
import java.util.function.Function;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.dynamic.SendSchemaNameParameterExtractor;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class SchedulerHintConverter implements Function<String, Optional<SendSchemaName>> {
  public static final Function<String, Optional<SendSchemaName>> INSTANCE = new SchedulerHintConverter();
  private static final String DEFAULT_SCHEDULE_NAME_START = "common_";
  private static final String[] DEFAULT_SCHEDULE_NAME_SUFFIXES = new String[] {"very_high", "high", "mid", "low", "setup", "rswdl", "short", "long"};
  private static final Logger logger = LoggerFactory.getLogger(SchedulerHintConverter.class);

  private SchedulerHintConverter() {
    // do nothing
  }

  private static Optional<SendSchemaName> createSendSchemaName(String schedulerOptionHint) {
    return switch (schedulerOptionHint) {
      case "common-very-high" -> Optional.of(SendSchemaName.COMMON_VERY_HIGH);
      case "darf-config" -> Optional.of(SendSchemaName.DARF_CONFIG);
      case "dfol-high" -> Optional.of(SendSchemaName.DFOL_HIGH);
      case "dfol-low" -> Optional.of(SendSchemaName.DFOL_LOW);
      case "dfol-mid" -> Optional.of(SendSchemaName.DFOL_MID);
      case "dfol-mid-plus" -> Optional.of(SendSchemaName.DFOL_MID_PLUS);
      case "dfol-setup" -> Optional.of(SendSchemaName.DFOL_SETUP);
      case "drut-very-high" -> Optional.of(SendSchemaName.DRUT_VERY_HIGH);
      case "high" -> Optional.of(SendSchemaName.COMMON_HIGH);
      case "long" -> Optional.of(SendSchemaName.COMMON_LONG);
      case "low" -> Optional.of(SendSchemaName.COMMON_LOW);
      case "mid" -> Optional.of(SendSchemaName.COMMON_MID);
      case "normal" -> Optional.of(SendSchemaName.COMMON_NORMAL);
      case "rswdl" -> Optional.of(SendSchemaName.COMMON_RSWDL);
      case "rswdl-low" -> Optional.of(SendSchemaName.RSWDL_LOW);
      case "rswdl-mid" -> Optional.of(SendSchemaName.RSWDL_MID);
      case "rdns-mid" -> Optional.of(SendSchemaName.RDNS_MID);
      case "sat-only" -> Optional.of(SendSchemaName.SAT_ONLY);
      case "sms-only" -> Optional.of(SendSchemaName.SMS_ONLY);
      case "setup" -> Optional.of(SendSchemaName.COMMON_SETUP);
      case "short" -> Optional.of(SendSchemaName.COMMON_SHORT);
      case "uptime-very-high" -> Optional.of(SendSchemaName.UPTIME_VERY_HIGH);
      case "vlink-high" -> Optional.of(SendSchemaName.VLINK_HIGH);
      case "vlink-low" -> Optional.of(SendSchemaName.VLINK_LOW);
      default -> getDynamicSendSchemaNameOrFindBestMatch(schedulerOptionHint);
    };
  }

  private static Optional<SendSchemaName> findBestMatchScheduleName(String schedulerOptionHint) {
    String[] stArr = schedulerOptionHint.split("-", 2);
    String hintNameSuffix = (stArr.length > 1 ? stArr[1] : stArr[0]).toLowerCase(Locale.ROOT).replace("-", "_");

    for (String suffix : DEFAULT_SCHEDULE_NAME_SUFFIXES) {
      if (suffix.equals(hintNameSuffix)) {
        String scheduleName = DEFAULT_SCHEDULE_NAME_START + suffix;
        return SendSchemaName.fromLegacyString(scheduleName);
      }
    }

    logger.debug("send schema name hint {} is defaulted to {}", schedulerOptionHint, SendSchemaName.COMMON_NORMAL);
    return Optional.of(SendSchemaName.COMMON_NORMAL);
  }

  private static Optional<SendSchemaName> getDynamicSendSchemaNameOrFindBestMatch(String schedulerOptionHint) {
    if (SendSchemaNameParameterExtractor.NAME_PATTERN.matcher(schedulerOptionHint).matches()) {
      return Optional.of(new SendSchemaName(schedulerOptionHint));
    }
    return findBestMatchScheduleName(schedulerOptionHint);
  }

  @Override
  public Optional<SendSchemaName> apply(String schedulerOptionHint) {
    Validate.notEmpty(schedulerOptionHint, "schedulerOptionHint");

    return createSendSchemaName(schedulerOptionHint);
  }
}
