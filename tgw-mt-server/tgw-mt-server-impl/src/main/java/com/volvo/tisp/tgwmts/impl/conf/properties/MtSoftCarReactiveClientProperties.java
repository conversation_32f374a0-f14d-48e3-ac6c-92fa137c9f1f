package com.volvo.tisp.tgwmts.impl.conf.properties;

import java.net.URI;
import java.time.Duration;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

import software.amazon.awssdk.utils.Validate;

@Configuration
public class MtSoftCarReactiveClientProperties {
  private final URI baseUrl;
  private final String mtSoftCarRequestPath;
  private final Duration requestTimeout;

  public MtSoftCarReactiveClientProperties(
      @Value("${mtSoftCar.base-url:http://tucs}") URI baseUrl,
      @Value("${mtSoftCar.request-path:/tucs/tgw/mtmessage}") String mtSoftCarRequestPath,
      @Value("${mtSoftCar.request-timeout:PT5S}") Duration requestTimeout) {
    Validate.isNotNegative(requestTimeout, "requestTimeout");

    this.baseUrl = baseUrl;
    this.mtSoftCarRequestPath = mtSoftCarRequestPath;
    this.requestTimeout = requestTimeout;
  }

  public URI getBaseUrl() {
    return baseUrl;
  }

  public String getMtSoftCarRequestPath() {
    return mtSoftCarRequestPath;
  }

  public Duration getRequestTimeout() {
    return requestTimeout;
  }
}
