package com.volvo.tisp.tgwmts.impl.scheduler;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgwmts.impl.utils.Throttler;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtSchedulerRunnable implements Runnable {
  private static final Logger logger = LoggerFactory.getLogger(MtSchedulerRunnable.class);

  private final MtSchedulerWorker mtSchedulerWorker;
  private final Throttler throttler;

  public MtSchedulerRunnable(MtSchedulerWorker mtSchedulerWorker, Throttler throttler) {
    Validate.notNull(mtSchedulerWorker, "mtSchedulerWorker");
    Validate.notNull(throttler, "throttler");

    this.mtSchedulerWorker = mtSchedulerWorker;
    this.throttler = throttler;
  }

  @Override
  public void run() {
    logger.info("MtSchedulerRunnable started");

    while (!Thread.currentThread().isInterrupted()) {
      try {
        TispContext.runInContext(this::doWorkOnceOrThrottle);
      } catch (RuntimeException e) {
        logger.error("", e);
      }
    }

    logger.info("MtSchedulerRunnable stopped");
  }

  private void doWorkOnceOrThrottle() {
    if (mtSchedulerWorker.doWorkOnce() == 0) {
      logger.debug("No MT messages to process, acquiring a permit from throttler: {}", throttler);

      try {
        throttler.acquire();
      } catch (InterruptedException e) {
        Thread.currentThread().interrupt();
        logger.debug("", e);
      }
    }
  }
}
