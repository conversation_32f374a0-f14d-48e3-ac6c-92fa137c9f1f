package com.volvo.tisp.tgwmts.impl.integration.logging;

import java.util.Map;
import java.util.Optional;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class MetaData {
  private final Map<String, String> map;

  MetaData(MetaDataBuilder metaDataBuilder) {
    Validate.notNull(metaDataBuilder, "metaDataBuilder");

    this.map = Map.copyOf(metaDataBuilder.getMap());
  }

  public Optional<Map<String, String>> getMapAsOptional() {
    return Optional.of(map);
  }

  @Override
  public String toString() {
    return map.toString();
  }
}
