package com.volvo.tisp.tgwmts.impl.schema;

import java.time.Duration;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;

/**
 * A {@code SendSchema} describes how to send out an MT message to a vehicle. It consists of a sequence of at least one {@link SendSchemaStep}. At every
 * {@code SendSchemaStep} a send attempt is made followed by a duration to wait for an MT ACK message from the vehicle, except for the
 * {@link SendSchemaStepType#WAIT} step.
 * <p>
 * Within one {@code SendSchema} every {@code SendSchemaStep} must have a unique {@link SendSchemaStepId}.
 * <p>
 * Every {@code SendSchema} must have a unique name which can be retrieved via {@link #getSendSchemaName()}.
 * <p>
 * If the {@code SendSchema} contains a {@code WAIT} step it will be eligible for being retried again from {@code sendSchemaStepId} 0 for as many times as is
 * defined by the {@link #getMaxRetryAttempts()}. Resetting of the {@code sendSchemaStepId} is triggered by a {@code connectionEstablishedEvent}.
 */
public interface SendSchema {
  /**
   * @return the global timeout of this {@code SendSchema}. This is the maximum duration an active MT message will be processed by the scheduler. If it has not
   * been delivered within this period, it will be considered having timed out, irrespective of which step the send schema is currently on.
   */
  Duration getGlobalTimeout();

  /**
   * @return the maximum number of times this {@code SendSchema} will be attempted for a given MT message
   */
  short getMaxRetryAttempts();

  /**
   * @return the identifier of this {@code SendSchema}. Every {@code SendSchema} must have a unique name.
   */
  SendSchemaName getSendSchemaName();

  /**
   * @return the {@link SendSchemaStep} with the given {@code sendSchemaStepId}. Returns {@link Optional#empty()} if there is no more {@link SendSchemaStep}
   * in this {@code SendSchema}. After the wait duration of the last {@code SendSchemaStep} has elapsed, no further send attempts are made.
   */
  Optional<SendSchemaStep> getSendSchemaStep(SendSchemaStepId sendSchemaStepId);
}
