package com.volvo.tisp.tgwmts.impl.db.queue.reporter;

import java.util.TimerTask;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;

@Component
public class MtMessageQueueReporterTask extends TimerTask {
  private static final Logger logger = LoggerFactory.getLogger(MtMessageQueueReporterTask.class);

  private final ActiveMtMessageReaderFactory activeMtMessageReaderFactory;
  private final MtMessageQueueMetricReporter mtMessageQueueMetricReporter;

  public MtMessageQueueReporterTask(ActiveMtMessageReaderFactory activeMtMessageReaderFactory, MtMessageQueueMetricReporter mtMessageQueueMetricReporter) {
    this.activeMtMessageReaderFactory = activeMtMessageReaderFactory;
    this.mtMessageQueueMetricReporter = mtMessageQueueMetricReporter;
  }

  @Override
  public void run() {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      int mtQueueSize = activeMtMessageReader.countMtMessages();
      mtMessageQueueMetricReporter.mtDbQueueSize(mtQueueSize);

      int activeMtQueueSize = activeMtMessageReader.countActiveMtMessages();
      mtMessageQueueMetricReporter.activeMtDbQueueSize(activeMtQueueSize);
      logger.debug("Number of mt messages in DB: {}, number of active mt messages in DB: {}", Integer.valueOf(mtQueueSize), Integer.valueOf(activeMtQueueSize));
    } catch (RuntimeException e) {
      logger.error("", e);
    }
  }
}
