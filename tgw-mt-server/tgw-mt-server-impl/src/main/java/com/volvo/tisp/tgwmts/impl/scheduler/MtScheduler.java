package com.volvo.tisp.tgwmts.impl.scheduler;

import java.io.Closeable;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

@Component
public class MtScheduler implements Closeable {
  private static final Logger logger = LoggerFactory.getLogger(MtScheduler.class);

  private final MtSchedulerContext mtSchedulerContext;

  public MtScheduler(MtSchedulerContext mtSchedulerContext) {
    this.mtSchedulerContext = mtSchedulerContext;
  }

  @Override
  public void close() {
    mtSchedulerContext.close();
    logger.info("MtScheduler closed");
  }

  @EventListener(ApplicationReadyEvent.class)
  public void start() {
    mtSchedulerContext.start();
    logger.info("MtScheduler started");
  }
}
