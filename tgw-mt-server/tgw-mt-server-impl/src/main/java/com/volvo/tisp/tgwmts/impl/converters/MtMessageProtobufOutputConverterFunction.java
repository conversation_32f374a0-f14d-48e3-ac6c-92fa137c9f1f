package com.volvo.tisp.tgwmts.impl.converters;

import java.util.function.Function;

import org.apache.commons.lang3.StringUtils;

import com.google.protobuf.ByteString;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.protobuf.common.PlatformIdentifier;
import com.volvo.tisp.tce.proto.MtMessage;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.SimInfo;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.EncodedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.IdentifiedActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.model.ScheduledActiveMtMessage;
import com.volvo.tisp.tgwmts.impl.utils.AppConstants;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;

public final class MtMessageProtobufOutputConverterFunction implements Function<EncodedActiveMtMessage, MtMessage> {
  private static final char PADDING_CHAR = '0';

  private final Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction;

  private MtMessageProtobufOutputConverterFunction(Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction) {
    this.encodeServiceRoutingPduWrapperFunction = encodeServiceRoutingPduWrapperFunction;
  }

  public static MtMessageProtobufOutputConverterFunction create(Function<ServiceRoutingPduWrapper, ImmutableByteArray> encodeServiceRoutingPduWrapperFunction) {
    Validate.notNull(encodeServiceRoutingPduWrapperFunction, "encodeServiceRoutingPduWrapperFunction");

    return new MtMessageProtobufOutputConverterFunction(encodeServiceRoutingPduWrapperFunction);
  }

  private static PlatformIdentifier buildPlatformIdentifier(EncodedActiveMtMessage encodedActiveMtMessage) {
    ActiveMtMessageId activeMtMessageId = getActiveMtMessageId(encodedActiveMtMessage);
    return PlatformIdentifier.newBuilder().setValue(getAndLeftZeroPadActiveMtMessageId(activeMtMessageId)).build();
  }

  private static ActiveMtMessageId getActiveMtMessageId(EncodedActiveMtMessage encodedActiveMtMessage) {
    PersistedActiveMtMessage persistedActiveMtMessage = getPersistedActiveMtMessage(encodedActiveMtMessage);

    return persistedActiveMtMessage.getActiveMtMessageId();
  }

  /**
   * Convert {@link ActiveMtMessageId} into a 32 character string where the missing digits are left padded with zeroes.
   * Needed because the vwtp-lib parses {@code messageId} as {@link TrackingIdentifier}
   */
  private static String getAndLeftZeroPadActiveMtMessageId(ActiveMtMessageId activeMtMessageId) {
    return StringUtils.leftPad(activeMtMessageId.toString(), 32, PADDING_CHAR);
  }

  private static DeviceInfo getDeviceInfo(EncodedActiveMtMessage encodedActiveMtMessage) {
    PersistedDeviceInfo persistedDeviceInfo = getPersistedDeviceInfo(encodedActiveMtMessage);

    return persistedDeviceInfo.getDeviceInfo();
  }

  private static IdentifiedActiveMtMessage getIdentifiedActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    ScheduledActiveMtMessage scheduledActiveMtMessage = getScheduledActiveMtMessage(encodedActiveMtMessage);

    return scheduledActiveMtMessage.identifiedActiveMtMessage();
  }

  private static JoinedActiveMtMessage getJoinedActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = getIdentifiedActiveMtMessage(encodedActiveMtMessage);

    return identifiedActiveMtMessage.joinedActiveMtMessage();
  }

  private static ObsAlias getObsAlias(EncodedActiveMtMessage encodedActiveMtMessage) {
    return encodedActiveMtMessage.scheduledActiveMtMessage().identifiedActiveMtMessage().persistedDeviceInfo().getDeviceInfo().getObsAlias();
  }

  private static PersistedActiveMtMessage getPersistedActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    JoinedActiveMtMessage joinedActiveMtMessage = getJoinedActiveMtMessage(encodedActiveMtMessage);

    return joinedActiveMtMessage.persistedActiveMtMessage();
  }

  private static PersistedDeviceInfo getPersistedDeviceInfo(EncodedActiveMtMessage encodedActiveMtMessage) {
    IdentifiedActiveMtMessage identifiedActiveMtMessage = getIdentifiedActiveMtMessage(encodedActiveMtMessage);

    return identifiedActiveMtMessage.persistedDeviceInfo();
  }

  private static ScheduledActiveMtMessage getScheduledActiveMtMessage(EncodedActiveMtMessage encodedActiveMtMessage) {
    return encodedActiveMtMessage.scheduledActiveMtMessage();
  }

  private static SimInfo getSimInfo(EncodedActiveMtMessage encodedActiveMtMessage) {
    DeviceInfo deviceInfo = getDeviceInfo(encodedActiveMtMessage);

    return deviceInfo.getSimInfo();
  }

  private static String getSmsValidPeriodSecondsAsString(EncodedActiveMtMessage encodedActiveMtMessage) {
    return String.valueOf(encodedActiveMtMessage.scheduledActiveMtMessage().sendSchemaStep().getWaitDuration().getSeconds());
  }

  private static Tid getTrackingId(EncodedActiveMtMessage encodedActiveMtMessage) {
    return encodedActiveMtMessage.
        scheduledActiveMtMessage().
        identifiedActiveMtMessage().
        joinedActiveMtMessage().
        persistedMtMessage().
        getMtMessage().
        getTid();
  }

  private static Vpi getVpi(EncodedActiveMtMessage encodedActiveMtMessage) {
    DeviceInfo deviceInfo = getDeviceInfo(encodedActiveMtMessage);

    return deviceInfo.getVpi();
  }

  @Override
  public MtMessage apply(EncodedActiveMtMessage encodedActiveMtMessage) {
    Validate.notNull(encodedActiveMtMessage, "encodedActiveMtMessage");

    SimInfo simInfo = getSimInfo(encodedActiveMtMessage);

    MtMessage.Builder mtMessageBuilder = MtMessage.newBuilder()
        .setMessageId(buildPlatformIdentifier(encodedActiveMtMessage))
        .putMessageProperties(MetaData.IP_DST_ADDRESS.name(), simInfo.getIpv4Address().toString())
        .putMessageProperties(MetaData.IP_DST_PORT.name(), simInfo.getIpv4Port().toString())
        .putMessageProperties(MetaData.OPERATOR.name(), simInfo.getMobileNetworkOperator().toString())
        .putMessageProperties(MetaData.SMPP_DEST_ADDRESS.name(), simInfo.getMsisdn().toString())
        .putMessageProperties(MetaData.SMS_VALID_PERIOD_SECONDS.name(), getSmsValidPeriodSecondsAsString(encodedActiveMtMessage))
        .putMessageProperties(MetaData.SRP_OBS_ALIAS.name(), getObsAlias(encodedActiveMtMessage).toString())
        .putMessageProperties(AppConstants.TRACKING_ID, getTrackingId(encodedActiveMtMessage).toString())
        .putMessageProperties(MetaData.VPI.name(), getVpi(encodedActiveMtMessage).toString())
        .putMessageProperties(MetaData.WTP_VERSION.name(), getDeviceInfo(encodedActiveMtMessage).getWtpVersion().toString())
        .setPayload(getByteStringPayload(encodedActiveMtMessage.serviceRoutingPduWrapper()))
        .setVehicleID(getVpi(encodedActiveMtMessage).toString());
    return mtMessageBuilder.build();
  }

  private ByteString getByteStringPayload(ServiceRoutingPduWrapper serviceRoutingPduWrapper) {
    ImmutableByteArray immutableByteArray = encodeServiceRoutingPduWrapperFunction.apply(serviceRoutingPduWrapper);

    return ByteString.copyFrom(immutableByteArray.toByteArray());
  }

  private enum MetaData {
    IP_DST_ADDRESS,
    IP_DST_PORT,
    OPERATOR,
    SMPP_DEST_ADDRESS,
    SMS_VALID_PERIOD_SECONDS,
    SRP_OBS_ALIAS,
    VPI,
    WTP_VERSION;
  }
}
