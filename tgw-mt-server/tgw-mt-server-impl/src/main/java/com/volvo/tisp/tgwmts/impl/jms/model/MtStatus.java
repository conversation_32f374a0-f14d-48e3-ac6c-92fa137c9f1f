package com.volvo.tisp.tgwmts.impl.jms.model;

public enum MtStatus {
  /*
   * The MT request was delivered successfully to the vehicle
   */
  DELIVERED,

  /*
   * Something went wrong when processing the MT message, for example the MT message has been discarded.
   */
  FAILED,

  /*
   * The MT request timed out according to the 'time to live' for the send schema. We weren't able to deliver it to the vehicle
   */
  TIMEOUT,

  /*
   * The TGW does not support this service, or it has not been activated on the telematic unit.
   */
  SERVICE_UNSUPPORTED,

  /*
   * Message was overridden my new message with the same vpi and queueId
   */
  OVERRIDDEN
}
