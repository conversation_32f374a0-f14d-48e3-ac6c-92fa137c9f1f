package com.volvo.tisp.tgwmts.impl.conf;

import java.time.Clock;

import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.mapper.RowMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import com.volvo.tisp.tgw.device.info.cache.core.api.CacheDeviceInfoReader;
import com.volvo.tisp.tgw.device.info.cache.core.api.CacheInvalidator;
import com.volvo.tisp.tgw.device.info.cache.core.impl.CacheDeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.cache.core.impl.CacheDeviceInfoReaderFactoryImpl;
import com.volvo.tisp.tgw.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.volvo.tisp.tgw.device.info.cache.notify.impl.pg.PgCacheInvalidationChannelNotifier;
import com.volvo.tisp.tgw.device.info.cache.notify.impl.pg.PgCacheInvalidationListener;
import com.volvo.tisp.tgw.device.info.cache.notify.impl.pg.PgCacheInvalidator;
import com.volvo.tisp.tgw.device.info.cache.notify.metrics.NotifyMetricReporter;
import com.volvo.tisp.tgw.device.info.database.api.DatabaseEncryptionService;
import com.volvo.tisp.tgw.device.info.database.api.DatabaseSecurityParametersResolver;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoWriterFactory;
import com.volvo.tisp.tgw.device.info.database.database.DeviceInfoReaderFactoryImpl;
import com.volvo.tisp.tgw.device.info.database.database.DeviceInfoRowMapper;
import com.volvo.tisp.tgw.device.info.database.database.DeviceInfoWriterFactoryImpl;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;

import io.micrometer.core.instrument.MeterRegistry;

@Configuration
class DeviceServiceConfig {
  private static final Logger logger = LoggerFactory.getLogger(DeviceServiceConfig.class);

  @Bean
  CacheDeviceInfoReader createCacheDeviceInfoReader(@Qualifier("deviceInfoReaderFactory") DeviceInfoReaderFactory deviceInfoReaderFactory,
      @Value("${device-service.maximum-cache-size-in-bytes}") long maxCacheSizeInBytes) {
    logger.info("maxCacheSizeInBytes: {}", maxCacheSizeInBytes);
    return CacheDeviceInfoReaderFactory.create(deviceInfoReaderFactory, maxCacheSizeInBytes);
  }

  @Bean("cacheDeviceInfoReaderFactory")
  @Primary
  DeviceInfoReaderFactory createCacheDeviceInfoReaderFactory(CacheDeviceInfoReader cacheDeviceInfoReader) {
    return CacheDeviceInfoReaderFactoryImpl.create(cacheDeviceInfoReader);
  }

  @Bean
  CacheInvalidator createCacheInvalidator(Jdbi jdbi, NotifyMetricReporter notifyMetricReporter) {
    return new PgCacheInvalidator(new PgCacheInvalidationChannelNotifier(jdbi, notifyMetricReporter));
  }

  @Bean("deviceInfoReaderFactory")
  DeviceInfoReaderFactory createDeviceInfoReaderFactory(Clock clock, DatabaseEncryptionService databaseEncryptionService,
      DatabaseSecurityParametersResolver databaseSecurityParametersResolver, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    return DeviceInfoReaderFactoryImpl.create(clock, databaseEncryptionService, databaseSecurityParametersResolver, jdbi, rowMapper);
  }

  @Bean
  DeviceInfoWriterFactory createDeviceInfoWriterFactory(Clock clock, DatabaseEncryptionService databaseEncryptionService,
      DatabaseSecurityParametersResolver databaseSecurityParametersResolver, Jdbi jdbi, RowMapper<PersistedDeviceInfo> rowMapper) {
    return DeviceInfoWriterFactoryImpl.create(clock, databaseEncryptionService, databaseSecurityParametersResolver, jdbi, rowMapper);
  }

  @Bean
  PgCacheInvalidationListener createDeviceNotificationMessageListener(DeviceServiceCacheManager deviceServiceCacheManager, Jdbi jdbi,
      NotifyMetricReporter notifyMetricReporter) {
    return new PgCacheInvalidationListener(deviceServiceCacheManager, jdbi, notifyMetricReporter);
  }

  @Bean
  DeviceServiceCacheManager createDeviceServiceCacheManager(CacheDeviceInfoReader cacheDeviceInfoReader) {
    return new DeviceServiceCacheManager(cacheDeviceInfoReader);
  }

  @Bean
  NotifyMetricReporter createNotifyMetricReporter(MeterRegistry meterRegistry) {
    return new NotifyMetricReporter(meterRegistry);
  }

  @Bean
  RowMapper<PersistedDeviceInfo> createRowMapper(DatabaseEncryptionService databaseEncryptionService) {
    return DeviceInfoRowMapper.create(databaseEncryptionService);
  }
}
