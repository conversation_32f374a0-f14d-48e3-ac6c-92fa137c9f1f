package com.volvo.tisp.tgwmts.impl.integration.logging;

import java.util.Locale;

import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class IntegrationMessage {
  private static final String MT = "MT/";
  private static final String MT_DOORKEEPER = "MT/discardMessageDueToTooManyMessagesPerVehicle";
  private static final String MT_FAILED_TO_PERSIST = "MT/Failed to persist";
  private static final String MT_SEND = "MT/Send/";

  private final String string;

  private IntegrationMessage(String string) {
    Validate.notEmpty(string, "string");

    this.string = string;
  }

  public static IntegrationMessage onDoorkeeper() {
    return new IntegrationMessage(MT_DOORKEEPER);
  }

  public static IntegrationMessage onHandleAck(ReceivedMtStatus receivedMtStatus, JoinedActiveMtMessage joinedActiveMtMessage) {
    Validate.notNull(receivedMtStatus, "receivedMtStatus");

    SrpDestinationService srpDestinationService = joinedActiveMtMessage.persistedMtMessage()
        .getMtMessage()
        .getSrpOption()
        .getSrpDestinationService();

    return new IntegrationMessage(String.format(Locale.ROOT, "%s%s/%s", MT, receivedMtStatus, srpDestinationService));
  }

  public static IntegrationMessage onPersist(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    return new IntegrationMessage(MT + sendSchemaName);
  }

  public static IntegrationMessage onPersistFail() {
    return new IntegrationMessage(MT_FAILED_TO_PERSIST);
  }

  public static IntegrationMessage onSend(SendSchemaStepType sendSchemaStepType, JoinedActiveMtMessage joinedActiveMtMessage) {
    Validate.notNull(sendSchemaStepType, "sendSchemaStepType");

    SrpDestinationService srpDestinationService = joinedActiveMtMessage
        .persistedMtMessage()
        .getMtMessage()
        .getSrpOption()
        .getSrpDestinationService();

    return new IntegrationMessage(String.format(Locale.ROOT, "%s%s/%s", MT_SEND, sendSchemaStepType, srpDestinationService));
  }

  @Override
  public String toString() {
    return string;
  }
}
