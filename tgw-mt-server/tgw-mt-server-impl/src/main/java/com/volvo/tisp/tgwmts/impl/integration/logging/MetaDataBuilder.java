package com.volvo.tisp.tgwmts.impl.integration.logging;

import java.util.HashMap;
import java.util.Map;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationVersion;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class MetaDataBuilder {
  private final Map<String, String> map;

  public MetaDataBuilder() {
    this.map = new HashMap<>();
  }

  public MetaData build() {
    return new MetaData(this);
  }

  public MetaDataBuilder setMobileDirection(String mobileDirection) {
    Validate.notNull(mobileDirection, "mobileDirection");

    map.put(MetaDataKey.MOBILE_DIRECTION.toString(), mobileDirection);
    return this;
  }

  public MetaDataBuilder setSendSchemaName(SendSchemaName sendSchemaName) {
    Validate.notNull(sendSchemaName, "sendSchemaName");

    map.put(MetaDataKey.SCHEDULE_NAME.toString(), sendSchemaName.toString());
    return this;
  }

  public MetaDataBuilder setSendSchemaStepType(SendSchemaStepType sendSchemaStepType) {
    Validate.notNull(sendSchemaStepType, "sendSchemaStepType");

    map.put(MetaDataKey.STACK_NAME.toString(), sendSchemaStepType.name());
    return this;
  }

  public MetaDataBuilder setSrpDestinationService(SrpDestinationService srpDestinationService) {
    Validate.notNull(srpDestinationService, "srpDestinationService");

    map.put(MetaDataKey.SERVICE_ID.toString(), srpDestinationService.toString());
    return this;
  }

  public MetaDataBuilder setSrpDestinationVersion(SrpDestinationVersion srpDestinationVersion) {
    Validate.notNull(srpDestinationVersion, "srpDestinationVersion");

    map.put(MetaDataKey.SERVICE_VERSION.toString(), srpDestinationVersion.toString());
    return this;
  }

  Map<String, String> getMap() {
    return map;
  }
}
