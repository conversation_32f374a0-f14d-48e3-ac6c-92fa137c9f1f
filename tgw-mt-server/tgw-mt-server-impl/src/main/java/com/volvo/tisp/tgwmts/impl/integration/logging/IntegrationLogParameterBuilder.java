package com.volvo.tisp.tgwmts.impl.integration.logging;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.wirelesscar.componentbase.logging.Logging;

public final class IntegrationLogParameterBuilder {
  private Logging.Direction direction;
  private IntegrationMessage integrationMessage;
  private MetaData metaData;
  private Logging.Status status;
  private VehicleDetail vehicleDetail;

  public IntegrationLogParameter build() {
    Validate.notNull(direction, "direction");
    Validate.notNull(integrationMessage, "integrationMessage");
    Validate.notNull(metaData, "metaData");
    Validate.notNull(status, "status");
    Validate.notNull(vehicleDetail, "vehicleDetail");

    return new IntegrationLogParameter(this);
  }

  public Logging.Direction getDirection() {
    return direction;
  }

  public IntegrationMessage getIntegrationMessage() {
    return integrationMessage;
  }

  public MetaData getMetaData() {
    return metaData;
  }

  public Logging.Status getStatus() {
    return status;
  }

  public VehicleDetail getVehicleDetail() {
    return vehicleDetail;
  }

  public IntegrationLogParameterBuilder setDirection(Logging.Direction direction) {
    Validate.notNull(direction, "direction");

    this.direction = direction;
    return this;
  }

  public IntegrationLogParameterBuilder setIntegrationMessage(IntegrationMessage integrationMessage) {
    Validate.notNull(integrationMessage, "integrationMessage");

    this.integrationMessage = integrationMessage;
    return this;
  }

  public IntegrationLogParameterBuilder setMetaData(MetaData metaData) {
    Validate.notNull(metaData, "metaData");

    this.metaData = metaData;
    return this;
  }

  public IntegrationLogParameterBuilder setStatus(Logging.Status status) {
    Validate.notNull(status, "status");

    this.status = status;
    return this;
  }

  public IntegrationLogParameterBuilder setVehicleDetail(VehicleDetail vehicleDetail) {
    Validate.notNull(vehicleDetail, "vehicleDetail");

    this.vehicleDetail = vehicleDetail;
    return this;
  }
}
