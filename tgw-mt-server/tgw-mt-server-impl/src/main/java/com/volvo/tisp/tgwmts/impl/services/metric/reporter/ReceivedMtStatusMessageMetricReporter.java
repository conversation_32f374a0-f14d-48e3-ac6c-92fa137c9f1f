package com.volvo.tisp.tgwmts.impl.services.metric.reporter;

import java.time.Duration;

import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatus;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.vcss.client.protobuf.common.TransportTypeProtobuf;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.Timer;

@Component
public class ReceivedMtStatusMessageMetricReporter {
  static final String RECEIVED_MT_STATUS = "received-mt-status";
  static final String RECEIVED_MT_STATUS_ANOMALY = "received-mt-status-anomaly";
  static final String TYPE = "TYPE";

  private final Counter canceledCounter;
  private final Counter failedProcessingCounter;
  private final Timer findActiveMtMessageWithVpiLockTimer;
  private final MeterRegistry meterRegistry;
  private final Counter notFoundCounter;
  private final Counter unknownTransportTypeCounter;

  public ReceivedMtStatusMessageMetricReporter(MeterRegistry meterRegistry) {
    this.meterRegistry = meterRegistry;
    canceledCounter = meterRegistry.counter(RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "CANCELED"));
    failedProcessingCounter = meterRegistry.counter(RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "FAILED_TO_PROCESS"));
    findActiveMtMessageWithVpiLockTimer = meterRegistry.timer("received-mt-status.find-active-mt-message");
    notFoundCounter = meterRegistry.counter(RECEIVED_MT_STATUS_ANOMALY, Tags.of(TYPE, "NOT_FOUND"));
    unknownTransportTypeCounter = meterRegistry.counter("received-mt-status.unknown-transport-type");
  }

  public void onCanceled() {
    canceledCounter.increment();
  }

  public void onFailedToProcess() {
    failedProcessingCounter.increment();
  }

  public void onFindActiveMtMessageWithVpiLock(Duration duration) {
    Validate.notNegative(duration, "duration");

    findActiveMtMessageWithVpiLockTimer.record(duration);
  }

  public void onMtStatusStatisticTransportType(TransportTypeProtobuf.TransportType transportType) {
    Validate.notNull(transportType, "transportType");

    meterRegistry.counter("mt-status-statistic.transport-type", Tags.of(TYPE, transportType.name())).increment();
  }

  public void onNotFound() {
    notFoundCounter.increment();
  }

  public void onReceivedMtStatus(ReceivedMtStatus receivedMtStatus, Duration duration) {
    Validate.notNull(receivedMtStatus, "receivedMtStatus");
    Validate.notNegative(duration, "duration");

    meterRegistry.timer(RECEIVED_MT_STATUS, Tags.of("TYPE", receivedMtStatus.name())).record(duration);
  }

  public void onUnknownTransportType() {
    unknownTransportTypeCounter.increment();
  }
}
