package com.volvo.tisp.tgwmts.impl.converters;

import java.util.Locale;
import java.util.Optional;
import java.util.function.Function;

import com.google.common.base.Strings;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.tgwmts.database.model.mtmessage.QueueId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOption;
import com.volvo.tisp.tgwmts.database.model.mtmessage.ReplyOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationService;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpDestinationVersion;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOptionBuilder;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SrpPayload;
import com.volvo.tisp.tgwmts.impl.conf.MtMessageDefaultParameters;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessage;
import com.volvo.tisp.tgwmts.impl.jms.model.ReceivedMtMessageBuilder;
import com.volvo.tisp.vc.common.dto.lib.Tid;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;

public final class MtMessageInputConverterFunction implements Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> {
  private final MtMessageDefaultParameters mtMessageDefaultParameters;
  private final Function<String, Optional<SendSchemaName>> schedulerHintConverter;

  private MtMessageInputConverterFunction(Function<String, Optional<SendSchemaName>> schedulerHintConverter,
      MtMessageDefaultParameters mtMessageDefaultParameters) {
    this.schedulerHintConverter = schedulerHintConverter;
    this.mtMessageDefaultParameters = mtMessageDefaultParameters;
  }

  public static Function<MtMessage, Either<RuntimeException, ReceivedMtMessage>> create(
      Function<String, Optional<SendSchemaName>> schedulerHintConverter, MtMessageDefaultParameters mtMessageDefaultParameters) {
    Validate.notNull(schedulerHintConverter, "schedulerHintConverter");
    Validate.notNull(mtMessageDefaultParameters, "mtMessageDefaultParameters");

    return new MtMessageInputConverterFunction(schedulerHintConverter, mtMessageDefaultParameters);
  }

  private static Optional<EnqueueingType> getEnqueueingType(SchedulerOption schedulerOption) {
    if (schedulerOption == null || Strings.isNullOrEmpty(schedulerOption.getEnqueueingType())) {
      return Optional.empty();
    }

    String enqueueingTypeString = schedulerOption.getEnqueueingType().toUpperCase(Locale.ENGLISH);

    return Optional.of(EnqueueingType.valueOf(enqueueingTypeString));
  }

  private static Optional<ReplyOption> getReplyOption(MtStatusReplyOption mtStatusReplyOption) {
    if (mtStatusReplyOption != null) {
      return Optional.of(
          new ReplyOptionBuilder()
              .setCorrelationId(CorrelationId.ofString(mtStatusReplyOption.getCorrelationId()))
              .setReplyTo(ReplyTo.ofString(mtStatusReplyOption.getReplyDestination()))
              .build());
    }
    return Optional.empty();
  }

  private static Optional<QueueId> getScheduleQueueId(SchedulerOption schedulerOption, String clientId) {
    if (schedulerOption == null) {
      return Optional.empty();
    }

    if (Strings.isNullOrEmpty(clientId)) {
      throw new IllegalArgumentException("clientId should not be null or empty");
    }

    String queueId = schedulerOption.getQueueId();

    return Strings.isNullOrEmpty(queueId)
        ? Optional.of(QueueId.ofString(clientId))
        : Optional.of(QueueId.ofString(clientId + '.' + queueId));
  }

  private static com.volvo.tisp.tgwmts.database.model.mtmessage.SrpOption getSrpOption(MtMessage mtMessage) {
    SrpOption srpOption = mtMessage.getSrpOption();

    if (srpOption == null) {
      throw new IllegalArgumentException("srpOption must not be null");
    }

    return new SrpOptionBuilder()
        .setSrpDestinationService(SrpDestinationService.ofInt(srpOption.getDstService()))
        .setSrpDestinationVersion(SrpDestinationVersion.ofShort((short) srpOption.getDstVersion()))
        .setSrpLevel12(isSrp12(srpOption))
        .setSrpPayload(SrpPayload.ofImmutableByteArray(ImmutableByteArray.of(mtMessage.getPayload())))
        .build();
  }

  private static boolean isSrp12(SrpOption srpOption) {
    SrpLevel srpLevel = srpOption.getSrpLevel();

    if (srpLevel == null) {
      return false;
    }

    return srpLevel == SrpLevel.SRP_12;
  }

  @Override
  public Either<RuntimeException, ReceivedMtMessage> apply(MtMessage mtMessage) {
    Validate.notNull(mtMessage, "mtMessage");

    try {
      return Either.right(convertToReceivedMtMessage(mtMessage));
    } catch (RuntimeException e) {
      return Either.left(e);
    }
  }

  private ReceivedMtMessage convertToReceivedMtMessage(MtMessage mtMessage) {
    return new ReceivedMtMessageBuilder()
        .setEnqueueingType(getEnqueueingType(mtMessage.getSchedulerOption()).orElse(mtMessageDefaultParameters.enqueueingType()))
        .setQueueId(getScheduleQueueId(mtMessage.getSchedulerOption(), mtMessage.getClientId()).orElse(mtMessageDefaultParameters.queueId()))
        .setReplyOption(getReplyOption(mtMessage.getMtStatusReplyOption()))
        .setSendSchemaName(getSendSchemaName(mtMessage.getSchedulerOption()).orElse(mtMessageDefaultParameters.sendSchemaName()))
        .setSrpOption(getSrpOption(mtMessage))
        .setTid(Tid.ofString(TispContext.current().tid().toString()))
        .setVpi(Vpi.ofString(mtMessage.getVehiclePlatformId()))
        .build();
  }

  private Optional<SendSchemaName> getSendSchemaName(SchedulerOption schedulerOption) {
    if (schedulerOption == null || Strings.isNullOrEmpty(schedulerOption.getHint())) {
      return Optional.empty();
    }

    return schedulerHintConverter.apply(schedulerOption.getHint());
  }
}
