package com.volvo.tisp.tgwmts.impl.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public final class ThreadUtils {
  private static final Logger logger = LoggerFactory.getLogger(ThreadUtils.class);
  private static final String THREADS = "threads";

  private ThreadUtils() {
    throw new IllegalStateException();
  }

  public static void interrupt(Iterable<Thread> threads) {
    Validate.notNull(threads, THREADS);

    for (Thread thread : threads) {
      thread.interrupt();
    }
  }

  public static void join(Iterable<Thread> threads) {
    Validate.notNull(threads, THREADS);

    for (Thread thread : threads) {
      join(thread);
    }
  }

  public static void startThreads(Iterable<Thread> threads) {
    Validate.notNull(threads, THREADS);

    for (Thread thread : threads) {
      logger.debug("Starting thread: {}", thread.getName());
      thread.start();
    }
  }

  private static void join(Thread thread) {
    try {
      thread.join();
    } catch (InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new IllegalStateException(e);
    }
  }
}
