package com.volvo.tisp.tgwmts.impl.services.mtdoorkeeper;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.tisp.vc.main.utils.lib.Validate;

public class MtDoorkeeperToggle {
  private static final Logger logger = LoggerFactory.getLogger(MtDoorkeeperToggle.class);

  private final BooleanConsumer booleanConsumer;
  private boolean value;

  private MtDoorkeeperToggle(BooleanConsumer booleanConsumer) {
    this.booleanConsumer = booleanConsumer;
  }

  public static MtDoorkeeperToggle create(BooleanConsumer booleanConsumer, boolean value) {
    Validate.notNull(booleanConsumer, "booleanConsumer");

    MtDoorkeeperToggle mtDoorkeeperToggle = new MtDoorkeeperToggle(booleanConsumer);
    mtDoorkeeperToggle.setEnabled(value);
    return mtDoorkeeperToggle;
  }

  public boolean isEnabled() {
    return value;
  }

  public void setEnabled(boolean value) {
    this.value = value;
    booleanConsumer.accept(value);
    logger.debug("value: {}", this);
  }

  @Override
  public String toString() {
    return Boolean.toString(value);
  }
}
