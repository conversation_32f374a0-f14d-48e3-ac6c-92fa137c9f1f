package com.volvo.tisp.tgwmts.impl.clients;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;

import com.volvo.connectivity.proto.MtMessage;
import com.volvo.tisp.tgwmts.impl.conf.properties.MtRouterReactiveClientConfig;
import com.volvo.tisp.vc.main.utils.lib.Validate;

import reactor.core.publisher.Mono;

public class MtRouterReactiveClient {
  private static final Logger logger = LoggerFactory.getLogger(MtRouterReactiveClient.class);
  private final MtRouterReactiveClientConfig mtRouterReactiveClientConfig;
  private final WebClient mtRouterWebClient;

  public MtRouterReactiveClient(MtRouterReactiveClientConfig mtRouterReactiveClientConfig, WebClient mtRouterWebClient) {
    Validate.notNull(mtRouterReactiveClientConfig, "mtRouterRestClientConfig");
    Validate.notNull(mtRouterWebClient, "mtRouterWebClient");

    this.mtRouterReactiveClientConfig = mtRouterReactiveClientConfig;
    this.mtRouterWebClient = mtRouterWebClient;
  }

  private static Mono<Boolean> handleError(Throwable throwable) {
    logger.error("", throwable);
    return Mono.just(false);
  }

  private static Mono<Boolean> handleResponse(ClientResponse clientResponse) {
    if (clientResponse.statusCode().is2xxSuccessful()) {
      return Mono.just(true);
    } else {
      logger.warn("response error: {}", clientResponse.statusCode().value());
      return Mono.just(false);
    }
  }

  public Mono<Boolean> post(MtMessage mtMessage) {
    Validate.notNull(mtMessage, "mtMessage");

    return mtRouterWebClient.post()
        .uri(mtRouterReactiveClientConfig.getMtRouterRequestPath())
        .bodyValue(mtMessage)
        .exchangeToMono(MtRouterReactiveClient::handleResponse)
        .timeout(mtRouterReactiveClientConfig.getRequestTimeout())
        .onErrorResume(MtRouterReactiveClient::handleError);
  }
}
