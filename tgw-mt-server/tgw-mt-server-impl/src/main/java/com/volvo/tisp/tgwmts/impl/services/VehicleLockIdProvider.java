package com.volvo.tisp.tgwmts.impl.services;

import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageWriter;
import com.volvo.tisp.tgwmts.database.model.InsertionFailure;
import com.volvo.tisp.tgwmts.database.model.InsertionFailureReason;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLock;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockBuilder;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.VehicleLockId;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.VehicleLockIdProviderMetricReporter;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

@Component
public class VehicleLockIdProvider {
  private static final Logger logger = LoggerFactory.getLogger(VehicleLockIdProvider.class);

  private final Clock clock;
  private final VehicleLockIdProviderMetricReporter vehicleLockIdProviderMetricReporter;

  public VehicleLockIdProvider(Clock clock, VehicleLockIdProviderMetricReporter vehicleLockIdProviderMetricReporter) {
    this.clock = clock;
    this.vehicleLockIdProviderMetricReporter = vehicleLockIdProviderMetricReporter;
  }

  private static Set<Vpi> getNonPersistedVpis(List<PersistedVehicleLock> persistedVehicleLocks, Set<Vpi> vpis) {
    Set<Vpi> nonPersistedVpis = new HashSet<>(vpis);
    for (PersistedVehicleLock persistedVehicleLock : persistedVehicleLocks) {
      nonPersistedVpis.remove(persistedVehicleLock.getVehicleLock().getVpi());
    }
    return Collections.unmodifiableSet(nonPersistedVpis);
  }

  private static List<VehicleLockId> getVehicleLockIds(List<PersistedVehicleLock> persistedVehicleLocks) {
    return persistedVehicleLocks.stream().map(PersistedVehicleLock::getVehicleLockId).toList();
  }

  public List<VehicleLockId> insertVehicleLockIfMissingAndAcquireVehicleLockId(Set<Vpi> vpis, ActiveMtMessageWriter activeMtMessageWriter) {
    Validate.notEmpty(vpis, "vpis");
    Validate.notNull(activeMtMessageWriter, "activeMtMessageWriter");

    Instant startTime = clock.instant();
    List<PersistedVehicleLock> persistedVehicleLocks = activeMtMessageWriter.findAndLockByVpis(vpis);
    vehicleLockIdProviderMetricReporter.onVpiLock(Duration.between(startTime, clock.instant()));

    Set<Vpi> nonPersistedVpis = getNonPersistedVpis(persistedVehicleLocks, vpis);

    if (nonPersistedVpis.isEmpty()) {
      return getVehicleLockIds(persistedVehicleLocks);
    }

    insertVehicleLocks(nonPersistedVpis, activeMtMessageWriter);
    return retryAcquireVehicleLock(vpis, activeMtMessageWriter);
  }

  private Either<InsertionFailure, VehicleLockId> insertVehicleLock(ActiveMtMessageWriter activeMtMessageWriter, VehicleLock vehicleLock) {
    Instant startTime = clock.instant();
    Either<InsertionFailure, VehicleLockId> either = activeMtMessageWriter.insertVehicleLock(vehicleLock);

    if (either.isRight()) {
      vehicleLockIdProviderMetricReporter.onVehicleLockInsertionSuccess(Duration.between(startTime, clock.instant()));
    }

    return either;
  }

  private void insertVehicleLocks(Set<Vpi> vpis, ActiveMtMessageWriter activeMtMessageWriter) {
    logger.debug("Missing VPIs in vehicle lock table, inserting: {}", vpis);
    List<VehicleLock> vehicleLocks = vpis.stream().map(VehicleLockBuilder::ofVpi).toList();
    List<Either<InsertionFailure, VehicleLockId>> eithers = vehicleLocks.stream()
        .map(vehicleLock -> insertVehicleLock(activeMtMessageWriter, vehicleLock))
        .toList();
    eithers.stream().filter(Either::isLeft).map(Either::getLeft).forEach(this::processInsertionFailure);
  }

  private void processInsertionFailure(InsertionFailure insertionFailure) {
    InsertionFailureReason insertionFailureReason = insertionFailure.insertionFailureReason();
    vehicleLockIdProviderMetricReporter.onVehicleLockInsertionFailure(insertionFailureReason);

    switch (insertionFailureReason) {
      case DUPLICATE_KEY:
        break;

      case FOREIGN_KEY_VIOLATION:
      case UNKNOWN:
      default:
        throw insertionFailure.runtimeException();
    }
  }

  private List<VehicleLockId> retryAcquireVehicleLock(Set<Vpi> vpis, ActiveMtMessageWriter activeMtMessageWriter) {
    logger.debug("Trying again to acquire VPI locks for: {}", vpis);

    Instant startTime = clock.instant();
    List<PersistedVehicleLock> persistedVehicleLocks = activeMtMessageWriter.findAndLockByVpis(vpis);
    vehicleLockIdProviderMetricReporter.onVpiLock(Duration.between(startTime, clock.instant()));

    if (persistedVehicleLocks.size() != vpis.size()) {
      throw new IllegalStateException("could not acquire all locks for vpis: " + getNonPersistedVpis(persistedVehicleLocks, vpis));
    }

    return getVehicleLockIds(persistedVehicleLocks);
  }
}
