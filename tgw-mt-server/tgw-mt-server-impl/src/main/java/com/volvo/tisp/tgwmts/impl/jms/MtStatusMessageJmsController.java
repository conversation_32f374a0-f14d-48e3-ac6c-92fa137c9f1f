package com.volvo.tisp.tgwmts.impl.jms;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.volvo.connectivity.proto.MtStatus;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.tgwmts.impl.converters.MtStatusMessageInputConverterFunction;
import com.volvo.tisp.tgwmts.impl.model.MtStatusClient;
import com.volvo.tisp.tgwmts.impl.model.ReceivedMtStatusMessage;
import com.volvo.tisp.tgwmts.impl.services.ReceivedMtStatusMessageProcessor;
import com.volvo.tisp.tgwmts.impl.services.metric.reporter.MtStatusMetricReporter;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

@JmsController(destination = MtStatusMessageJmsController.MT_STATUS_MESSAGE_IN_QUEUE_NAME)
public class MtStatusMessageJmsController {
  public static final String MT_STATUS_MESSAGE_IN_QUEUE_NAME = "MT-STATUS-IN";
  public static final String MT_STATUS_MESSAGE_TYPE = "MT_STATUS_MESSAGE";
  public static final String MT_STATUS_MESSAGE_VERSION = "1.0";
  private static final Logger logger = LoggerFactory.getLogger(MtStatusMessageJmsController.class);

  private final MtStatusMessageInputConverterFunction inputConverter;
  private final MtStatusMetricReporter mtStatusMetricReporter;
  private final ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor;

  public MtStatusMessageJmsController(
      MtStatusMetricReporter mtStatusMetricReporter,
      ReceivedMtStatusMessageProcessor receivedMtStatusMessageProcessor,
      MtStatusMessageInputConverterFunction inputConverter) {
    this.mtStatusMetricReporter = mtStatusMetricReporter;
    this.receivedMtStatusMessageProcessor = receivedMtStatusMessageProcessor;
    this.inputConverter = inputConverter;
  }

  private static MtStatusClient getMtStatusClient(JmsMessage<MtStatus> jmsMessage) {
    return jmsMessage.header(TispJmsHeader.CLIENT, String.class)
        .flatMap(MtStatusClient::fromString)
        .orElse(MtStatusClient.UNKNOWN);
  }

  @JmsMessageMapping(consumesType = MT_STATUS_MESSAGE_TYPE, consumesVersion = MT_STATUS_MESSAGE_VERSION)
  public void receiveMtStatus(JmsMessage<MtStatus> jmsMessage) {
    Validate.notNull(jmsMessage, "jmsMessage");

    logger.debug("received mtStatus: {}", jmsMessage);
    Either<IllegalArgumentException, ReceivedMtStatusMessage> either = inputConverter.apply(jmsMessage.payload(), getMtStatusClient(jmsMessage));

    if (either.isLeft()) {
      logger.warn("failed to convert mtStatus: {}", jmsMessage, either.getLeft());
      mtStatusMetricReporter.onInvalidMtStatus();
      return;
    }

    receivedMtStatusMessageProcessor.processMtStatusMessage(either.getRight());
  }
}
