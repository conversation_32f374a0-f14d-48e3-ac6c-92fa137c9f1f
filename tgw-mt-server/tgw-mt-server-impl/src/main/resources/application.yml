# Global properties
server.port: 12720
spring:
  flyway.enabled: false
  liquibase.enabled: false
  jms.listener:
    concurrency: 1
    max-concurrency: 10

bulk-sync:
  conrepo2.url: http://conrepo2.<DOMAIN>:33581/api/v1/bulksync
  retain.processing-tags: false

device-service.maximum-cache-size-in-bytes: 50000000

---
#DE
spring.config.activate.on-profile: de_component-test_eu-west-1

influx.event.enabled: false
db:
  url: ****************************************
  user: de_admin
  pw:
bulk-sync:
  security:
    client.key-alias: tcetisp2-test-1
    conrepo2.key-alias: conrepo2-test-1

security:
  keystore:
    path: keystore.pkcs12
    password: tcetisp
  database.key: tcetisp-test
  transport:
    key.private: tcetisp-test
    notify.key.public: tcetisp-test

sqs:
  region: us-east-1
  endpoint: http://mockhost:4566

---
#LOCAL
spring.config.activate.on-profile: local_local_local

server.host: 127.0.0.1
influx.event.enabled: false
db:
  url: *****************************************
  user: postgres
  pw: postgres
bulk-sync.security:
  client.key-alias: tgwmts-local-1
  conrepo2.key-alias: conrepo2-local-1

security:
  keystore:
    path: ../deployable-assembly/src/main/content/jar/local/keystore.pkcs12
    password: tcetisp
  database.key: tcetisp-test
  transport:
    key.private: tcetisp-test
    notify.key.public: tcetisp-test

sqs:
  region: us-east-1
  endpoint: http://localhost:4566
