package com.volvo.tisp.tgwmts.integration.tests;

import java.time.Duration;

import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import com.google.protobuf.InvalidProtocolBufferException;
import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tce.proto.ConnectionEstablishedRegistration;
import com.volvo.tisp.tgwmts.impl.cache.ConnectionEstablishedCacheService;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.JmsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.MtMessageTestsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.RestUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.config.SendSchemaOverrideTestConfig;

class ConnectionEstablishedRegistrationFailureIntegrationTest {

  private static void verifyConnectionEstablishedRegistrationFailurePersisted(WireMockServerWrapper wireMockServerWrapper,
      ConnectionEstablishedCacheService connectionEstablishedCacheService) throws InvalidProtocolBufferException {
    LoggedRequest loggedRequest = RestUtil.awaitPostRequestToMoConnectionEstablishedRegistrationService(wireMockServerWrapper.getWireMockServer(),
        Duration.ofSeconds(5)).orElseThrow();
    ConnectionEstablishedRegistration connectionEstablishedRegistration = ConnectionEstablishedRegistration.parseFrom(loggedRequest.getBody());

    Assertions.assertEquals(DataUtil.VPI.toString(), connectionEstablishedRegistration.getVpi().getValue());

    Awaitility.await()
        .atMost(Duration.ofSeconds(20))
        .pollInterval(Duration.ofMillis(300))
        .untilAsserted(() -> Assertions.assertEquals(1, connectionEstablishedCacheService.getEntries(10).size()));
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);
      IntegrationTestHelper.stubMoConnectionEstablishedRegistrationApi(wireMockServerWrapper);
      IntegrationTestHelper.stubMtRouterServer(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class,
          SendSchemaOverrideTestConfig.class)) {
        wireMockServerWrapper.getWireMockServer().stubFor(WireMock.post("/api/v1/connectionEstablishedRegistration").willReturn(WireMock.serverError()));

        ConnectionEstablishedCacheService connectionEstablishedCacheService = configurableApplicationContext.getBean(ConnectionEstablishedCacheService.class);

        DataUtil.createAndPersistSrp11Device(configurableApplicationContext);
        JmsUtil.sendMtMessage(DataUtil.createMtMessage(MtMessageTestsUtil.CORRELATION_ID, "low"));

        RestUtil.awaitPostRequestToMtRouter(wireMockServerWrapper.getWireMockServer(), Duration.ofSeconds(10)).orElseThrow();

        verifyConnectionEstablishedRegistrationFailurePersisted(wireMockServerWrapper, connectionEstablishedCacheService);
      }
    }
  }
}
