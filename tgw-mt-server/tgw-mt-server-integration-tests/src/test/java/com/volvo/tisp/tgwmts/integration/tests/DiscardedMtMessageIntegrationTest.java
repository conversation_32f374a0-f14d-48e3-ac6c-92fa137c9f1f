package com.volvo.tisp.tgwmts.integration.tests;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.MtMessageTestsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;

class DiscardedMtMessageIntegrationTest {
  private static void sendMtMessagesAndVerifyLastInsertedMtMessageIdAndActiveMtMessageId(ActiveMtMessageReaderFactory activeMtMessageReaderFactory,
      Pair<MtMessageId, ActiveMtMessageId> pair,
      EnqueueingType enqueueingType) throws Exception {
    MtMessageTestsUtil.sendMtMessageAndMtStatusExpected(enqueueingType, CorrelationId.ofString("correlationId5"), MtStatus.FAILED);
    MtMessageTestsUtil.checkMtMessageTable(activeMtMessageReaderFactory, true, 3, 2);
    Assertions.assertEquals(pair,
        MtMessageTestsUtil.getLastInsertedMtMessageIdAndActivateMtMessagePair(activeMtMessageReaderFactory));
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        ActiveMtMessageReaderFactory activeMtMessageReaderFactory = configurableApplicationContext.getBean(ActiveMtMessageReaderFactory.class);

        DataUtil.createAndPersistSrp10Device(configurableApplicationContext);

        Pair<MtMessageId, ActiveMtMessageId> pair = MtMessageTestsUtil.sentThreeNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(
            activeMtMessageReaderFactory,
            DataUtil.CLIENT_ID, DataUtil.CLIENT_ID, DataUtil.CLIENT_ID);

        sendMtMessagesAndVerifyLastInsertedMtMessageIdAndActiveMtMessageId(activeMtMessageReaderFactory, pair, EnqueueingType.NORMAL);
        sendMtMessagesAndVerifyLastInsertedMtMessageIdAndActiveMtMessageId(activeMtMessageReaderFactory, pair, EnqueueingType.IMMEDIATE);
      }
    }
  }
}
