package com.volvo.tisp.tgwmts.integration.tests.util;

import java.io.Closeable;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.volvo.tisp.vc.main.utils.lib.Validate;

public class WireMockServerWrapper implements Closeable {
  private final WireMockServer wireMockServer;

  public WireMockServerWrapper(WireMockServer wireMockServer) {
    Validate.notNull(wireMockServer, "wireMockServer");

    this.wireMockServer = wireMockServer;
  }

  @Override
  public void close() {
    wireMockServer.stop();
  }

  public WireMockServer getWireMockServer() {
    return wireMockServer;
  }
}
