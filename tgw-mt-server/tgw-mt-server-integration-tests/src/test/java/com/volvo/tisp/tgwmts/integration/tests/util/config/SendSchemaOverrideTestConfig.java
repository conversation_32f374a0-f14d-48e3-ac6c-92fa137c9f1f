package com.volvo.tisp.tgwmts.integration.tests.util.config;

import java.util.function.Function;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.integration.tests.util.ConnectionEstablishedTestSendSchema;

public class SendSchemaOverrideTestConfig {
  @Primary
  @Bean
  Function<SendSchemaName, SendSchema> createTestSendSchemaNameMappingFunction() {
    return sendSchemaName -> ConnectionEstablishedTestSendSchema.INSTANCE;
  }
}
