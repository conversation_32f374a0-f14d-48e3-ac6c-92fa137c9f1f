package com.volvo.tisp.tgwmts.integration.tests.util;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import java.util.Optional;

import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.TextMessage;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;

import javax.crypto.Cipher;
import javax.crypto.CipherInputStream;
import javax.xml.transform.stream.StreamSource;

import org.apache.activemq.artemis.core.config.Configuration;
import org.apache.activemq.artemis.core.config.impl.ConfigurationImpl;
import org.apache.activemq.artemis.core.server.embedded.EmbeddedActiveMQ;
import org.apache.activemq.artemis.jms.client.ActiveMQTextMessage;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.MappingBuilder;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.common.Slf4jNotifier;
import com.github.tomakehurst.wiremock.core.WireMockConfiguration;
import com.google.common.collect.ArrayListMultimap;
import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.subscriptionrepository.client.Destination;
import com.volvo.tisp.subscriptionrepository.client.SubscriptionStubber;
import com.volvo.tisp.tgwmts.integration.tests.crypto.CryptoUtil;
import com.volvo.tisp.vc.device.bulk.sync.model.Region;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.vcss.client.protobuf.MessageTypes;
import com.volvo.vc.crypto.asymmetric.signature.Signature;
import com.volvo.vc.crypto.common.entity.EncryptedAesKey;
import com.volvo.vc.crypto.common.keystore.KeyAlias;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesCipherFactory;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;
import com.volvo.vc.crypto.symmetric.key.SymmetricKeyUtil;
import com.volvo.vc.crypto.symmetric.key.Transformation;

import io.opentelemetry.api.GlobalOpenTelemetry;

public final class IntegrationTestHelper {
  private static final String MO_SERVER_SHORTNAME = "moshort";
  private static final String MR_ROUTER_SHORTNAME = "rtshort";

  private IntegrationTestHelper() {
    throw new IllegalStateException();
  }

  public static EmbeddedActiveMqWrapper createAndStartEmbeddedActiveMqWrapper() throws Exception {
    EmbeddedActiveMQ embeddedActiveMQ = new EmbeddedActiveMQ();
    embeddedActiveMQ.setConfiguration(createActiveMQConfiguration());
    embeddedActiveMQ.start();
    return new EmbeddedActiveMqWrapper(embeddedActiveMQ);
  }

  public static WireMockServerWrapper createAndStartWireMockServer() {
    WireMockServer wireMockServer = createWireMockServer();
    wireMockServer.start();

    return new WireMockServerWrapper(wireMockServer);
  }

  public static <T> String marshalToXml(T object) throws JAXBException, IOException {
    JAXBContext jaxbContext = JAXBContext.newInstance(object.getClass());
    Marshaller marshaller = jaxbContext.createMarshaller();

    try (StringWriter stringWriter = new StringWriter()) {
      marshaller.marshal(object, stringWriter);
      return stringWriter.toString();
    }
  }

  public static void mockAppConfig(EmbeddedPostgres embeddedPostgres, WireMockServerWrapper wireMockServerWrapper, LocalStackContainer sqsLocalStackContainer) {
    System.setProperty("service.confdir", "src/test/resources");
    System.setProperty("CONFIG", "file://");
    System.setProperty("spring.artemis.broker-url", JmsUtil.AMQ_URL);

    final int wireMockPortNumber = wireMockServerWrapper.getWireMockServer().port();

    System.setProperty("bulk-sync.conrepo2.url", "http://localhost:" + wireMockPortNumber + "/api/v1/bulksync");
    System.setProperty("db.url", "jdbc:postgresql://" + embeddedPostgres.getHost() + ":" + embeddedPostgres.getPort() + "/postgres");
    System.setProperty("moserver.base-url", "http://" + MO_SERVER_SHORTNAME);
    System.setProperty("servicediscovery." + MO_SERVER_SHORTNAME, "http://localhost:" + wireMockPortNumber + "/");
    System.setProperty("mtrouter.request-path", RestUtil.MT_ROUTER_REST_API_PATH);
    System.setProperty("mtrouter.base-url", "http://" + MR_ROUTER_SHORTNAME);
    System.setProperty("servicediscovery." + MR_ROUTER_SHORTNAME, "http://localhost:" + wireMockPortNumber + "/");
    System.setProperty("sqs.region", sqsLocalStackContainer.getRegion());
    System.setProperty("sqs.endpoint", sqsLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS).toString());
    System.setProperty("service-monitoring.sqs-queue", SqsUtil.getQueueUrl(SqsUtil.QUALITY_CAR_MONITORING_QUEUE, sqsLocalStackContainer));
    System.setProperty("servicediscovery.subr", "http://localhost:" + wireMockPortNumber + "/");
  }

  public static ConfigurableApplicationContext runSpringApplication(Class<?>... configurationClass) {
    return SpringApplication.run(configurationClass, new String[0]);
  }

  public static ConfigurableApplicationContext runSpringApplication(
      Class<?> configurationClass, int port) {
    GlobalOpenTelemetry.resetForTest();
    System.setProperty("server.port", String.valueOf(port));
    System.setProperty("management.tracing.enabled", "false");
    return runSpringApplication(configurationClass);
  }

  public static EmbeddedPostgres startEmbeddedPostgres() throws IOException {
    return EmbeddedPostgres.builder()
        .setImage(DockerImageName.parse("artifactory.sharedservices.prod.euw1.vg-cs.net/docker-public/postgres:latest"))
        .setLocaleConfig("locale", "C")
        .start();
  }

  public static void stubConRepo2(WireMockServerWrapper wireMockServerWrapper, byte[] payload, Optional<Region> region, int status)
      throws NoSuchAlgorithmException, IOException {
    String testUrl = region.map(value -> "/api/v1/bulksync?region=" + com.volvo.cos.conrepo.bulk_sync.v1.Region.valueOf(value.name())
            + "&clientKeyAlias=tcetisp-test-1&conrepoKeyAlias=tcetisp-test-1&softcarOnly=false")
        .orElse("/api/v1/bulksync?clientKeyAlias=tcetisp-test-1&conrepoKeyAlias=tcetisp-test-1&softcarOnly=false");
    MappingBuilder mappingBuilder = WireMock.get(WireMock.urlEqualTo(testUrl));

    SymmetricKey symmetricKey = SymmetricKey.create(SymmetricKeyUtil.createAesKey());
    EncryptedAesKey encryptedAesKey = CryptoUtil.encrypSymmetricKey(CryptoUtil.getPublicKey(KeyAlias.ofString("tcetisp-test-1")).getRight(), symmetricKey);
    Signature signature = CryptoUtil.sign(CryptoUtil.getPrivateKey(KeyAlias.ofString("tcetisp-test-1")).getRight(), encryptedAesKey);
    Cipher cipher = SymmetricAesCipherFactory.initEncryptCipher(symmetricKey, Transformation.AES_CBC_PKCS5PADDING);

    CipherInputStream cipherInputStream = new CipherInputStream(new ByteArrayInputStream(payload), cipher);
    ResponseDefinitionBuilder responseDefinitionBuilder = WireMock.ok()
        .withStatus(status)
        .withBody(cipherInputStream.readAllBytes())
        .withHeader("ENCRYPTED_AES_KEY", encodeToBase64String(encryptedAesKey.getImmutableByteArray()))
        .withHeader("IV", encodeToBase64String(cipher.getIV()))
        .withHeader("SIGNATURE", encodeToBase64String(signature.getImmutableByteArray()));
    mappingBuilder.willReturn(responseDefinitionBuilder);

    wireMockServerWrapper.getWireMockServer().stubFor(mappingBuilder);
  }

  public static void stubMoConnectionEstablishedRegistrationApi(WireMockServerWrapper wireMockServerWrapper) {
    WireMockServer wireMockServer = wireMockServerWrapper.getWireMockServer();
    wireMockServer.stubFor(WireMock.post(RestUtil.MO_CONNECTION_ESTABLISHED_REGISTRATION_API_PATH).willReturn(WireMock.ok()));
    wireMockServer.stubFor(WireMock.post(RestUtil.MO_CONNECTION_ESTABLISHED_REGISTRATION_MULTIPLE_API_PATH).willReturn(WireMock.ok()));
  }

  public static void stubMtRouterServer(WireMockServerWrapper wireMockServerWrapper) {
    WireMockServer wireMockServer = wireMockServerWrapper.getWireMockServer();
    wireMockServer.stubFor(WireMock.post(RestUtil.MT_ROUTER_REST_API_PATH).willReturn(WireMock.ok()));
  }

  public static void stubSubRepo() {
    Destination vcssMtStatusStatisticDestination = new Destination(MessageTypes.VCSS_MT_STATUS_STATISTIC, MessageTypes.VERSION_1_0,
        "activemq:queue:" + MessageTypes.VCSS_MT_STATUS_STATISTIC, "vcssMtStatusStatisticDestination");

    ArrayListMultimap<String, Object> optionsMap = ArrayListMultimap.create();
    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(MessageTypes.VCSS_MT_STATUS_STATISTIC)
        .withOptions(optionsMap)
        .thenMessageShouldBeDeliveredTo(vcssMtStatusStatisticDestination);
  }

  public static <T> T unmarshal(Message message, Class<T> type) throws JAXBException, JMSException {
    TextMessage textMessage = (ActiveMQTextMessage) message;
    JAXBContext jaxbContext = JAXBContext.newInstance(type);
    Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

    try (StringReader stringReader = new StringReader(textMessage.getText())) {
      return unmarshaller.unmarshal(new StreamSource(stringReader), type).getValue();
    }
  }

  private static Configuration createActiveMQConfiguration() throws Exception {
    return new ConfigurationImpl()
        .addAcceptorConfiguration("tcp", JmsUtil.AMQ_URL)
        .setPersistenceEnabled(false)
        .setSecurityEnabled(false);
  }

  private static WireMockServer createWireMockServer() {
    WireMockConfiguration wireMockConfiguration = new WireMockConfiguration().dynamicPort().notifier(new Slf4jNotifier(false));
    return new WireMockServer(wireMockConfiguration);
  }

  private static String encodeToBase64String(ImmutableByteArray immutableByteArray) {
    return encodeToBase64String(immutableByteArray.toByteArray());
  }

  private static String encodeToBase64String(byte[] bytes) {
    return Base64.getEncoder().encodeToString(bytes);
  }
}
