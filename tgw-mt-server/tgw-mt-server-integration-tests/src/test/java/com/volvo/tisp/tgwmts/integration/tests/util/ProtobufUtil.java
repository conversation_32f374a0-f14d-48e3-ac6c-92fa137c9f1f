package com.volvo.tisp.tgwmts.integration.tests.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;

import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import com.volvo.cos.conrepo.bulk_sync.v1.Region;
import com.volvo.cos.conrepo.bulk_sync.v1.SrpLevel;
import com.volvo.cos.conrepo.bulk_sync.v1.TgwSubType;
import com.volvo.cos.conrepo.bulk_sync.v1.VehicleInfo;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.vc.conrepo.api.v2.DeviceCryptoKey;
import com.volvo.vc.conrepo.api.v2.DevicePendingCryptoKey;

public final class ProtobufUtil {
  private ProtobufUtil() {
    throw new IllegalStateException();
  }

  public static byte[] createDuplicateVehicleInfoProtobuf(Ipv4Address ipv4Address, Vpi vpi) throws IOException {
    try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
      createVehicleInfo(ipv4Address, vpi).writeDelimitedTo(byteArrayOutputStream);
      createVehicleInfo(ipv4Address, vpi).writeDelimitedTo(byteArrayOutputStream);
      return byteArrayOutputStream.toByteArray();
    }
  }

  public static byte[] createEmptyVehicleInfoProtobuf() {
    return VehicleInfo.newBuilder().build().toByteArray();
  }

  public static byte[] createVehicleInfoProtobuf(Ipv4Address ipv4Address, Vpi vpi) throws IOException {
    return createVehicleInfoProtobuf(ipv4Address, vpi, "");
  }

  public static byte[] createVehicleInfoProtobuf(Ipv4Address ipv4Address, Vpi vpi, String satelliteId) throws IOException {
    try (ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream()) {
      createVehicleInfo(ipv4Address, vpi, satelliteId).writeDelimitedTo(byteArrayOutputStream);
      return byteArrayOutputStream.toByteArray();
    }
  }

  public static com.volvo.connectivity.proto.MtMessage parseProtobufMtRouterMtMessage(byte[] data) throws InvalidProtocolBufferException {
    return com.volvo.connectivity.proto.MtMessage.parseFrom(data);
  }

  private static DeviceCryptoKey createDeviceCryptoKey() {
    DeviceCryptoKey deviceCryptoKey = new DeviceCryptoKey();
    deviceCryptoKey.setKeyId("key1");
    deviceCryptoKey.setKey(new byte[] {1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1});
    return deviceCryptoKey;
  }

  private static DevicePendingCryptoKey createDevicePendingCryptoKey() {
    DevicePendingCryptoKey pendingDeviceCryptoKey = new DevicePendingCryptoKey();
    pendingDeviceCryptoKey.setKeyId("key2");
    pendingDeviceCryptoKey.setKey(new byte[] {2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2});
    return pendingDeviceCryptoKey;
  }

  private static VehicleInfo createVehicleInfo(Ipv4Address ipv4Address, Vpi vpi) {
    return createVehicleInfo(ipv4Address, vpi, "");
  }

  private static VehicleInfo createVehicleInfo(Ipv4Address ipv4Address, Vpi vpi, String satelliteId) {
    final DeviceCryptoKey deviceCryptoKey = createDeviceCryptoKey();
    final DevicePendingCryptoKey pendingDeviceCryptoKey = createDevicePendingCryptoKey();

    return VehicleInfo.newBuilder()
        .setActiveAesKey(ByteString.copyFrom(deviceCryptoKey.getKey()))
        .setActiveAesKeyId(deviceCryptoKey.getKeyId())
        .setHandle(DataUtil.HANDLE.toLong())
        .setIpV4Address(ipv4Address.toInt())
        .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR.toString())
        .setMsisdn(DataUtil.MSISDN.toString())
        .setObsAlias(DataUtil.OBS_ALIAS.toLong())
        .setPendingAesKey(ByteString.copyFrom(pendingDeviceCryptoKey.getKey()))
        .setPendingAesKeyId(pendingDeviceCryptoKey.getKeyId())
        .setPort(DataUtil.IPV4_PORT.toInt())
        .setRegion(Region.NA)
        .setSrpLevel(SrpLevel.SRP_10)
        .setTgwSubType(TgwSubType.TGW_1)
        .setVpi(ByteString.copyFrom(vpi.toBytes()))
        .setSatelliteId(satelliteId)
        .setWtpVersion(com.volvo.cos.conrepo.bulk_sync.v1.WtpVersion.VERSION_1)
        .build();
  }
}
