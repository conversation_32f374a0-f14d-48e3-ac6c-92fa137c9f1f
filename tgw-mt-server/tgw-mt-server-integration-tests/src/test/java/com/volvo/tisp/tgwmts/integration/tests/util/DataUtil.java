package com.volvo.tisp.tgwmts.integration.tests.util;

import java.security.SecureRandom;
import java.time.Duration;
import java.util.Optional;
import java.util.Set;

import org.apache.commons.lang3.tuple.Pair;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Assertions;
import org.springframework.context.ConfigurableApplicationContext;

import com.volvo.connectivity.cms.events.model.CapabilityState;
import com.volvo.connectivity.cms.events.model.ConnectivityCapability;
import com.volvo.connectivity.cms.events.model.TgwAsset;
import com.volvo.connectivity.cms.events.model.TgwNotifyEvent;
import com.volvo.connectivity.cms.events.model.Wifi;
import com.volvo.tisp.tgw.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoWriter;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoWriterFactory;
import com.volvo.tisp.tgw.device.info.database.model.AesKey;
import com.volvo.tisp.tgw.device.info.database.model.CryptoKeyInfo;
import com.volvo.tisp.tgw.device.info.database.model.DatabaseError;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfoBuilder;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfoId;
import com.volvo.tisp.tgw.device.info.database.model.Handle;
import com.volvo.tisp.tgw.device.info.database.model.Ipv4Port;
import com.volvo.tisp.tgw.device.info.database.model.MobileNetworkOperator;
import com.volvo.tisp.tgw.device.info.database.model.SimInfo;
import com.volvo.tisp.tgw.device.info.database.model.SimInfoBuilder;
import com.volvo.tisp.tgw.device.info.database.model.WtpVersion;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.DestinationService;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.DestinationVersion;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.Priority;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.SourceService;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.SourceVersion;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.api.v2.MtStatusReplyOption;
import com.wirelesscar.tce.api.v2.SchedulerOption;
import com.wirelesscar.tce.api.v2.SrpLevel;
import com.wirelesscar.tce.api.v2.SrpOption;

public final class DataUtil {
  public static final AesKey AES_KEY = generateRandomAesKey();
  public static final String CLIENT_ID = "someClientId";
  public static final DestinationService DESTINATION_SERVICE = DestinationService.ofInt(42);
  public static final DestinationVersion DESTINATION_VERSION = DestinationVersion.ofShort((short) 1);
  public static final Handle HANDLE = Handle.ofLong(42);
  public static final Imsi IMSI = Imsi.ofLong(240080008192573L);
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  public static final Ipv4Port IPV4_PORT = Ipv4Port.ofInt(51_000);
  public static final MobileNetworkOperator MOBILE_NETWORK_OPERATOR = MobileNetworkOperator.ofString("telenor");
  public static final Msisdn MSISDN = Msisdn.ofString("+461234567891");
  public static final ObsAlias OBS_ALIAS = ObsAlias.ofLong(42);
  public static final ImmutableByteArray PAYLOAD = ImmutableByteArray.of(new byte[] {1, -2, 3, -4, 5});
  public static final Priority PRIORITY = Priority.ofShort((short) 44);
  public static final ReplyTo REPLY_TO = ReplyTo.ofString("LOCAL.LOCAL.LOCAL.SOME.REPLY-TO-QUEUE");
  public static final SourceService SOURCE_SERVICE = SourceService.ofInt(42);
  public static final SourceVersion SOURCE_VERSION = SourceVersion.ofShort((short) 1);
  public static final Vpi VPI = Vpi.ofString("1234567890ABCDEF1234567890ABCDEF");
  private static final String SCHEDULE_HINT = "low";

  private DataUtil() {
    throw new IllegalStateException();
  }

  public static void checkDeviceInDatabase(DeviceInfoReaderFactory deviceInfoReaderFactory) {
    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      DeviceInfo deviceInfo = deviceInfoReader.findDeviceInfoByVpi(VPI).orElseThrow().getDeviceInfo();
      verifyDeviceInfo(deviceInfo);
    }
  }

  public static void createAndPersistSrp10Device(ConfigurableApplicationContext configurableApplicationContext) {
    createAndPersistDevice(configurableApplicationContext, createSrp10DeviceInfo());
  }

  public static void createAndPersistSrp11Device(ConfigurableApplicationContext configurableApplicationContext) {
    createAndPersistDevice(configurableApplicationContext, createSrp11DeviceInfo());
  }

  public static MtMessage createMtMessage(CorrelationId correlationId, String hint) {
    return createMtMessage(CLIENT_ID, hint, EnqueueingType.NORMAL, correlationId, DataUtil.VPI);
  }

  public static MtMessage createMtMessage(EnqueueingType enqueueingType, CorrelationId correlationId) {
    return createMtMessage(CLIENT_ID, SCHEDULE_HINT, enqueueingType, correlationId, DataUtil.VPI);
  }

  public static MtMessage createMtMessage(String clientId, EnqueueingType enqueueingType, CorrelationId correlationId) {
    return createMtMessage(clientId, SCHEDULE_HINT, enqueueingType, correlationId, DataUtil.VPI);
  }

  public static MtMessage createMtMessage(String clientId, String hint, EnqueueingType enqueueingType, CorrelationId correlationId, Vpi vpi) {
    MtMessage mtMessage = new MtMessage();

    mtMessage.setClientId(clientId);
    mtMessage.setMtStatusReplyOption(createMtStatusReplyOption(correlationId));
    mtMessage.setPayload(PAYLOAD.toByteArray());
    mtMessage.setSchedulerOption(createSchedulerOption(hint, enqueueingType));
    mtMessage.setSrpOption(createSrpOption());
    mtMessage.setVehiclePlatformId(vpi.toString());

    return mtMessage;
  }

  public static TgwNotifyEvent createWifiStatusChangeTgwNotifyEvent(CapabilityState capabilityState) {
    TgwNotifyEvent tgwNotifyEvent = createTgwNotifyEvent();
    tgwNotifyEvent.setConnectivityCapability(createWifiConnectivityCapability(capabilityState));

    return tgwNotifyEvent;
  }

  public static void verifyActiveMtMessagesDeletedFromDb(ActiveMtMessageReaderFactory activeMtMessageReaderFactory) {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      Awaitility.await().atMost(Duration.ofSeconds(5)).until(() -> activeMtMessageReader.countActiveMtMessages() == 0);
    }
  }

  public static void verifyDatabaseIsEmpty(DeviceInfoReaderFactory deviceInfoReaderFactory) {
    try (DeviceInfoReader deviceInfoReader = deviceInfoReaderFactory.create()) {
      Assertions.assertEquals(0, deviceInfoReader.countAllDevices());
    }
  }

  public static void verifyMtStatus(CorrelationId expectedCorrelationId, MtStatus expectedMtStatus, MtStatusMessage receivedMtStatusMessage) {
    Assertions.assertEquals(expectedCorrelationId.toString(), receivedMtStatusMessage.getCorrelationId());
    Assertions.assertEquals(DataUtil.HANDLE.toString(), receivedMtStatusMessage.getHandle());
    Assertions.assertEquals(expectedMtStatus.name(), receivedMtStatusMessage.getStatus());
    Assertions.assertEquals(DataUtil.VPI.toString(), receivedMtStatusMessage.getVehiclePlatformId());
  }

  public static <L, R> void verifyPairBothLeftAndRightNotEquals(Pair<L, R> pair1, Pair<L, R> pair2) {
    Assertions.assertNotEquals(pair1.getLeft(), pair2.getLeft());
    Assertions.assertNotEquals(pair1.getRight(), pair2.getRight());
  }

  public static <L, R> void verifyPairOnlyRightEquals(Pair<L, R> pair1, Pair<L, R> pair2) {
    Assertions.assertNotEquals(pair1.getLeft(), pair2.getLeft());
    Assertions.assertEquals(pair1.getRight(), pair2.getRight());
  }

  public static void verifyWifiDisabled(ActiveMtMessageReaderFactory activeMtMessageReaderFactory) {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      Awaitility.await().atMost(Duration.ofSeconds(5)).until(() -> !activeMtMessageReader.findAssetCapabilityState(DataUtil.VPI).get().isEnabled());
    }
  }

  public static void verifyWifiEnabled(ActiveMtMessageReaderFactory activeMtMessageReaderFactory) {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      Awaitility.await().atMost(Duration.ofSeconds(5)).until(() -> activeMtMessageReader.findAssetCapabilityState(DataUtil.VPI).get().isEnabled());
    }
  }

  private static void createAndPersistDevice(ConfigurableApplicationContext configurableApplicationContext, DeviceInfo deviceInfo) {
    DeviceInfoWriterFactory deviceInfoWriterFactory = configurableApplicationContext.getBean(DeviceInfoWriterFactory.class);
    DeviceServiceCacheManager deviceServiceCacheManager = configurableApplicationContext.getBean(DeviceServiceCacheManager.class);

    try (DeviceInfoWriter deviceInfoWriter = deviceInfoWriterFactory.createReadCommitted()) {
      Either<DatabaseError, DeviceInfoId> either = deviceInfoWriter.insertDeviceInfo(deviceInfo);
      Assertions.assertTrue(either.isRight(), () -> either.getLeft().toString());

      deviceServiceCacheManager.invalidateCache(Set.of(deviceInfo.getVpi()));
    }
  }

  private static DeviceInfoBuilder createDeviceInfoBuilder(com.volvo.tisp.tgw.device.info.database.model.SrpLevel srpLevel) {
    return new DeviceInfoBuilder()
        .setHandle(HANDLE)
        .setObsAlias(OBS_ALIAS)
        .setSimInfo(createSimInfo())
        .setSrpLevel(srpLevel)
        .setVpi(DataUtil.VPI)
        .setWtpVersion(WtpVersion.VERSION_1);
  }

  private static MtStatusReplyOption createMtStatusReplyOption(CorrelationId correlationId) {
    MtStatusReplyOption mtStatusReplyOption = new MtStatusReplyOption();

    mtStatusReplyOption.setCorrelationId(correlationId.toString());
    mtStatusReplyOption.setReplyDestination(REPLY_TO.toString());

    return mtStatusReplyOption;
  }

  private static SchedulerOption createSchedulerOption(String hint, EnqueueingType enqueueingType) {
    SchedulerOption schedulerOption = new SchedulerOption();

    schedulerOption.setEnqueueingType(enqueueingType.name());
    schedulerOption.setHint(hint);

    return schedulerOption;
  }

  private static SimInfo createSimInfo() {
    return new SimInfoBuilder()
        .setIpv4Address(IPV4_ADDRESS)
        .setIpv4Port(IPV4_PORT)
        .setMsisdn(MSISDN)
        .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
        .build();
  }

  private static DeviceInfo createSrp10DeviceInfo() {
    return createDeviceInfoBuilder(com.volvo.tisp.tgw.device.info.database.model.SrpLevel.SRP_10).build();
  }

  private static DeviceInfo createSrp11DeviceInfo() {
    return createDeviceInfoBuilder(com.volvo.tisp.tgw.device.info.database.model.SrpLevel.SRP_11)
        .setCryptoKeyInfo(Optional.of(new CryptoKeyInfo(AES_KEY, "someCryptoKeyId")))
        .build();
  }

  private static SrpOption createSrpOption() {
    SrpOption srpOption = new SrpOption();

    srpOption.setDstService(DESTINATION_SERVICE.toInt());
    srpOption.setDstVersion(DESTINATION_VERSION.toShort());
    srpOption.setPriority((int) PRIORITY.toShort());
    srpOption.setSrpLevel(SrpLevel.SRP_10);

    return srpOption;
  }

  private static TgwNotifyEvent createTgwNotifyEvent() {
    TgwNotifyEvent tgwNotifyEvent = new TgwNotifyEvent();
    TgwAsset tgwAsset = new TgwAsset();
    tgwAsset.setVpi(VPI.toString());
    tgwNotifyEvent.setAsset(tgwAsset);
    return tgwNotifyEvent;
  }

  private static ConnectivityCapability createWifiConnectivityCapability(CapabilityState capabilityState) {
    ConnectivityCapability connectivityCapability = new ConnectivityCapability();

    Wifi wifi = new Wifi();
    wifi.setState(capabilityState);
    connectivityCapability.setWifi(wifi);
    return connectivityCapability;
  }

  private static AesKey generateRandomAesKey() {
    byte[] bytes = new byte[AesKey.AES_KEY_LENGTH_IN_BYTES];
    new SecureRandom().nextBytes(bytes);
    return AesKey.create(ImmutableByteArray.of(bytes));
  }

  private static void verifyDeviceInfo(DeviceInfo deviceInfo) {
    Assertions.assertTrue(deviceInfo.getCryptoKeyInfo().isEmpty());
    Assertions.assertEquals(HANDLE, deviceInfo.getHandle());
    Assertions.assertEquals(OBS_ALIAS, deviceInfo.getObsAlias());
    Assertions.assertTrue(deviceInfo.getPendingCryptoKeyInfo().isEmpty());
    Assertions.assertEquals(com.volvo.tisp.tgw.device.info.database.model.SrpLevel.SRP_10, deviceInfo.getSrpLevel());
    Assertions.assertTrue(deviceInfo.getTelematicUnitSubType().isEmpty());
    Assertions.assertEquals(com.volvo.tisp.tgw.device.info.database.model.WtpVersion.VERSION_1, deviceInfo.getWtpVersion());

    verifySimInfo(deviceInfo.getSimInfo());
  }

  private static void verifySimInfo(SimInfo simInfo) {
    Assertions.assertEquals(IPV4_ADDRESS, simInfo.getIpv4Address());
    Assertions.assertEquals(IPV4_PORT, simInfo.getIpv4Port());
    Assertions.assertEquals(MSISDN, simInfo.getMsisdn());
    Assertions.assertEquals(MOBILE_NETWORK_OPERATOR, simInfo.getMobileNetworkOperator());
  }
}
