package com.volvo.tisp.tgwmts.integration.tests;

import java.io.IOException;
import java.security.NoSuchAlgorithmException;
import java.util.List;
import java.util.Optional;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestClientException;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReader;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoWriter;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoWriterFactory;
import com.volvo.tisp.tgw.device.info.database.model.DatabaseError;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfoBuilder;
import com.volvo.tisp.tgw.device.info.database.model.DeviceInfoId;
import com.volvo.tisp.tgw.device.info.database.model.Handle;
import com.volvo.tisp.tgw.device.info.database.model.Ipv4Port;
import com.volvo.tisp.tgw.device.info.database.model.PersistedDeviceInfo;
import com.volvo.tisp.tgw.device.info.database.model.SimInfo;
import com.volvo.tisp.tgw.device.info.database.model.SimInfoBuilder;
import com.volvo.tisp.tgw.device.info.database.model.SrpLevel;
import com.volvo.tisp.tgw.device.info.database.model.WtpVersion;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.ProtobufUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.RestUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.tisp.vc.device.bulk.sync.model.BulkSyncFailureReason;
import com.volvo.tisp.vc.device.bulk.sync.model.BulkSyncResult;
import com.volvo.tisp.vc.device.bulk.sync.model.BulkSyncResultStatus;
import com.volvo.tisp.vc.device.bulk.sync.model.Region;
import com.volvo.tisp.vc.main.utils.lib.type.Either;

class BulkSyncRestControllerIntegrationTest {
  private static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("*******");
  private static final Region REGION = Region.EMEA;
  private static final Vpi VPI = Vpi.ofString("22222222222222222222222222222222");

  private static DeviceInfo createDeviceInfo(Vpi vpi, SimInfo simInfo, Handle handle, ObsAlias obsAlias) {
    return new DeviceInfoBuilder()
        .setHandle(handle)
        .setObsAlias(obsAlias)
        .setSimInfo(simInfo)
        .setSrpLevel(SrpLevel.SRP_10)
        .setVpi(vpi)
        .setWtpVersion(WtpVersion.VERSION_1)
        .build();
  }

  private static void insertFirstDeviceInfo(DeviceInfoWriter deviceInfoWriter) {
    SimInfo simInfo = new SimInfoBuilder()
        .setIpv4Port(DataUtil.IPV4_PORT)
        .setIpv4Address(DataUtil.IPV4_ADDRESS)
        .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR)
        .setMsisdn(DataUtil.MSISDN)
        .build();
    DeviceInfo deviceInfo = createDeviceInfo(DataUtil.VPI, simInfo, DataUtil.HANDLE, DataUtil.OBS_ALIAS);
    Either<DatabaseError, DeviceInfoId> deviceInfoIdEither = deviceInfoWriter.insertDeviceInfo(deviceInfo);
    Assertions.assertTrue(deviceInfoIdEither.isRight());
  }

  private static void insertSecondDeviceInfo(DeviceInfoWriter deviceInfoWriter) {
    SimInfo simInfo = new SimInfoBuilder()
        .setIpv4Port(Ipv4Port.ofInt(43_000))
        .setIpv4Address(IPV4_ADDRESS)
        .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR)
        .setMsisdn(Msisdn.ofString("+461234567892"))
        .build();
    DeviceInfo deviceInfo = createDeviceInfo(VPI, simInfo, Handle.ofLong(456), ObsAlias.ofLong(32));
    Either<DatabaseError, DeviceInfoId> deviceInfoIdEither = deviceInfoWriter.insertDeviceInfo(deviceInfo);
    Assertions.assertTrue(deviceInfoIdEither.isRight());
  }

  private static void populateDatabaseWithDummyDeviceInfos(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    try (DeviceInfoWriter deviceInfoWriter = deviceInfoWriterFactory.createReadCommitted()) {
      deviceInfoWriter.startTransaction();
      Assertions.assertEquals(0, deviceInfoWriter.countAllDevices());

      insertFirstDeviceInfo(deviceInfoWriter);
      insertSecondDeviceInfo(deviceInfoWriter);

      deviceInfoWriter.commitTransaction();
      Assertions.assertEquals(2, deviceInfoWriter.countAllDevices());
    }
  }

  private static void verifyChangesInDatabase(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    try (DeviceInfoReader deviceInfoReader = deviceInfoWriterFactory.createReadCommitted()) {
      verifyDeviceInfoExists(deviceInfoReader, DataUtil.VPI, DataUtil.IPV4_ADDRESS);
      List<DeviceInfo> deviceInfos = deviceInfoReader.findAllDevices().map(PersistedDeviceInfo::getDeviceInfo).toList();
      Assertions.assertEquals(1, deviceInfos.size());

      DeviceInfo deviceInfo = deviceInfos.get(0);
      Assertions.assertTrue(deviceInfo.isSatEnabled());
      Assertions.assertEquals(DataUtil.VPI, deviceInfo.getVpi());
      Assertions.assertEquals(DataUtil.IPV4_ADDRESS, deviceInfo.getSimInfo().getIpv4Address());
    }
  }

  private static void verifyDeviceInfoExists(DeviceInfoReader deviceInfoReader, Vpi vpi, Ipv4Address ipv4Address) {
    Optional<PersistedDeviceInfo> optionalPersistedDeviceInfo = deviceInfoReader.findDeviceInfoByVpi(vpi);

    DeviceInfo deviceInfoId = optionalPersistedDeviceInfo.get().getDeviceInfo();
    Assertions.assertEquals(vpi, deviceInfoId.getVpi());
    Assertions.assertEquals(ipv4Address, deviceInfoId.getSimInfo().getIpv4Address());
  }

  private static void verifyFailureWithDuplicateDeviceInfoInPayload(WireMockServerWrapper wireMockServerWrapper,
      DeviceInfoWriterFactory deviceInfoWriterFactory) throws IOException, NoSuchAlgorithmException {
    byte[] payload = ProtobufUtil.createDuplicateVehicleInfoProtobuf(DataUtil.IPV4_ADDRESS, DataUtil.VPI);

    IntegrationTestHelper.stubConRepo2(wireMockServerWrapper, payload, Optional.of(REGION), 200);
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(REGION, false);

    verifyResponseEntity(responseEntity, HttpStatus.INTERNAL_SERVER_ERROR, BulkSyncFailureReason.DUPLICATE_KEY.toString());
    verifyNoChangeInDatabase(deviceInfoWriterFactory);
  }

  private static void verifyFailureWithEmptyPayload(WireMockServerWrapper wireMockServerWrapper, DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws NoSuchAlgorithmException, IOException {
    byte[] payload = ProtobufUtil.createEmptyVehicleInfoProtobuf();
    IntegrationTestHelper.stubConRepo2(wireMockServerWrapper, payload, Optional.of(REGION), 200);
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(REGION, false);

    verifyResponseEntity(responseEntity, HttpStatus.INTERNAL_SERVER_ERROR, "the conrepo2 response contained no deviceInfo");
    verifyNoChangeInDatabase(deviceInfoWriterFactory);
  }

  private static void verifyFailureWithInvalidProtobufPayload(WireMockServerWrapper wireMockServerWrapper, DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws RestClientException, NoSuchAlgorithmException, IOException {
    byte[] payload = new byte[] {1, 2, 3};
    IntegrationTestHelper.stubConRepo2(wireMockServerWrapper, payload, Optional.of(REGION), 200);
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(REGION, false);

    verifyResponseEntity(responseEntity, HttpStatus.INTERNAL_SERVER_ERROR, BulkSyncFailureReason.CONVERSION_ERROR.toString());
    verifyNoChangeInDatabase(deviceInfoWriterFactory);
  }

  private static void verifyFailureWithInvalidRegionInputPayload(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(Optional.of("foo"), false);
    verifyResponseEntity(responseEntity, HttpStatus.BAD_REQUEST, BulkSyncFailureReason.INVALID_REGION.toString());
    verifyNoChangeInDatabase(deviceInfoWriterFactory);
  }

  private static void verifyNoChangeInDatabase(DeviceInfoWriterFactory deviceInfoWriterFactory) {
    try (DeviceInfoReader deviceInfoReader = deviceInfoWriterFactory.createReadCommitted()) {
      verifyDeviceInfoExists(deviceInfoReader, DataUtil.VPI, DataUtil.IPV4_ADDRESS);
      verifyDeviceInfoExists(deviceInfoReader, VPI, IPV4_ADDRESS);
      Assertions.assertEquals(2, deviceInfoReader.countAllDevices());
    }
  }

  private static void verifyNoSuccessResponseFromConRepo2(WireMockServerWrapper wireMockServerWrapper, DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws IOException, NoSuchAlgorithmException {
    byte[] payload = ProtobufUtil.createVehicleInfoProtobuf(DataUtil.IPV4_ADDRESS, DataUtil.VPI);
    IntegrationTestHelper.stubConRepo2(wireMockServerWrapper, payload, Optional.of(REGION), 500);
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(REGION, false);

    verifyResponseEntity(responseEntity, HttpStatus.INTERNAL_SERVER_ERROR, BulkSyncFailureReason.BAD_RESPONSE.toString());
    verifyNoChangeInDatabase(deviceInfoWriterFactory);
  }

  private static void verifyResponseEntity(ResponseEntity<String> responseEntity, HttpStatus httpStatusToVerify, String responseToVerify) {
    Assertions.assertEquals(httpStatusToVerify, responseEntity.getStatusCode());
    Assertions.assertTrue(responseEntity.getBody().contains(responseToVerify), responseEntity::getBody);
  }

  private static void verifySuccess(WireMockServerWrapper wireMockServerWrapper, DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws IOException, NoSuchAlgorithmException {
    byte[] payload = ProtobufUtil.createVehicleInfoProtobuf(DataUtil.IPV4_ADDRESS, DataUtil.VPI, "satelliteId");
    IntegrationTestHelper.stubConRepo2(wireMockServerWrapper, payload, Optional.of(REGION), 200);
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(REGION, false);

    BulkSyncResult bulkSyncResult = new BulkSyncResult(BulkSyncResultStatus.COMMIT, 1);
    verifyResponseEntity(responseEntity, HttpStatus.OK, bulkSyncResult.toString());

    verifyChangesInDatabase(deviceInfoWriterFactory);
  }

  private static void verifySuccessRollbackWithDryRun(WireMockServerWrapper wireMockServerWrapper, DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws IOException, NoSuchAlgorithmException {
    byte[] payload = ProtobufUtil.createVehicleInfoProtobuf(DataUtil.IPV4_ADDRESS, DataUtil.VPI);
    IntegrationTestHelper.stubConRepo2(wireMockServerWrapper, payload, Optional.of(REGION), 200);
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(REGION, true);

    BulkSyncResult bulkSyncResult = new BulkSyncResult(BulkSyncResultStatus.DRY_RUN_SUCCESS, 1);
    verifyResponseEntity(responseEntity, HttpStatus.OK, bulkSyncResult.toString());

    verifyNoChangeInDatabase(deviceInfoWriterFactory);
  }

  private static void verifySuccessRollbackWithDryRunWithoutRegion(WireMockServerWrapper wireMockServerWrapper, DeviceInfoWriterFactory deviceInfoWriterFactory)
      throws IOException, NoSuchAlgorithmException {
    byte[] payload = ProtobufUtil.createVehicleInfoProtobuf(DataUtil.IPV4_ADDRESS, DataUtil.VPI);
    IntegrationTestHelper.stubConRepo2(wireMockServerWrapper, payload, Optional.empty(), 200);
    ResponseEntity<String> responseEntity = RestUtil.startBulkSync(Optional.empty(), true);

    BulkSyncResult bulkSyncResult = new BulkSyncResult(BulkSyncResultStatus.DRY_RUN_SUCCESS, 1);
    verifyResponseEntity(responseEntity, HttpStatus.OK, bulkSyncResult.toString());

    verifyNoChangeInDatabase(deviceInfoWriterFactory);
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        DeviceInfoWriterFactory deviceInfoWriterFactory = configurableApplicationContext.getBean(DeviceInfoWriterFactory.class);
        populateDatabaseWithDummyDeviceInfos(deviceInfoWriterFactory);

        verifyNoSuccessResponseFromConRepo2(wireMockServerWrapper, deviceInfoWriterFactory);

        verifyFailureWithInvalidRegionInputPayload(deviceInfoWriterFactory);

        verifyFailureWithDuplicateDeviceInfoInPayload(wireMockServerWrapper, deviceInfoWriterFactory);

        verifyFailureWithInvalidProtobufPayload(wireMockServerWrapper, deviceInfoWriterFactory);

        verifyFailureWithEmptyPayload(wireMockServerWrapper, deviceInfoWriterFactory);

        verifySuccessRollbackWithDryRun(wireMockServerWrapper, deviceInfoWriterFactory);

        verifySuccessRollbackWithDryRunWithoutRegion(wireMockServerWrapper, deviceInfoWriterFactory);

        verifySuccess(wireMockServerWrapper, deviceInfoWriterFactory);
      }
    }
  }
}
