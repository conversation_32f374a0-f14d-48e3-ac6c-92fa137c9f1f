package com.volvo.tisp.tgwmts.integration.tests.crypto;

import java.nio.file.Paths;

import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.vc.crypto.asymmetric.encryption.rsa.RsaEncryptionServiceImpl;
import com.volvo.vc.crypto.asymmetric.key.AsymmetricKeyRepository;
import com.volvo.vc.crypto.asymmetric.key.AsymmetricPrivateKey;
import com.volvo.vc.crypto.asymmetric.key.AsymmetricPublicKey;
import com.volvo.vc.crypto.asymmetric.key.rsa.RsaKeyRepositoryImpl;
import com.volvo.vc.crypto.asymmetric.signature.Signature;
import com.volvo.vc.crypto.asymmetric.signature.rsa.RsaSigningService;
import com.volvo.vc.crypto.common.entity.EncryptedAesKey;
import com.volvo.vc.crypto.common.entity.EncryptedPayload;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.common.keystore.KeyAlias;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepository;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepositoryConfig;
import com.volvo.vc.crypto.common.keystore.impl.KeyStoreRepositoryImpl;
import com.volvo.vc.crypto.common.keystore.password.Password;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;

public final class CryptoUtil {
  private CryptoUtil() {
    throw new IllegalStateException();
  }

  public static EncryptedAesKey encrypSymmetricKey(AsymmetricPublicKey asymmetricPublicKey, SymmetricKey symmetricKey) {
    return RsaEncryptionServiceImpl.INSTANCE.encrypt(asymmetricPublicKey, PlainTextPayload.create(symmetricKey.getImmutableByteArray()))
        .applyRight(encryptedPayload -> EncryptedAesKey.create(encryptedPayload.getImmutableByteArray()))
        .orElseThrowLeft(e -> e);
  }

  public static Either<RuntimeException, AsymmetricPrivateKey> getPrivateKey(KeyAlias keyAlias) {
    return createAsymmetricKeyRepository().getPrivateKey(keyAlias);
  }

  public static Either<RuntimeException, AsymmetricPublicKey> getPublicKey(KeyAlias keyAlias) {
    return createAsymmetricKeyRepository().getPublicKey(keyAlias);
  }

  public static Signature sign(AsymmetricPrivateKey asymmetricPrivateKey, EncryptedAesKey encryptedAesKey) {
    return RsaSigningService.INSTANCE.sign(EncryptedPayload.create(encryptedAesKey.getImmutableByteArray()), asymmetricPrivateKey).orElseThrowLeft(e -> e);
  }

  private static AsymmetricKeyRepository createAsymmetricKeyRepository() {
    KeyStoreRepositoryConfig keyStoreRepositoryConfig = new KeyStoreRepositoryConfig(Password.ofString("tcetisp"),
        Paths.get("src/test/resources/keystore.pkcs12"));

    KeyStoreRepository keyStoreRepository = KeyStoreRepositoryImpl.createKeyStoreRepository(keyStoreRepositoryConfig);

    return RsaKeyRepositoryImpl.create(keyStoreRepository);
  }
}
