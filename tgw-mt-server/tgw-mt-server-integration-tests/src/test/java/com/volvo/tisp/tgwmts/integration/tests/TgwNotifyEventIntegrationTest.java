package com.volvo.tisp.tgwmts.integration.tests;

import java.util.function.Consumer;

import org.jdbi.v3.core.Handle;
import org.jdbi.v3.core.Jdbi;
import org.jdbi.v3.core.statement.Update;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.tgw.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.JmsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.MtMessageTestsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.RestUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

class TgwNotifyEventIntegrationTest {
  public static void verifyOverriddenMtStatusMessage(MtStatusMessage mtStatusMessage, CorrelationId correlationId) {
    Assertions.assertEquals(correlationId.toString(), mtStatusMessage.getCorrelationId());
    Assertions.assertEquals(DataUtil.VPI.toString(), mtStatusMessage.getVehiclePlatformId());
    Assertions.assertEquals("OVERRIDDEN", mtStatusMessage.getStatus());
    Assertions.assertEquals(DataUtil.HANDLE.toString(), mtStatusMessage.getHandle());
  }

  public static void verifySatMtStatusMessage(MtStatusMessage mtStatusMessage, String expectedStatus) {
    Assertions.assertEquals(MtMessageTestsUtil.CORRELATION_ID.toString(), mtStatusMessage.getCorrelationId());
    Assertions.assertEquals(DataUtil.VPI.toString(), mtStatusMessage.getVehiclePlatformId());
    Assertions.assertEquals(expectedStatus, mtStatusMessage.getStatus());
    Assertions.assertEquals(DataUtil.HANDLE.toString(), mtStatusMessage.getHandle());
  }

  private static void enableSat(ConfigurableApplicationContext configurableApplicationContext) {
    runDbTransactionAndInvalidateCache(configurableApplicationContext, jdbi -> {
      try (Handle handle = jdbi.open()) {
        try (Update update = handle.createUpdate("UPDATE device_info SET sat_enabled = :satEnabled WHERE vpi = :vpi")) {
          int numberOfUpdatedRows = update.bind("satEnabled", true).bind("vpi", DataUtil.VPI.toString()).execute();
          Assertions.assertEquals(1, numberOfUpdatedRows);
        }
      }
    });
  }

  private static void enableWifi(ConfigurableApplicationContext configurableApplicationContext) {
    runDbTransactionAndInvalidateCache(configurableApplicationContext, jdbi -> {
      try (Handle handle = jdbi.open()) {
        try (Update update = handle.createUpdate("UPDATE device_info SET wifi_enabled = :wifiEnabled WHERE vpi = :vpi")) {
          int numberOfUpdatedRows = update.bind("wifiEnabled", true).bind("vpi", DataUtil.VPI.toString()).execute();
          Assertions.assertEquals(1, numberOfUpdatedRows);
        }
      }
    });
  }

  private static void runDbTransactionAndInvalidateCache(ConfigurableApplicationContext configurableApplicationContext, Consumer<Jdbi> consumer) {
    Jdbi jdbi = configurableApplicationContext.getBean(Jdbi.class);
    DeviceServiceCacheManager deviceServiceCacheManager = configurableApplicationContext.getBean(DeviceServiceCacheManager.class);
    consumer.accept(jdbi);
    deviceServiceCacheManager.invalidateAllCache();
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);
      IntegrationTestHelper.stubMtRouterServer(wireMockServerWrapper);
      IntegrationTestHelper.stubSubRepo();

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        ActiveMtMessageReaderFactory activeMtMessageReaderFactory = configurableApplicationContext.getBean(ActiveMtMessageReaderFactory.class);

        DataUtil.createAndPersistSrp11Device(configurableApplicationContext);

        JmsUtil.sendMtMessage(DataUtil.createMtMessage(MtMessageTestsUtil.CORRELATION_ID, "low"));
        RestUtil.receiveMtMessageAndReplyWithMtAck(wireMockServerWrapper.getWireMockServer(), Status.DELIVERED, 1, Transport.UDP);

        JmsUtil.sendWifiEnabledTgwNotifyEvent();
        DataUtil.verifyWifiEnabled(activeMtMessageReaderFactory);

        JmsUtil.sendWifiDisabledTgwNotifyEvent();
        DataUtil.verifyWifiDisabled(activeMtMessageReaderFactory);
      }
    }
  }
}
