package com.volvo.tisp.tgwmts.integration.tests.util;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import org.testcontainers.containers.localstack.LocalStackContainer;
import org.testcontainers.utility.DockerImageName;

import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.sqs.SqsAsyncClient;
import software.amazon.awssdk.services.sqs.SqsClient;
import software.amazon.awssdk.services.sqs.model.CreateQueueRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueAttributesRequest;
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest;
import software.amazon.awssdk.services.sqs.model.QueueAttributeName;

public class SqsUtil {

  public static final String QUALITY_CAR_MONITORING_QUEUE = "quality-card-monitoring";
  public static final Region REGION = Region.US_EAST_1;

  private SqsUtil() {
    throw new IllegalStateException();
  }

  public static long getNumOfMessagesInQueue(String queueName, LocalStackContainer sqsLocalStackContainer) {
    return Long.parseLong(getSqsClient(sqsLocalStackContainer).getQueueAttributes(GetQueueAttributesRequest.builder()
        .queueUrl(getQueueUrl(queueName, sqsLocalStackContainer))
        .attributeNames(QueueAttributeName.APPROXIMATE_NUMBER_OF_MESSAGES)
        .build()).attributes().get(QueueAttributeName.APPROXIMATE_NUMBER_OF_MESSAGES));
  }

  public static String getQueueUrl(String queueName, LocalStackContainer localStackContainer) {
    return getSqsClient(localStackContainer).getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl();
  }

  public static void setupQueues(LocalStackContainer sqsLocalStackContainer) throws ExecutionException, InterruptedException {
    createQueue(QUALITY_CAR_MONITORING_QUEUE, sqsLocalStackContainer);
  }

  public static LocalStackContainer startSQSLocalStackContainer() {
    System.setProperty("aws.accessKeyId", "foo");
    System.setProperty("aws.secretAccessKey", "bar");
    System.setProperty("aws.region", REGION.toString());
    LocalStackContainer sqsLocalStackContainer = new LocalStackContainer(DockerImageName.parse("localstack/localstack:latest")).withServices(
        LocalStackContainer.Service.SQS);
    sqsLocalStackContainer.start();
    return sqsLocalStackContainer;
  }

  private static CreateQueueRequest createCreateQueueRequest(String queueName) {
    return CreateQueueRequest.builder().queueName(queueName).build();
  }

  private static void createQueue(String queueName, LocalStackContainer sqsLocalStackContainer) throws ExecutionException, InterruptedException {
    getSqsAsyncClient(sqsLocalStackContainer)
        .createQueue(createCreateQueueRequest(queueName))
        .thenCompose(createQueueResponse -> CompletableFuture.completedFuture(createQueueResponse.queueUrl()))
        .get();
  }

  private static SqsAsyncClient getSqsAsyncClient(LocalStackContainer sqsLocalStackContainer) {
    return SqsAsyncClient.builder()
        .region(Region.of(sqsLocalStackContainer.getRegion()))
        .httpClientBuilder(NettyNioAsyncHttpClient.builder())
        .endpointOverride(sqsLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS))
        .build();
  }

  private static SqsClient getSqsClient(LocalStackContainer sqsLocalStackContainer) {
    return SqsClient.builder()
        .region(Region.of(sqsLocalStackContainer.getRegion()))
        .endpointOverride(sqsLocalStackContainer.getEndpointOverride(LocalStackContainer.Service.SQS))
        .build();
  }
}
