package com.volvo.tisp.tgwmts.integration.tests;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.JmsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.vc.conrepo.api.v1.ActivationMessageType;
import com.volvo.vc.conrepo.api.v1.ActivationNotifyEvent;
import com.volvo.vc.conrepo.api.v1.ActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v1.ChangeStatus;
import com.volvo.vc.conrepo.api.v1.DeviceDetailedEntry;
import com.volvo.vc.conrepo.api.v1.DeviceSim;
import com.volvo.vc.conrepo.api.v1.NotifyResponseMessage;
import com.volvo.vc.conrepo.api.v1.ResponseStatus;
import com.volvo.vc.conrepo.api.v1.SrpLevel;
import com.volvo.vc.conrepo.api.v1.State;
import com.volvo.vc.conrepo.api.v1.WtpProtocolVersion;

class ActivationNotifyEventMessageV1IntegrationTest {
  private static final String REFERENCE_ID = "someReferenceId";

  private static ActivationNotifyEvent createActivationNotifyEvent(ChangeStatus changeStatus, State state) {
    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();

    activationNotifyEvent.setChangeStatus(changeStatus);
    activationNotifyEvent.setDeviceDetail(createDeviceDetailedEntry(state));

    return activationNotifyEvent;
  }

  private static ActivationNotifyEventMessage createActivationNotifyEventMessage(ActivationMessageType activationMessageType, ChangeStatus changeStatus,
      State state) {
    ActivationNotifyEventMessage activationNotifyEventMessage = new ActivationNotifyEventMessage();

    activationNotifyEventMessage.getActivationNotifyEvents().add(createActivationNotifyEvent(changeStatus, state));
    activationNotifyEventMessage.setMessageType(activationMessageType);
    activationNotifyEventMessage.setReferenceId(REFERENCE_ID);
    activationNotifyEventMessage.setReplyTo("LOCAL.LOCAL.LOCAL.ACTIVATION.NOTIFY.RESPONSE.IN");

    return activationNotifyEventMessage;
  }

  private static DeviceDetailedEntry createDeviceDetailedEntry(State state) {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setHandle(DataUtil.HANDLE.toString());
    deviceDetailedEntry.setObsAlias(DataUtil.OBS_ALIAS.toLong());
    deviceDetailedEntry.setSimEntry(createDeviceSim());
    deviceDetailedEntry.setSrpLevel(SrpLevel.SRP_10);
    deviceDetailedEntry.setState(state);
    deviceDetailedEntry.setVehiclePlatformId(DataUtil.VPI.toString());
    deviceDetailedEntry.setWtpProtocolVersion(WtpProtocolVersion.VERSION_1);

    return deviceDetailedEntry;
  }

  private static DeviceSim createDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setImsi(DataUtil.IMSI.toString());
    deviceSim.setIp(DataUtil.IPV4_ADDRESS.toString());
    deviceSim.setMsisdn(DataUtil.MSISDN.toString());
    deviceSim.setOperator(DataUtil.MOBILE_NETWORK_OPERATOR.toString());
    deviceSim.setPort(DataUtil.IPV4_PORT.toInt());

    return deviceSim;
  }

  private static ActivationNotifyEventMessage createMinimalActivationNotifyEventMessage() {
    ActivationNotifyEventMessage activationNotifyEventMessage = createActivationNotifyEventMessage(ActivationMessageType.DEACTIVATION_NOTIFY,
        ChangeStatus.DELETED, State.DEACTIVATED);
    activationNotifyEventMessage.getActivationNotifyEvents().get(0).getDeviceDetail().setSimEntry(null);
    return activationNotifyEventMessage;
  }

  private static void verifyNotifyResponseMessage(NotifyResponseMessage notifyResponseMessage, ActivationMessageType expectedActivationMessageType,
      ResponseStatus expectedResponseStatus, String expectedDescription) {
    Assertions.assertEquals(expectedActivationMessageType, notifyResponseMessage.getMessageType());
    Assertions.assertEquals(REFERENCE_ID, notifyResponseMessage.getReferenceId());
    Assertions.assertEquals(expectedResponseStatus, notifyResponseMessage.getResponseStatus());
    Assertions.assertEquals(expectedDescription, notifyResponseMessage.getDescription());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        verifyNotifyResponseMessage(JmsUtil.publishActivationNotifyEventMessageV1AndReceiveResponse(
                createActivationNotifyEventMessage(ActivationMessageType.DEACTIVATION_NOTIFY, ChangeStatus.DELETED, State.DEACTIVATED)).get(),
            ActivationMessageType.DEACTIVATION_NOTIFY, ResponseStatus.SUCCESS, "Success");

        verifyNotifyResponseMessage(JmsUtil.publishActivationNotifyEventMessageV1AndReceiveResponse(
                createMinimalActivationNotifyEventMessage()).get(),
            ActivationMessageType.DEACTIVATION_NOTIFY, ResponseStatus.SUCCESS, "Success");

        DeviceInfoReaderFactory deviceInfoReaderFactory = configurableApplicationContext.getBean(DeviceInfoReaderFactory.class);
        DataUtil.verifyDatabaseIsEmpty(deviceInfoReaderFactory);

        verifyNotifyResponseMessage(JmsUtil.publishActivationNotifyEventMessageV1AndReceiveResponse(
                createActivationNotifyEventMessage(ActivationMessageType.NOTIFY, ChangeStatus.UPDATED, State.ACTIVATED)).get(),
            ActivationMessageType.NOTIFY, ResponseStatus.SUCCESS, "Success");

        DataUtil.checkDeviceInDatabase(deviceInfoReaderFactory);

        verifyNotifyResponseMessage(JmsUtil.publishActivationNotifyEventMessageV1AndReceiveResponse(
                createActivationNotifyEventMessage(ActivationMessageType.DEACTIVATION_NOTIFY, ChangeStatus.DELETED, State.DEACTIVATED)).get(),
            ActivationMessageType.DEACTIVATION_NOTIFY, ResponseStatus.SUCCESS, "Success");

        DataUtil.verifyDatabaseIsEmpty(deviceInfoReaderFactory);
      }
    }
  }
}
