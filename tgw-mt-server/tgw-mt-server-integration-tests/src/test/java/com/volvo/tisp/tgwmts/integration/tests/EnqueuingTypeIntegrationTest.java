package com.volvo.tisp.tgwmts.integration.tests;

import org.apache.commons.lang3.tuple.Pair;
import org.jdbi.v3.core.Jdbi;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgw.device.info.cache.core.impl.DeviceServiceCacheManager;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.MtMessageTestsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;

class EnqueuingTypeIntegrationTest {
  private static JoinedMtMessage getJoinedMtMessageForClient(ActiveMtMessageReader activeMtMessageReader, String clientId) {
    return activeMtMessageReader.findMtMessageAndActiveMtMessages(DataUtil.VPI).stream()
        .filter(joinedMtMessage -> joinedMtMessage.persistedMtMessage().getMtMessage().getQueueId().toString().equals(clientId))
        .findFirst()
        .orElseThrow();
  }

  private static void truncateMessageTablesAndInvalidateCache(ConfigurableApplicationContext configurableApplicationContext) {
    Jdbi jdbi = configurableApplicationContext.getBean(Jdbi.class);
    DeviceServiceCacheManager deviceServiceCacheManager = configurableApplicationContext.getBean(DeviceServiceCacheManager.class);

    jdbi.useHandle(handle -> handle.execute("TRUNCATE TABLE vehicle_lock CASCADE"));
    deviceServiceCacheManager.invalidateAllCache();
  }

  private static void verifyActiveAndInactive(ActiveMtMessageReaderFactory factory, String expectedActiveClient, String expectedInactiveClient) {
    try (ActiveMtMessageReader activeMtMessageReader = factory.create()) {
      Assertions.assertTrue(getJoinedMtMessageForClient(activeMtMessageReader, expectedActiveClient).persistedActiveMtMessage().isPresent());
      Assertions.assertTrue(getJoinedMtMessageForClient(activeMtMessageReader, expectedInactiveClient).persistedActiveMtMessage().isEmpty());
    }
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        ActiveMtMessageReaderFactory activeMtMessageReaderFactory = configurableApplicationContext.getBean(ActiveMtMessageReaderFactory.class);

        DataUtil.createAndPersistSrp10Device(configurableApplicationContext);

        // ENQUEUING_TYPE IGNORED
        Pair<MtMessageId, ActiveMtMessageId> pair1 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(
            activeMtMessageReaderFactory, EnqueueingType.IGNORE, CorrelationId.ofString("correlationId1"), 1, 1);
        Pair<MtMessageId, ActiveMtMessageId> pair2 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(
            activeMtMessageReaderFactory, EnqueueingType.IGNORE, CorrelationId.ofString("correlationId2"), 1, 1);
        Assertions.assertEquals(pair1, pair2);

        truncateMessageTablesAndInvalidateCache(configurableApplicationContext);

        // ENQUEUING_TYPE IMMEDIATE
        Pair<MtMessageId, ActiveMtMessageId> pair3 = MtMessageTestsUtil.sentTwoNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(
            activeMtMessageReaderFactory, DataUtil.CLIENT_ID, DataUtil.CLIENT_ID);

        Pair<MtMessageId, ActiveMtMessageId> pair4 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(
            activeMtMessageReaderFactory, EnqueueingType.IMMEDIATE, CorrelationId.ofString("correlationId3"), 3, 3);
        DataUtil.verifyPairBothLeftAndRightNotEquals(pair3, pair4);

        truncateMessageTablesAndInvalidateCache(configurableApplicationContext);

        // ENQUEUING_TYPE OVERRIDE_IMMEDIATE
        Pair<MtMessageId, ActiveMtMessageId> pair5 = MtMessageTestsUtil.sentThreeNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(
            activeMtMessageReaderFactory,
            "client1", "client2", DataUtil.CLIENT_ID);

        Pair<MtMessageId, ActiveMtMessageId> pair6 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(
            activeMtMessageReaderFactory,
            EnqueueingType.OVERRIDE_IMMEDIATE, CorrelationId.ofString("correlationId4"), 3, 3);
        DataUtil.verifyPairBothLeftAndRightNotEquals(pair5, pair6);

        truncateMessageTablesAndInvalidateCache(configurableApplicationContext);

        // ENQUEUING_TYPE OVERRIDE
        Pair<MtMessageId, ActiveMtMessageId> pair7 = MtMessageTestsUtil.sentThreeNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(
            activeMtMessageReaderFactory,
            "client1", "client2", DataUtil.CLIENT_ID);

        Pair<MtMessageId, ActiveMtMessageId> pair8 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(
            activeMtMessageReaderFactory,
            EnqueueingType.OVERRIDE, CorrelationId.ofString("correlationId4"), 3, 2);
        DataUtil.verifyPairOnlyRightEquals(pair7, pair8);

        truncateMessageTablesAndInvalidateCache(configurableApplicationContext);

        // OVERRIDE with queued mt_message activation (note: max maxNumberOfInFlightMessagesPerVehicle is set to 2 in this test)

        MtMessageTestsUtil.sentThreeNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(activeMtMessageReaderFactory, "client1",
            "client2", "client3");

        // verify client1 message is activate while client3 message is queued
        verifyActiveAndInactive(activeMtMessageReaderFactory, "client1", "client3");

        MtMessageTestsUtil.sendMtMessageAndReceiveOptionalStatus(EnqueueingType.OVERRIDE, "client1", CorrelationId.ofString("correlationId4"));

        // client1's active message should now be overridden and the new one queued, while client3's message should be activated
        verifyActiveAndInactive(activeMtMessageReaderFactory, "client3", "client1");
      }
    }
  }
}
