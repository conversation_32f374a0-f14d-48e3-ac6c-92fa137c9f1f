package com.volvo.tisp.tgwmts.integration.tests.util;

import java.math.BigInteger;
import java.nio.file.Path;
import java.nio.file.Paths;

import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEvent;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.ChangeStatus;
import com.volvo.vc.conrepo.api.v2.DeviceDetailedEntry;
import com.volvo.vc.conrepo.api.v2.DeviceSim;
import com.volvo.vc.conrepo.api.v2.NotifyResponseMessage;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import com.volvo.vc.conrepo.api.v2.SecureActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.SrpLevel;
import com.volvo.vc.conrepo.api.v2.State;
import com.volvo.vc.conrepo.api.v2.WtpProtocolVersion;
import com.volvo.vc.crypto.asymmetric.encryption.rsa.RsaEncryptionServiceImpl;
import com.volvo.vc.crypto.asymmetric.key.rsa.RsaKeyRepositoryImpl;
import com.volvo.vc.crypto.asymmetric.signature.rsa.RsaSigningService;
import com.volvo.vc.crypto.common.entity.KeyId;
import com.volvo.vc.crypto.common.keystore.KeyAlias;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepository;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepositoryConfig;
import com.volvo.vc.crypto.common.keystore.impl.KeyStoreCacheRepositoryImpl;
import com.volvo.vc.crypto.common.keystore.impl.KeyStoreRepositoryImpl;
import com.volvo.vc.crypto.common.keystore.password.Password;
import com.volvo.vc.crypto.message.EncryptedMessage;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionConfig;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionService;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionServiceImpl;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;

public class ActivationNotifyEventMessageV2Utils {
  public static final String REPLY_TO = "LOCAL.LOCAL.LOCAL.ACTIVATION.NOTIFY.RESPONSE.IN";
  private static final String REFERENCE_ID = "someReferenceId";

  private ActivationNotifyEventMessageV2Utils() {
    throw new IllegalStateException();
  }

  public static ActivationNotifyEventMessage createActivationNotifyEventMessage(ActivationMessageType activationMessageType, ChangeStatus changeStatus,
      State state) {
    ActivationNotifyEventMessage activationNotifyEventMessage = new ActivationNotifyEventMessage();

    activationNotifyEventMessage.getActivationNotifyEvents().add(createActivationNotifyEvent(changeStatus, state));
    activationNotifyEventMessage.setMessageType(activationMessageType);
    activationNotifyEventMessage.setReferenceId(REFERENCE_ID);
    activationNotifyEventMessage.setReplyTo(REPLY_TO);

    return activationNotifyEventMessage;
  }

  public static SecureActivationNotifyEventMessage encryptActivationNotifyEventMessage(ActivationNotifyEventMessage activationNotifyEventMessage) {
    MessageEncryptionConfig<ActivationNotifyEventMessage> messageEncryptionConfig = createMessageEncryptionConfig();
    MessageEncryptionService messageEncryptionService = createMessageEncryptionService(Paths.get("src/test/resources/keystore.pkcs12"));
    Either<RuntimeException, EncryptedMessage> either = messageEncryptionService.encrypt(messageEncryptionConfig, activationNotifyEventMessage);
    return createSecureActivationNotifyEventMessage(either.getRight());
  }

  public static void verifyNotifyResponseMessage(NotifyResponseMessage notifyResponseMessage, ActivationMessageType expectedActivationMessageType,
      ResponseStatus expectedResponseStatus, String expectedDescription) {
    Assertions.assertEquals(expectedActivationMessageType, notifyResponseMessage.getMessageType());
    Assertions.assertEquals(REFERENCE_ID, notifyResponseMessage.getReferenceId());
    Assertions.assertEquals(expectedResponseStatus, notifyResponseMessage.getResponseStatus());
    Assertions.assertEquals(expectedDescription, notifyResponseMessage.getDescription());
  }

  private static ActivationNotifyEvent createActivationNotifyEvent(ChangeStatus changeStatus, State state) {
    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();

    activationNotifyEvent.setChangeStatus(changeStatus);
    activationNotifyEvent.setDeviceDetail(createDeviceDetailedEntry(state));

    return activationNotifyEvent;
  }

  private static KeyStoreRepository createAsymmetricKeyStoreRepository(Path transportKeyStorePath) {
    KeyStoreRepositoryConfig keyStoreRepositoryConfig = new KeyStoreRepositoryConfig(Password.ofString("tcetisp"), transportKeyStorePath);
    KeyStoreRepository keyStoreRepository = KeyStoreRepositoryImpl.createKeyStoreRepository(keyStoreRepositoryConfig);
    return KeyStoreCacheRepositoryImpl.create(keyStoreRepository);
  }

  private static DeviceDetailedEntry createDeviceDetailedEntry(State state) {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setHandle(DataUtil.HANDLE.toString());
    deviceDetailedEntry.setObsAlias(DataUtil.OBS_ALIAS.toLong());
    deviceDetailedEntry.setSimEntry(createDeviceSim());
    deviceDetailedEntry.setSrpLevel(SrpLevel.SRP_10);
    deviceDetailedEntry.setState(state);
    deviceDetailedEntry.setVehiclePlatformId(DataUtil.VPI.toString());
    deviceDetailedEntry.setWtpProtocolVersion(WtpProtocolVersion.VERSION_1);

    return deviceDetailedEntry;
  }

  private static DeviceSim createDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();

    deviceSim.setImsi(DataUtil.IMSI.toString());
    deviceSim.setIp(DataUtil.IPV4_ADDRESS.toString());
    deviceSim.setMsisdn(DataUtil.MSISDN.toString());
    deviceSim.setOperator(DataUtil.MOBILE_NETWORK_OPERATOR.toString());
    deviceSim.setPort(DataUtil.IPV4_PORT.toInt());

    return deviceSim;
  }

  private static MessageEncryptionConfig<ActivationNotifyEventMessage> createMessageEncryptionConfig() {
    return MessageEncryptionConfig.create(KeyAlias.ofString("tcetisp-test-1"), KeyAlias.ofString("tcetisp-test-1"), KeyId.ofLong(1), KeyId.ofLong(1),
        MessageEncoderXmlImpl.INSTANCE, ActivationNotifyEventMessage.class);
  }

  private static MessageEncryptionService createMessageEncryptionService(Path transportKeyStorePath) {
    return MessageEncryptionServiceImpl.create(RsaEncryptionServiceImpl.INSTANCE, RsaSigningService.INSTANCE,
        RsaKeyRepositoryImpl.create(createAsymmetricKeyStoreRepository(transportKeyStorePath)),
        SymmetricAesGcmEncryptionServiceImpl.INSTANCE);
  }

  private static SecureActivationNotifyEventMessage createSecureActivationNotifyEventMessage(EncryptedMessage encryptedMessage) {
    SecureActivationNotifyEventMessage secureActivationNotifyEventMessage = new SecureActivationNotifyEventMessage();

    secureActivationNotifyEventMessage.setEncryptedAesKey(encryptedMessage.getEncryptedAesKey().getImmutableByteArray().toByteArray());
    secureActivationNotifyEventMessage.setEncryptedPayload(getPayload(encryptedMessage.getAesGcmEncryptionResult()).toByteArray());
    secureActivationNotifyEventMessage.setPrivateKeyId(BigInteger.valueOf(encryptedMessage.getPrivateKeyId().toLong()));
    secureActivationNotifyEventMessage.setPublicKeyId(BigInteger.valueOf(encryptedMessage.getPublicKeyId().toLong()));
    secureActivationNotifyEventMessage.setSignature(encryptedMessage.getSignature().getImmutableByteArray().toByteArray());

    return secureActivationNotifyEventMessage;
  }

  private static ImmutableByteArray getPayload(AesGcmEncryptionResult aesGcmEncryptionResult) {
    return aesGcmEncryptionResult.getInitializationVector()
        .getImmutableByteArray()
        .concat(aesGcmEncryptionResult.getEncryptedPayloadWithoutMac().getImmutableByteArray())
        .concat(aesGcmEncryptionResult.getMessageAuthenticationCode().getImmutableByteArray());
  }
}
