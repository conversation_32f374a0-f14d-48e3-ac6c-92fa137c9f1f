package com.volvo.tisp.tgwmts.integration.tests.util;

import java.time.Duration;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.tuple.Pair;
import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.PrimaryKey;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.ActiveMtMessageId;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.PersistedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.mtmessage.MtMessageId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.PersistedMtMessage;
import com.volvo.tisp.tgwmts.database.model.vehiclelock.PersistedVehicleLock;
import com.volvo.tisp.tgwmts.impl.jms.model.EnqueueingType;
import com.volvo.tisp.tgwmts.impl.jms.model.MtStatus;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;

public class MtMessageTestsUtil {
  public static final CorrelationId CORRELATION_ID = CorrelationId.ofString("a-correlation-id");

  public static void checkMtMessageTable(ActiveMtMessageReaderFactory activeMtMessageReaderFactory, boolean vehicleLockExist,
      int numberOfMtMessages, int numberOfActiveMtMessages) {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      Optional<PersistedVehicleLock> persistedVehicleLockOptional = activeMtMessageReader.findVehicleLockByVpi(DataUtil.VPI);

      Assertions.assertEquals(vehicleLockExist, persistedVehicleLockOptional.isPresent());
      Assertions.assertEquals(numberOfMtMessages, activeMtMessageReader.countMtMessages());
      Assertions.assertEquals(numberOfActiveMtMessages, activeMtMessageReader.countActiveMtMessages());
    }
  }

  public static List<JoinedActiveMtMessage> getActiveMtMessagesByVpi(ActiveMtMessageReaderFactory activeMtMessageReaderFactory, Vpi vpi) {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      return activeMtMessageReader.findActiveMtMessagesByVpi(vpi);
    }
  }

  public static Pair<MtMessageId, ActiveMtMessageId> getLastInsertedMtMessageIdAndActivateMtMessagePair(
      ActiveMtMessageReaderFactory activeMtMessageReaderFactory) {
    return Pair.of(getLastInsertedMtMessageId(activeMtMessageReaderFactory), getLastInsertedActiveMtMessageId(activeMtMessageReaderFactory));
  }

  public static void receiveAndVerifyJmsMtStatusMessage() throws Exception {
    MtStatusMessage mtStatusMessage = JmsUtil.receiveMtStatusMessage(DataUtil.REPLY_TO.toString(), Duration.ofSeconds(2)).orElseThrow();
    verifyMtStatusMessage(mtStatusMessage, CORRELATION_ID);
  }

  public static void receiveAndVerifyJmsMtStatusMessage(CorrelationId correlationId) throws Exception {
    MtStatusMessage mtStatusMessage = JmsUtil.receiveMtStatusMessage(DataUtil.REPLY_TO.toString(), Duration.ofSeconds(2)).orElseThrow();
    verifyMtStatusMessage(mtStatusMessage, correlationId);
  }

  public static void sendMtMessageAndMtStatusExpected(EnqueueingType enqueueingType, CorrelationId expectedCorrelationId, MtStatus expectedMtStatus)
      throws Exception {
    MtMessage opusClientMtMessage = DataUtil.createMtMessage(enqueueingType, expectedCorrelationId);
    JmsUtil.sendMtMessage(opusClientMtMessage);

    MtStatusMessage receivedMtStatusMessage = JmsUtil.receiveMtStatusMessage(opusClientMtMessage.getMtStatusReplyOption().getReplyDestination(),
        Duration.ofSeconds(2)).orElseThrow();

    DataUtil.verifyMtStatus(expectedCorrelationId, expectedMtStatus, receivedMtStatusMessage);
  }

  public static void sendMtMessageAndReceiveOptionalStatus(EnqueueingType enqueueingType, CorrelationId correlationId) throws Exception {
    MtMessage opusClientMtMessage = DataUtil.createMtMessage(enqueueingType, correlationId);
    JmsUtil.sendMtMessage(opusClientMtMessage);
    receiveOptionalStatus(enqueueingType, opusClientMtMessage);
  }

  public static void sendMtMessageAndReceiveOptionalStatus(EnqueueingType enqueueingType, String clientId, CorrelationId correlationId) throws Exception {
    MtMessage opusClientMtMessage = DataUtil.createMtMessage(clientId, enqueueingType, correlationId);
    JmsUtil.sendMtMessage(opusClientMtMessage);
    receiveOptionalStatus(enqueueingType, opusClientMtMessage);
  }

  public static Pair<MtMessageId, ActiveMtMessageId> sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(
      ActiveMtMessageReaderFactory activeMtMessageReaderFactory, EnqueueingType enqueueingType,
      CorrelationId correlationId, int numberOfMtMessages, int numberOfActiveMtMessages) throws Exception {
    sendMtMessageAndReceiveOptionalStatus(enqueueingType, correlationId);
    checkMtMessageTable(activeMtMessageReaderFactory, true, numberOfMtMessages, numberOfActiveMtMessages);
    return getLastInsertedMtMessageIdAndActivateMtMessagePair(activeMtMessageReaderFactory);
  }

  public static Pair<MtMessageId, ActiveMtMessageId> sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(
      ActiveMtMessageReaderFactory activeMtMessageReaderFactory, EnqueueingType enqueueingType,
      String clientId, CorrelationId correlationId, int numberOfMtMessages, int numberOfActiveMtMessages) throws Exception {
    sendMtMessageAndReceiveOptionalStatus(enqueueingType, clientId, correlationId);
    checkMtMessageTable(activeMtMessageReaderFactory, true, numberOfMtMessages, numberOfActiveMtMessages);
    return getLastInsertedMtMessageIdAndActivateMtMessagePair(activeMtMessageReaderFactory);
  }

  public static Pair<MtMessageId, ActiveMtMessageId> sentThreeNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(
      ActiveMtMessageReaderFactory activeMtMessageReaderFactory,
      String client1, String client2, String client3) throws Exception {
    Pair<MtMessageId, ActiveMtMessageId> pair1 = sentTwoNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(
        activeMtMessageReaderFactory, client1, client2);

    Pair<MtMessageId, ActiveMtMessageId> pair2 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(activeMtMessageReaderFactory,
        EnqueueingType.NORMAL, client3, CorrelationId.ofString("correlationId3"), 3, 2);

    DataUtil.verifyPairOnlyRightEquals(pair1, pair2);
    return pair2;
  }

  public static Pair<MtMessageId, ActiveMtMessageId> sentTwoNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(
      ActiveMtMessageReaderFactory activeMtMessageReaderFactory, String client1, String client2)
      throws Exception {
    Pair<MtMessageId, ActiveMtMessageId> pair1 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(activeMtMessageReaderFactory,
        EnqueueingType.NORMAL, client1, CorrelationId.ofString("correlationId1"), 1, 1);

    Pair<MtMessageId, ActiveMtMessageId> pair2 = MtMessageTestsUtil.sendMtMessagesAndVerifyProperlyInsertedAndThenReturnLastOne(activeMtMessageReaderFactory,
        EnqueueingType.NORMAL, client2, CorrelationId.ofString("correlationId2"), 2, 2);
    DataUtil.verifyPairBothLeftAndRightNotEquals(pair1, pair2);

    return pair2;
  }

  public static void verifyMtStatusMessage(MtStatusMessage mtStatusMessage, CorrelationId correlationId) {
    Assertions.assertEquals(correlationId.toString(), mtStatusMessage.getCorrelationId());
    Assertions.assertEquals(DataUtil.VPI.toString(), mtStatusMessage.getVehiclePlatformId());
    Assertions.assertEquals("DELIVERED", mtStatusMessage.getStatus());
    Assertions.assertEquals(DataUtil.HANDLE.toString(), mtStatusMessage.getHandle());
  }

  private static ActiveMtMessageId getLastInsertedActiveMtMessageId(ActiveMtMessageReaderFactory activeMtMessageReaderFactory) {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      return activeMtMessageReader.findActiveMtMessagesByVpi(DataUtil.VPI)
          .stream()
          .map(JoinedActiveMtMessage::persistedActiveMtMessage)
          .map(PersistedActiveMtMessage::getActiveMtMessageId)
          .max(Comparator.comparingLong(PrimaryKey::toLong))
          .orElseThrow();
    }
  }

  private static MtMessageId getLastInsertedMtMessageId(ActiveMtMessageReaderFactory activeMtMessageReaderFactory) {
    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      return activeMtMessageReader.findMtMessagesByVpi(DataUtil.VPI)
          .stream()
          .map(PersistedMtMessage::getMtMessageId)
          .max(Comparator.comparingLong(PrimaryKey::toLong))
          .orElseThrow();
    }
  }

  private static void receiveOptionalStatus(EnqueueingType enqueueingType, MtMessage opusClientMtMessage) throws Exception {
    switch (enqueueingType) {
      case IGNORE, IMMEDIATE, NORMAL -> Assertions.assertTrue(
          JmsUtil.receiveMtStatusMessage(opusClientMtMessage.getMtStatusReplyOption().getReplyDestination(), Duration.ofSeconds(2)).isEmpty());

      case OVERRIDE, OVERRIDE_IMMEDIATE -> Assertions.assertFalse(
          JmsUtil.receiveMtStatusMessage(opusClientMtMessage.getMtStatusReplyOption().getReplyDestination(), Duration.ofSeconds(2)).isEmpty());
    }
  }
}
