package com.volvo.tisp.tgwmts.integration.tests.util;

import java.net.URI;
import java.time.Duration;
import java.util.List;
import java.util.Optional;

import org.apache.commons.lang3.StringUtils;
import org.awaitility.Awaitility;
import org.awaitility.core.ConditionTimeoutException;
import org.junit.jupiter.api.Assertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.github.tomakehurst.wiremock.verification.LoggedRequest;
import com.google.protobuf.InvalidProtocolBufferException;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.protobuf.common.PlatformIdentifier;
import com.volvo.tisp.tce.proto.ConnectionEstablishedEvent;
import com.volvo.tisp.tce.proto.ConnectionEstablishedRegistration;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReader;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.database.model.JoinedActiveMtMessage;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.RetryAttempt;
import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.impl.rest.ConnectionEstablishedEventRestController;
import com.volvo.tisp.vc.device.bulk.sync.model.Region;

public final class RestUtil {
  public static final String MO_CONNECTION_ESTABLISHED_REGISTRATION_API_PATH = "/api/v1/connectionEstablishedRegistration";
  public static final String MO_CONNECTION_ESTABLISHED_REGISTRATION_MULTIPLE_API_PATH = "/api/v1/connectionEstablishedRegistration/multiple";
  public static final String MT_ROUTER_REST_API_PATH = "/mtRouter/mtMessage";
  private static final String BASE_URL = "http://localhost:12720";
  private static final Logger logger = LoggerFactory.getLogger(RestUtil.class);

  private RestUtil() {
    throw new IllegalArgumentException();
  }

  public static Optional<LoggedRequest> awaitPostRequestToMoConnectionEstablishedRegistrationMultipleService(WireMockServer wireMockServer,
      Duration waitDuration) {
    return awaitPostRequest(wireMockServer, waitDuration, MO_CONNECTION_ESTABLISHED_REGISTRATION_MULTIPLE_API_PATH);
  }

  public static Optional<LoggedRequest> awaitPostRequestToMoConnectionEstablishedRegistrationService(WireMockServer wireMockServer, Duration waitDuration) {
    return awaitPostRequest(wireMockServer, waitDuration, MO_CONNECTION_ESTABLISHED_REGISTRATION_API_PATH);
  }

  public static Optional<LoggedRequest> awaitPostRequestToMtRouter(WireMockServer wireMockServer, Duration waitDuration) {
    return awaitPostRequest(wireMockServer, waitDuration, MT_ROUTER_REST_API_PATH);
  }

  public static void checkResponseEntity(ResponseEntity<String> responseEntity, HttpStatus expectedHttpStatus, String expectedBody) {
    Assertions.assertEquals(expectedHttpStatus, responseEntity.getStatusCode(), responseEntity::getBody);
    Assertions.assertEquals(expectedBody, responseEntity.getBody());
  }

  public static void createAndPostConnectionEstablishedEvent() {
    ConnectionEstablishedEvent connectionEstablishedEvent = ConnectionEstablishedEvent.newBuilder()
        .setVpi(PlatformIdentifier.newBuilder().setValue(DataUtil.VPI.toString()).build())
        .build();
    RestUtil.postConnectionEstablishedEvent(connectionEstablishedEvent);
  }

  public static RestTemplate createRestTemplate() {
    RestTemplate restTemplate = new RestTemplate();
    restTemplate.setErrorHandler(NoOpResponseErrorHandler.INSTANCE);
    return restTemplate;
  }

  public static URI createUri(String path) {
    return UriComponentsBuilder.fromHttpUrl(BASE_URL).path(path).build().toUri();
  }

  public static UriComponentsBuilder createUriBuilder(String path) {
    return UriComponentsBuilder.fromHttpUrl(BASE_URL).path(path);
  }

  public static void postConnectionEstablishedEvent(ConnectionEstablishedEvent connectionEstablishedEvent) {
    ResponseEntity<String> responseEntity = createRestTemplate().exchange(createUri(ConnectionEstablishedEventRestController.REQUEST_MAPPING_PATH),
        HttpMethod.POST, createProtobufHttpEntity(connectionEstablishedEvent.toByteArray()), String.class);

    checkResponseEntity(responseEntity, HttpStatus.OK, null);
  }

  public static void receiveConnectionEstablishedRegistration(WireMockServer wireMockServer) throws InvalidProtocolBufferException {
    LoggedRequest loggedRequest = RestUtil.awaitPostRequestToMoConnectionEstablishedRegistrationMultipleService(wireMockServer, Duration.ofSeconds(5))
        .orElseThrow();
    ConnectionEstablishedRegistration connectionEstablishedRegistration = ConnectionEstablishedRegistration.parseFrom(loggedRequest.getBody());

    Assertions.assertEquals(DataUtil.VPI.toString(), connectionEstablishedRegistration.getVpi().getValue().trim());
  }

  public static void receiveConnectionEstablishedRegistrationAndVerifyMessageIsInWaitState(WireMockServer wireMockServer,
      ActiveMtMessageReaderFactory activeMtMessageReaderFactory) throws InvalidProtocolBufferException {
    LoggedRequest loggedRequest = RestUtil.awaitPostRequestToMoConnectionEstablishedRegistrationService(wireMockServer, Duration.ofSeconds(5)).orElseThrow();
    ConnectionEstablishedRegistration connectionEstablishedRegistration = ConnectionEstablishedRegistration.parseFrom(loggedRequest.getBody());

    Assertions.assertEquals(DataUtil.VPI.toString(), connectionEstablishedRegistration.getVpi().getValue());

    try (ActiveMtMessageReader activeMtMessageReader = activeMtMessageReaderFactory.create()) {
      List<JoinedActiveMtMessage> activeMtMessagesByVpi = activeMtMessageReader.findActiveMtMessagesByVpi(DataUtil.VPI);
      Assertions.assertEquals(SendSchemaStepId.ofInt(2),
          activeMtMessagesByVpi.get(0).persistedActiveMtMessage().getActiveMtMessage().getSendSchemaStepId());
      Assertions.assertEquals(RetryAttempt.ofShort((short) 0),
          activeMtMessagesByVpi.get(0).persistedActiveMtMessage().getActiveMtMessage().getRetryAttempt());
    }
  }

  public static void receiveMtMessageAndReplyWithMtAck(WireMockServer wireMockServer, Status status, int expectedId, Transport expectedTransport)
      throws Exception {
    LoggedRequest loggedRequest = RestUtil.awaitPostRequestToMtRouter(wireMockServer, Duration.ofSeconds(5)).orElseThrow();
    MtMessage mtMessage = ProtobufUtil.parseProtobufMtRouterMtMessage(loggedRequest.getBody());

    verifyTgwMtMessage(mtMessage, expectedId, expectedTransport);

    MtStatus mtStatus = MtStatus.newBuilder()
        .setMessageId(mtMessage.getMessageId())
        .setStatus(status)
        .setTransport(Transport.UDP)
        .build();

    JmsUtil.publishMtRouterMtStatusMessage(mtStatus);
  }

  public static void receiveMtMessageAndReplyWithMtAck(WireMockServer wireMockServer) throws Exception {
    receiveMtMessageAndReplyWithMtAck(wireMockServer, Status.DELIVERED, 1, Transport.UDP);
  }

  public static ResponseEntity<String> startBulkSync(Region region, boolean dryRun) {
    return startBulkSync(Optional.of(region.name()), dryRun);
  }

  public static ResponseEntity<String> startBulkSync(Optional<String> regionString, boolean dryRun) {
    UriComponentsBuilder builder = UriComponentsBuilder.fromUriString("http://localhost:12720/api/v1/bulk-sync");
    regionString.ifPresent(region -> builder.queryParam("region", region));
    builder.queryParam("dryRun", dryRun);
    builder.build();

    HttpHeaders httpHeaders = new HttpHeaders();
    HttpEntity<byte[]> httpEntity = new HttpEntity<>(httpHeaders);

    return createRestTemplate().exchange(builder.toUriString(), HttpMethod.GET, httpEntity, String.class);
  }

  public static void verifyOkHttpResponse(String urlString) {
    ResponseEntity<String> responseEntity = RestUtil.createRestTemplate().exchange(urlString, HttpMethod.GET, null, String.class);
    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode(), responseEntity::getBody);
  }

  private static List<LoggedRequest> awaitAtLeastOnePostRequest(WireMockServer wireMockServer, Duration duration, String urlPath) {
    return Awaitility.await().atMost(duration).until(() -> findAllPostRequests(wireMockServer, urlPath), loggedRequests -> !loggedRequests.isEmpty());
  }

  private static Optional<LoggedRequest> awaitPostRequest(WireMockServer wireMockServer, Duration waitDuration, String urlPath) {
    try {
      List<LoggedRequest> loggedRequests = awaitAtLeastOnePostRequest(wireMockServer, waitDuration, urlPath);
      wireMockServer.resetRequests();
      return Optional.of(loggedRequests.get(0));
    } catch (ConditionTimeoutException e) {
      logger.debug("", e);
      return Optional.empty();
    }
  }

  private static HttpEntity<byte[]> createProtobufHttpEntity(byte[] bytes) {
    HttpHeaders httpHeaders = new HttpHeaders();
    httpHeaders.add("Content-Type", "application/x-protobuf");
    return new HttpEntity<>(bytes, httpHeaders);
  }

  private static List<LoggedRequest> findAllPostRequests(WireMockServer wireMockServer, String urlPath) {
    return wireMockServer.findAll(WireMock.postRequestedFor(WireMock.urlPathEqualTo(urlPath)));
  }

  private static void verifyTgwMtMessage(MtMessage mtMessage, int expectedId, Transport expectedTransport) {
    SrpUtil.decodeAndCheckSrpPayload(mtMessage.getPayload().toByteArray());

    Assertions.assertEquals(StringUtils.leftPad(String.valueOf(expectedId), 32, '0'), mtMessage.getMessageId());
    Assertions.assertSame(expectedTransport, mtMessage.getTransport());
    Assertions.assertNotNull(mtMessage.getPayload());
    Assertions.assertEquals(DataUtil.VPI.toString(), mtMessage.getVpi());
  }
}
