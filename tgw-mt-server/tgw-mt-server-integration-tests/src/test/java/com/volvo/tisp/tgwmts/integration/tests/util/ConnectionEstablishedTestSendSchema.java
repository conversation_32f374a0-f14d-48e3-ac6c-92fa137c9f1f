package com.volvo.tisp.tgwmts.integration.tests.util;

import java.time.Duration;
import java.util.Optional;

import com.volvo.tisp.tgwmts.database.model.activemtmessage.SendSchemaStepId;
import com.volvo.tisp.tgwmts.database.model.mtmessage.SendSchemaName;
import com.volvo.tisp.tgwmts.impl.schema.SendSchema;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStep;

public final class ConnectionEstablishedTestSendSchema implements SendSchema {
  public static final SendSchema INSTANCE = new ConnectionEstablishedTestSendSchema();
  private static final SendSchemaStep SEND_SCHEMA_STEP_1 = SendSchemaStep.forUdp(SendSchemaStepId.ofInt(1), Duration.ofSeconds(1));
  private static final SendSchemaStep SEND_SCHEMA_STEP_2 = SendSchemaStep.forWait(SendSchemaStepId.ofInt(2), Duration.ofMinutes(5));

  private ConnectionEstablishedTestSendSchema() {
    // do nothing
  }

  @Override
  public Duration getGlobalTimeout() {
    return Duration.ofMinutes(10);
  }

  @Override
  public short getMaxRetryAttempts() {
    return 1;
  }

  @Override
  public SendSchemaName getSendSchemaName() {
    return SendSchemaName.COMMON_LOW;
  }

  @Override
  public Optional<SendSchemaStep> getSendSchemaStep(SendSchemaStepId sendSchemaStepId) {
    return switch (sendSchemaStepId.toInt()) {
      case 1 -> Optional.of(SEND_SCHEMA_STEP_1);
      case 2 -> Optional.of(SEND_SCHEMA_STEP_2);
      default -> Optional.empty();
    };
  }
}
