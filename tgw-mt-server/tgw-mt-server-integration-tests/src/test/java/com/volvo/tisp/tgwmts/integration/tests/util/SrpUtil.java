package com.volvo.tisp.tgwmts.integration.tests.util;

import java.util.List;

import org.junit.jupiter.api.Assertions;

import com.volvo.tisp.vc.main.utils.lib.type.Either3;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.tisp.vc.service.routing.protocol.lib.converter.SrpConverter;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.ServiceRoutingPduWrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.common.Priority;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v10.Srp10Wrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.Srp11Wrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v11.SrpHeaderWrapper;
import com.volvo.tisp.vc.service.routing.protocol.lib.model.v12.Srp12Wrapper;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AdditionalAuthenticatedData;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;
import com.volvo.vc.crypto.symmetric.key.SymmetricKey;

import junit.framework.AssertionFailedError;

public final class SrpUtil {
  private static final Priority PRIORITY = Priority.ofShort((short) 1);

  private SrpUtil() {
    throw new IllegalStateException();
  }

  public static void decodeAndCheckSrpPayload(byte[] payload) {
    Either3<List<Srp10Wrapper>, List<Srp11Wrapper>, Srp12Wrapper> either3 = decodeServiceRoutingPduWrapper(payload).getEither3();

    if (either3.isLeft()) {
      List<Srp10Wrapper> srp10Wrappers = either3.getLeft();
      Assertions.assertEquals(1, srp10Wrappers.size());
      checkSrp10Wrapper(srp10Wrappers.get(0));
      return;
    }

    if (either3.isMiddle()) {
      List<Srp11Wrapper> srp11Wrappers = either3.getMiddle();
      Assertions.assertEquals(1, srp11Wrappers.size());
      checkSrp11Wrapper(srp11Wrappers.get(0));
      return;
    }

    throw new AssertionFailedError("srp payload could not be decoded");
  }

  private static void checkSrp10Wrapper(Srp10Wrapper srp10Wrapper) {
    Assertions.assertEquals(DataUtil.DESTINATION_SERVICE, srp10Wrapper.getDestinationService());
    Assertions.assertEquals(DataUtil.DESTINATION_VERSION, srp10Wrapper.getDestinationVersion());
    Assertions.assertEquals(DataUtil.OBS_ALIAS, srp10Wrapper.getObsAlias());
    Assertions.assertEquals(PRIORITY, srp10Wrapper.getPriority());
    Assertions.assertEquals(DataUtil.SOURCE_SERVICE, srp10Wrapper.getSourceService());
    Assertions.assertEquals(DataUtil.SOURCE_VERSION, srp10Wrapper.getSourceVersion());
    Assertions.assertEquals(DataUtil.PAYLOAD, srp10Wrapper.getPayload());
  }

  private static void checkSrp11Wrapper(Srp11Wrapper srp11Wrapper) {
    checkSrpHeaderWrapper(SrpConverter.decodeSrpHeaderWrapper(srp11Wrapper.getEncodedSrpHeader()).getRight());

    PlainTextPayload plainTextPayload = decryptAesGcm(srp11Wrapper);
    Assertions.assertEquals(DataUtil.PAYLOAD, plainTextPayload.getImmutableByteArray());
  }

  private static void checkSrpHeaderWrapper(SrpHeaderWrapper srpHeaderWrapper) {
    Assertions.assertEquals(DataUtil.DESTINATION_SERVICE, srpHeaderWrapper.getDestinationService());
    Assertions.assertEquals(DataUtil.DESTINATION_VERSION, srpHeaderWrapper.getDestinationVersion());
    Assertions.assertEquals(DataUtil.OBS_ALIAS, srpHeaderWrapper.getObsAlias());
    Assertions.assertEquals(PRIORITY, srpHeaderWrapper.getPriority());
    Assertions.assertEquals(DataUtil.SOURCE_SERVICE, srpHeaderWrapper.getSourceService());
    Assertions.assertEquals(DataUtil.SOURCE_VERSION, srpHeaderWrapper.getSourceVersion());
  }

  private static ServiceRoutingPduWrapper decodeServiceRoutingPduWrapper(byte[] payload) {
    return SrpConverter.decodeServiceRoutingPduWrapper(ImmutableByteArray.of(payload)).getRight();
  }

  private static PlainTextPayload decryptAesGcm(Srp11Wrapper srp11Wrapper) {
    SymmetricKey symmetricKey = SymmetricKey.create(DataUtil.AES_KEY.getImmutableByteArray());

    AesGcmEncryptionResult aesGcmEncryptionResult = AesGcmEncryptionResult.create(srp11Wrapper.getEncryptedPayloadWithoutMac(),
        srp11Wrapper.getNonce(), srp11Wrapper.getMessageAuthenticationCode());
    AdditionalAuthenticatedData additionalAuthenticatedData = AdditionalAuthenticatedData.create(srp11Wrapper.getEncodedSrpHeader().getImmutableByteArray());

    return SymmetricAesGcmEncryptionServiceImpl.INSTANCE
        .decrypt(symmetricKey, aesGcmEncryptionResult, additionalAuthenticatedData)
        .getRight();
  }
}
