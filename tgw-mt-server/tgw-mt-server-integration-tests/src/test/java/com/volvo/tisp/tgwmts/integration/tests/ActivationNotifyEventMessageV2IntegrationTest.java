package com.volvo.tisp.tgwmts.integration.tests;

import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgw.device.info.database.api.DeviceInfoReaderFactory;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.integration.tests.util.ActivationNotifyEventMessageV2Utils;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.JmsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ChangeStatus;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import com.volvo.vc.conrepo.api.v2.SecureActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.State;

class ActivationNotifyEventMessageV2IntegrationTest {
  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {
        SecureActivationNotifyEventMessage secureActivationNotifyEventMessage = ActivationNotifyEventMessageV2Utils.encryptActivationNotifyEventMessage(
            ActivationNotifyEventMessageV2Utils.createActivationNotifyEventMessage(ActivationMessageType.NOTIFY, ChangeStatus.UPDATED, State.ACTIVATED));

        ActivationNotifyEventMessageV2Utils.verifyNotifyResponseMessage(
            JmsUtil
                .publishSecureActivationNotifyEventMessageAndReceiveResponse(secureActivationNotifyEventMessage, ActivationNotifyEventMessageV2Utils.REPLY_TO)
                .get(),
            ActivationMessageType.NOTIFY, ResponseStatus.SUCCESS, "Success");

        DataUtil.checkDeviceInDatabase(configurableApplicationContext.getBean(DeviceInfoReaderFactory.class));
      }
    }
  }
}
