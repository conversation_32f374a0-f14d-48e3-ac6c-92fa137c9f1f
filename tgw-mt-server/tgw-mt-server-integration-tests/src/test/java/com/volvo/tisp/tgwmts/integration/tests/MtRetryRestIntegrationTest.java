package com.volvo.tisp.tgwmts.integration.tests;

import java.net.URI;
import java.time.Duration;
import java.util.List;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.github.tomakehurst.wiremock.WireMockServer;
import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.impl.rest.MtRetryRestController;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.JmsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.MtMessageTestsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.RestUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.config.SendSchemaOverrideTestConfig;

class MtRetryRestIntegrationTest {
  private static void makeRetryPostRequest() {
    RestTemplate restTemplate = RestUtil.createRestTemplate();
    URI uri = RestUtil.createUri(MtRetryRestController.REQUEST_MAPPING_PATH);
    ResponseEntity<String> responseEntity = restTemplate.postForEntity(uri, List.of(DataUtil.VPI.toString()), String.class);
    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);
      IntegrationTestHelper.stubMoConnectionEstablishedRegistrationApi(wireMockServerWrapper);
      IntegrationTestHelper.stubMtRouterServer(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class,
          SendSchemaOverrideTestConfig.class)) {

        WireMockServer wireMockServer = wireMockServerWrapper.getWireMockServer();
        ActiveMtMessageReaderFactory activeMtMessageReaderFactory = configurableApplicationContext.getBean(ActiveMtMessageReaderFactory.class);

        DataUtil.createAndPersistSrp11Device(configurableApplicationContext);
        JmsUtil.sendMtMessage(DataUtil.createMtMessage(MtMessageTestsUtil.CORRELATION_ID, "low"));
        RestUtil.awaitPostRequestToMtRouter(wireMockServer, Duration.ofSeconds(5)).orElseThrow();

        RestUtil.receiveConnectionEstablishedRegistrationAndVerifyMessageIsInWaitState(wireMockServer, activeMtMessageReaderFactory);

        makeRetryPostRequest();

        RestUtil.receiveMtMessageAndReplyWithMtAck(wireMockServer);
        MtMessageTestsUtil.receiveAndVerifyJmsMtStatusMessage();
        DataUtil.verifyActiveMtMessagesDeletedFromDb(activeMtMessageReaderFactory);
      }
    }
  }
}
