package com.volvo.tisp.tgwmts.integration.tests;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.JmsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.MtMessageTestsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.RestUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.vc.vcss.client.protobuf.MtStatusStatisticProtobuf;
import com.volvo.tisp.vc.vcss.client.protobuf.common.TransportTypeProtobuf;

class Srp10MtMessageIntegrationTest {
  private static void checkMtStatusStatistic(MtStatusStatisticProtobuf.MtStatusStatistic mtStatusStatistic) {
    Assertions.assertEquals(DataUtil.VPI.toString(), mtStatusStatistic.getVpi());
    Assertions.assertNotNull(mtStatusStatistic.getTransportType());
    Assertions.assertEquals(TransportTypeProtobuf.TransportType.UDP, mtStatusStatistic.getTransportType());
    Assertions.assertEquals(MtStatusStatisticProtobuf.Status.DELIVERED, mtStatusStatistic.getStatus());
  }

  private static void checkMtStatusStatisticsMessage(byte[] protobufBytes)
      throws IOException {
    try (InputStream inputStream = new ByteArrayInputStream(protobufBytes)) {
      checkMtStatusStatistic(MtStatusStatisticProtobuf.MtStatusStatistic.parseDelimitedFrom(inputStream));
      Assertions.assertNull(MtStatusStatisticProtobuf.MtStatusStatistic.parseDelimitedFrom(inputStream));
    }
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      WireMock.configureFor(wireMockServerWrapper.getWireMockServer().port());
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);
      IntegrationTestHelper.stubMtRouterServer(wireMockServerWrapper);
      IntegrationTestHelper.stubSubRepo();

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class)) {

        DataUtil.createAndPersistSrp10Device(configurableApplicationContext);

        JmsUtil.sendMtMessage(DataUtil.createMtMessage(MtMessageTestsUtil.CORRELATION_ID, "low"));
        RestUtil.receiveMtMessageAndReplyWithMtAck(wireMockServerWrapper.getWireMockServer(), Status.DELIVERED, 1, Transport.UDP);
        MtMessageTestsUtil.receiveAndVerifyJmsMtStatusMessage();
        checkMtStatusStatisticsMessage(JmsUtil.receiveMtStatusStatisticsProtobufMessage().orElseThrow());
      }
    }
  }
}
