package com.volvo.tisp.tgwmts.integration.tests;

import java.net.URI;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.RestTemplate;
import org.testcontainers.containers.localstack.LocalStackContainer;

import com.opentable.db.postgres.embedded.EmbeddedPostgres;
import com.volvo.tisp.tgwmts.database.api.ActiveMtMessageReaderFactory;
import com.volvo.tisp.tgwmts.impl.conf.AppConfig;
import com.volvo.tisp.tgwmts.impl.model.MtMessageInformation;
import com.volvo.tisp.tgwmts.impl.rest.MtMessageInformationRestController;
import com.volvo.tisp.tgwmts.impl.schema.SendSchemaStepType;
import com.volvo.tisp.tgwmts.integration.tests.util.DataUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.EmbeddedActiveMqWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.IntegrationTestHelper;
import com.volvo.tisp.tgwmts.integration.tests.util.MtMessageTestsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.RestUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.SqsUtil;
import com.volvo.tisp.tgwmts.integration.tests.util.WireMockServerWrapper;
import com.volvo.tisp.tgwmts.integration.tests.util.config.SendSchemaOverrideTestConfig;

class MessageInformationIntegrationTest {

  private static MtMessageInformation makeMessageInformationRequest() {
    RestTemplate restTemplate = RestUtil.createRestTemplate();
    URI uri = RestUtil.createUriBuilder(MtMessageInformationRestController.REQUEST_MAPPING_PATH)
        .queryParam("vpi", DataUtil.VPI.toString())
        .build()
        .toUri();
    ResponseEntity<MtMessageInformation> responseEntity = restTemplate.getForEntity(uri, MtMessageInformation.class);
    Assertions.assertEquals(HttpStatus.OK, responseEntity.getStatusCode());
    return responseEntity.getBody();
  }

  @Test
  void integrationTest() throws Exception {
    try (EmbeddedActiveMqWrapper embeddedActiveMqWrapper = IntegrationTestHelper.createAndStartEmbeddedActiveMqWrapper();
        WireMockServerWrapper wireMockServerWrapper = IntegrationTestHelper.createAndStartWireMockServer();
        EmbeddedPostgres embeddedPostgres = IntegrationTestHelper.startEmbeddedPostgres();
        LocalStackContainer sqsLocalStackContainer = SqsUtil.startSQSLocalStackContainer()) {
      SqsUtil.setupQueues(sqsLocalStackContainer);
      IntegrationTestHelper.mockAppConfig(embeddedPostgres, wireMockServerWrapper, sqsLocalStackContainer);
      IntegrationTestHelper.stubMoConnectionEstablishedRegistrationApi(wireMockServerWrapper);
      IntegrationTestHelper.stubMtRouterServer(wireMockServerWrapper);

      try (ConfigurableApplicationContext configurableApplicationContext = IntegrationTestHelper.runSpringApplication(AppConfig.class,
          SendSchemaOverrideTestConfig.class)) {

        ActiveMtMessageReaderFactory activeMtMessageReaderFactory = configurableApplicationContext.getBean(ActiveMtMessageReaderFactory.class);

        DataUtil.createAndPersistSrp11Device(configurableApplicationContext);

        MtMessageTestsUtil.sentThreeNormalMtMessagesToEmptyDatabaseAndReturnLastInsertedMtMessageAndActiveMtMessagePair(activeMtMessageReaderFactory,
            DataUtil.CLIENT_ID, DataUtil.CLIENT_ID, DataUtil.CLIENT_ID);

        MtMessageTestsUtil.checkMtMessageTable(activeMtMessageReaderFactory, true, 3, 2);

        MtMessageInformation responseBody = makeMessageInformationRequest();
        Assertions.assertEquals(DataUtil.VPI.toString(), responseBody.vpi());

        Assertions.assertEquals(DataUtil.REPLY_TO.toString(), responseBody.mtMessageDetails().get(0).replyTo().get());
        Assertions.assertEquals(1L, responseBody.mtMessageDetails().get(0).activeMessageDetails().activeMtMessageId());
        Assertions.assertEquals(SendSchemaStepType.WAIT, responseBody.mtMessageDetails().get(0).activeMessageDetails().sendSchemaStep());

        Assertions.assertEquals(DataUtil.REPLY_TO.toString(), responseBody.mtMessageDetails().get(1).replyTo().get());
        Assertions.assertEquals(2L, responseBody.mtMessageDetails().get(1).activeMessageDetails().activeMtMessageId());
        Assertions.assertEquals(SendSchemaStepType.WAIT, responseBody.mtMessageDetails().get(1).activeMessageDetails().sendSchemaStep());

        Assertions.assertEquals(DataUtil.REPLY_TO.toString(), responseBody.mtMessageDetails().get(2).replyTo().get());
        Assertions.assertNull(responseBody.mtMessageDetails().get(2).activeMessageDetails());

      }
    }
  }
}
