package com.volvo.tisp.tgwmts.integration.tests.util;

import java.io.Serializable;
import java.time.Duration;
import java.util.Optional;

import jakarta.jms.BytesMessage;
import jakarta.jms.Connection;
import jakarta.jms.ConnectionFactory;
import jakarta.jms.DeliveryMode;
import jakarta.jms.Destination;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.MessageConsumer;
import jakarta.jms.MessageProducer;
import jakarta.jms.Session;
import jakarta.jms.TextMessage;

import org.apache.activemq.artemis.jms.client.ActiveMQBytesMessage;
import org.apache.activemq.artemis.jms.client.ActiveMQConnectionFactory;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.volvo.connectivity.cms.events.model.CapabilityState;
import com.volvo.tisp.framework.jms.TispJmsHeader;
import com.volvo.tisp.tgwmts.impl.jms.MtMessageJmsController;
import com.volvo.tisp.tgwmts.impl.jms.MtStatusMessageJmsController;
import com.volvo.tisp.tgwmts.impl.jms.TgwNotifyEventJmsController;
import com.volvo.tisp.vc.common.dto.lib.jms.CorrelationId;
import com.volvo.tisp.vc.common.dto.lib.jms.ReplyTo;
import com.volvo.vc.conrepo.MessageTypes;
import com.wirelesscar.tce.api.v2.MtMessage;
import com.wirelesscar.tce.api.v2.MtStatusMessage;
import com.wirelesscar.tce.client.opus.MessageTypesJms;

public final class JmsUtil {
  public static final String AMQ_URL = "tcp://localhost:61616";
  public static final String LEGACY_MT_QUEUE = "LOCAL.LOCAL.LOCAL.MTSCHD." + MtMessageJmsController.MT_MESSAGE_IN_QUEUE_NAME;
  public static final String MT_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT." + MtMessageJmsController.MT_MESSAGE_IN_QUEUE_NAME;
  public static final String MT_STATUS_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT." + MtStatusMessageJmsController.MT_STATUS_MESSAGE_IN_QUEUE_NAME;
  public static final String TGW_NOTIFY_QUEUE = "LOCAL.LOCAL.LOCAL.COMPSHRT." + TgwNotifyEventJmsController.TGW_NOTIFY_QUEUE_NAME;
  private static final Duration DEFAULT_RECEIVE_DURATION = Duration.ofSeconds(3);
  private static final String QUEUE_PREFIX = "LOCAL.LOCAL.LOCAL.COMPSHRT.";

  private JmsUtil() {
    throw new IllegalStateException();
  }

  public static Optional<com.volvo.vc.conrepo.api.v1.NotifyResponseMessage> publishActivationNotifyEventMessageV1AndReceiveResponse(
      com.volvo.vc.conrepo.api.v1.ActivationNotifyEventMessage activationNotifyEventMessage) throws Exception {
    sendRequest(activationNotifyEventMessage, "LOCAL.LOCAL.LOCAL.COMPSHRT.DEVICE.NOTIFY.IN", MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE,
        MessageTypes.VERSION_1_1, Optional.empty(), Optional.empty());

    return consumeAndUnmarshalMessage(activationNotifyEventMessage.getReplyTo(), DEFAULT_RECEIVE_DURATION,
        com.volvo.vc.conrepo.api.v1.NotifyResponseMessage.class);
  }

  public static void publishMtRouterMtStatusMessage(com.volvo.connectivity.proto.MtStatus mtStatus) throws Exception {
    publishProtobufMessage(MT_STATUS_QUEUE, mtStatus.toByteArray(), MtStatusMessageJmsController.MT_STATUS_MESSAGE_TYPE,
        MtStatusMessageJmsController.MT_STATUS_MESSAGE_VERSION);
  }

  public static Optional<com.volvo.vc.conrepo.api.v2.NotifyResponseMessage> publishSecureActivationNotifyEventMessageAndReceiveResponse(
      com.volvo.vc.conrepo.api.v2.SecureActivationNotifyEventMessage secureActivationNotifyEventMessage, String fullReplyToQueueName) throws Exception {
    sendRequest(secureActivationNotifyEventMessage, "LOCAL.LOCAL.LOCAL.COMPSHRT.DEVICE.NOTIFY.IN", MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE,
        MessageTypes.VERSION_2_0, Optional.empty(), Optional.empty());
    return consumeAndUnmarshalMessage(fullReplyToQueueName, DEFAULT_RECEIVE_DURATION, com.volvo.vc.conrepo.api.v2.NotifyResponseMessage.class);
  }

  public static Optional<MtStatusMessage> receiveMtStatusMessage(String fullQueueName, Duration waitDuration) throws Exception {
    return consumeAndUnmarshalMessage(fullQueueName, waitDuration, MtStatusMessage.class);
  }

  public static Optional<byte[]> receiveMtStatusStatisticsProtobufMessage() throws JMSException {
    Optional<Message> optionalMessage = consumeOneMessage(createActiveMQConnectionFactory(),
        QUEUE_PREFIX + com.volvo.tisp.vc.vcss.client.protobuf.MessageTypes.VCSS_MT_STATUS_STATISTIC,
        Duration.ofSeconds(5));

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }

    ActiveMQBytesMessage activeMQBytesMessage = (ActiveMQBytesMessage) optionalMessage.get();
    return Optional.of(readBytes(activeMQBytesMessage));
  }

  public static void sendMtMessage(MtMessage mtMessage) throws Exception {
    sendRequest(mtMessage, MT_QUEUE, MessageTypesJms.TCE_MT_MESSAGE_TYPE, MessageTypesJms.VERSION_2_0, Optional.empty(), Optional.empty());
  }

  public static void sendMtMessageToLegacyQueue(MtMessage mtMessage) throws Exception {
    sendRequest(mtMessage, LEGACY_MT_QUEUE, MessageTypesJms.TCE_MT_MESSAGE_TYPE, MessageTypesJms.VERSION_2_0, Optional.empty(), Optional.empty());
  }

  public static void sendWifiDisabledTgwNotifyEvent() throws Exception {
    sendObjectRequest(DataUtil.createWifiStatusChangeTgwNotifyEvent(CapabilityState.UNAVAILABLE), TGW_NOTIFY_QUEUE, TgwNotifyEventJmsController.MESSAGE_TYPE,
        TgwNotifyEventJmsController.MESSAGE_VERSION);
  }

  public static void sendWifiEnabledTgwNotifyEvent() throws Exception {
    sendObjectRequest(DataUtil.createWifiStatusChangeTgwNotifyEvent(CapabilityState.AVAILABLE), TGW_NOTIFY_QUEUE, TgwNotifyEventJmsController.MESSAGE_TYPE,
        TgwNotifyEventJmsController.MESSAGE_VERSION);
  }

  private static <T> Optional<T> consumeAndUnmarshalMessage(String fullQueueName, Duration timeout, Class<T> clazz) throws Exception {
    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    Optional<Message> optionalMessage = consumeOneMessage(connectionFactory, fullQueueName, timeout);

    if (optionalMessage.isEmpty()) {
      return Optional.empty();
    }
    return Optional.of(IntegrationTestHelper.unmarshal(optionalMessage.get(), clazz));
  }

  private static Optional<Message> consumeOneMessage(ConnectionFactory connectionFactory, String fullQueueName, Duration timeout) throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        Destination destination = session.createQueue(fullQueueName);

        try (MessageConsumer messageConsumer = session.createConsumer(destination)) {
          Message message = messageConsumer.receive(timeout.toMillis());

          return Optional.ofNullable(message);
        }
      }
    }
  }

  private static ActiveMQConnectionFactory createActiveMQConnectionFactory() {
    return new ActiveMQConnectionFactory(AMQ_URL);
  }

  private static TextMessage getTextMessage(Serializable payload, Session session) throws JMSException, JsonProcessingException {
    if (payload instanceof String) {
      return session.createTextMessage((String) payload);
    }

    return session.createTextMessage(new ObjectMapper().writeValueAsString(payload));
  }

  private static void publishMessage(ConnectionFactory connectionFactory, String fullQueueName, String xmlString, String messageType, String version,
      Optional<String> replyToHeader, Optional<String> correlationId) throws JMSException {
    try (Connection connection = connectionFactory.createConnection()) {
      connection.start();

      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        TextMessage textMessage = session.createTextMessage(xmlString);

        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), messageType);
        textMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), version);

        if (replyToHeader.isPresent()) {
          textMessage.setJMSReplyTo(session.createQueue(replyToHeader.get()));
        }
        if (correlationId.isPresent()) {
          textMessage.setJMSCorrelationID(correlationId.get());
        }

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(textMessage);
        }
      }
    }
  }

  private static void publishProtobufMessage(String fullQueueName, byte[] request, String messageType, String version) throws Exception {
    try (Connection connection = createActiveMQConnectionFactory().createConnection()) {
      connection.start();
      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        BytesMessage bytesMessage = session.createBytesMessage();
        bytesMessage.writeBytes(request);
        bytesMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), messageType);
        bytesMessage.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), version);
        bytesMessage.setStringProperty(TispJmsHeader.CLIENT.value(), "vwtpinit");

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(bytesMessage);
        }
      }
    }
  }

  private static byte[] readBytes(ActiveMQBytesMessage activeMQBytesMessage) throws JMSException {
    byte[] bytes = new byte[(int) activeMQBytesMessage.getBodyLength()];
    int numberOfReadBytes = activeMQBytesMessage.readBytes(bytes);

    if (numberOfReadBytes != bytes.length) {
      throw new IllegalStateException("bytes.length : " + bytes.length + ", numberOfReadBytes: " + numberOfReadBytes);
    }

    return bytes;
  }

  private static void sendObjectRequest(Serializable payload, String fullQueueName, String messageType, String messageVersion) throws Exception {
    sendObjectRequest(payload, fullQueueName, messageType, messageVersion, null, null);
  }

  private static void sendObjectRequest(Serializable payload, String fullQueueName, String messageType, String messageVersion, CorrelationId correlationId,
      ReplyTo replyTo) throws Exception {
    try (Connection connection = createActiveMQConnectionFactory().createConnection()) {
      connection.start();
      try (Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE)) {
        Message message = getTextMessage(payload, session);

        message.setStringProperty(TispJmsHeader.MESSAGE_TYPE.value(), messageType);
        message.setStringProperty(TispJmsHeader.MESSAGE_TYPE_VERSION.value(), messageVersion);

        if (correlationId != null) {
          message.setJMSCorrelationID(correlationId.toString());
        }
        if (replyTo != null) {
          message.setJMSReplyTo(session.createQueue(replyTo.toString()));
        }

        Destination destination = session.createQueue(fullQueueName);

        try (MessageProducer messageProducer = session.createProducer(destination)) {
          messageProducer.setDeliveryMode(DeliveryMode.NON_PERSISTENT);
          messageProducer.send(message);
        }
      } catch (JsonProcessingException e) {
        throw new RuntimeException(e);
      }
    }
  }

  private static void sendRequest(Object request, String fullQueueName, String messageType, String messageVersion, Optional<String> replyToHeader,
      Optional<String> correlationId) throws Exception {
    String xmlString = IntegrationTestHelper.marshalToXml(request);

    ConnectionFactory connectionFactory = createActiveMQConnectionFactory();
    publishMessage(connectionFactory, fullQueueName, xmlString, messageType, messageVersion, replyToHeader, correlationId);
  }
}
