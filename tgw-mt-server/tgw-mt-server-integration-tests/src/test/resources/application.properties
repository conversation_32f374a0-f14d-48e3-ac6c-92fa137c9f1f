influx.event.enabled=false
pg-stat-reporting.enabled=false
spring.artemis.mode=native
spring.cloud.zookeeper.discovery.enabled=false
spring.liquibase.enabled=false
spring.flyway.enabled=false
spring.jpa.database=postgresql
spring.datasource.username=postgres
spring.datasource.password=postgres
management.influx.metrics.export.enabled=false
spring.profiles.include=transportSecured
connection-established.registration.failure.persist.enabled=true
bulk-sync.retain.processing-tags=false
bulk-sync.security.client.key-alias=tcetisp-test-1
bulk-sync.security.conrepo2.key-alias=tcetisp-test-1
server.host=127.0.0.1
server.port=12720
db.user=postgres
db.pw=postgres
db.maxpoolsize=4
db.minpoolsize=2
management.influx.metrics.export.db=vehicle_communication
management.influx.metrics.export.retention-policy=30days
scheduler.throttler.duration=PT0.5S
security.database.key=tcetisp-test
security.transport.notify.key.public=tcetisp-test
security.transport.key.private=tcetisp-test
security.keystore.path=src/test/resources/keystore.pkcs12
security.keystore.password=tcetisp
device-service.maximum-cache-size-in-bytes=1000000
servicediscovery.active-by-default=true
tce.jms.conrepo2.notify.device.changes.enable=true
mt.doorkeeper.threshold=3
mt.max-in-flight-messages-per-vehicle=2
mtschd.mt.queue.reuse=true
legacy.jms.mt.queue.name=${solution}.${site}.${environment}.MTSCHD.MT-MESSAGES
throttled-wait-timeout=PT1S
solution-service-monitoring[0].vehicle=1234567890ABCDEF1234567890ABCDEF
solution-service-monitoring[0].flows=position