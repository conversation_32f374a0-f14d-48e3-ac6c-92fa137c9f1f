package com.volvo.tisp.flow;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;
import org.reactivestreams.Publisher;
import org.reactivestreams.Subscriber;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.FluxSink;
import reactor.core.publisher.FluxSink.OverflowStrategy;
import reactor.core.scheduler.Scheduler;
import reactor.util.Logger;
import reactor.util.Loggers;
import reactor.util.context.Context;

/**
 * Randomly publishes messages on each provided {@link Scheduler} for provided {@link FlowComposer}
 *
 * @param <I> element type of input {@link Flux}
 * @param <O> element type of output {@link Publisher}
 * @see FlowComposer
 */
public class FlowBalancer<I, O> implements FlowComposer<I, O> {
  private static final Logger logger = Loggers.getLogger(FlowBalancer.class);

  private final FlowComposer<I, O> flowComposer;
  private final Collection<Scheduler> schedulers;
  private int roundRobinIndex = 0;

  public FlowBalancer(
      final FlowComposer<I, O> flowComposer, final Collection<Scheduler> schedulers) {
    this.flowComposer = flowComposer;
    this.schedulers = schedulers;
  }

  @Override
  @SuppressWarnings("unchecked")
  public Publisher<O> apply(final Flux<I> flux) {
    logger.info("Composing reactive flow");
    final Publisher<O>[] publishers =
        (Publisher<O>[]) Array.newInstance(Publisher.class, schedulers.size() + 1);
    final Map.Entry<Scheduler, FluxSink<I>>[] entries =
        (Map.Entry<Scheduler, FluxSink<I>>[]) Array.newInstance(Map.Entry.class, schedulers.size());
    int index = 0;
    for (final Scheduler scheduler : schedulers) {
      final int stupidFinalIndex = index;
      publishers[index] =
          Flux.<I>push(
                  sink -> entries[stupidFinalIndex] = Map.entry(scheduler, sink),
                  OverflowStrategy.IGNORE)
              .transform(flowComposer)
              .contextWrite(Context.of(Scheduler.class, scheduler));
      index++;
    }

    final FlowContainer flowContainer = new FlowContainer(entries);

    publishers[index] =
        flux.doOnComplete(flowContainer::complete).mapNotNull(flowContainer::balance);

    return Flux.merge(publishers);
  }

  /** */
  private class FlowContainer {
    private final Map.Entry<Scheduler, FluxSink<I>>[] entries;

    private FlowContainer(final Map.Entry<Scheduler, FluxSink<I>>[] entries) {
      this.entries = entries;
    }

    @Nullable
    private O balance(@NonNull final I object) {
      this.next(object);
      return null;
    }

    /**
     * @see Subscriber#onComplete()
     */
    private void complete() {
      for (final Map.Entry<Scheduler, FluxSink<I>> entry : entries) {
        if (entry != null) {
          entry.getValue().complete();
        }
      }
    }

    /**
     * Try emitting, might throw an unchecked exception.
     *
     * @see Subscriber#onNext(Object)
     * @param object the value to emit, not null
     * @implNote Ignore the race condition here, its already random who gets which {@link FluxSink}
     *     and {@link Scheduler} pair
     */
    private void next(@NonNull final I object) {
      int index;
      do {
        index = roundRobinIndex;
        if (index == entries.length) {
          index = 0;
          roundRobinIndex = 1;
        } else {
          roundRobinIndex = index + 1;
        }
      } while (entries[index] == null);
      final Map.Entry<Scheduler, FluxSink<I>> entry = entries[index];
      entry.getKey().schedule(() -> entry.getValue().next(object));
    }
  }
}
