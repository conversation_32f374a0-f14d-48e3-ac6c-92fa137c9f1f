package com.volvo.tisp.flow;

import java.time.Duration;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;
import org.reactivestreams.Publisher;
import reactor.core.publisher.Flux;
import reactor.util.Logger;
import reactor.util.Loggers;

/**
 * Flow composer that collects all flowing objects to a {@link Queue}.
 *
 * @param <T> element type of {@link Flux}
 */
public class FlowCollector<T> implements FlowComposer<T, T> {
  private static final Logger logger = Loggers.getLogger(FlowCollector.class);

  private final LinkedBlockingQueue<T> queue = new LinkedBlockingQueue<>();

  @Override
  public Publisher<T> apply(final Flux<T> flow) {
    logger.info("Composing reactive flow");
    return flow.doOnNext(queue::add);
  }

  /**
   * Returns next object from the buffer {@link Queue} in FIFO fashion
   *
   * @param waitDuration duration to wait for a next message
   * @return instance of &lt;T&gt;
   * @throws InterruptedException if waiting on a {@link Queue} gets interrupted
   */
  public T next(final Duration waitDuration) throws InterruptedException {
    return queue.poll(waitDuration.toMillis(), TimeUnit.MILLISECONDS);
  }

  /** Clear internal {@link Queue} */
  public void clear() {
    queue.clear();
  }
}
