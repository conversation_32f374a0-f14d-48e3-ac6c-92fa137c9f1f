package @@@PACKAGE@@@;

import java.math.BigInteger;

import org.jdom.Element;

/**
 * <p>Title: ASN Integer class.</p>
 * <p>Description: Part of ASN</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: WirelessCar</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ASNInteger extends ASNConstrainedValue {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	protected long m_value;
    
    public ASNInteger() {
        constrainType = UNBOUNDED_INTEGER;
    }
    
    public ASNValue createObject() throws ASNException {
        return new ASNInteger();
    }
    
    public long get() {
        return m_value;
    }
    
    public void set( long value ) {
        m_value = value;
    }

    public void encode( PERStream stream ) throws ASNException {
        if (constrainType == UNBOUNDED_INTEGER || constrainType == UNBOUNDED_LOW) {
            if (constrainType == UNBOUNDED_LOW && m_value > highBoundary) {
                throw new ASNException(
                        "Integer is out of range. Expected value in range (MIN.."
                        + ".." + highBoundary
                        + ") but was " + m_value);
            }
            BigInteger bi = BigInteger.valueOf(m_value);
            byte[] bs = bi.toByteArray();
            stream.encodeLengthDeterminant(bs.length);
            for (int i = 0; i < bs.length; i++) {
                stream.encodeByte(bs[i]);
            }
        }
        else if (constrainType == UNBOUNDED_HIGH) {
            if (m_value < lowBoundary) {
                throw new ASNException(
                        "Integer is out of range. Expected value in range ("
                                + lowBoundary + "..MAX" + ") but was "
                                + m_value);
                
            }
            long offsetVal = m_value - lowBoundary;
            int octets = offsetVal > 0 ? (countBits(0, offsetVal) + 7) / 8 : 1;
            
            stream.encodeLengthDeterminant(octets);
            stream.encodeInteger(offsetVal, octets * 8);
        }
        else {
            if (m_value < lowBoundary || m_value > highBoundary) {
                throw new ASNException(
                        "Integer is out of range. Expected value in range ("
                        + lowBoundary + ".." + highBoundary
                        + ") but was " + m_value);
            }
            stream.encodeInteger( m_value-lowBoundary, countBits( lowBoundary, highBoundary ) );
        }
    }
    
    public void decode( PERStream stream ) throws ASNException {
        if ( stream.isDebug() )
            System.out.println( "S " + stream.position() + " " + getAsnObjectName() + "\tASNInteger");
        
        if (constrainType == UNBOUNDED_INTEGER || constrainType == UNBOUNDED_LOW) {
            long numOctets = stream.decodeLengthDeterminant();
            byte[] bs = new byte[(int)numOctets];
            for (int i = 0; i < bs.length; i++) {
                bs[i] = stream.decodeByte();
            }
            BigInteger bi = new BigInteger(bs);
            m_value = bi.longValue();
            if (numOctets > 8) {
                System.err.println("Decoded an Integer with " + numOctets
                        + " octets. Max java Long is supported. The value "
                        + bi + " became " + m_value);
            }
        }
        else if (constrainType == UNBOUNDED_HIGH) {
            long numOctets = stream.decodeLengthDeterminant();
            if (numOctets > 0) {
                m_value = lowBoundary + stream.decodeInteger((int)numOctets*8);
            }
        }
        else {
            int bitCount = countBits( lowBoundary, highBoundary );
            // Special case to not destroy a zero size default value
            if ( bitCount > 0 )
            {	
                m_value = lowBoundary + stream.decodeInteger( bitCount );
            }
        }
        
        if ( stream.isDebug() )
        {	
            System.out.println( "E " + stream.position() + " " + getAsnObjectName() + "\tASNInteger");
        }
    }
    
    public void encode( Element oElement ) throws ASNException {
        Element oElem = new Element( getAsnObjectName() );
        oElem.addContent( Long.toString( m_value ) );
        oElement.addContent( oElem );
    }
    
    public void decode( Element oElement ) throws ASNException {
        set(Long.valueOf(oElement.getTextTrim()).longValue());
    }
    
    public long encodedSize() {
        if (constrainType == UNBOUNDED_INTEGER || constrainType == UNBOUNDED_LOW) {
            BigInteger bi = BigInteger.valueOf(m_value);
            byte[] bs = bi.toByteArray();
            try {
                return PERStream.getLengthDeterminatorSizeInBits(bs.length) + bs.length * 8;
            } catch (ASNException e) {
                throw new RuntimeException(e.getMessage(), e);
            }
        } else if (constrainType == UNBOUNDED_HIGH) {
            long offsetVal = m_value - lowBoundary;
            int octets = offsetVal > 0 ? (countBits(offsetVal) + 7) / 8 : 1;
            try {
                return super.encodedSize() + PERStream.getLengthDeterminatorSizeInBits(octets)+octets*8;
            } catch (ASNException e) {
                throw new RuntimeException(e.getMessage(), e);
            }
        }
        return super.encodedSize() + countBits( lowBoundary, highBoundary );
    }
    
    public void writeExternal( java.io.ObjectOutput out) throws java.io.IOException {
        super.writeExternal( out );
        out.writeLong( m_value );
    }
    
    public void readExternal( java.io.ObjectInput in) throws java.io.IOException, ClassNotFoundException {
        super.readExternal( in );
        m_value = in.readLong();
    }
}