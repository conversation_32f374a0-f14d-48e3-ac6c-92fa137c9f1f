/*
 * Created on 28 feb 2008
 * 
 * Copyright (c) 2008 WirelessCar. All Rights Reserved.
 * 
 * This SOURCE CODE FILE, which has been provided by WirelessCar as part of a
 * WirelessCar product for use ONLY by licensed users of the product, includes
 * CONFIDENTIAL and PROPRIETARY information of WirelessCar.
 */

package @@@PACKAGE@@@;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * 
 */
public class ASNUTF8String extends AbstractStringType {

    private static final String CHARSET_NAME = "UTF-8";

    public ASNUTF8String() {
        this("UTF8String");
    }

    public ASNUTF8String(String name) {
        super(name);
    }

    @Override
    public ASNValue createObject() throws ASNException {
        return new ASNUTF8String();
    }

    @Override
    public void decode(PERStream stream) throws ASNException {
        int size =(int) stream.decodeLengthDeterminant();
        byte[] buf = new byte[size];
        for (int i = 0; i < size; i++) {
            buf[i] = stream.decodeByte();
        }

        try {
            value = new String(buf, CHARSET_NAME);
        } catch (UnsupportedEncodingException e) {
            throw new ASNException(e.getMessage(), e);
        }
        
    }

    @Override
    public void encode(PERStream stream) throws ASNException {
        switch (constrainType) {
        case FIXED_SIZE:
            int size = (int) highBoundary + 1;
            if (value.length() != size) {
                throw new ASNException("String is out of range. Expected "
                        + size + " characters but was " + value.length());
            }
        case UNBOUNDED_COUNT:
            break;
        default:
            if (value.length() > highBoundary || value.length() < lowBoundary) {
                throw new ASNException("String is out of range. Expected ("
                        + lowBoundary + ".." + highBoundary
                        + ") characters but was " + value.length());
            }
            break;
        }

        byte[] buf;
        try {
            buf = value.getBytes(CHARSET_NAME);
        } catch (UnsupportedEncodingException e) {
            throw new ASNException(e.getMessage(), e);
        }

        stream.encodeLengthDeterminant(buf.length);
        
        for (int i = 0; i < buf.length; i++) {
            stream.encodeByte(buf[i]);
        }
        
    }

    public long encodedSize() {
        byte[] buf;
        try {
            buf = value.getBytes(CHARSET_NAME);
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
        int bufBits = buf.length * 8;
        try {
            return PERStream.getLengthDeterminatorSizeInBits(buf.length)
                    + bufBits;
        } catch (ASNException e) {
            throw new RuntimeException(e.getMessage(), e);
        }
    }
}
