package @@@PACKAGE@@@;

import java.util.Hashtable;
import java.util.List;

import org.jdom.Element;
import org.jdom.Text;

/**
 * <p>Title: ASN Enumeration base class.</p>
 * <p>Description: Part of ASN</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: WirelessCar</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ASNEnumeration extends ASNInteger {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
    
    //protected long m_value;
    
    public ASNEnumeration() {
    }
    
    protected Hashtable<String,Integer> getNameMap() {
    	return null;
    }
    
    protected Hashtable<Integer,String> getValueMap() {
    	return null;
    }
    
    public ASNValue createObject() throws ASNException {
        return new ASNEnumeration();
    }
    
    public long get() {
        return m_value;
    }
    
    public void set( long value ) {
        m_value = value;
    }
    
    public void encode( PERStream stream ) throws ASNException {
        stream.encodeInteger( m_value-lowBoundary, countBits( lowBoundary, highBoundary ) );
    }
    
    public void decode( PERStream stream ) throws ASNException {
        if ( stream.isDebug() )
        {	
            System.out.println( "S " + stream.position() + " " + getAsnObjectName() + "\tASNEnumeration");
        }
        
        m_value = lowBoundary + stream.decodeInteger( countBits( lowBoundary, highBoundary ) );
        
        if ( stream.isDebug() )
        {	
            System.out.println( "E " + stream.position() + " " + getAsnObjectName() + "\tASNEnumeration");
        }
    }
    
    public void encode( Element oElement ) throws ASNException {
        Element oElem = new Element( getAsnObjectName() );
        oElem.addContent( getValueAsString() );
        oElement.addContent( oElem );
    }
    
    public void decode( Element oElement ) throws ASNException {
        if (oElement.getName().equals(getAsnObjectName())) 
        {
            List content = oElement.getContent();
            
            if (content.size() == 1) 
            {
                Text enumValue = (Text) content.get(0);
                setValueAsString(enumValue.getText());
            }
            else 
            {
                throw new ASNException("Choice element '" + oElement.getName() + "' must have exactly one child!");
            }
        }
        else 
        {
            throw new ASNException("Element '" + oElement.getName() 
                    + "' can not be unserialized for ASN object of type '" 
                    + getAsnObjectName() + "'");
        }
    	
    }
    
    public long encodedSize() {
        return countBits( lowBoundary, highBoundary );
    }
    
    public String getValueAsString() {
    	Integer key = Integer.valueOf((int)m_value);
    	if ( getValueMap().containsKey(key))
    		return getValueMap().get(key);
    	else
    		return "UNDEFINED (" + key.toString() + ")";
    }
    
    public void setValueAsString(String newValue) throws ASNException {
        if ( getNameMap().containsKey(newValue))
            set(getNameMap().get(newValue));
        else
            throw new ASNException("Enum string not found");
    }
    
    public void writeExternal( java.io.ObjectOutput out) throws java.io.IOException {
        super.writeExternal( out );
        out.writeLong( m_value );
    }
    
    public void readExternal( java.io.ObjectInput in) throws java.io.IOException, ClassNotFoundException {
        super.readExternal( in );
        m_value = in.readLong();
    }
}