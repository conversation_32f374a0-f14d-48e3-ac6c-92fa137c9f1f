package @@@PACKAGE@@@;

import java.util.List;

import org.jdom.Element;

/**
 * <p>Title: ASN Array base class.</p>
 * <p>Description: Part of ASN</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: WirelessCar</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ASNArray extends ASNInteger {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	public java.util.ArrayList<ASNValue> m_Array;
    protected ASNInteger m_iReferenceSize = null;
    
    public ASNArray() {
    }
    
    public void encode( PERStream stream ) throws ASNException {
        if ( this.constrainType == VARIABLE_SIZE )
        {	
            if(m_Array.size()  < lowBoundary || m_Array.size() > highBoundary) {
                throw new ASNException("Array size breaks constraints low: " + lowBoundary + " high: " + highBoundary
                        + " actual size: " + m_Array.size());
            }
            stream.encodeInteger( m_Array.size()-lowBoundary, countBits( lowBoundary, highBoundary ) );
        }
        
        for ( int i = 0; i < m_Array.size(); i++ )
        {	
            m_Array.get(i).encode( stream );
        }
    }
    
    public void decode( PERStream stream ) throws ASNException {
        if ( stream.isDebug() )
        {	
            System.out.println( "S " + stream.position() + " " + getAsnObjectName() + "\tASNArray");
        }
        
        if ( this.constrainType == VARIABLE_SIZE )
        {		
            super.set( lowBoundary + stream.decodeInteger( countBits( lowBoundary, highBoundary ) ) );
        } 
        else if ( this.constrainType == FIXED_SIZE )
        {
            super.set(highBoundary + 1);
        }
        else 
        {	
            super.set( m_iReferenceSize.get() );
        }
        
        m_Array = new java.util.ArrayList<ASNValue>( (int)get() );
        for ( int i = 0; i < (int)get(); i++ )
        {
            ASNValue oNewObject = this.createObject();
            oNewObject.decode( stream );
            m_Array.add( i, oNewObject );
        }
        if ( stream.isDebug() )
        {	
            System.out.println( "E " + stream.position() + " " + getAsnObjectName() + "\tASNArray");
        }
    }
    
    public void encode( Element oElement ) throws ASNException {
        Element oArrayElem = new Element( getAsnObjectName() );
        oArrayElem.setAttribute( "size", Integer.toString( m_Array.size() ) );
        
        for ( int i = 0; i < m_Array.size(); i++ )
        {	
            m_Array.get(i).encode( oArrayElem );
        }
        
        oElement.addContent( oArrayElem );
    }
    
    public void decode( Element oElement ) throws ASNException {
        List<?> children = oElement.getChildren();
        if (children == null)
        {
            throw new ASNException("Null children returned for " + getAsnObjectName() + ". XML element " + oElement);
        }
        int size = children.size();
        setSize(size);
        for (int i = 0; i < size; i++)
        {
            Element e = (Element) children.get(i);
            ASNValue asnValue = m_Array.get(i);
            asnValue.decode(e);
        }
    }
    
    public long encodedSize() {
        long lSize = 0;
        
        if ( m_iReferenceSize ==  null )
        {	
            lSize += countBits( lowBoundary, highBoundary );
        }
        
        for ( int i = 0; i < m_Array.size(); i++ )
        {	
            lSize += m_Array.get(i).encodedSize();
        }
        return lSize;
    }
    
    void setReferenceSize( ASNInteger refVal ) {
        m_iReferenceSize = refVal;
    }
    
    public void setSize(long size) throws ASNException {
        if (this.constrainType == FIXED_SIZE && size != (this.highBoundary + 1))
        {
          throw new ASNException(
                    "Can not set size on fixed size array that differs from protocol definition: "
                            + getAsnObjectName());   
        }
        
        super.set(size);
        if (m_iReferenceSize != null)
        {	
            m_iReferenceSize.set(size);
        }
        m_Array = new java.util.ArrayList<ASNValue>((int)size);
        for (int i = 0; i < (int)size; i++)
        {
            m_Array.add(i, this.createObject());
        }
    }
    
    public long getSize() {
        return get();
    }
    
    public ASNValue createObject() throws ASNException {
        return new ASNArray();
    }
    
    public void writeExternal( java.io.ObjectOutput out) throws java.io.IOException {
        super.writeExternal( out );
        out.writeObject( m_Array );
    }
    
    @SuppressWarnings("unchecked")
	public void readExternal( java.io.ObjectInput in) throws java.io.IOException, ClassNotFoundException {
        super.readExternal( in );
        m_Array = ( java.util.ArrayList)in.readObject();
    }
}

