package @@@PACKAGE@@@;

import java.util.List;

import org.jdom.Element;

/**
 * <p>Title: ASN Array base class.</p>
 * <p>Description: Part of ASN</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: WirelessCar</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ASNCustomTerminatedArray extends ASNArray {
    
    private int terminationChar = 0;
    
    public ASNCustomTerminatedArray() {
    }
    
    public void encode( PERStream stream ) throws ASNException {
        for ( int i = 0; i < m_Array.size(); i++ )
        {	
            m_Array.get(i).encode( stream );
        }
        // Set the terminating byte
        stream.encodeInteger( terminationChar, 8 );
    }
    
    public void decode( PERStream stream ) throws ASNException {
        if ( stream.isDebug() )
        {	
            System.out.println( "S " + stream.position() + " " + getAsnObjectName() + "\tASNNullTerminatedArray");
        }
        
        m_Array = new java.util.ArrayList<ASNValue>();
        
        for ( int i = 0; stream.peekInteger(8) != terminationChar ; i++ )
        {
            ASNValue oNewObject = this.createObject();
            oNewObject.decode( stream );
            m_Array.add( i, oNewObject );
        }
        set(m_Array.size());
        if (m_iReferenceSize != null)
        {	
            m_iReferenceSize.set(m_Array.size());
        }
        // Swallow the ending zero
        stream.decodeInteger(8);
        
        if ( stream.isDebug() )
        {	
            System.out.println( "E " + stream.position() + " " + getAsnObjectName() + "\tASNArray");
        }
    }
    
    public void encode( Element oElement ) throws ASNException {
        Element oArrayElem = new Element( getAsnObjectName() );
        oArrayElem.setAttribute( "size", Integer.toString( m_Array.size() ) );
        
        for ( int i = 0; i < m_Array.size(); i++ )
        {	
            m_Array.get(i).encode( oArrayElem );
        }
        
        oElement.addContent( oArrayElem );
    }
    
    public void decode( Element oElement ) throws ASNException {
        List<?> children = oElement.getChildren();
        if (children == null)
        {
            throw new ASNException("Null children returned for " + getAsnObjectName() + ". XML element " + oElement);
        }
        int size = children.size();
        setSize(size);
        for (int i = 0; i < size; i++)
        {
            Element e = (Element) children.get(i);
            ASNValue asnValue = m_Array.get(i);
            asnValue.decode(e);
        }
    }
    
    public long encodedSize() {
        long lSize = 0;
              
        for ( int i = 0; i < m_Array.size(); i++ )
        {	
            lSize += m_Array.get(i).encodedSize();
        }
        // The terminating zero
        lSize += 8;

        return lSize;
    }
    
    void setReferenceSize( ASNInteger refVal ) {
        m_iReferenceSize = refVal;
    }
    
    public void setSize(long size) throws ASNException {
        super.set(size);
        if (m_iReferenceSize != null)
        {	
            m_iReferenceSize.set(size);
        }
        m_Array = new java.util.ArrayList<ASNValue>();
        m_Array.ensureCapacity((int)size);
        for (int i = 0; i < (int)size; i++)
        {
            m_Array.add(i, this.createObject());
        }
    }
    
    public long getSize() {
        return get();
    }
    
    public ASNValue createObject() throws ASNException {
        return new ASNCustomTerminatedArray();
    }
    
    public int getTerminationChar() {
    	return terminationChar;
    }
    
    public void setTerminationChar( int terminationChar ) {
    	this.terminationChar = terminationChar;
    }
    
    public void writeExternal( java.io.ObjectOutput out) throws java.io.IOException {
        super.writeExternal( out );
        out.writeObject( m_Array );
    }
    
    @SuppressWarnings("unchecked")
	public void readExternal( java.io.ObjectInput in) throws java.io.IOException, ClassNotFoundException {
        super.readExternal( in );
        m_Array = ( java.util.ArrayList)in.readObject();
    }
}

