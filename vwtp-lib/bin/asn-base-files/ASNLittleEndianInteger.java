package @@@PACKAGE@@@;

/**
 * <p>Title: ASN Little Endian Integer base class.</p>
 * <p>Description: Part of ASN</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: WirelessCar</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ASNLittleEndianInteger extends ASNInteger {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	public ASNLittleEndianInteger() {
    }
    
    public ASNValue createObject() throws ASNException {
        return new ASNLittleEndianInteger();
    }
    
    public void encode( PERStream stream ) throws ASNException {
        int bitCount = countBits( lowBoundary, highBoundary );
        
        if ( bitCount == 16 || bitCount == 32 )
        {	
            stream.encodeLittleEndianInteger( m_value-lowBoundary, bitCount );
        }
        else
        {	
            stream.encodeInteger( m_value-lowBoundary, bitCount );
        }
    }
    
    public void decode( PERStream stream ) throws ASNException {
        if ( stream.isDebug() )
        {	
            System.out.println( "S " + stream.position() + " " + getAsnObjectName() + "\tASNInteger");
        }
        int bitCount = countBits( lowBoundary, highBoundary );
        // Special case to not destroy a zero size default value
        if ( bitCount > 0 )
        {
            if ( bitCount == 16 || bitCount == 32 )
            {	
                m_value = lowBoundary + stream.decodeLittleEndianInteger( bitCount );
            }
            else
            {	
                m_value = lowBoundary + stream.decodeInteger( bitCount );
            }
        }
        if ( stream.isDebug() )
        {	
            System.out.println( "E " + stream.position() + " " + getAsnObjectName() + "\tASNInteger");
        }
    }
}