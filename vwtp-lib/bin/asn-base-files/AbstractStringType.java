/*
 * Created on 28 feb 2008
 * 
 * Copyright (c) 2008 WirelessCar. All Rights Reserved.
 * 
 * This SOURCE CODE FILE, which has been provided by WirelessCar as part of a
 * WirelessCar product for use ONLY by licensed users of the product, includes
 * CONFIDENTIAL and PROPRIETARY information of WirelessCar.
 */

package @@@PACKAGE@@@;

import java.io.UnsupportedEncodingException;

import org.jdom.Element;

/**
 * <AUTHOR>
 * 
 */
public abstract class AbstractStringType extends ASNConstrainedValue {

    private static final long serialVersionUID = 1L;

    protected String value = null;

    protected AbstractStringType(String name) {
        // If any constraints apply it will be set after constructor. 
        constrainType = UNBOUNDED_COUNT;
        setAsnObjectName(name);
    }

    public void set(String val) {
        this.value = val;
    }

    public String get() {
        return value;
    }

    public long getSize() {
        return value.length();
    }


    public byte[] getAsByteArray(String charsetName)
            throws UnsupportedEncodingException {
        return value.getBytes(charsetName);
    }

    @Override
    public abstract ASNValue createObject() throws ASNException;

    @Override
    public abstract void encode(PERStream stream) throws ASNException;

    @Override
    public abstract void decode(PERStream stream) throws ASNException;

    @Override
    public abstract long encodedSize();
    
    public void encode(Element oElement) throws ASNException {
        Element oElem = new Element(getAsnObjectName());
        oElem.addContent(get());
        oElement.addContent(oElem);
    }

    public void decode(Element oElement) throws ASNException {
        set(oElement.getTextTrim());
    }

    public void writeExternal(java.io.ObjectOutput out)
            throws java.io.IOException {
        super.writeExternal(out);
        out.writeUTF(value);
    }

    public void readExternal(java.io.ObjectInput in)
            throws java.io.IOException, ClassNotFoundException {
        super.readExternal(in);
        value = in.readUTF();
    }

}
