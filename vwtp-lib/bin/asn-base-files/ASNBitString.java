package @@@PACKAGE@@@;

import java.io.IOException;
import java.io.ObjectInput;
import java.io.ObjectOutput;

import org.jdom.Element;

/**
 * <p>Title: ASN BitString class.</p>
 * <p>Description: Part of ASN</p>
 * <p>Copyright: Copyright (c) 2003</p>
 * <p>Company: WirelessCar</p>
 * <AUTHOR>
 * @version 1.0
 */
public class ASNBitString extends ASNConstrainedValue {
    
    /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private long value;
    
    public ASNBitString() {
    }
    
    public ASNValue createObject() throws ASNException {
        return new ASNBitString();
    }
    
    public long get() {
        return value;
    }
    
    public void set( long val ) {
        this.value = val;
    }
    
    void setFixedConstraint( long bits) {
        this.bits = ( int)bits;
    }
    
    public void encode( PERStream stream ) throws ASNException {
        stream.encodeInteger( value, bits );
    }
    
    public void decode( PERStream stream ) throws ASNException {
        value = stream.decodeInteger( bits );
    }
    
    public void encode( Element oElement ) throws ASNException {
        Element oElem = new Element( getAsnObjectName() );
        oElem.addContent( Long.toString( value ) );
        oElement.addContent( oElem );
    }
    
    public void decode( Element oElement ) throws ASNException {
        set(Long.valueOf(oElement.getTextTrim()).longValue());
    }
    
    public long encodedSize() {
        return super.encodedSize() + bits;
    }
    
    public void writeExternal( ObjectOutput out) throws IOException {
        super.writeExternal( out );
        out.writeLong( value );
    }
    
    public void readExternal( ObjectInput in) throws IOException, ClassNotFoundException {
        super.readExternal( in );
        value = in.readLong();
    }
}