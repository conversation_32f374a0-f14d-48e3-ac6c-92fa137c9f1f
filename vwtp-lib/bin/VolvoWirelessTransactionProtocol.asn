--
-- This file defines the ASN.1 notation of the
-- Volvo Wireless Transaction Protocol Data Units
--
-- NOTE !!!
--      Any chanes to this specification *MUST* be reviewed and approved by
--      VGCS: Vehicle Communication <<EMAIL>>
--          and
--      GTT: On-Board Connectivity Platform <?>
--
-- $Revision: 1.2 $
--
WirelessTransactionProtocol DEFINITIONS AUTOMATIC TAGS ::=
BEGIN

    -- =======================================================================
    --   W T P - Wireless Transaction Protocol
    -- =======================================================================
    WirelessTransactionPdu ::= SEQUENCE {
        con BOOLEAN,  -- Continuation flag
        pdu CHOICE {

            -- part of GTAG4, generations: TGW-1, TGW-2
            concatenated ConcatenatedInd,   -- possbibly implemented but never used in production
            invokePdu     InvokePdu,         -- used
            resultPdu     ResultPdu,         -- part of class 2 transactions and never used
            ackPdu        AckPdu,            -- used
            abortPdu      AbortPdu,          -- used
            segInvokePdu  SegInvokePdu,      -- used
            segResultPdu  SegResultPdu,      -- part of class 2 transactions and never used
            nackPdu       NackPdu,           -- used

            -- part of OCP, generation: TGW3
            invokePdu2    InvokePdu2,
            ackPdu2       AckPdu2,
            abortPdu2     AbortPdu2,
            segInvokePdu2 SegInvokePdu2,
            nackPdu2      NackPdu2,

            reserved13    NULL,
            reserved14    NULL,
            reserved15    NULL
        }
    }

    ConcatenatedInd ::= SEQUENCE {
        -- Just eat the whole Byte (must be 0x00 for a concatenated WTP message)
        res         INTEGER(0..7)
        -- After this indicator you should parse the whole payload by using:
        --   ConcatenatedMsgs        <--
        --                              |
        --      WirelessTransactionPdu --
    }

    ConcatenatedMsgs ::= SEQUENCE {
        pdu CHOICE {
            short   OCTET STRING (SIZE(0..127)),
            long    OCTET STRING (SIZE(0..32767))
        }
    }

    InvokePdu ::= SEQUENCE {
        gtr         BOOLEAN,           -- Group Trailer flag
        ttr         BOOLEAN,           -- Transmission Trailer flag
        rid         BOOLEAN,           -- Re-transmission Indicator
        tid         INTEGER(0..65535), -- Transaction ID
        version     INTEGER(0..3),     -- 0x00 (current version)
        tidNew      BOOLEAN,           -- Used when the initiator wrapped the TID
        userAck     BOOLEAN,           -- The initiator requests user acknowledgent
        res1        BOOLEAN,           -- Reserved
        res2        BOOLEAN,           -- Reserved
        tcl         INTEGER(0..3)      -- Transaction Class
    }

    ResultPdu ::= SEQUENCE {
        gtr         BOOLEAN,           -- Group Trailer flag
        ttr         BOOLEAN,           -- Transmission Trailer flag
        rid         BOOLEAN,           -- Re-transmission Indicator
        tid         INTEGER(0..65535)  -- Transaction ID
    }

    AckPdu ::= SEQUENCE {
        otr         BOOLEAN,           -- Tve/Tok (depends on direction), Oustanding Transaction (otr)
        res         BOOLEAN,           -- Reserved
        rid         BOOLEAN,           -- Re-transmission Indicator
        tid         INTEGER(0..65535)  -- Transaction ID
    }

    AbortPdu ::= SEQUENCE {
        abortType   INTEGER(0..7),      -- Abort type
        tid         INTEGER(0..65535),  -- Transaction ID
        abortReason INTEGER(0..255)     -- Abort reason
    }

    SegInvokePdu ::= SEQUENCE {
        gtr         BOOLEAN,           -- Group Trailer flag
        ttr         BOOLEAN,           -- Transmission Trailer flag
        rid         BOOLEAN,           -- Re-transmission Indicator
        tid         INTEGER(0..65535), -- Transaction ID
        psn         INTEGER(0..255)    -- Packet Sequence Number
    }

    SegResultPdu ::= SEQUENCE {
        gtr         BOOLEAN,           -- Group Trailer flag
        ttr         BOOLEAN,           -- Transmission Trailer flag
        rid         BOOLEAN,           -- Re-transmission Indicator
        tid         INTEGER(0..65535), -- Transaction ID
        psn         INTEGER(0..255)    -- Packet Sequence Number
    }

    NackPdu ::= SEQUENCE {
        res         INTEGER(0..3),      -- Reserved
        rid         BOOLEAN,            -- Re-transmission Indicator
        tid         INTEGER(0..65535),  -- Transaction ID

        -- Missing Packets (if empty then all packets are missing)
        mp          SEQUENCE (SIZE(0..255)) OF INTEGER(0..255)
    }

    TpiPdu ::= SEQUENCE {
        con         BOOLEAN,  -- Continuation flag
        tpiId       TpiIdentity,
        pdu         CHOICE {
            short   TpiShortPdu,
            long    TpiLongPdu
        }
    }

    TpiShortPdu ::= SEQUENCE {
        data        OCTET STRING (SIZE(0..3))
    }

    TpiLongPdu ::= SEQUENCE {
        res         INTEGER(0..3),      -- Reserved
        data        OCTET STRING (SIZE(0..255))
    }

    TpiIdentity ::= ENUMERATED {
        error      (0),
        info       (1),
        option     (2),
        psn        (3),
        sdu        (4),
        frame      (5),
        reserved6  (6),
        reserved7  (7),
        reserved8  (8),
        reserved9  (9),
        reserved10 (10),
        reserved11 (11),
        reserved12 (12),
        reserved13 (13),
        reserved14 (14),
        reserved15 (15)
    }

    -- =======================================================================
    --   V W T P - Volvo Wireless Transaction Protocol
    --
    --  specification: volvo-wap-224-wtp-20170615-a.pdf
    --  Volvo adaptions on the original WTP protocol.
    -- =======================================================================

    -- chapter 8.3.1
    InvokePdu2 ::= SEQUENCE {
        gtr         BOOLEAN,                -- Group Trailer flag
        ttr         BOOLEAN,                -- Transmission Trailer flag
        rid         BOOLEAN,                -- Re-transmission Indicator
        tid         INTEGER(0..65535),      -- Transaction ID
        version     INTEGER(0..3),          -- 0x01 (current version)
        tidNew      BOOLEAN,                -- Used when the initiator wrapped the TID
        userAck     BOOLEAN,                -- The initiator requests user acknowledgent
        res1        BOOLEAN,                -- Reserved
        res2        BOOLEAN,                -- Reserved
        tcl         INTEGER(0..3),          -- Transaction Class
        vid         INTEGER(0..4294967295) -- vehicle identifier (either SRP.obsAlias is used or TGW serial number)
    }

    -- chapter 8.3.3
    AckPdu2 ::= SEQUENCE {
        otr         BOOLEAN,                -- Tve/Tok (depends on direction), Oustanding Transaction (otr)
        res         BOOLEAN,                -- Reserved
        rid         BOOLEAN,                -- Re-transmission Indicator
        tid         INTEGER(0..65535),      -- Transaction ID
        vid         INTEGER(0..4294967295) -- vehicle identifier (either SRP.obsAlias is used or TGW serial number)
    }

    -- chapter 8.3.4
    AbortPdu2 ::= SEQUENCE {
        abortType   INTEGER(0..7),          -- Abort type
        tid         INTEGER(0..65535),      -- Transaction ID

        -- Standard abort reasons, chapter 8.3.4.2
        -- Volvo specific reasons, chapter 8.3.4.3
        --   0x80   The user did not support the SWAP service or version in service data unit.
        --          Onboard software can not continue processing.
        abortReason INTEGER(0..255),        -- Abort reason

        vid         INTEGER(0..4294967295) -- vehicle identifier (either SRP.obsAlias is used or TGW serial number)
    }

    -- chapter 8.3.5
    SegInvokePdu2 ::= SEQUENCE {
        gtr         BOOLEAN,                -- Group Trailer flag
        ttr         BOOLEAN,                -- Transmission Trailer flag
        rid         BOOLEAN,                -- Re-transmission Indicator
        tid         INTEGER(0..65535),      -- Transaction ID
        psn         INTEGER(0..255),        -- Packet Sequence Number
        vid         INTEGER(0..4294967295) -- vehicle identifier (either SRP.obsAlias is used or TGW serial number)
    }

    -- chapter 8.3.7
    NackPdu2 ::= SEQUENCE {
        res         INTEGER(0..3),          -- Reserved
        rid         BOOLEAN,                -- Re-transmission Indicator
        tid         INTEGER(0..65535),      -- Transaction ID

        -- Missing Packets (if empty then all packets are missing)
        mp          SEQUENCE (SIZE(0..255)) OF INTEGER(0..255),

        vid         INTEGER(0..4294967295) -- vehicle identifier (either SRP.obsAlias is used or TGW serial number)
    }

END
