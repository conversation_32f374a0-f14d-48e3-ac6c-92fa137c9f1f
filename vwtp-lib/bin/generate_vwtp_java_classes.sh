#!/bin/bash
# *********************************************************************************
#  Compilation of ASN.1 files for VWTP protocol
# *********************************************************************************

BASEDIR=`dirname $0`

ASN1PARSER="${BASEDIR}/asnparser-264.exe"
ASN1BASE="${BASEDIR}/asn-base-files"
SOURCE_FILE="${BASEDIR}/VolvoWirelessTransactionProtocol.asn"
TARGET_DIR="${BASEDIR}/../model/src/main/java/com/volvo/tisp/vwtp/codec"
JAVA_PACKAGE="com.volvo.tisp.vwtp.codec"

#--
echo "* create directory: ${TARGET_DIR}"
mkdir -p ${TARGET_DIR}

#--
echo "* remove java source files: ${TARGET_DIR}/*.java"
rm ${TARGET_DIR}/*.java

#--
echo "* copy asn base files: ${ASN1BASE} -> ${TARGET_DIR}"
cp -p ${ASN1BASE}/* ${TARGET_DIR}

#--
echo "* set java package: ${JAVA_PACKAGE}"
sed -i s/@@@PACKAGE@@@/${JAVA_PACKAGE}/g ${TARGET_DIR}/*

#--
echo "* run compiler: ${ASN1PARSER} -a -j -v -m ${PACKAGE} -o ${TARGET_DIR} ${SOURCE_FILE}"
${ASN1PARSER} -a -j -v -m ${JAVA_PACKAGE} -o ${TARGET_DIR} ${SOURCE_FILE}
