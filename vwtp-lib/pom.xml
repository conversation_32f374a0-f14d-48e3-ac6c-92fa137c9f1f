<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>154</version>
  </parent>

  <groupId>com.volvo.tisp.vwtp</groupId>
  <artifactId>vwtp-lib</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>

  <modules>
    <module>vwtp-api</module>
    <module>vwtp-impl</module>
    <module>vwtp-model</module>
    <module>vwtp-uml</module>
    <module>vwtp-benchmark</module>
  </modules>

  <properties>
    <component.long.name>vwtp-lib</component.long.name>
    <component.short.name>vwtp-lib</component.short.name>
    <jdom.version>2.0.2</jdom.version>
    <tisp-dependencies.version>153</tisp-dependencies.version>
    <component-commons.version>47</component-commons.version>
    <blockhound.version>1.0.10.RELEASE</blockhound.version>
    <unit-test-lib.version>65</unit-test-lib.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.tools</groupId>
        <artifactId>blockhound</artifactId>
        <version>${blockhound.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom</artifactId>
        <version>${jdom.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>unit-test-lib</artifactId>
        <version>${unit-test-lib.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.connectivity.common</groupId>
        <artifactId>component-commons</artifactId>
        <version>${component-commons.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <trimStackTrace>false</trimStackTrace>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.jacoco</groupId>
          <artifactId>jacoco-maven-plugin</artifactId>
          <executions>
            <execution>
              <id>default-prepare-agent</id>
              <goals>
                <goal>prepare-agent</goal>
              </goals>
            </execution>
            <execution>
              <id>default-report</id>
              <goals>
                <goal>report</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <configuration>
            <trimStackTrace>false</trimStackTrace>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <id>format-check</id>
      <activation>
        <property>
          <name>de_task</name>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>com.diffplug.spotless</groupId>
            <artifactId>spotless-maven-plugin</artifactId>
            <executions>
              <execution>
                <goals>
                  <goal>check</goal>
                </goals>
                <phase>initialize</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
