//
// ConcatenatedMsgs.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** ConcatenatedMsgs. */
public class ConcatenatedMsgs extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ConcatenatedMsgs_pdu m_pdu = new ConcatenatedMsgs_pdu();

  public ConcatenatedMsgs() {
    setAsnObjectName("ConcatenatedMsgs");
    m_pdu.setAsnObjectName("pdu");
  }

  public void decode(PERStream stream) throws ASNException {
    m_pdu.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_pdu.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_pdu.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_pdu.encodedSize();
    return length;
  }

  public ConcatenatedMsgs_pdu getPdu() {
    return m_pdu;
  }

  public void setPdu(ConcatenatedMsgs_pdu value) {
    m_pdu = value;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_pdu.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_pdu.readExternal(in);
  }
}

// End of ConcatenatedMsgs.java
