//
// WirelessTransactionPdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** WirelessTransactionPdu. */
public class WirelessTransactionPdu extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNBoolean m_con = new ASNBoolean();
  WirelessTransactionPdu_pdu m_pdu = new WirelessTransactionPdu_pdu();

  public WirelessTransactionPdu() {
    setAsnObjectName("WirelessTransactionPdu");
    m_con.setAsnObjectName("con");
    m_pdu.setAsnObjectName("pdu");
  }

  public void decode(PERStream stream) throws ASNException {
    m_con.decode(stream);
    m_pdu.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_con.encode(stream);
    m_pdu.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_con.encode(oElem);
    m_pdu.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_con.encodedSize();
    length += m_pdu.encodedSize();
    return length;
  }

  public boolean getCon() {
    return m_con.get();
  }

  public void setCon(boolean value) {
    m_con.set(value);
  }

  public WirelessTransactionPdu_pdu getPdu() {
    return m_pdu;
  }

  public void setPdu(WirelessTransactionPdu_pdu value) {
    m_pdu = value;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_con.writeExternal(out);
    m_pdu.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_con.readExternal(in);
    m_pdu.readExternal(in);
  }
}

// End of WirelessTransactionPdu.java
