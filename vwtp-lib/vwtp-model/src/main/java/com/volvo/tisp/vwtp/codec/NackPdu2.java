//
// NackPdu2.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** NackPdu2. */
public class NackPdu2 extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNInteger m_res = new ASNInteger();
  ASNBoolean m_rid = new ASNBoolean();
  ASNInteger m_tid = new ASNInteger();
  NackPdu2_mp m_mp = new NackPdu2_mp();
  ASNInteger m_vid = new ASNInteger();

  public NackPdu2() {
    setAsnObjectName("NackPdu2");
    m_res.setAsnObjectName("res");
    m_res.setFixedConstraint(0L, 3L);
    m_rid.setAsnObjectName("rid");
    m_tid.setAsnObjectName("tid");
    m_tid.setFixedConstraint(0L, 65535L);
    m_mp.setAsnObjectName("mp");
    m_vid.setAsnObjectName("vid");
    m_vid.setFixedConstraint(0L, 4294967295L);
  }

  public void decode(PERStream stream) throws ASNException {
    m_res.decode(stream);
    m_rid.decode(stream);
    m_tid.decode(stream);
    m_mp.decode(stream);
    m_vid.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_res.encode(stream);
    m_rid.encode(stream);
    m_tid.encode(stream);
    m_mp.encode(stream);
    m_vid.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_res.encode(oElem);
    m_rid.encode(oElem);
    m_tid.encode(oElem);
    m_mp.encode(oElem);
    m_vid.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_res.encodedSize();
    length += m_rid.encodedSize();
    length += m_tid.encodedSize();
    length += m_mp.encodedSize();
    length += m_vid.encodedSize();
    return length;
  }

  public long getRes() {
    return m_res.get();
  }

  public void setRes(long value) {
    m_res.set(value);
  }

  public boolean getRid() {
    return m_rid.get();
  }

  public void setRid(boolean value) {
    m_rid.set(value);
  }

  public long getTid() {
    return m_tid.get();
  }

  public void setTid(long value) {
    m_tid.set(value);
  }

  public NackPdu2_mp getMp() {
    return m_mp;
  }

  public void setMp(NackPdu2_mp value) {
    m_mp = value;
  }

  public long getVid() {
    return m_vid.get();
  }

  public void setVid(long value) {
    m_vid.set(value);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_res.writeExternal(out);
    m_rid.writeExternal(out);
    m_tid.writeExternal(out);
    m_mp.writeExternal(out);
    m_vid.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_res.readExternal(in);
    m_rid.readExternal(in);
    m_tid.readExternal(in);
    m_mp.readExternal(in);
    m_vid.readExternal(in);
  }
}

// End of NackPdu2.java
