//
// ConcatenatedInd.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** ConcatenatedInd. */
public class ConcatenatedInd extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNInteger m_res = new ASNInteger();

  public ConcatenatedInd() {
    setAsnObjectName("ConcatenatedInd");
    m_res.setAsnObjectName("res");
    m_res.setFixedConstraint(0L, 7L);
  }

  public void decode(PERStream stream) throws ASNException {
    m_res.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_res.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_res.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_res.encodedSize();
    return length;
  }

  public long getRes() {
    return m_res.get();
  }

  public void setRes(long value) {
    m_res.set(value);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_res.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_res.readExternal(in);
  }
}

// End of ConcatenatedInd.java
