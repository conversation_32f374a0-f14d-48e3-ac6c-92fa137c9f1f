package com.volvo.tisp.vwtp.codec;

import java.lang.reflect.Method;
import java.util.Hashtable;
import java.util.List;
import org.jdom2.Element;

/**
 * Title: ASN Choice base class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ASNChoice extends ASNConstrainedValue {

  /** */
  private static final long serialVersionUID = 1L;

  protected int m_choiceId = -1;
  protected ASNValue m_value = null;
  protected ASNInteger m_iReferenceChoiceId = null;

  public ASNChoice() {}

  public ASNValue createObject() throws ASNException {
    return new ASNChoice();
  }

  protected Hashtable<String, Integer> getNameMap() {
    return null;
  }

  protected Hashtable<Integer, String> getValueMap() {
    return null;
  }

  public String getElemName() {
    return String.valueOf(m_choiceId);
  }

  /**
   * @return The type in the choice as an integer
   * @deprecated Use getChoice instead.
   */
  public int getTag() {
    return m_choiceId;
  }

  public int getChoice() {
    return m_choiceId;
  }

  public String getChoiceAsString() {
    return (String) getValueMap().get(Integer.valueOf(m_choiceId));
  }

  public void encode(PERStream stream) throws ASNException {
    if (m_iReferenceChoiceId == null) {
      if (m_choiceId < lowBoundary || m_choiceId > highBoundary)
        throw new ASNException("Illegal choice in " + getAsnObjectName());

      stream.encodeInteger(m_choiceId, countBits(lowBoundary, highBoundary));
    }
    m_value.encode(stream);
  }

  public void decode(PERStream stream) throws ASNException {
    if (stream.isDebug()) {
      System.out.println("S " + stream.position() + " " + getAsnObjectName() + "\tASNChoice");
    }

    if (m_iReferenceChoiceId != null) {
      setChoice((int) m_iReferenceChoiceId.get());
    } else {
      setChoice((int) stream.decodeInteger(countBits(lowBoundary, highBoundary)));
    }

    m_value.decode(stream);
    if (stream.isDebug()) {
      System.out.println("E " + stream.position() + " " + getAsnObjectName() + "\tASNChoice");
    }
  }

  public void encode(Element oElement) throws ASNException {
    Element oChoiceElem = new Element(getAsnObjectName());
    m_value.encode(oChoiceElem);
    oElement.addContent(oChoiceElem);
  }

  public void decode(Element oElement) throws ASNException {
    if (oElement.getName().equals(getAsnObjectName())) {
      List children = oElement.getChildren();

      if (children.size() == 1) {
        Element choice = (Element) children.get(0);
        String choiceName = choice.getName();
        String setMethodName =
            "set" + Character.toUpperCase(choiceName.charAt(0)) + choiceName.substring(1);

        try {
          Method method = getClass().getMethod(setMethodName, (Class[]) null);
          method.invoke(this, (Object[]) null);
        } catch (NoSuchMethodException e) {
          setChoice(choiceName);
        } catch (Exception e) {
          throw new ASNException(
              "Could not call method "
                  + getClass()
                  + "."
                  + setMethodName
                  + " or set new choice to:"
                  + choiceName,
              e);
        }
        m_value.decode(choice);
      } else {
        throw new ASNException(
            "Choice element '" + oElement.getName() + "' must have exactly one child!");
      }
    } else {
      throw new ASNException(
          "Element '"
              + oElement.getName()
              + "' can not be unserialized for ASN object of type '"
              + getAsnObjectName()
              + "'");
    }
  }

  /**
   * @return Sets the choice and creates the corresponding object
   * @deprecated Use setChoice() instead.
   */
  public void setTag(int iNewChoice) throws ASNException {
    setChoice(iNewChoice);
  }

  public void setChoice(String newChoice) throws ASNException {
    if (getNameMap().containsKey(newChoice)) setChoice(getNameMap().get(newChoice));
    else throw new ASNException("Choice string not found");
  }

  public void setChoice(int iNewChoice) throws ASNException {
    if (iNewChoice < lowBoundary || iNewChoice > highBoundary) {
      throw new ASNException(
          "Choice value: " + iNewChoice + " must be between " + lowBoundary + " - " + highBoundary);
    }
    m_choiceId = iNewChoice;
    if (m_iReferenceChoiceId != null) {
      m_iReferenceChoiceId.set(iNewChoice);
    }
    m_value = createObject();
  }

  protected void setChoiceValue(int choiceId, ASNValue value) {
    m_choiceId = choiceId;
    if (m_iReferenceChoiceId != null) {
      m_iReferenceChoiceId.set(choiceId);
    }
    m_value = value;
  }

  void setReferenceChoice(ASNInteger refChoice) {
    m_iReferenceChoiceId = refChoice;
  }

  public long encodedSize() {
    long retVal = 0;

    // If we have a normal choice we count the choice id size.
    if (m_iReferenceChoiceId == null) {
      retVal += countBits(lowBoundary, highBoundary);
    }
    if (m_value != null) {
      retVal += m_value.encodedSize();
    }
    return retVal;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    out.writeInt(m_choiceId);

    // If the choice id is valid, and only then we write the value to the stream.
    if (getValueMap().containsKey(Integer.valueOf(m_choiceId))) m_value.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);

    // We must read the choice from the stream even if we have a reference value that we should use.
    m_choiceId = in.readInt();
    if (m_iReferenceChoiceId != null) {
      m_choiceId = (int) m_iReferenceChoiceId.get();
    }

    // If the choice id is valid, and only then we read the value from the stream.
    if (getValueMap().containsKey(Integer.valueOf(m_choiceId))) {
      try {
        m_value = createObject();
      } catch (ASNException ex) {
        throw new ClassNotFoundException(
            "Invalid choice value in ASNChoice object: "
                + getAsnObjectName()
                + ", impossible to create a consistent object");
      }
      m_value.readExternal(in);
    }
  }
}
