//
// NackPdu2_mp.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

/** NackPdu2_mp. */
public class NackPdu2_mp extends ASNArray {
  private static final long serialVersionUID = 1L;

  public NackPdu2_mp() {
    setAsnObjectName("NackPdu2_mp");
    setFixedConstraint(0L, 255L);
  }

  public ASNInteger getArrayItem(int iIndex) {
    return (ASNInteger) m_Array.get(iIndex);
  }

  public void setArrayItem(int iIndex, ASNInteger value) {
    m_Array.set(iIndex, value);
  }

  public long get(int iIndex) {
    return ((ASNInteger) m_Array.get(iIndex)).get();
  }

  public void set(int iIndex, long value) {
    ((ASNInteger) m_Array.get(iIndex)).set(value);
  }

  public ASNValue createObject() throws ASNException {
    ASNInteger oObj = new ASNInteger();
    oObj.setFixedConstraint(0L, 255L);
    return oObj;
  }
}

// End of NackPdu2_mp.java
