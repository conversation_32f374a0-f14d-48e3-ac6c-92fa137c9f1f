/*
 * Created on 29 feb 2008
 *
 * Copyright (c) 2008 WirelessCar. All Rights Reserved.
 *
 * This SOURCE CODE FILE, which has been provided by WirelessCar as part of a
 * WirelessCar product for use ONLY by licensed users of the product, includes
 * CONFIDENTIAL and PROPRIETARY information of WirelessCar.
 */

package com.volvo.tisp.vwtp.codec;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 */
public class ASNBMPString extends AbstractKnownMultiplierString {

  private static final String CHARSET_NAME = "UTF-16BE";

  public ASNBMPString() {
    this("BMPString");
  }

  public ASNBMPString(String name) {
    super(name);
  }

  @Override
  public ASNValue createObject() throws ASNException {
    return new ASNBMPString();
  }

  @Override
  protected void encodeValue(PERStream stream) throws ASNException {
    byte[] buf;
    try {
      buf = value.getBytes(CHARSET_NAME);
    } catch (UnsupportedEncodingException e) {
      throw new ASNException(e.getMessage(), e);
    }
    for (int i = 0; i < buf.length; i++) {
      stream.encodeByte(buf[i]);
    }
  }

  @Override
  protected void decodeValue(PERStream stream, int size) throws ASNException {
    StringBuilder sb = new StringBuilder(size);
    for (int i = 0; i < size; i++) {
      byte b1 = stream.decodeByte();
      char c = (char) (b1 & 0xff);
      if ((b1 & 0x80) == 0x80) {
        byte b2 = stream.decodeByte();
        c = (char) ((c << 8) | (b2 & 0xff));
      }
      sb.append(c);
    }

    value = sb.toString();
  }

  @Override
  protected int getEncodedValueSizeInBits() throws ASNException {
    try {
      return value.getBytes(CHARSET_NAME).length * 8;
    } catch (UnsupportedEncodingException e) {
      throw new ASNException(e.getMessage(), e);
    }
  }

  @Override
  protected void assertValidValue() throws ASNException {
    // All chars possible
  }
}
