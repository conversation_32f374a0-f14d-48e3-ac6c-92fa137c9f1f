/*
 * Created on 19 feb 2008
 *
 * Copyright (c) 2008 WirelessCar. All Rights Reserved.
 *
 * This SOURCE CODE FILE, which has been provided by WirelessCar as part of a
 * WirelessCar product for use ONLY by licensed users of the product, includes
 * CONFIDENTIAL and PROPRIETARY information of WirelessCar.
 */

package com.volvo.tisp.vwtp.codec;

/**
 * <AUTHOR>
 */
public class ASNNumericString extends AbstractKnownMultiplierString {

  public ASNNumericString() {
    this("NumericString");
  }

  public ASNNumericString(String name) {
    super(name);
  }

  @Override
  public ASNValue createObject() throws ASNException {
    return new ASNNumericString();
  }

  @Override
  protected void decodeValue(PERStream stream, int size) throws ASNException {
    byte[] numBuf = new byte[size];
    for (int i = 0; i < size; i++) {
      numBuf[i] = stream.decodeBits(4);
    }

    value = getNumericString(numBuf);
  }

  @Override
  protected void encodeValue(PERStream stream) throws ASNException {
    byte[] numBuf = getNumericStringBytes(get());
    for (int i = 0; i < numBuf.length; i++) {
      stream.encodeBits(numBuf[i], 4);
    }
  }

  @Override
  protected int getEncodedValueSizeInBits() {
    return value.length() * 4;
  }

  private static String getNumericString(byte[] buf) throws ASNException {
    StringBuffer sb = new StringBuffer();
    for (int i = 0; i < buf.length; i++) {
      sb.append(getNumericStringChar(buf[i]));
    }
    return sb.toString();
  }

  private static char getNumericStringChar(int c) throws ASNException {
    switch (c) {
      case 0:
        return ' ';
      default:
        if (c >= 1 && c <= 10) {
          return (char) (c + 47); // 47 == '0' - 1
        }
    }
    throw new ASNException(
        "Character not in NumericString alphabet: 0x" + Integer.toHexString(c) + " (" + c + ")");
  }

  private static byte[] getNumericStringBytes(String string) throws ASNException {
    if (string == null || string.length() == 0) {
      return new byte[0];
    }
    byte[] buf = new byte[string.length()];
    for (int i = 0; i < string.length(); i++) {
      buf[i] = getNumericStringIndex(string.charAt(i));
    }
    return buf;
  }

  private static byte getNumericStringIndex(char c) throws ASNException {
    switch (c) {
      case ' ':
        return 0;
      default:
        if (c >= '0' && c <= '9') {
          return (byte) ((c - 47)); // 47 == '0' - 1
        }
    }
    throw new ASNException(
        "Character not in NumericString alphabet: 0x" + Integer.toHexString(c) + " ('" + c + "')");
  }

  @Override
  protected void assertValidValue() throws ASNException {
    for (int i = 0; i < value.length(); i++) {
      getNumericStringIndex(value.charAt(i));
    }
  }
}
