//
// AbortPdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** AbortPdu. */
public class AbortPdu extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNInteger m_abortType = new ASNInteger();
  ASNInteger m_tid = new ASNInteger();
  ASNInteger m_abortReason = new ASNInteger();

  public AbortPdu() {
    setAsnObjectName("AbortPdu");
    m_abortType.setAsnObjectName("abortType");
    m_abortType.setFixedConstraint(0L, 7L);
    m_tid.setAsnObjectName("tid");
    m_tid.setFixedConstraint(0L, 65535L);
    m_abortReason.setAsnObjectName("abortReason");
    m_abortReason.setFixedConstraint(0L, 255L);
  }

  public void decode(PERStream stream) throws ASNException {
    m_abortType.decode(stream);
    m_tid.decode(stream);
    m_abortReason.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_abortType.encode(stream);
    m_tid.encode(stream);
    m_abortReason.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_abortType.encode(oElem);
    m_tid.encode(oElem);
    m_abortReason.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_abortType.encodedSize();
    length += m_tid.encodedSize();
    length += m_abortReason.encodedSize();
    return length;
  }

  public long getAbortType() {
    return m_abortType.get();
  }

  public void setAbortType(long value) {
    m_abortType.set(value);
  }

  public long getTid() {
    return m_tid.get();
  }

  public void setTid(long value) {
    m_tid.set(value);
  }

  public long getAbortReason() {
    return m_abortReason.get();
  }

  public void setAbortReason(long value) {
    m_abortReason.set(value);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_abortType.writeExternal(out);
    m_tid.writeExternal(out);
    m_abortReason.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_abortType.readExternal(in);
    m_tid.readExternal(in);
    m_abortReason.readExternal(in);
  }
}

// End of AbortPdu.java
