//
// TpiPdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** TpiPdu. */
public class TpiPdu extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNBoolean m_con = new ASNBoolean();
  TpiIdentity m_tpiId = new TpiIdentity();
  TpiPdu_pdu m_pdu = new TpiPdu_pdu();

  public TpiPdu() {
    setAsnObjectName("TpiPdu");
    m_con.setAsnObjectName("con");
    m_tpiId.setAsnObjectName("tpiId");
    m_pdu.setAsnObjectName("pdu");
  }

  public void decode(PERStream stream) throws ASNException {
    m_con.decode(stream);
    m_tpiId.decode(stream);
    m_pdu.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_con.encode(stream);
    m_tpiId.encode(stream);
    m_pdu.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_con.encode(oElem);
    m_tpiId.encode(oElem);
    m_pdu.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_con.encodedSize();
    length += m_tpiId.encodedSize();
    length += m_pdu.encodedSize();
    return length;
  }

  public boolean getCon() {
    return m_con.get();
  }

  public void setCon(boolean value) {
    m_con.set(value);
  }

  public long getTpiId() {
    return m_tpiId.get();
  }

  public String getTpiIdLabel() {
    return m_tpiId.getValueAsString();
  }

  public void setTpiId(long value) {
    m_tpiId.set(value);
  }

  public TpiPdu_pdu getPdu() {
    return m_pdu;
  }

  public void setPdu(TpiPdu_pdu value) {
    m_pdu = value;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_con.writeExternal(out);
    m_tpiId.writeExternal(out);
    m_pdu.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_con.readExternal(in);
    m_tpiId.readExternal(in);
    m_pdu.readExternal(in);
  }
}

// End of TpiPdu.java
