//
// TpiIdentity.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import java.util.Hashtable;

/** TpiIdentity. */
public class TpiIdentity extends ASNEnumeration {
  private static final long serialVersionUID = 1L;

  protected static final Hashtable<String, Integer> m_nameToValue =
      new Hashtable<String, Integer>();
  protected static final Hashtable<Integer, String> m_valueToName =
      new Hashtable<Integer, String>();
  // Enumeration element identifier constants
  public static final int E_ERROR = 0;
  public static final int E_INFO = 1;
  public static final int E_OPTION = 2;
  public static final int E_PSN = 3;
  public static final int E_SDU = 4;
  public static final int E_FRAME = 5;
  public static final int E_RESERVED6 = 6;
  public static final int E_RESERVED7 = 7;
  public static final int E_RESERVED8 = 8;
  public static final int E_RESERVED9 = 9;
  public static final int E_RESERVED10 = 10;
  public static final int E_RESERVED11 = 11;
  public static final int E_RESERVED12 = 12;
  public static final int E_RESERVED13 = 13;
  public static final int E_RESERVED14 = 14;
  public static final int E_RESERVED15 = 15;

  static {
    m_nameToValue.put("error", Integer.valueOf(E_ERROR));
    m_valueToName.put(Integer.valueOf(E_ERROR), "error");
    m_nameToValue.put("info", Integer.valueOf(E_INFO));
    m_valueToName.put(Integer.valueOf(E_INFO), "info");
    m_nameToValue.put("option", Integer.valueOf(E_OPTION));
    m_valueToName.put(Integer.valueOf(E_OPTION), "option");
    m_nameToValue.put("psn", Integer.valueOf(E_PSN));
    m_valueToName.put(Integer.valueOf(E_PSN), "psn");
    m_nameToValue.put("sdu", Integer.valueOf(E_SDU));
    m_valueToName.put(Integer.valueOf(E_SDU), "sdu");
    m_nameToValue.put("frame", Integer.valueOf(E_FRAME));
    m_valueToName.put(Integer.valueOf(E_FRAME), "frame");
    m_nameToValue.put("reserved6", Integer.valueOf(E_RESERVED6));
    m_valueToName.put(Integer.valueOf(E_RESERVED6), "reserved6");
    m_nameToValue.put("reserved7", Integer.valueOf(E_RESERVED7));
    m_valueToName.put(Integer.valueOf(E_RESERVED7), "reserved7");
    m_nameToValue.put("reserved8", Integer.valueOf(E_RESERVED8));
    m_valueToName.put(Integer.valueOf(E_RESERVED8), "reserved8");
    m_nameToValue.put("reserved9", Integer.valueOf(E_RESERVED9));
    m_valueToName.put(Integer.valueOf(E_RESERVED9), "reserved9");
    m_nameToValue.put("reserved10", Integer.valueOf(E_RESERVED10));
    m_valueToName.put(Integer.valueOf(E_RESERVED10), "reserved10");
    m_nameToValue.put("reserved11", Integer.valueOf(E_RESERVED11));
    m_valueToName.put(Integer.valueOf(E_RESERVED11), "reserved11");
    m_nameToValue.put("reserved12", Integer.valueOf(E_RESERVED12));
    m_valueToName.put(Integer.valueOf(E_RESERVED12), "reserved12");
    m_nameToValue.put("reserved13", Integer.valueOf(E_RESERVED13));
    m_valueToName.put(Integer.valueOf(E_RESERVED13), "reserved13");
    m_nameToValue.put("reserved14", Integer.valueOf(E_RESERVED14));
    m_valueToName.put(Integer.valueOf(E_RESERVED14), "reserved14");
    m_nameToValue.put("reserved15", Integer.valueOf(E_RESERVED15));
    m_valueToName.put(Integer.valueOf(E_RESERVED15), "reserved15");
  }

  public TpiIdentity() {
    setAsnObjectName("TpiIdentity");
    // Set the default size based on the last enum value + 1
    // A manual size constraint may follow that superseed this
    setFixedSizeConstraint(16);
  }

  protected Hashtable<String, Integer> getNameMap() {
    return m_nameToValue;
  }

  protected Hashtable<Integer, String> getValueMap() {
    return m_valueToName;
  }
}

// End of TpiIdentity.java
