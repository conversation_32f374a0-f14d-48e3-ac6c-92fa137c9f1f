package com.volvo.tisp.vwtp.codec;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import org.jdom2.Document;
import org.jdom2.Element;
import org.jdom2.output.Format;
import org.jdom2.output.XMLOutputter;

/**
 * Title: ASN Value base class.
 *
 * <p>Description: Part of ASN. The base class of all ASN values
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ASNValue implements java.io.Externalizable {

  /** */
  private static final long serialVersionUID = 1L;

  private String sAsnObjectName = "Unknown";

  public ASNValue() {}

  ASNValue(String name) {
    this.sAsnObjectName = name;
  }

  void setAsnObjectName(String name) {
    this.sAsnObjectName = name;
  }

  public String getAsnObjectName() {
    return sAsnObjectName;
  }

  public void encode(PERStream stream) throws ASNException {}

  public void decode(PERStream stream) throws ASNException {}

  public void encode(Element oElement) throws ASNException {}

  public Element encode() throws ASNException {
    Element dummy = new Element("dummy");

    encode(dummy);
    Element theElement = (Element) dummy.getChildren().get(0);

    theElement.detach();
    return theElement;
  }

  public void decode(Element oElement) throws ASNException {
    throw new ASNException(
        "ASNValue.decode method should not be called. Too broad to do anything.");
  }

  public long encodedSize() {
    return 0;
  }

  public String toString() {
    String sXML = "";
    try {
      Document oDoc = new Document(encode());
      Format format = Format.getPrettyFormat();
      format.setEncoding("ISO-8859-1");
      format.setOmitDeclaration(true);
      XMLOutputter op = new XMLOutputter(format);

      // encode( oDoc.getRootElement() );
      sXML = op.outputString(oDoc);
    } catch (ASNException ex) {
      ex.printStackTrace();
    } catch (Exception e) {
      e.printStackTrace(System.err);
    }
    return sXML;
  }

  // Misc functions
  static int countBits(long range) {
    int nBits = 0;
    while (range > (1L << nBits)) {
      nBits++;
    }
    return nBits;
  }

  static int countBits(long lower, long upper) {
    return countBits(upper - lower + 1);
  }

  static int countBits(int lower, int upper) {
    return countBits(upper - lower + 1);
  }

  ASNValue createObject() throws ASNException {
    return new ASNValue();
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    // out.writeObject( name );
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    // name = ( String)in.readObject();
  }

  protected Method[] getSetMethods() {
    Method[] methods = getClass().getMethods();
    List<Method> setMethodList = new ArrayList<Method>();

    for (int i = 0; i < methods.length; i++) {
      // System.out.println("method: " + methods[i].getName());
      if (methods[i].getName().startsWith("set")) {
        setMethodList.add(methods[i]);
      }
    }

    return setMethodList.toArray(new Method[0]);
  }

  protected Method getSetMethod(String attribute) {
    Method[] setMethods = getSetMethods();
    Method matchingMethod = null;

    String methodName = "set" + Character.toUpperCase(attribute.charAt(0)) + attribute.substring(1);
    int i = 0;
    boolean found = false;
    while (!found && (i < setMethods.length)) {
      if (setMethods[i].getName().equals(methodName)) {
        matchingMethod = setMethods[i];
      }

      i++;
    }

    return matchingMethod;
  }

  protected Class<?> getArgType(Method method) {
    Class<?> c = null;

    c = method.getParameterTypes()[0];

    return c;
  }

  protected Field getField(Element element) throws ASNException {
    Field field = null;

    String elementName = element.getName();
    String fieldName = "m_" + elementName;
    try {
      field = getClass().getDeclaredField(fieldName);
    } catch (NoSuchFieldException e) {
      throw new ASNException(
          "No field called '" + fieldName + "' exists in ASN object '" + getAsnObjectName());
    }

    return field;
  }
}
