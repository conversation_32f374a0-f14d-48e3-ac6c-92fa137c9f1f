//
// TpiShortPdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import java.io.UnsupportedEncodingException;
import org.jdom2.Element;

/** TpiShortPdu. */
public class TpiShortPdu extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNOctetString m_data = new ASNOctetString();

  public TpiShortPdu() {
    setAsnObjectName("TpiShortPdu");
    m_data.setAsnObjectName("data");
    m_data.setFixedConstraint(0L, 3L);
  }

  public void decode(PERStream stream) throws ASNException {
    m_data.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_data.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_data.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_data.encodedSize();
    return length;
  }

  public byte[] getData() {
    return m_data.getAsByteArray();
  }

  public String getDataAsString() {
    return m_data.getAsString();
  }

  public String getData(String charSetName) throws UnsupportedEncodingException {
    return m_data.get(charSetName);
  }

  public void setData(byte[] value) {
    m_data.set(value);
  }

  public void setData(String value) {
    m_data.set(value);
  }

  public void setData(String value, String charSetName) throws UnsupportedEncodingException {
    m_data.set(value, charSetName);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_data.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_data.readExternal(in);
  }
}

// End of TpiShortPdu.java
