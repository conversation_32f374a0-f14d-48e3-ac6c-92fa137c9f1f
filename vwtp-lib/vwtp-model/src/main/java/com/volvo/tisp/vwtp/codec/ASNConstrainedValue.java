package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/**
 * Title: ASN ConstrainedValue base class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ASNConstrainedValue extends ASNValue {

  /** */
  private static final long serialVersionUID = 1L;

  static final int UNKNOWN = 0;
  static final int FIXED_SIZE = 1;
  static final int VARIABLE_SIZE = 2;
  static final int REFERENCE_SIZE = 3;
  static final int UNBOUNDED_COUNT = 4;
  static final int UNBOUNDED_INTEGER = 5;
  static final int UNBOUNDED_HIGH = 6;
  static final int UNBOUNDED_LOW = 7;

  protected int constrainType = UNKNOWN;

  protected int bits = 0;
  protected long lowBoundary = 0;
  protected long highBoundary = 0;

  public ASNConstrainedValue() {}

  public ASNValue createObject() throws ASNException {
    return new ASNConstrainedValue();
  }

  void setUnboundConstraint() {
    this.constrainType = UNBOUNDED_INTEGER;
    this.highBoundary = this.lowBoundary = 0;
  }

  void setFixedSizeConstraint(int highBoundary) {
    constrainType = FIXED_SIZE;
    // Only used by BitString as a fixed size of bits!!!
    this.bits = highBoundary;
    this.lowBoundary = 0L;
    this.highBoundary = highBoundary - 1L;
  }

  void setFixedConstraint(long highBoundary) {
    constrainType = FIXED_SIZE;
    // Only used by BitString as a fixed size of bits!!!
    this.bits = (int) highBoundary;
    this.lowBoundary = 0L;
    this.highBoundary = highBoundary - 1L;
  }

  void setFixedSizeConstraint(int lowBoundary, int highBoundary) {
    constrainType = VARIABLE_SIZE;
    this.lowBoundary = lowBoundary;
    this.highBoundary = highBoundary;
  }

  void setFixedConstraint(long lowBoundary, long highBoundary) {
    constrainType = VARIABLE_SIZE;
    this.lowBoundary = lowBoundary;
    this.highBoundary = highBoundary;
  }

  void setLowBoundaryConstraint(long lowBoundary) {
    constrainType = UNBOUNDED_HIGH;
    this.lowBoundary = lowBoundary;
  }

  void setHighBoundaryConstraint(long highBoundary) {
    constrainType = UNBOUNDED_LOW;
    this.highBoundary = highBoundary;
  }

  long getLowBoundary() {
    return lowBoundary;
  }

  long getHighBoundary() {
    return highBoundary;
  }

  public void SetConstraints(ASNConstrainedValue[] list) {

    // Start with a copy of the first union
    if (list.length > 0) {
      this.constrainType = list[0].constrainType;
      this.lowBoundary = list[0].lowBoundary;
      this.highBoundary = list[0].highBoundary;
      this.bits = list[0].bits;
    }
    for (ASNConstrainedValue entry : list) {

      if (this.constrainType == UNBOUNDED_INTEGER || entry.constrainType == UNBOUNDED_INTEGER) {
        setUnboundConstraint();
        return;
      }

      // Check if "half unbounded" -> "full unbounded"
      if (this.constrainType == UNBOUNDED_LOW && entry.constrainType == UNBOUNDED_HIGH) {
        setUnboundConstraint();
        return;
      }

      // Check if "half unbounded" -> "full unbounded"
      if (this.constrainType == UNBOUNDED_HIGH && entry.constrainType == UNBOUNDED_LOW) {
        setUnboundConstraint();
        return;
      }

      // Check if "bounded" -> "low unbounded"
      if (this.constrainType != UNBOUNDED_LOW && entry.constrainType == UNBOUNDED_LOW) {
        setHighBoundaryConstraint(entry.highBoundary);
        return;
      }

      // Check if "bounded" -> "high unbounded"
      if (this.constrainType != UNBOUNDED_HIGH && entry.constrainType == UNBOUNDED_HIGH) {
        setLowBoundaryConstraint(entry.lowBoundary);
        return;
      }

      if (entry.lowBoundary < this.lowBoundary) this.lowBoundary = entry.lowBoundary;

      if (entry.highBoundary > this.highBoundary) this.highBoundary = entry.highBoundary;
    }
  }

  public void encode(PERStream stream) throws ASNException {}

  public void decode(PERStream stream) throws ASNException {}

  public void encode(Element oElement) throws ASNException {}

  public void decode(Element oElement) throws ASNException {}

  public long encodedSize() {
    return 0;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    out.writeInt(constrainType);
    out.writeInt(bits);
    out.writeLong(lowBoundary);
    out.writeLong(highBoundary);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    constrainType = in.readInt();
    bits = in.readInt();
    lowBoundary = in.readLong();
    highBoundary = in.readLong();
  }
}
