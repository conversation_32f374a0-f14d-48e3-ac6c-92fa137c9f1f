//
// TpiLongPdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import java.io.UnsupportedEncodingException;
import org.jdom2.Element;

/** TpiLongPdu. */
public class TpiLongPdu extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNInteger m_res = new ASNInteger();
  ASNOctetString m_data = new ASNOctetString();

  public TpiLongPdu() {
    setAsnObjectName("TpiLongPdu");
    m_res.setAsnObjectName("res");
    m_res.setFixedConstraint(0L, 3L);
    m_data.setAsnObjectName("data");
    m_data.setFixedConstraint(0L, 255L);
  }

  public void decode(PERStream stream) throws ASNException {
    m_res.decode(stream);
    m_data.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_res.encode(stream);
    m_data.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_res.encode(oElem);
    m_data.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_res.encodedSize();
    length += m_data.encodedSize();
    return length;
  }

  public long getRes() {
    return m_res.get();
  }

  public void setRes(long value) {
    m_res.set(value);
  }

  public byte[] getData() {
    return m_data.getAsByteArray();
  }

  public String getDataAsString() {
    return m_data.getAsString();
  }

  public String getData(String charSetName) throws UnsupportedEncodingException {
    return m_data.get(charSetName);
  }

  public void setData(byte[] value) {
    m_data.set(value);
  }

  public void setData(String value) {
    m_data.set(value);
  }

  public void setData(String value, String charSetName) throws UnsupportedEncodingException {
    m_data.set(value, charSetName);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_res.writeExternal(out);
    m_data.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_res.readExternal(in);
    m_data.readExternal(in);
  }
}

// End of TpiLongPdu.java
