package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/**
 * Title: ASN Boolean class.
 *
 * <p>Description: Part of ASN
 *
 * <p>Copyright: Copyright (c) 2003
 *
 * <p>Company: WirelessCar
 *
 * <AUTHOR>
 * @version 1.0
 */
public class ASNBoolean extends ASNConstrainedValue {

  /** */
  private static final long serialVersionUID = 1L;

  private boolean m_value;

  public ASNBoolean() {}

  public ASNValue createObject() throws ASNException {
    return new ASNBoolean();
  }

  public boolean get() {
    return m_value;
  }

  public void set(boolean value) {
    m_value = value;
  }

  public void encode(PERStream stream) throws ASNException {
    stream.encodeBits((byte) (m_value ? 0x01 : 0x00), 1);
  }

  public void decode(PERStream stream) throws ASNException {
    if (stream.isDebug()) {
      System.out.println("S " + stream.position() + " " + getAsnObjectName() + "\tASNBoolean");
    }
    if (stream.decodeInteger(1) == 0x01) {
      m_value = true;
    } else {
      m_value = false;
    }
    if (stream.isDebug()) {
      System.out.println("E " + stream.position() + " " + getAsnObjectName() + "\tASNBoolean");
    }
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    oElem.addContent(m_value ? "true" : "false");
    oElement.addContent(oElem);
  }

  public void decode(Element oElement) throws ASNException {
    set(Boolean.valueOf(oElement.getTextTrim()).booleanValue());
  }

  public long encodedSize() {
    return 1;
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    out.writeBoolean(m_value);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_value = in.readBoolean();
  }
}
