//
// NackPdu_mp.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

/** NackPdu_mp. */
public class NackPdu_mp extends ASNArray {
  private static final long serialVersionUID = 1L;

  public NackPdu_mp() {
    setAsnObjectName("NackPdu_mp");
    setFixedConstraint(0L, 255L);
  }

  public ASNInteger getArrayItem(int iIndex) {
    return (ASNInteger) m_Array.get(iIndex);
  }

  public void setArrayItem(int iIndex, ASNInteger value) {
    m_Array.set(iIndex, value);
  }

  public long get(int iIndex) {
    return ((ASNInteger) m_Array.get(iIndex)).get();
  }

  public void set(int iIndex, long value) {
    ((ASNInteger) m_Array.get(iIndex)).set(value);
  }

  public ASNValue createObject() throws ASNException {
    ASNInteger oObj = new ASNInteger();
    oObj.setFixedConstraint(0L, 255L);
    return oObj;
  }
}

// End of NackPdu_mp.java
