/*
 * Created on 2004-okt-27
 *
 * Copyright (c) 2004 WirelessCar. All Rights Reserved.
 *
 * This SOURCE CODE FILE, which has been provided by WirelessCar as part of a
 * WirelessCar product for use ONLY by licensed users of the product, includes
 * CONFIDENTIAL and PROPRIETARY information of WirelessCar.
 */
package com.volvo.tisp.vwtp.codec;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 */
public class ASNPrintableString extends AbstractKnownMultiplierString {

  private static final String CHARSET_NAME = "US-ASCII";
  private static final String VALID_CHARS =
      "ABCDEFGHIJLKMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789 '()+,-./:=?";

  public ASNPrintableString() {
    this("PrintableString");
  }

  public ASNPrintableString(String name) {
    super(name);
  }

  public ASNValue createObject() throws ASNException {
    return new ASNPrintableString();
  }

  @Override
  protected void decodeValue(PERStream stream, int count) throws ASNException {
    StringBuilder sb = new StringBuilder(count);
    for (int i = 0; i < count; i++) {
      byte b1 = stream.decodeBits(7);
      char c = (char) (b1 & 0xff);
      sb.append(c);
    }

    value = sb.toString();
  }

  @Override
  protected void encodeValue(PERStream stream) throws ASNException {
    byte[] buf;
    try {
      buf = value.getBytes(CHARSET_NAME);
    } catch (UnsupportedEncodingException e) {
      throw new ASNException(e.getMessage(), e);
    }
    for (int i = 0; i < buf.length; i++) {
      stream.encodeBits(buf[i], 7);
    }
  }

  @Override
  protected int getEncodedValueSizeInBits() throws ASNException {
    try {
      return value.getBytes(CHARSET_NAME).length * 7;
    } catch (UnsupportedEncodingException e) {
      throw new ASNException(e.getMessage(), e);
    }
  }

  @Override
  protected void assertValidValue() throws ASNException {
    for (int i = 0; i < value.length(); i++) {
      if (!isValidChar(value.charAt(i))) {
        throw new ASNException(
            "Value contains character not allowed in PrintableString: '" + value.charAt(i) + "'");
      }
    }
  }

  private boolean isValidChar(char c) {
    return VALID_CHARS.indexOf(c) != -1;
  }
}
