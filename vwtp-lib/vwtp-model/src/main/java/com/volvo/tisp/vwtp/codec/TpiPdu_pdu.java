//
// TpiPdu_pdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import java.util.Hashtable;

/** TpiPdu_pdu. */
public class TpiPdu_pdu extends ASNChoice {
  private static final long serialVersionUID = 1L;

  protected static final Hashtable<String, Integer> m_nameToValue =
      new Hashtable<String, Integer>();
  protected static final Hashtable<Integer, String> m_valueToName =
      new Hashtable<Integer, String>();
  // Choice element identifier constants
  public static final int E_SHORT = 0;
  public static final int E_LONG = 1;

  static {
    m_nameToValue.put("short", Integer.valueOf(E_SHORT));
    m_valueToName.put(Integer.valueOf(E_SHORT), "short");
    m_nameToValue.put("long", Integer.valueOf(E_LONG));
    m_valueToName.put(Integer.valueOf(E_LONG), "long");
  }

  public TpiPdu_pdu() {
    setAsnObjectName("TpiPdu_pdu");
    setFixedSizeConstraint(2); // Might be superseeded by a user defined "FixedConstraint"
  }

  protected Hashtable<String, Integer> getNameMap() {
    return m_nameToValue;
  }

  protected Hashtable<Integer, String> getValueMap() {
    return m_valueToName;
  }

  public ASNValue createObject() throws ASNException {
    ASNValue oTmp = null;
    switch (m_choiceId) {
      case E_SHORT:
        oTmp = new TpiShortPdu();
        oTmp.setAsnObjectName("short");
        break;
      case E_LONG:
        oTmp = new TpiLongPdu();
        oTmp.setAsnObjectName("long");
        break;
      default:
        throw new ASNException("Undefined Choice value ( " + m_choiceId + " ) in type TpiPdu_pdu");
    }
    return oTmp;
  }

  public TpiShortPdu getShort() throws ASNException {
    if (m_choiceId != E_SHORT || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (TpiShortPdu) m_value;
  }

  public void setShort() throws ASNException {
    if (m_choiceId != E_SHORT || m_value == null) {
      setChoice(E_SHORT);
    }
  }

  public void setShort(TpiShortPdu value) {
    m_choiceId = E_SHORT;
    m_value = value;
  }

  public TpiLongPdu getLong() throws ASNException {
    if (m_choiceId != E_LONG || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (TpiLongPdu) m_value;
  }

  public void setLong() throws ASNException {
    if (m_choiceId != E_LONG || m_value == null) {
      setChoice(E_LONG);
    }
  }

  public void setLong(TpiLongPdu value) {
    m_choiceId = E_LONG;
    m_value = value;
  }
}

// End of TpiPdu_pdu.java
