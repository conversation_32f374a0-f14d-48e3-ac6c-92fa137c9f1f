//
// WirelessTransactionPdu_pdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import java.util.Hashtable;

/** WirelessTransactionPdu_pdu. */
public class WirelessTransactionPdu_pdu extends ASNChoice {
  private static final long serialVersionUID = 1L;

  protected static final Hashtable<String, Integer> m_nameToValue =
      new Hashtable<String, Integer>();
  protected static final Hashtable<Integer, String> m_valueToName =
      new Hashtable<Integer, String>();
  // Choice element identifier constants
  public static final int E_CONCATENATED = 0;
  public static final int E_INVOKEPDU = 1;
  public static final int E_RESULTPDU = 2;
  public static final int E_ACKPDU = 3;
  public static final int E_ABORTPDU = 4;
  public static final int E_SEGINVOKEPDU = 5;
  public static final int E_SEGRESULTPDU = 6;
  public static final int E_NACKPDU = 7;
  public static final int E_INVOKEPDU2 = 8;
  public static final int E_ACKPDU2 = 9;
  public static final int E_ABORTPDU2 = 10;
  public static final int E_SEGINVOKEPDU2 = 11;
  public static final int E_NACKPDU2 = 12;
  public static final int E_RESERVED13 = 13;
  public static final int E_RESERVED14 = 14;
  public static final int E_RESERVED15 = 15;

  static {
    m_nameToValue.put("concatenated", Integer.valueOf(E_CONCATENATED));
    m_valueToName.put(Integer.valueOf(E_CONCATENATED), "concatenated");
    m_nameToValue.put("invokePdu", Integer.valueOf(E_INVOKEPDU));
    m_valueToName.put(Integer.valueOf(E_INVOKEPDU), "invokePdu");
    m_nameToValue.put("resultPdu", Integer.valueOf(E_RESULTPDU));
    m_valueToName.put(Integer.valueOf(E_RESULTPDU), "resultPdu");
    m_nameToValue.put("ackPdu", Integer.valueOf(E_ACKPDU));
    m_valueToName.put(Integer.valueOf(E_ACKPDU), "ackPdu");
    m_nameToValue.put("abortPdu", Integer.valueOf(E_ABORTPDU));
    m_valueToName.put(Integer.valueOf(E_ABORTPDU), "abortPdu");
    m_nameToValue.put("segInvokePdu", Integer.valueOf(E_SEGINVOKEPDU));
    m_valueToName.put(Integer.valueOf(E_SEGINVOKEPDU), "segInvokePdu");
    m_nameToValue.put("segResultPdu", Integer.valueOf(E_SEGRESULTPDU));
    m_valueToName.put(Integer.valueOf(E_SEGRESULTPDU), "segResultPdu");
    m_nameToValue.put("nackPdu", Integer.valueOf(E_NACKPDU));
    m_valueToName.put(Integer.valueOf(E_NACKPDU), "nackPdu");
    m_nameToValue.put("invokePdu2", Integer.valueOf(E_INVOKEPDU2));
    m_valueToName.put(Integer.valueOf(E_INVOKEPDU2), "invokePdu2");
    m_nameToValue.put("ackPdu2", Integer.valueOf(E_ACKPDU2));
    m_valueToName.put(Integer.valueOf(E_ACKPDU2), "ackPdu2");
    m_nameToValue.put("abortPdu2", Integer.valueOf(E_ABORTPDU2));
    m_valueToName.put(Integer.valueOf(E_ABORTPDU2), "abortPdu2");
    m_nameToValue.put("segInvokePdu2", Integer.valueOf(E_SEGINVOKEPDU2));
    m_valueToName.put(Integer.valueOf(E_SEGINVOKEPDU2), "segInvokePdu2");
    m_nameToValue.put("nackPdu2", Integer.valueOf(E_NACKPDU2));
    m_valueToName.put(Integer.valueOf(E_NACKPDU2), "nackPdu2");
    m_nameToValue.put("reserved13", Integer.valueOf(E_RESERVED13));
    m_valueToName.put(Integer.valueOf(E_RESERVED13), "reserved13");
    m_nameToValue.put("reserved14", Integer.valueOf(E_RESERVED14));
    m_valueToName.put(Integer.valueOf(E_RESERVED14), "reserved14");
    m_nameToValue.put("reserved15", Integer.valueOf(E_RESERVED15));
    m_valueToName.put(Integer.valueOf(E_RESERVED15), "reserved15");
  }

  public WirelessTransactionPdu_pdu() {
    setAsnObjectName("WirelessTransactionPdu_pdu");
    setFixedSizeConstraint(16); // Might be superseeded by a user defined "FixedConstraint"
  }

  protected Hashtable<String, Integer> getNameMap() {
    return m_nameToValue;
  }

  protected Hashtable<Integer, String> getValueMap() {
    return m_valueToName;
  }

  public ASNValue createObject() throws ASNException {
    ASNValue oTmp = null;
    switch (m_choiceId) {
      case E_CONCATENATED:
        oTmp = new ConcatenatedInd();
        oTmp.setAsnObjectName("concatenated");
        break;
      case E_INVOKEPDU:
        oTmp = new InvokePdu();
        oTmp.setAsnObjectName("invokePdu");
        break;
      case E_RESULTPDU:
        oTmp = new ResultPdu();
        oTmp.setAsnObjectName("resultPdu");
        break;
      case E_ACKPDU:
        oTmp = new AckPdu();
        oTmp.setAsnObjectName("ackPdu");
        break;
      case E_ABORTPDU:
        oTmp = new AbortPdu();
        oTmp.setAsnObjectName("abortPdu");
        break;
      case E_SEGINVOKEPDU:
        oTmp = new SegInvokePdu();
        oTmp.setAsnObjectName("segInvokePdu");
        break;
      case E_SEGRESULTPDU:
        oTmp = new SegResultPdu();
        oTmp.setAsnObjectName("segResultPdu");
        break;
      case E_NACKPDU:
        oTmp = new NackPdu();
        oTmp.setAsnObjectName("nackPdu");
        break;
      case E_INVOKEPDU2:
        oTmp = new InvokePdu2();
        oTmp.setAsnObjectName("invokePdu2");
        break;
      case E_ACKPDU2:
        oTmp = new AckPdu2();
        oTmp.setAsnObjectName("ackPdu2");
        break;
      case E_ABORTPDU2:
        oTmp = new AbortPdu2();
        oTmp.setAsnObjectName("abortPdu2");
        break;
      case E_SEGINVOKEPDU2:
        oTmp = new SegInvokePdu2();
        oTmp.setAsnObjectName("segInvokePdu2");
        break;
      case E_NACKPDU2:
        oTmp = new NackPdu2();
        oTmp.setAsnObjectName("nackPdu2");
        break;
      case E_RESERVED13:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved13");
        break;
      case E_RESERVED14:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved14");
        break;
      case E_RESERVED15:
        oTmp = new ASNNull();
        oTmp.setAsnObjectName("reserved15");
        break;
      default:
        throw new ASNException(
            "Undefined Choice value ( " + m_choiceId + " ) in type WirelessTransactionPdu_pdu");
    }
    return oTmp;
  }

  public ConcatenatedInd getConcatenated() throws ASNException {
    if (m_choiceId != E_CONCATENATED || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ConcatenatedInd) m_value;
  }

  public void setConcatenated() throws ASNException {
    if (m_choiceId != E_CONCATENATED || m_value == null) {
      setChoice(E_CONCATENATED);
    }
  }

  public void setConcatenated(ConcatenatedInd value) {
    m_choiceId = E_CONCATENATED;
    m_value = value;
  }

  public InvokePdu getInvokePdu() throws ASNException {
    if (m_choiceId != E_INVOKEPDU || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (InvokePdu) m_value;
  }

  public void setInvokePdu() throws ASNException {
    if (m_choiceId != E_INVOKEPDU || m_value == null) {
      setChoice(E_INVOKEPDU);
    }
  }

  public void setInvokePdu(InvokePdu value) {
    m_choiceId = E_INVOKEPDU;
    m_value = value;
  }

  public ResultPdu getResultPdu() throws ASNException {
    if (m_choiceId != E_RESULTPDU || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ResultPdu) m_value;
  }

  public void setResultPdu() throws ASNException {
    if (m_choiceId != E_RESULTPDU || m_value == null) {
      setChoice(E_RESULTPDU);
    }
  }

  public void setResultPdu(ResultPdu value) {
    m_choiceId = E_RESULTPDU;
    m_value = value;
  }

  public AckPdu getAckPdu() throws ASNException {
    if (m_choiceId != E_ACKPDU || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (AckPdu) m_value;
  }

  public void setAckPdu() throws ASNException {
    if (m_choiceId != E_ACKPDU || m_value == null) {
      setChoice(E_ACKPDU);
    }
  }

  public void setAckPdu(AckPdu value) {
    m_choiceId = E_ACKPDU;
    m_value = value;
  }

  public AbortPdu getAbortPdu() throws ASNException {
    if (m_choiceId != E_ABORTPDU || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (AbortPdu) m_value;
  }

  public void setAbortPdu() throws ASNException {
    if (m_choiceId != E_ABORTPDU || m_value == null) {
      setChoice(E_ABORTPDU);
    }
  }

  public void setAbortPdu(AbortPdu value) {
    m_choiceId = E_ABORTPDU;
    m_value = value;
  }

  public SegInvokePdu getSegInvokePdu() throws ASNException {
    if (m_choiceId != E_SEGINVOKEPDU || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (SegInvokePdu) m_value;
  }

  public void setSegInvokePdu() throws ASNException {
    if (m_choiceId != E_SEGINVOKEPDU || m_value == null) {
      setChoice(E_SEGINVOKEPDU);
    }
  }

  public void setSegInvokePdu(SegInvokePdu value) {
    m_choiceId = E_SEGINVOKEPDU;
    m_value = value;
  }

  public SegResultPdu getSegResultPdu() throws ASNException {
    if (m_choiceId != E_SEGRESULTPDU || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (SegResultPdu) m_value;
  }

  public void setSegResultPdu() throws ASNException {
    if (m_choiceId != E_SEGRESULTPDU || m_value == null) {
      setChoice(E_SEGRESULTPDU);
    }
  }

  public void setSegResultPdu(SegResultPdu value) {
    m_choiceId = E_SEGRESULTPDU;
    m_value = value;
  }

  public NackPdu getNackPdu() throws ASNException {
    if (m_choiceId != E_NACKPDU || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (NackPdu) m_value;
  }

  public void setNackPdu() throws ASNException {
    if (m_choiceId != E_NACKPDU || m_value == null) {
      setChoice(E_NACKPDU);
    }
  }

  public void setNackPdu(NackPdu value) {
    m_choiceId = E_NACKPDU;
    m_value = value;
  }

  public InvokePdu2 getInvokePdu2() throws ASNException {
    if (m_choiceId != E_INVOKEPDU2 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (InvokePdu2) m_value;
  }

  public void setInvokePdu2() throws ASNException {
    if (m_choiceId != E_INVOKEPDU2 || m_value == null) {
      setChoice(E_INVOKEPDU2);
    }
  }

  public void setInvokePdu2(InvokePdu2 value) {
    m_choiceId = E_INVOKEPDU2;
    m_value = value;
  }

  public AckPdu2 getAckPdu2() throws ASNException {
    if (m_choiceId != E_ACKPDU2 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (AckPdu2) m_value;
  }

  public void setAckPdu2() throws ASNException {
    if (m_choiceId != E_ACKPDU2 || m_value == null) {
      setChoice(E_ACKPDU2);
    }
  }

  public void setAckPdu2(AckPdu2 value) {
    m_choiceId = E_ACKPDU2;
    m_value = value;
  }

  public AbortPdu2 getAbortPdu2() throws ASNException {
    if (m_choiceId != E_ABORTPDU2 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (AbortPdu2) m_value;
  }

  public void setAbortPdu2() throws ASNException {
    if (m_choiceId != E_ABORTPDU2 || m_value == null) {
      setChoice(E_ABORTPDU2);
    }
  }

  public void setAbortPdu2(AbortPdu2 value) {
    m_choiceId = E_ABORTPDU2;
    m_value = value;
  }

  public SegInvokePdu2 getSegInvokePdu2() throws ASNException {
    if (m_choiceId != E_SEGINVOKEPDU2 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (SegInvokePdu2) m_value;
  }

  public void setSegInvokePdu2() throws ASNException {
    if (m_choiceId != E_SEGINVOKEPDU2 || m_value == null) {
      setChoice(E_SEGINVOKEPDU2);
    }
  }

  public void setSegInvokePdu2(SegInvokePdu2 value) {
    m_choiceId = E_SEGINVOKEPDU2;
    m_value = value;
  }

  public NackPdu2 getNackPdu2() throws ASNException {
    if (m_choiceId != E_NACKPDU2 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (NackPdu2) m_value;
  }

  public void setNackPdu2() throws ASNException {
    if (m_choiceId != E_NACKPDU2 || m_value == null) {
      setChoice(E_NACKPDU2);
    }
  }

  public void setNackPdu2(NackPdu2 value) {
    m_choiceId = E_NACKPDU2;
    m_value = value;
  }

  public ASNNull getReserved13() throws ASNException {
    if (m_choiceId != E_RESERVED13 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public void setReserved13() throws ASNException {
    if (m_choiceId != E_RESERVED13 || m_value == null) {
      setChoice(E_RESERVED13);
    }
  }

  public void setReserved13(ASNNull value) {
    m_choiceId = E_RESERVED13;
    m_value = value;
  }

  public ASNNull getReserved14() throws ASNException {
    if (m_choiceId != E_RESERVED14 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public void setReserved14() throws ASNException {
    if (m_choiceId != E_RESERVED14 || m_value == null) {
      setChoice(E_RESERVED14);
    }
  }

  public void setReserved14(ASNNull value) {
    m_choiceId = E_RESERVED14;
    m_value = value;
  }

  public ASNNull getReserved15() throws ASNException {
    if (m_choiceId != E_RESERVED15 || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return (ASNNull) m_value;
  }

  public void setReserved15() throws ASNException {
    if (m_choiceId != E_RESERVED15 || m_value == null) {
      setChoice(E_RESERVED15);
    }
  }

  public void setReserved15(ASNNull value) {
    m_choiceId = E_RESERVED15;
    m_value = value;
  }
}

// End of WirelessTransactionPdu_pdu.java
