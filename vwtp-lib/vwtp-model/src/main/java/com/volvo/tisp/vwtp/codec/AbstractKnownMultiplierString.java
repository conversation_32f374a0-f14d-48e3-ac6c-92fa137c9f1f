/*
 * Created on 28 feb 2008
 *
 * Copyright (c) 2008 WirelessCar. All Rights Reserved.
 *
 * This SOURCE CODE FILE, which has been provided by WirelessCar as part of a
 * WirelessCar product for use ONLY by licensed users of the product, includes
 * CONFIDENTIAL and PROPRIETARY information of WirelessCar.
 */
package com.volvo.tisp.vwtp.codec;

/**
 * <AUTHOR>
 */
public abstract class AbstractKnownMultiplierString extends AbstractStringType {

  public AbstractKnownMultiplierString(String name) {
    super(name);
  }

  protected abstract void encodeValue(PERStream stream) throws ASNException;

  protected abstract void decodeValue(PERStream stream, int count) throws ASNException;

  protected abstract int getEncodedValueSizeInBits() throws ASNException;

  protected abstract void assertValidValue() throws ASNException;

  protected int decodeLength(PERStream stream) throws ASNException {
    switch (constrainType) {
      case UNBOUNDED_COUNT:
        return (int) stream.decodeLengthDeterminant();
      case FIXED_SIZE:
        return (int) highBoundary + 1;
      default:
        return (int) (lowBoundary + stream.decodeInteger(countBits(lowBoundary, highBoundary)));
    }
  }

  @Override
  public void decode(PERStream stream) throws ASNException {
    if (stream.isDebug()) {
      System.out.println(
          "S " + stream.position() + " " + getAsnObjectName() + "\t" + getClass().getSimpleName());
    }

    int size = decodeLength(stream);
    decodeValue(stream, size);

    if (stream.isDebug()) {
      System.out.println(
          "E " + stream.position() + " " + getAsnObjectName() + "\t" + getClass().getSimpleName());
    }
  }

  protected void encodeLength(PERStream stream) throws ASNException {
    int len = value.length();
    if (constrainType == UNBOUNDED_COUNT) {
      stream.encodeLengthDeterminant(len);
    } else if (constrainType != FIXED_SIZE) {
      // Range constraint
      if (len > highBoundary || len < lowBoundary) {
        throw new ASNException(
            "String is out of range. Expected ("
                + lowBoundary
                + ".."
                + highBoundary
                + ") characters but was "
                + len);
      }
      stream.encodeInteger(len - lowBoundary, countBits(lowBoundary, highBoundary));
    } else {
      // Fixed size constraint
      int size = (int) highBoundary + 1;
      if (len != size) {
        throw new ASNException(
            "String is out of range. Expected " + size + " characters but was " + len);
      }
    }
  }

  @Override
  public void encode(PERStream stream) throws ASNException {
    if (value == null) {
      throw new ASNException("Cannot encode null value");
    }
    assertValidValue();
    encodeLength(stream);
    encodeValue(stream);
  }

  @Override
  public long encodedSize() {
    int bufBits;
    try {
      bufBits = getEncodedValueSizeInBits();
    } catch (ASNException e) {
      throw new RuntimeException(e.getMessage(), e);
    }

    switch (constrainType) {
      case UNBOUNDED_COUNT:
        try {
          return PERStream.getLengthDeterminatorSizeInBits(value.length()) + bufBits;
        } catch (ASNException e) {
          throw new RuntimeException(e.getMessage(), e);
        }
      case FIXED_SIZE:
        return bufBits;
      default:
        return countBits(lowBoundary, highBoundary) + bufBits;
    }
  }
}
