//
// InvokePdu2.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** InvokePdu2. */
public class InvokePdu2 extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNBoolean m_gtr = new ASNBoolean();
  ASNBoolean m_ttr = new ASNBoolean();
  ASNBoolean m_rid = new ASNBoolean();
  ASNInteger m_tid = new ASNInteger();
  ASNInteger m_version = new ASNInteger();
  ASNBoolean m_tidNew = new ASNBoolean();
  ASNBoolean m_userAck = new ASNBoolean();
  ASNBoolean m_res1 = new ASNBoolean();
  ASNBoolean m_res2 = new ASNBoolean();
  ASNInteger m_tcl = new ASNInteger();
  ASNInteger m_vid = new ASNInteger();

  public InvokePdu2() {
    setAsnObjectName("InvokePdu2");
    m_gtr.setAsnObjectName("gtr");
    m_ttr.setAsnObjectName("ttr");
    m_rid.setAsnObjectName("rid");
    m_tid.setAsnObjectName("tid");
    m_tid.setFixedConstraint(0L, 65535L);
    m_version.setAsnObjectName("version");
    m_version.setFixedConstraint(0L, 3L);
    m_tidNew.setAsnObjectName("tidNew");
    m_userAck.setAsnObjectName("userAck");
    m_res1.setAsnObjectName("res1");
    m_res2.setAsnObjectName("res2");
    m_tcl.setAsnObjectName("tcl");
    m_tcl.setFixedConstraint(0L, 3L);
    m_vid.setAsnObjectName("vid");
    m_vid.setFixedConstraint(0L, 4294967295L);
  }

  public void decode(PERStream stream) throws ASNException {
    m_gtr.decode(stream);
    m_ttr.decode(stream);
    m_rid.decode(stream);
    m_tid.decode(stream);
    m_version.decode(stream);
    m_tidNew.decode(stream);
    m_userAck.decode(stream);
    m_res1.decode(stream);
    m_res2.decode(stream);
    m_tcl.decode(stream);
    m_vid.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_gtr.encode(stream);
    m_ttr.encode(stream);
    m_rid.encode(stream);
    m_tid.encode(stream);
    m_version.encode(stream);
    m_tidNew.encode(stream);
    m_userAck.encode(stream);
    m_res1.encode(stream);
    m_res2.encode(stream);
    m_tcl.encode(stream);
    m_vid.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_gtr.encode(oElem);
    m_ttr.encode(oElem);
    m_rid.encode(oElem);
    m_tid.encode(oElem);
    m_version.encode(oElem);
    m_tidNew.encode(oElem);
    m_userAck.encode(oElem);
    m_res1.encode(oElem);
    m_res2.encode(oElem);
    m_tcl.encode(oElem);
    m_vid.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_gtr.encodedSize();
    length += m_ttr.encodedSize();
    length += m_rid.encodedSize();
    length += m_tid.encodedSize();
    length += m_version.encodedSize();
    length += m_tidNew.encodedSize();
    length += m_userAck.encodedSize();
    length += m_res1.encodedSize();
    length += m_res2.encodedSize();
    length += m_tcl.encodedSize();
    length += m_vid.encodedSize();
    return length;
  }

  public boolean getGtr() {
    return m_gtr.get();
  }

  public void setGtr(boolean value) {
    m_gtr.set(value);
  }

  public boolean getTtr() {
    return m_ttr.get();
  }

  public void setTtr(boolean value) {
    m_ttr.set(value);
  }

  public boolean getRid() {
    return m_rid.get();
  }

  public void setRid(boolean value) {
    m_rid.set(value);
  }

  public long getTid() {
    return m_tid.get();
  }

  public void setTid(long value) {
    m_tid.set(value);
  }

  public long getVersion() {
    return m_version.get();
  }

  public void setVersion(long value) {
    m_version.set(value);
  }

  public boolean getTidNew() {
    return m_tidNew.get();
  }

  public void setTidNew(boolean value) {
    m_tidNew.set(value);
  }

  public boolean getUserAck() {
    return m_userAck.get();
  }

  public void setUserAck(boolean value) {
    m_userAck.set(value);
  }

  public boolean getRes1() {
    return m_res1.get();
  }

  public void setRes1(boolean value) {
    m_res1.set(value);
  }

  public boolean getRes2() {
    return m_res2.get();
  }

  public void setRes2(boolean value) {
    m_res2.set(value);
  }

  public long getTcl() {
    return m_tcl.get();
  }

  public void setTcl(long value) {
    m_tcl.set(value);
  }

  public long getVid() {
    return m_vid.get();
  }

  public void setVid(long value) {
    m_vid.set(value);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_gtr.writeExternal(out);
    m_ttr.writeExternal(out);
    m_rid.writeExternal(out);
    m_tid.writeExternal(out);
    m_version.writeExternal(out);
    m_tidNew.writeExternal(out);
    m_userAck.writeExternal(out);
    m_res1.writeExternal(out);
    m_res2.writeExternal(out);
    m_tcl.writeExternal(out);
    m_vid.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_gtr.readExternal(in);
    m_ttr.readExternal(in);
    m_rid.readExternal(in);
    m_tid.readExternal(in);
    m_version.readExternal(in);
    m_tidNew.readExternal(in);
    m_userAck.readExternal(in);
    m_res1.readExternal(in);
    m_res2.readExternal(in);
    m_tcl.readExternal(in);
    m_vid.readExternal(in);
  }
}

// End of InvokePdu2.java
