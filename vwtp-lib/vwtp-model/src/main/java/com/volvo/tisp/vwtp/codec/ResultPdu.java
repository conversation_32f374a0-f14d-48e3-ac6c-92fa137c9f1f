//
// ResultPdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import org.jdom2.Element;

/** ResultPdu. */
public class ResultPdu extends ASNSequence {
  private static final long serialVersionUID = 1L;

  ASNBoolean m_gtr = new ASNBoolean();
  ASNBoolean m_ttr = new ASNBoolean();
  ASNBoolean m_rid = new ASNBoolean();
  ASNInteger m_tid = new ASNInteger();

  public ResultPdu() {
    setAsnObjectName("ResultPdu");
    m_gtr.setAsnObjectName("gtr");
    m_ttr.setAsnObjectName("ttr");
    m_rid.setAsnObjectName("rid");
    m_tid.setAsnObjectName("tid");
    m_tid.setFixedConstraint(0L, 65535L);
  }

  public void decode(PERStream stream) throws ASNException {
    m_gtr.decode(stream);
    m_ttr.decode(stream);
    m_rid.decode(stream);
    m_tid.decode(stream);
  }

  public void encode(PERStream stream) throws ASNException {
    m_gtr.encode(stream);
    m_ttr.encode(stream);
    m_rid.encode(stream);
    m_tid.encode(stream);
  }

  public void encode(Element oElement) throws ASNException {
    Element oElem = new Element(getAsnObjectName());
    m_gtr.encode(oElem);
    m_ttr.encode(oElem);
    m_rid.encode(oElem);
    m_tid.encode(oElem);
    oElement.addContent(oElem);
  }

  public long encodedSize() {
    long length = super.encodedSize();
    length += m_gtr.encodedSize();
    length += m_ttr.encodedSize();
    length += m_rid.encodedSize();
    length += m_tid.encodedSize();
    return length;
  }

  public boolean getGtr() {
    return m_gtr.get();
  }

  public void setGtr(boolean value) {
    m_gtr.set(value);
  }

  public boolean getTtr() {
    return m_ttr.get();
  }

  public void setTtr(boolean value) {
    m_ttr.set(value);
  }

  public boolean getRid() {
    return m_rid.get();
  }

  public void setRid(boolean value) {
    m_rid.set(value);
  }

  public long getTid() {
    return m_tid.get();
  }

  public void setTid(long value) {
    m_tid.set(value);
  }

  public void writeExternal(java.io.ObjectOutput out) throws java.io.IOException {
    super.writeExternal(out);
    m_gtr.writeExternal(out);
    m_ttr.writeExternal(out);
    m_rid.writeExternal(out);
    m_tid.writeExternal(out);
  }

  public void readExternal(java.io.ObjectInput in)
      throws java.io.IOException, ClassNotFoundException {
    super.readExternal(in);
    m_gtr.readExternal(in);
    m_ttr.readExternal(in);
    m_rid.readExternal(in);
    m_tid.readExternal(in);
  }
}

// End of ResultPdu.java
