//
// ConcatenatedMsgs_pdu.java
//
// Code automatically generated by asnparse.
//

package com.volvo.tisp.vwtp.codec;

import java.io.UnsupportedEncodingException;
import java.util.Hashtable;

/** ConcatenatedMsgs_pdu. */
public class ConcatenatedMsgs_pdu extends ASNChoice {
  private static final long serialVersionUID = 1L;

  protected static final Hashtable<String, Integer> m_nameToValue =
      new Hashtable<String, Integer>();
  protected static final Hashtable<Integer, String> m_valueToName =
      new Hashtable<Integer, String>();
  // Choice element identifier constants
  public static final int E_SHORT = 0;
  public static final int E_LONG = 1;

  static {
    m_nameToValue.put("short", Integer.valueOf(E_SHORT));
    m_valueToName.put(Integer.valueOf(E_SHORT), "short");
    m_nameToValue.put("long", Integer.valueOf(E_LONG));
    m_valueToName.put(Integer.valueOf(E_LONG), "long");
  }

  public ConcatenatedMsgs_pdu() {
    setAsnObjectName("ConcatenatedMsgs_pdu");
    setFixedSizeConstraint(2); // Might be superseeded by a user defined "FixedConstraint"
  }

  protected Hashtable<String, Integer> getNameMap() {
    return m_nameToValue;
  }

  protected Hashtable<Integer, String> getValueMap() {
    return m_valueToName;
  }

  public ASNValue createObject() throws ASNException {
    ASNValue oTmp = null;
    switch (m_choiceId) {
      case E_SHORT:
        oTmp = new ASNOctetString();
        oTmp.setAsnObjectName("short");
        ((ASNOctetString) oTmp).setFixedConstraint(0L, 127L);
        break;
      case E_LONG:
        oTmp = new ASNOctetString();
        oTmp.setAsnObjectName("long");
        ((ASNOctetString) oTmp).setFixedConstraint(0L, 32767L);
        break;
      default:
        throw new ASNException(
            "Undefined Choice value ( " + m_choiceId + " ) in type ConcatenatedMsgs_pdu");
    }
    return oTmp;
  }

  public byte[] getShort() throws ASNException {
    if (m_choiceId != E_SHORT || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return ((ASNOctetString) m_value).getAsByteArray();
  }

  public String getShortAsString() throws ASNException {
    if (m_choiceId != E_SHORT || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return ((ASNOctetString) m_value).getAsString();
  }

  public String getShort(String charSetName) throws ASNException, UnsupportedEncodingException {
    if (m_choiceId != E_SHORT || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return ((ASNOctetString) m_value).get(charSetName);
  }

  public void setShort(byte[] value) throws ASNException {
    if (m_choiceId != E_SHORT || m_value == null) {
      setChoice(E_SHORT);
    }
    ((ASNOctetString) m_value).set(value);
  }

  public void setShort(String value, String charSetName)
      throws ASNException, UnsupportedEncodingException {
    if (m_choiceId != E_SHORT || m_value == null) {
      setChoice(E_SHORT);
    }
    ((ASNOctetString) m_value).set(value, charSetName);
  }

  public void setShort(String value) throws ASNException {
    if (m_choiceId != E_SHORT || m_value == null) {
      setChoice(E_SHORT);
    }
    ((ASNOctetString) m_value).set(value);
  }

  public byte[] getLong() throws ASNException {
    if (m_choiceId != E_LONG || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return ((ASNOctetString) m_value).getAsByteArray();
  }

  public String getLongAsString() throws ASNException {
    if (m_choiceId != E_LONG || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return ((ASNOctetString) m_value).getAsString();
  }

  public String getLong(String charSetName) throws ASNException, UnsupportedEncodingException {
    if (m_choiceId != E_LONG || m_value == null) {
      throw new ASNException("The ASNChoice is not of the requested type");
    }
    return ((ASNOctetString) m_value).get(charSetName);
  }

  public void setLong(byte[] value) throws ASNException {
    if (m_choiceId != E_LONG || m_value == null) {
      setChoice(E_LONG);
    }
    ((ASNOctetString) m_value).set(value);
  }

  public void setLong(String value, String charSetName)
      throws ASNException, UnsupportedEncodingException {
    if (m_choiceId != E_LONG || m_value == null) {
      setChoice(E_LONG);
    }
    ((ASNOctetString) m_value).set(value, charSetName);
  }

  public void setLong(String value) throws ASNException {
    if (m_choiceId != E_LONG || m_value == null) {
      setChoice(E_LONG);
    }
    ((ASNOctetString) m_value).set(value);
  }
}

// End of ConcatenatedMsgs_pdu.java
