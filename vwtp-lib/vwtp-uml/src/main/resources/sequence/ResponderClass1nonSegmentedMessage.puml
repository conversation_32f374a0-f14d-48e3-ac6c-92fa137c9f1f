@startuml

box "Wtp Internal" #LightBlue
participant "Controller" as C order 20
participant "Router" as R order 30
participant "SimpleService" as SS order 40
participant "SegmentationService" as SEG order 60
participant "TimerService" as TS order 25
participant "TidVerificationService" as TVS order 50
participant "TransactionService" as CS order 25
end box
box "Wtp External" #Yellow
participant "Transport" as T order 10
participant "WtpUser" as WU order 70
end box


== Act as Responder ==
T -> C: Receive byte[], URI
C -> C: direction bit is set so act as Responder
C -> C: Decodes data to DecodedMessage
C -> C: Is WtpTid verification needed?
C -> CS: Store transactionObject by URI
C -> TS: Start Wtp.timer.A
alt tidverification needed

C -> TVS: do a tid verification
ref over TVS
 Tid verification is covered in
 another sequence diagram
end ref
TVS -> C: tid verification done
C -> CS: get transactionObject by URI
CS -> C: transactionObject
end
C -> R: route decodedMessage

alt is SimpleMessage
R -> SS: Route to decodedMessage simple service (class1 message no seg)
SS -> WU: Send UserMessage

alt response successful
WU -> C: Send response back with tid
C -> CS: pick up transactionObject by tid
C -> SS: route decodedMessage to SimpleService
SS -> SS: check condition class1, ExitInfo, Uack=true
SS -> TS: Stop Wtp.timer.A
SS -> TS: Start Wtp.timer.W

SS -> C: send ackPdu with Info TPI
C -> C: encode ackPdu
C -> T: send to vehicle


else response failure
TS -> C: Timeout Wtp.timer.A
C -> C: pickup transactionObject
C -> SS: route to SimpleService
loop Acknowledgment Expiration Counter (AEC) (TIMERTO_A)
    alt AEC < AEC_MAX
      C -> CS: pick up transactionObject
      C -> SS: route decodedMessage to Simple service
      SS -> SS: Increment AEC
      SS -> TS: Start Wtp.timer.A
      SS-> CS: Store AEC counter
    else AEC == AEC_MAX
      C -> CS: pick up transactionObject
      C -> SS: route decodedMessage to Simple service
      SS -> SS: Send Abort PDU (NORESPONSE)
      SS -> C: Send abortPdu
      C -> T: Send abortPdu to vehicle
      SS -> C: Send TR-Abort.ind (WTPUser?)
      C -> WU: Send TR-Abort.ind (WTPUser?)
    end
end

TS -> C: Timeout Wtp.timer.W - Clear Transaction
end
else is segmented
R -> SEG:
ref over SEG
 Segmentation is covered in
 another sequence diagram
end ref
end




@enduml
