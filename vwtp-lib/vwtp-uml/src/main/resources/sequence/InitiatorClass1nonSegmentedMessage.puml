@startuml

box "Wtp Internal" #LightBlue
participant "Controller" as C order 20
participant "Router" as R order 30
participant "SimpleService" as SS order 40
participant "SegmentationService" as SEG order 60
participant "TimerService" as TS order 25
participant "TidVerificationService" as TVS order 50
participant "TransactionService" as CS order 25
end box
box "Wtp External" #Yellow
participant "Transport" as T order 70
participant "WtpUser" as WU order 10
end box

WU -> C: receive UserMessage
C -> R: send to route
alt is simple message
R -> SS: route DecodedMessage to simple message service
SS -> CS: get WtpId from cache by uri
CS -> SS: return current WtpTid
SS -> SS: Generate SendWtpTid from current\nvehicle WtpTid
SS -> SS: create InvokePdu
SS -> C: send InvokePdu
SS -> CS: Store WtpTid
SS -> SS: Reset RCR counter
SS -> TS: Start Wtp.timer R (RCR)
C -> T: send to vehicle
alt need Tidverification
C -> TVS: do a tid verification
ref over TVS
 Tid verification is covered in
 another sequence diagram
end ref
TVS -> C: tid verification done
end
alt success
T -> C: receive ack
C -> CS: pick up transaction by WtpTid, uri
CS -> C: transactionObject returned
C -> SS: route to Simple Service
SS -> TS: Stop Wtp.timer.R
SS -> SS: Generate Confirmation message
SS -> C: send confirmation
C -> WU: send confirmation
SS -> SS: Clear transaction

else failure
loop Resend timeout (RCR) (TIMERTO_R)
    alt RCR < MAX_RCR
      C -> CS: pick up transactionObject
      C -> SS: route decodedMessage to Simple service
      SS -> SS: Increment RCR
      SS -> TS: Start timer R (RCR)
      SS -> C: send InvokePdu
      C -> T: send to vehicle
    else RCR == MAX_RCR
      C -> CS: pick up transactionObject
      C -> SS: route to SS
      SS -> SS: Abort transaction
      SS -> SS: Generate TR-Abort.ind
      SS -> C: send Cancel/Failure to WtpUser
      C -> WU: send Cancel/Failure to WtpUser
    end
end
else is segmented message
end
@enduml
