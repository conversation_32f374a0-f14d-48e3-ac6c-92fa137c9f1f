@startuml


box "Wtp Internal" #LightBlue
participant "Controller" as C order 20
participant "Router" as R order 30
participant "SimpleService" as SS order 40
participant "SegmentationService" as SEG order 60
participant "TimerService" as TS order 25
participant "TidVerificationService" as TVS order 50
participant "CacheService" as CS order 60
participant "TransactionService" as PS order 65
end box


box "Wtp External" #Yellow
participant "Transport" as T order 10
participant "WtpUser" as WU order 70
end box


== STATE: STATE: RESULT WAIT ==
alt RcvAck, TIDve, Class == 1|2, RCR < MAX_RCR
  WU -> C: receive(URI, byte[])
  C -> C: VwtpMessageDtoToWtpTransactionConverter.convert(VwtpMessageDto)
  C -> R: receiveUser(WtpTransaction)
  R -> C: send(URI, AckPdu(TIDok))
  C -> WU: send(URI, byte[])
  R -> TS: stopTimer(R)
  R -> R: Increment RCR
  R -> TS: startTimer(R [RCR])
  ref over T, C
    STATE: RESULT WAIT
  end ref
end


@enduml
