@startuml
title Class 1 Reassemby as Responder

box "Wtp External" #Yellow
    participant "Transport"
end box

box "Wtp Internal" #LightBlue
    participant "Controller"
    participant "Responder"
    participant "TransactionService"
    participant "TimerService"
end box

box "Wtp External" #Yellow
    participant "WtpUser"
end box

rnote left of TransactionService
    == **<size:18>Transaction Context</size>** ==
    -- Status Enum --
    * INPROGRESS (Default)
    * USER_WAIT
    * COMPLEATED
    -- Counter --
    * retry
    -- Timer --
    * timeout
    * response
    * user
    -- Data --
    - vwtpTidValid: boolean = true
    - userAckRequired: boolean = false
    - lastSegmentReceived: boolean = false
    - maxSegment: byte = 0
    - segments: byte[256][] = null
end rnote

Transport -> Controller: NetworkMessageDto
Controller -> Controller: Decode to DecodedMessage
Controller -> Controller: Check VwtpTid significant bit\nif it is Responder message

alt if Initiator message
    ref over Controller: Redirect control to Initiator
end

Controller -> Responder: DecodedMessage
Responder -> TransactionService: Get Transaction Context
Responder <-- TransactionService

alt if VWTP Transaction Context is __NULL__
    alt Ivoke or SegInvoke PDU
        Responder -> TransactionService: Create new transaction
        Responder <-- TransactionService
        Responder -> TransactionService: Get VwtpTid counter
        Responder <-- TransactionService
    alt VwtpTid is not within window
            Responder ->o TransactionService: Set isVwtpTidValid=false
        end
    else Ack PDU or Abort PDU
        Responder ->x Responder: Terminate
    end
end

alt Ivoke or SegInvoke PDU
    alt NOT INPROGRESS OR Segment allready in Transaction Context
        alt USER_WAIT
            Responder ->o TimerService: Restart timeout timer
        end
        Responder ->x Responder: Terminate
    end
    alt Invoke PDU and U/P flag is set
        Responder ->o TransactionService: Set userAckRequired=true
    end
    alt SegInvoke PDU and maxSegment < PSN
        Responder ->o TransactionService: Set maxSegment=PSN
    end
    alt TTR
        Responder ->o TransactionService: Set lastSegmentReceived=true
    end
    Responder ->o TransactionService: Store payload in Transaction Context
    Responder ->o TimerService: Start/Restart timeout timer
    alt Is (GTR or TTR) and packets are missing
        Responder ->o TimerService: Start response timer with [NACK(missing packets)]
        Responder ->x Responder: Terminate
    end
    alt (Group OR message is compleate) AND vwtpTidValid=false
        Responder ->o TimerService: Cancel response timer and reset retry counter
        Responder -> Responder: Set retry counter to 1
        ref over Responder: Function: send response [ACK] [with Tve]
        Responder ->x Responder: Terminate
    end
    ref over Responder: Function: message assembly
else Ack PDU
    alt INPROGRESS AND Tok
        Responder -> Responder: vwtpTidValid=true
        Responder ->o TimerService: Restart timeout timer
        ref over Responder: Function: message assembly
    end
else Abort PDU
    Responder ->o TransactionService: set COMPLEATED
end
par sscritical
end
== Function: message assembly ==
alt Message can be assemled
    Responder ->o TimerService: Cancel response timer and reset retry counter
    Responder -> Responder: Assemble VwtpMessageDto
    alt userAckRequired
        Responder ->o TransactionService: set USER_WAIT
        TransactionService ->o TimerService: start user timer
    else userAckRequired=false
        Responder -> Responder: Set retry counter to 1
        ref over Responder: Function: send response [ACK]
    end
    Responder ->o WtpUser: VwtpMessageDto
    alt userAckRequired=false
        Responder ->o TransactionService: set COMPLEATED
    end
else Message cannot be assembled and response timer (with [NACK]) is set
    Responder ->o TimerService: Restart response timer with [NACK(missing packets)]
end

== Function: send response ==
Responder -> Controller: Send [Response]
Controller -> Controller: Encode to NetworkMessageDto
Controller ->o Transport: NetworkMessageDto
alt [Response] is not Abort
    Responder ->o TimerService: Start response timer for [Response]
end

== Timer: timeout ==
Responder ->o TransactionService: NULL Transaction Context

== Timer: response ==
Responder -> Responder: Increment retry counter
alt If retries exhausted
    Responder ->o TransactionService: set COMPLEATED
    Responder ->x Responder: Terminate
end
alt If retries > 0
    Responder -> Responder: Set RID on [Response]
end
ref over Responder: Function: send response [Response]

== Timer: user ==
ref over Responder: Function: send response [Abort (NORESPONSE)]
|||
...
== User Response ==
WtpUser -> Controller: VwtpStatusDto
Controller -> Responder: VwtpStatusDto
Responder -> TransactionService: Get Transaction Context
Responder <-- TransactionService
Responder ->o TransactionService: set COMPLEATED
alt Status SUCCESS
    ref over Responder: Function: send response [ACK]
else Status FAILURE
    ref over Responder: Function: send response [Abort (USER)]
end
@enduml
