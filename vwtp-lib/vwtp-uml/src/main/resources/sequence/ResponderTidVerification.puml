@startuml


box "Wtp Internal" #LightBlue
participant "Controller" as C order 20
participant "Router" as R order 30
participant "SimpleService" as SS order 40
participant "SegmentationService" as SEG order 60
participant "TimerService" as TS order 25
participant "TidVerificationService" as TVS order 50
participant "CacheService" as CS order 60
participant "TransactionService" as PS order 65
end box


box "Wtp External" #Yellow
participant "Transport" as T order 10
participant "WtpUser" as WU order 70
end box


== STATE: LISTEN ==
alt Class == 1, Invalid TID, TTR
  T -> C: receive(URI, byte[])
  C -> PS: store(URI, byte[])
  C -> C: NetworkMessageDtoToDecodedMessageConverter.convert(NetworkMessageDto)
  C -> R: receiveNetwork(WtpTransaction)
  R -> C: send(URI, AckPdu(Tve))
  C -> T: send(URI, byte[])
  ref over T, C
    STATE: TIDOK WAIT
  end ref
else Class == 1, Invalid TID, GTR
  T -> C: receive(URI, byte[])
  C -> PS: store(URI, byte[])
  C -> C: NetworkMessageDtoToDecodedMessageConverter.convert(NetworkMessageDto)
  C -> R: receiveNetwork(WtpTransaction)
  R -> C: send(URI, AckPdu(Tve))
  C -> T: send(URI, byte[])
  C -> TS: startTimer(SC)
  ref over T, C
    STATE: TIDOK WAIT
  end ref
end


== STATE: TIDOK WAIT ==
alt RcvAck, Class == 1, TIDok
  T -> C: receive(URI, byte[])
  C -> PS: store(URI, byte[])
  C -> C: NetworkMessageDtoToDecodedMessageConverter.convert(NetworkMessageDto)
  C -> R: receiveNetwork(WtpTransaction)
  R -> TS: startTimer(A)
  R -> R: Generate TR-Invoke.ind
  R -> CS: update(CacheKey, LastTID(RcvTID))
  ref over T, C
    STATE: INVOKE RESP WAIT
  end ref
else RcvAck, Class == 1, TIDok, Segmented
  T -> C: receive(URI, byte[])
  C -> PS: store(URI, byte[])
  C -> C: NetworkMessageDtoToDecodedMessageConverter.convert(NetworkMessageDto)
  C -> R: receiveNetwork(WtpTransaction)
  R -> C: send(URI, AckPdu)
  C -> T: send(URI, byte[])
  R -> CS: update(CacheKey, LastTID(RcvTID))
  ref over T, C
    STATE: SAR
  end ref
else RcvErrorPDU
  T -> C: receive(URI, byte[])
  C -> PS: store(URI, byte[])
  C -> C: NetworkMessageDtoToDecodedMessageConverter.convert(NetworkMessageDto)
  C -> R: receiveNetwork(WtpTransaction)
  R -> C: send(URI, AbortPdu)
  C -> T: send(URI, byte[])
  R -> R: abort transaction
  ref over T, C
    STATE: LISTEN
  end ref
else RcvAbort
  T -> C: receive(URI, byte[])
  C -> PS: store(URI, byte[])
  C -> C: NetworkMessageDtoToDecodedMessageConverter.convert(NetworkMessageDto)
  C -> R: receiveNetwork(WtpTransaction)
  R -> R: abort transaction
  ref over T, C
    STATE: LISTEN
  end ref
else RcvInvoke, RID=1
  T -> C: receive(URI, byte[])
  C -> PS: store(URI, byte[])
  C -> C: NetworkMessageDtoToDecodedMessageConverter.convert(NetworkMessageDto)
  C -> R: receiveNetwork(WtpTransaction)
  R -> C: send(URI, AckPdu(Tve))
  C -> T: send(URI, byte[])
  R -> CS: update(CacheKey, LastTID(RcvTID))
  ref over T, C
    STATE: TIDOK WAIT
  end ref
end


== STATE: INVOKE RESP WAIT ==
alt TimerTO_SN
  R -> C: send(URI, NackPdu(MissingPsns))
  C -> T: send(URI, byte[])
  ref over T, C
    STATE: SAR
  end ref
else TimerTO_A, AEC < AEC_MAX
  R -> R: Increment AEC
  R -> TS: startTimer(A)
  ref over T, C
    STATE: INVOKE RESP WAIT
  end ref
else TimerTO_A, AEC == AEC_MAX
  R -> R: abort transaction
  R -> C: send(URI, AbortPdu(NORESPONSE))
  C -> T: send(URI, byte[])
  R -> R: Generate TR-Abort.ind
  ref over T, C
    STATE: LISTEN
  end ref
else TimerTO_A, Class == 1, SAR, Uack == False
  R -> R: Queue(A) Ack PDU with PsnTPI
  R -> TS: startTimer(W)
  ref over T, C
    STATE: WAIT TIMEOUT
  end ref
else TimerTO_A, Class == 1, Uack == False
  R -> R: Queue(A) Ack PDU
  R -> TS: startTimer(W)
  ref over T, C
    STATE: WAIT TIMEOUT
  end ref
else TimerTO_A, Class == 2, SAR, Uack == False
  R -> C: send(URI, AckPdu)
  C -> T: send(URI, byte[])
  ref over T, C
    STATE: RESULT WAIT
  end ref
end


== STATE: SAR ==
alt TimerTO_SC
  R -> R: abort transaction
  R -> C: send(URI, AbortPdu(UNKNOWN))
  C -> T: send(URI, byte[])
  ref over T, C
    STATE: LISTEN
  end ref
else TimerTO_SN
  R -> C: send(URI, NackPdu(MissingPsns))
  C -> T: send(URI, byte[])
  ref over T, C
    STATE: SAR
  end ref
end


@enduml
