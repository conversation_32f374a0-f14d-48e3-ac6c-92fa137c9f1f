@startuml

start
:Receive User status;

if(Status is abort (TR-Abort.req)) then (yes)
    floating note left: Should a user be able to abort?
    #HotPink:Abort transaction;
    #HotPink:Send AbortPdu (user);
    stop

else
    if(Is segmented)
     if(Uack is true) then (yes)
        :Send AckPdu with PsnTPI;
        :Stop Wtp.Timer.A;
        :Start Wtp.Timer.W;
     endif
    else
     :Handle as non segmented (different activity diagram);
     stop
    endif
endif
stop
@enduml
