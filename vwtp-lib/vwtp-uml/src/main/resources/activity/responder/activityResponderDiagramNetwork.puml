@startuml

start
:Receive Udp;
fork
:Time out Timer A (TimerTO_A);
while (AEC < AEC_MAX)
 :Increment AEC;
 :Refresh/Start ack timer (A-timer);
endwhile
:Abort transaction;
:Send Abort PDU (NORESPONSE);
stop

fork again
:Convert to Pdu;

if(Is initiator?) then (yes)
:Initiator Flow;
:Perform Initiator Flow;
stop
else (no)
if(is InvokePdu) then (yes)
    :Responder Flow;

    if(Transaction class is) then (class1)
        if(Is Tidverification Needed?) then (yes)
        partition Tidverification {
            :Store InvokePdu;
            :Send Ack(TIDve);
            note right
            Wait for TIDok as ack from vehicle
            end note
            stop
        }
        else (no)
            if(TTR flag = true) then (yes)
                if(UserAck = true) then (yes)
                    :Store InvokePdu;
                    :Start Ackknowledge Timer Wtp.timer.A;
                    :Send user message;
                    :Uack true;
                    stop
                else
                    :Store InvokePdu;
                    :Start Ackknowledge Timer Wtp.timer.A;
                    :Send user message;
                    :Uack false;
                    stop
                endif
            else (no)
                :Can be segmented other diagram;
                stop
            endif

        endif
    else (class0)
        :Store InvokePdu;
        :Send user message;
        stop
    endif

elseif(is AckPdu) then (yes)
    if(Transaction class is) then (class1)
        if(OTR=true (TidOk)) then (yes)
            :Start Ackknowledge Timer Wtp.timer.A;
            :Send user message;
            :Update last wtptid;
            stop
        else
            stop
        endif
    else (class0 NA)
        stop
    endif
else (AbortPdu)
    :Remove transaction;
    stop
endif
@enduml
