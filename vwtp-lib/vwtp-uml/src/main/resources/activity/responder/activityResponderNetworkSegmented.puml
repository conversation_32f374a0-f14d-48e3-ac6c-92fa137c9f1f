@startuml

start

fork
partition "SAR Nack delay SN" {
:receive TimerTO_SN;
:Send NackPdu (MissingPSNs);
stop
}
fork again
partition "SAR transaction Cancel (SC)" {
:receive TimerTO_SC;
:Abort transaction;
:Send AbortPdu transaction (UNKNOWN);
stop
}
fork again
partition "Acknowledgement interval (A)" {
:receive TimerTO_A;
:TCL 1 && Segmented uack=false;
:Send AckPdu with PsnTPI;
:Start Wtp.timer.W;
stop
}
fork again
partition "Wait timeout interval (W)" {
:Receive TimerTO_W;
:Clear transaction;
}
stop
fork again
:Receive Udp;
:Convert to Pdu;
if(Is initiator?) then (yes)
:Initiator Flow;
:Perform Initiator Flow;
stop
else (no)
:Responder Flow;
endif
if(Is Tidverification Needed?) then (yes)
partition Tidverification {
    :Pdu is InvokePdu!;
    :WtpTid is invalid;
    :Store InvokePdu;
    :Send Ack(TIDve);
    :Start timer SAR transaction(wtp.timer.sc);
    :Set GTR PSN in context;
    :Set GtrReceived;
    :Set segmented;
    :Wait for TIDok as ack from vehicle;


    stop
}
else (no)
if(Pdu is InvokePdu)
  if(Transaction class is 1)
    if(GTR is true) then (yes)
        if(RID is true) then (yes)
            :Send AckPdu with RID;
            stop
        else
            :Store invoke pdu;
            :Start timer SAR transaction(wtp.timer.sc);
            :Set GTR PSN in context;
            :Set GtrReceived;
            :Set segmented;
            :Send Ack GTR to vehicle;
            stop
        endif
    else (no)
      :Store invoke pdu;
      :Start timer SAR transaction(wtp.timer.sc);
      :Set segmented;
      stop
    endif
  else
    :handle as class0;
    stop
  endif
elseif(Pdu is SegInvokePdu)
  if(TTR is true) then (yes)
    if(GTR is true) then (yes)
        :Abort transaction;
        :Send NackPdu (empty);
        stop
    else (no)
        if(Sequence is complete) then(yes)
            :Store Pdu;
            :Stop Timer SC;
            :Stop Timer SN;
            :Start Timer A;
            :Send UserMessage to User;
            stop
        else (no)
            :Store Pdu;
            :Set TTR PSN;
            :Start Timer SN;
            stop
        endif
    endif
  else (no)
      if(GTR is true) then (yes)
        if(Sequence is complete) then (yes)
            :Store Pdu;
            :Set Gtr Psn;
            :Send Ack GTR to vehicle;
            :Stop timer SN;
            :Stop timer SC;
            :Start timer SC;
            stop
        else (no)
            :Store Pdu;
            :Set Gtr Psn;
            :Start timer SN;
            stop
        endif
      else (no)
        if(GtrReceived or TtrReceived is false) then (yes)
            if(Gtr Sequence not complete)
                :Store Pdu;
            endif
            stop
        elseif(GtrReceived is true) then (yes)
            if(Gtr Sequence is complete) then (yes)
            :Store Pdu;
            :Send Gtr Ack to vehicle;
            :Stop timer SN;
            :Stop timer SC;
            :Start timer SC;
            stop
            else (NA)
                stop
            endif
        elseif(TtrReceived is true) then (yes)
            if(Sequence is complete) then (yes)
                :Store Pdu;
                :Stop timer SC;
                :Stop timer SN;
                :Start timer A;
                :send usermessage to user;
                stop
            else (NA)
                stop
            endif
        else (NA)
            stop
        endif
        stop
      endif
  endif
elseif(Pdu is AckPdu)
 :Response to a tidverification;
 :Continue as InvokePdu;
 stop
else(Pdu is AbortPdu)
  :Abort transaction (clear transaction);
  stop
endif
endif
stop
@enduml
