@startuml

start
fork
    :TimerTO_R;
    while (Resend counter < Resend CounterMax (RCR < MAX_RCR))
        :Increase resend counter;
        :Start Resend timer Wtp.Timer.R;

        if(state == Tidverification)
            :Send Ack(TIDok);
        else
            :Set RID flag;
            :Send InvokePdu;
        endif
    endwhile
    :Abort transaction;
    :Send user abort;
    stop
fork again
    :TimerTO_GR;
    while (Resend counter < Resend CounterMax (RCR < MAX_RCR))
        :Send last in group with RID flag;
        :Increment RCR;
        :Start group Resend timer Wtp.Timer.GR;

    endwhile
    :Abort transaction;
    :Send user abort;
    stop
fork again
    :Receive Abort Request;
    :Abort transaction;
    :Send Abort PDU (USER);
    stop
fork again
:Receive User request;
if(Transaction class is) then (Class1)
    :create new WtpTransaction;
    :Get next WtpTid for vehicle;
    :Set WtpTid in WtpTransaction;
    if(is new WtpTid a wrap around?) then (yes)
        :Set TidNew flag in WtpTransaction;
    endif

    if(payload > mtu) then (yes)
        :Set segmented flag = true;
        :Set GTR flag = true;
        :Set UserAck flag = true;
        :Send InvokePdu (first message);
        :Initiate resend counter;
        :Start Resend timer Wtp.Timer.R;
        stop
    else
        :Set segmented flag = false;
        :Set GTR flag = true;
        :Set TTR flag = true;
        :Set UserAck flag = true;
        :Send InvokePdu;
        :Initiate resend counter;
        :Start Resend timer Wtp.Timer.R;
        stop
    endif

else (Class0)
    stop
endif
@enduml
