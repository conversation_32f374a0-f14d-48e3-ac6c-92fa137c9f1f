@startuml

start
:Receive Udp;
if(is Initiator (response)) then (yes)
if(Receive AckPdu) then
    if(Transaction class is) then (Class1)
        if(OTR/TIDve is true) then (yes)
            while(RCR < MAX_RCR (Resend Counter))
                :Send Ack(TIDok);
                :Stop Resend Timer Wtp.Timer.R;
                :Increment RCR;
                :Start Resend timer Wtp.Timer.R;
            endwhile
            :Ignore additional TIDve!;
            stop
        else
            if(Is segmented = true) then (yes)
                if(Is last group?) then (yes)
                    :Stop Group timer Wtp.Timer.GR;
                    :Send User Confirmation;
                    :Clear transaction;
                    stop
                else (no)
                    :Stop Resend timer Wtp.Timer.R;
                    :Stop Group timer Wtp.Timer.GR;
                    :Set ongoing group acked;
                    :Reset resend counter (RCR);
                    :Start Group timer Wtp.Timer.GR;
                    :Send next group;
                    stop
                endif
            else (no)
                :Stop Resend timer Wtp.Timer.R;
                :Generate User Confirmation;
                :Send User Confirmation;
                :Clear Transaction;
                stop
            endif
        endif
    else (Class0)
        stop
    endif
elseif(Receive NackPdu) then
    if(Transaction class is) then (Class1)
        if(Is segmented = true) then (yes)
            :Stop Group timer Wtp.Timer.GR;
            :Reset resend counter (RCR);
            :Start Group timer Wtp.Timer.GR;
            :Send missing segment in group;
            stop
        else (NA)
            stop
        endif
    else (NA)
        stop
    endif
else (Receive AbortPdu)
    :Abort transaction;
    :Generate TR-Abort (cancel);
    stop
endif
else
    :Responder Flow;
    stop
endif



@enduml
