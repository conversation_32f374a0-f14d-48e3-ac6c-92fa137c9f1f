<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp.vwtp</groupId>
    <artifactId>vwtp-lib</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>vwtp-benchmark</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp.vwtp</groupId>
      <artifactId>vwtp-impl</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>unit-test-lib</artifactId>
    </dependency>
    <dependency>
      <groupId>io.projectreactor.tools</groupId>
      <artifactId>blockhound</artifactId>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <mainClass combine.self="override"/>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
