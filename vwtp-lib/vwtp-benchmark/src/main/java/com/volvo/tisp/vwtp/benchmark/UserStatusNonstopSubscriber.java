package com.volvo.tisp.vwtp.benchmark;

import com.volvo.tisp.vwtp.dto.UserStatusDto;
import java.util.function.Consumer;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;

@Component
@Profile("nonstop")
public class UserStatusNonstopSubscriber implements Consumer<UserStatusDto> {
  private final Metrics metrics;

  UserStatusNonstopSubscriber(final Metrics metrics) {
    this.metrics = metrics;
  }

  @Override
  public void accept(final UserStatusDto userStatus) {
    if (!userStatus.isDelivered()) {
      metrics.incrementUserStatusAborted();
    }
    metrics.incrementUserStatusReceived();
  }
}
