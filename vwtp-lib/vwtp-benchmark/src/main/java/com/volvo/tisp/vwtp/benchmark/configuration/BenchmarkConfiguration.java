package com.volvo.tisp.vwtp.benchmark.configuration;

import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.test.entity.NoOpEmptyMap;
import com.volvo.tisp.test.util.RandomPayload;
import com.volvo.tisp.vwtp.benchmark.Metrics;
import com.volvo.tisp.vwtp.configuration.ControllerConfiguration;
import com.volvo.tisp.vwtp.configuration.InitiatorConfiguration;
import com.volvo.tisp.vwtp.configuration.ResponderConfiguration;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import java.net.URI;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.annotation.Profile;
import reactor.blockhound.BlockHound;
import reactor.core.Disposable;
import reactor.core.Disposables;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SynchronousSink;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import reactor.util.context.Context;

/** Use for common beans */
@Configuration
@ComponentScan(basePackages = {"com.volvo.tisp.vwtp.benchmark"})
@Import({ControllerConfiguration.class, InitiatorConfiguration.class, ResponderConfiguration.class})
public class BenchmarkConfiguration implements DisposableBean {
  private static final Logger logger = LoggerFactory.getLogger(BenchmarkConfiguration.class);
  private static final String URI_PREFIX = "UDP://bench-";
  private static final Map<String, String> PROPERTIES = NoOpEmptyMap.instance();

  private final Metrics metrics;
  private final BenchmarkProperties configuration;
  private final List<Scheduler> flowSchedulers;
  private final Disposable.Composite flowsDisposable;

  public BenchmarkConfiguration(final BenchmarkProperties configuration, final Metrics metrics) {
    this.configuration = configuration;
    this.metrics = metrics;
    flowsDisposable = Disposables.composite();
    flowSchedulers =
        Stream.generate(() -> Schedulers.newSingle("reactor"))
            .limit(configuration.getThreadCount())
            .toList();
    BlockHound.install();
  }

  @Bean(destroyMethod = "dispose")
  protected Disposable disposable() {
    return flowsDisposable;
  }

  @Bean
  protected List<Scheduler> flowSchedulers() {
    return flowSchedulers;
  }

  private void pushGeneratedMessage(
      final SynchronousSink<UserMessageDto> sink, final AtomicLong vehicleNumber) {
    final long vehicleId =
        vehicleNumber.getAndUpdate(number -> number == configuration.getVehicles() ? 1 : ++number);
    sink.next(
        UserMessageDto.builder()
            .withMessageId(TrackingIdentifier.create().toString())
            .withProperties(PROPERTIES)
            .withAddress(URI.create(URI_PREFIX + vehicleId))
            .withVehicleId(vehicleId)
            .withWtpVersion(configuration.getMessageVersion())
            .withTransactionClass(configuration.getMessageClass())
            .withPayload(RandomPayload.generateWithChecksum(configuration.getMessageSize()))
            .build());
  }

  @Bean
  @Profile("!nonstop")
  protected Runnable defaultStarter(
      final FlowComposer<UserMessageDto, Void> initiatorUserMessageFlow) {
    return () -> {
      final AtomicLong vehicleNumber = new AtomicLong(1);
      final AtomicLong messagesLeft = new AtomicLong(configuration.getMessageCount());
      for (final Scheduler flowScheduler : flowSchedulers) {
        flowsDisposable.add(
            Flux.<UserMessageDto>generate(
                    sink -> {
                      if (messagesLeft.getAndDecrement() > 0) {
                        metrics.incrementUserMessageGenerated();
                        pushGeneratedMessage(sink, vehicleNumber);
                      } else {
                        logger.info("Compleated message generation - sink.onRequest()");
                        sink.complete();
                      }
                    })
                .transform(initiatorUserMessageFlow)
                .contextWrite(Context.of(Scheduler.class, flowScheduler))
                .subscribeOn(flowScheduler)
                .subscribe(null, throwable -> logger.error("", throwable)));
      }
    };
  }

  @Bean
  @Profile("nonstop")
  protected Runnable nonstopStarter(
      final FlowComposer<UserMessageDto, Void> initiatorUserMessageFlow) {
    return () -> {
      final AtomicLong vehicleNumber = new AtomicLong(1);
      for (final Scheduler flowScheduler : flowSchedulers) {
        flowsDisposable.add(
            Flux.<UserMessageDto>generate(
                    sink -> {
                      metrics.incrementUserMessageGenerated();
                      pushGeneratedMessage(sink, vehicleNumber);
                    })
                .transform(initiatorUserMessageFlow)
                .contextWrite(Context.of(Scheduler.class, flowScheduler))
                .subscribeOn(flowScheduler)
                .subscribe(null, throwable -> logger.error("", throwable)));
      }
    };
  }

  /**
   * Exposing "initiatorNetworkMessageFlow" as "outgoingControllerNetworkMessageFlowForInitiator"
   *
   * @param initiatorNetworkMessageFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean
  protected FlowComposer<NetworkMessageDto, Void> outgoingControllerNetworkMessageFlowForInitiator(
      @Lazy final FlowComposer<NetworkMessageDto, Void> initiatorNetworkMessageFlow) {
    logger.info(
        "Exposing - Initiator NetworkMessage Flow as Controller NetworkMessage Sink for Initiator");
    return initiatorNetworkMessageFlow;
  }

  /**
   * Exposing "responderNetworkMessageFlow" as "outgoingControllerNetworkMessageFlowForResponder"
   *
   * @param responderNetworkMessageFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean
  protected FlowComposer<NetworkMessageDto, Void> outgoingControllerNetworkMessageFlowForResponder(
      @Lazy final FlowComposer<NetworkMessageDto, Void> responderNetworkMessageFlow) {
    logger.info(
        "Exposing - Responder NetworkMessage Flow as Controller NetworkMessage Sink for Responder");
    return responderNetworkMessageFlow;
  }

  /**
   * Exposing "controllerNetworkMessageFlow" as "outgoingInitiatorNetworkMessageFlow" and
   * "outgoingResponderNetworkMessageFlow"
   *
   * @param controllerNetworkMessageFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean({"outgoingInitiatorNetworkMessageFlow", "outgoingResponderNetworkMessageFlow"})
  protected FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow(
      final FlowComposer<NetworkMessageDto, Void> controllerNetworkMessageFlow) {
    logger.info("Exposing - Controller NetworkMessage Flow as joint NetworkMessage Sink");
    return controllerNetworkMessageFlow;
  }

  /**
   * Exposing "responderUserStatusFlow" as "outgoingResponderUserMessageFlow" by chaining user
   * {@link UserMessageDto} validation and {@link UserStatusDto} generation
   *
   * @param responderUserStatusFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean
  protected FlowComposer<UserMessageDto, Void> outgoingResponderUserMessageFlow(
      final FlowComposer<UserStatusDto, Void> responderUserStatusFlow) {
    logger.info(
        "Exposing - Responder UserStatus Flow as Responder UserMessage Sink, by verifying UserMessage and transforming to UserStatus");
    return userMessageFlux ->
        userMessageFlux
            .map(this::verifyMessageAndGenerateStatus)
            .transform(responderUserStatusFlow);
  }

  @Bean
  protected FlowComposer<UserStatusDto, Void> outgoingInitiatorUserStatusFlow(
      final Consumer<UserStatusDto> userStatusSubscriber) {
    logger.info("Exposing - Delivery verification consumer as Initiators UserStatus Sink");
    return userStatusFlux -> {
      logger.info("Composing verification consumer into reactive flow");
      return userStatusFlux.doOnNext(userStatusSubscriber).then();
    };
  }

  /**
   * @param userMessage instance of {@link UserMessageDto}
   * @return instance of {@link UserStatusDto}
   */
  private UserStatusDto verifyMessageAndGenerateStatus(final UserMessageDto userMessage) {
    metrics.incrementResponderReceived();
    if (RandomPayload.isChecksumValid(userMessage.getPayload())) {
      return UserStatusDto.builder()
          .withMessageId(userMessage.getMessageId())
          .withProperties(userMessage.getProperties())
          .withDelivered(true)
          .withAbortCode(AbortCode.TCE_PROVIDER_UNKNOWN)
          .build();
    } else {
      metrics.incrementResponderChecksumFailure();
      return UserStatusDto.builder()
          .withMessageId(userMessage.getMessageId())
          .withProperties(userMessage.getProperties())
          .withDelivered(false)
          .withAbortCode(AbortCode.TCE_USER_UNKNOWN)
          .build();
    }
  }

  @Override
  public void destroy() {
    for (final Scheduler flowScheduler : flowSchedulers) {
      flowScheduler.dispose();
    }
  }
}
