package com.volvo.tisp.vwtp.benchmark.configuration;

import static com.volvo.tisp.vwtp.util.LogUtil.FORMAT_TABLE_OUTPUT;
import static java.util.Locale.US;

import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/** Use for value injections from YAML or command line and expose as getters */
@Component
public class BenchmarkProperties implements DisposableBean {
  private static final Logger logger = LoggerFactory.getLogger(BenchmarkProperties.class);

  private final String activeProfile;
  private final long loggingInterval;
  private final long messageCount;
  private final int messageSize;
  private final WtpVersion messageVersion;
  private final TransactionClass messageClass;
  private final int threadCount;
  private final int vehicles;

  protected BenchmarkProperties(
      @Value("${spring.profiles.active:default}") final String activeProfile,
      @Value("${benchmark.logging-interval:2000}") final long loggingInterval,
      @Value("${benchmark.message.count:10_000}") final long messageCount,
      @Value("${benchmark.message.size:100}") final int messageSize,
      @Value("${benchmark.message.version:VERSION_1}") final WtpVersion messageVersion,
      @Value("${benchmark.message.class:CLASS_1}") final TransactionClass messageClass,
      @Value("${benchmark.thread-count:2}") final int threadCount,
      @Value("${benchmark.vehicles:1_000}") final int vehicles) {
    this.activeProfile = activeProfile;
    this.loggingInterval = loggingInterval;
    this.vehicles = vehicles;
    this.messageCount = messageCount;
    this.messageSize = messageSize;
    this.messageVersion = messageVersion;
    this.threadCount = threadCount;
    this.messageClass = messageClass;

    logProperties();
  }

  public String getActiveProfile() {
    return activeProfile;
  }

  public long getLoggingInterval() {
    return loggingInterval;
  }

  public long getMessageCount() {
    return messageCount;
  }

  public int getMessageSize() {
    return messageSize;
  }

  public WtpVersion getMessageVersion() {
    return messageVersion;
  }

  public TransactionClass getMessageClass() {
    return messageClass;
  }

  public int getThreadCount() {
    return threadCount;
  }

  public int getVehicles() {
    return vehicles;
  }

  private void logProperties() {
    if (logger.isInfoEnabled()) {
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "BENCHMARK CONFIG", ""));
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "spring.profiles.active", activeProfile));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "benchmark.logging-interval", loggingInterval));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "benchmark.message.count", messageCount));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "benchmark.message.size", messageSize));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "benchmark.message.version", messageVersion));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "benchmark.message.class", messageClass));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "benchmark.thread-count", threadCount));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "benchmark.vehicles", vehicles));
    }
  }

  @Override
  public void destroy() throws Exception {
    logProperties();
  }
}
