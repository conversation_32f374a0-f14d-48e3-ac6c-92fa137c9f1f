package com.volvo.tisp.vwtp.benchmark;

import static com.volvo.tisp.vwtp.util.LogUtil.FORMAT_TABLE_OUTPUT;
import static java.util.Locale.US;

import com.volvo.tisp.vwtp.benchmark.configuration.BenchmarkProperties;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.stereotype.Component;

@Component
public class Metrics implements DisposableBean {
  private static final Logger logger = LoggerFactory.getLogger(Metrics.class);
  private static final float MEBIBYTES_DIVIDER = 1_048_576f;
  private static final float SECONDS_DIVIDER = 1E9f;

  private final BenchmarkProperties configuration;
  private long startTimeStamp;
  private long timeStampSinceLastLogged;
  private long userStatusReceivedPreviousValue = 0;
  private final AtomicLong responderChecksumFailure = new AtomicLong(0L);
  private final AtomicLong responderReceived = new AtomicLong(0L);
  private final AtomicLong userMessageGenerated = new AtomicLong(0L);
  private final AtomicLong userStatusReceived = new AtomicLong(0L);
  private final AtomicLong userStatusAborted = new AtomicLong(0L);

  public Metrics(final BenchmarkProperties configuration) {
    this.configuration = configuration;
    logRuntimeInformation();
  }

  public synchronized void setStartTime() {
    startTimeStamp = System.nanoTime();
    timeStampSinceLastLogged = startTimeStamp;
  }

  public long incrementResponderChecksumFailure() {
    return responderChecksumFailure.incrementAndGet();
  }

  public long incrementResponderReceived() {
    return responderReceived.incrementAndGet();
  }

  public long incrementUserMessageGenerated() {
    return userMessageGenerated.incrementAndGet();
  }

  public long incrementUserStatusReceived() {
    return userStatusReceived.incrementAndGet();
  }

  public long incrementUserStatusAborted() {
    return userStatusAborted.incrementAndGet();
  }

  public synchronized void logPerformanceSnapshot() {
    if (logger.isInfoEnabled()) {
      final long currentTimeStamp = System.nanoTime();
      final float executionDuration = (currentTimeStamp - startTimeStamp) / SECONDS_DIVIDER;
      final float sinceLastLoggedTime =
          (currentTimeStamp - timeStampSinceLastLogged) / SECONDS_DIVIDER;

      final long userStatusReceivedValue = userStatusReceived.get();
      final long receivedSinceLastLogged =
          userStatusReceivedValue - userStatusReceivedPreviousValue;

      final float currentDeliveredTPS = receivedSinceLastLogged / sinceLastLoggedTime;
      final float overallDeliveredTPS = userStatusReceivedValue / executionDuration;

      final float currentDeliveredMiB =
          receivedSinceLastLogged * configuration.getMessageSize() / MEBIBYTES_DIVIDER;
      final float overallDeliveredMiB =
          userStatusReceivedValue * configuration.getMessageSize() / MEBIBYTES_DIVIDER;

      final float currentDeliveredMiBPS = currentDeliveredMiB / sinceLastLoggedTime;
      final float overallDeliveredMiBPS = overallDeliveredMiB / executionDuration;

      timeStampSinceLastLogged = currentTimeStamp;
      userStatusReceivedPreviousValue = userStatusReceivedValue;

      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "PERFORMANCE SNAPSHOT", ""));
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "Duration (Sec)", executionDuration));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Messages Generated", userMessageGenerated.get()));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Responder Received", responderReceived.get()));
      logger.info(
          String.format(
              US, FORMAT_TABLE_OUTPUT, "Checksum Failure", responderChecksumFailure.get()));
      logger.info(
          String.format(
              US, FORMAT_TABLE_OUTPUT, "User Statuses Received", userStatusReceivedValue));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "User Status - Aborted", userStatusAborted.get()));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Current (Mebibytes/Sec)", currentDeliveredMiBPS));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Overall (Mebibytes/Sec)", overallDeliveredMiBPS));
      logger.info(
          String.format(
              US, FORMAT_TABLE_OUTPUT, "Current (Transactions/Sec)", currentDeliveredTPS));
      logger.info(
          String.format(
              US, FORMAT_TABLE_OUTPUT, "Overall (Transactions/Sec)", overallDeliveredTPS));
    }
  }

  private void logRuntimeInformation() {
    if (logger.isInfoEnabled()) {
      final Runtime runtime = Runtime.getRuntime();
      final int availableProcessors = runtime.availableProcessors();
      final long freeMemory = runtime.freeMemory();
      final long totalMemory = runtime.totalMemory();
      final long maxMemory = runtime.maxMemory();
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "RUNTIME INFO", ""));
      logger.info("");
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Available Processors", availableProcessors));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "Free Memory", freeMemory));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "Total Memory", totalMemory));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "Max Memory", maxMemory));
    }
  }

  @Override
  public void destroy() throws Exception {
    logRuntimeInformation();
  }
}
