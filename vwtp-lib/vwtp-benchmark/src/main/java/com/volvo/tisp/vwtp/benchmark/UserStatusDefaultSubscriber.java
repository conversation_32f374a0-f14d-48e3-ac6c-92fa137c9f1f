package com.volvo.tisp.vwtp.benchmark;

import com.volvo.tisp.vwtp.benchmark.configuration.BenchmarkProperties;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import java.util.function.Consumer;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Profile;
import org.springframework.stereotype.Component;
import reactor.util.Logger;
import reactor.util.Loggers;

@Component
@Profile("!nonstop")
public class UserStatusDefaultSubscriber implements Consumer<UserStatusDto> {
  private static final Logger logger = Loggers.getLogger(UserStatusDefaultSubscriber.class);

  private final Metrics metrics;
  private final ConfigurableApplicationContext applicationContext;
  private final BenchmarkProperties configuration;

  UserStatusDefaultSubscriber(
      final ConfigurableApplicationContext applicationContext,
      final BenchmarkProperties configuration,
      final Metrics metrics) {
    this.applicationContext = applicationContext;
    this.configuration = configuration;
    this.metrics = metrics;
  }

  @Override
  public void accept(final UserStatusDto userStatus) {
    if (!userStatus.isDelivered()) {
      metrics.incrementUserStatusAborted();
    }
    if (metrics.incrementUserStatusReceived() >= configuration.getMessageCount()) {
      logger.info("Last message received. Stopping application - Subscriber.onNext()");
      metrics.logPerformanceSnapshot();
      applicationContext.stop();
    }
  }
}
