package com.volvo.tisp.vwtp.benchmark;

import com.volvo.tisp.vwtp.benchmark.configuration.BenchmarkProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.Banner.Mode;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ConfigurableApplicationContext;

@SpringBootApplication
public class Application implements ApplicationRunner {
  private static final Logger logger = LoggerFactory.getLogger(Application.class);
  public static final String OUTPUT_LINE =
      "------------------------------------------------------------------";

  private final Runnable benchmarkStarter;
  private final ConfigurableApplicationContext context;
  private final Metrics metrics;
  private final BenchmarkProperties configurationService;

  public Application(
      final Runnable benchmarkStarter,
      final ConfigurableApplicationContext context,
      final Metrics metrics,
      final BenchmarkProperties configurationService) {
    this.benchmarkStarter = benchmarkStarter;
    this.context = context;
    this.metrics = metrics;
    this.configurationService = configurationService;
  }

  public static void main(final String[] args) {
    System.setProperty("platform.graphite.enable", "false");
    final SpringApplication application = new SpringApplication(Application.class);
    application.setBannerMode(Mode.OFF);
    application.run(args);
  }

  @Override
  public void run(final ApplicationArguments args) throws Exception {
    logger.info(OUTPUT_LINE);
    logger.info("Starting up VWTP benchmark...");
    logger.info(OUTPUT_LINE);
    benchmarkStarter.run();
    metrics.setStartTime();
    while (context.isRunning()) {
      metrics.logPerformanceSnapshot();
      Thread.sleep(configurationService.getLoggingInterval());
    }
    logger.info(OUTPUT_LINE);
    logger.info("Shutting down VWTP benchmark...");
    logger.info(OUTPUT_LINE);
  }
}
