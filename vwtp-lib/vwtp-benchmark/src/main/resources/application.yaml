#
# Spring properties
#
spring:
  main:
    web-application-type: none
  profiles:
    active: default

logging:
  level:
    root: INFO
  pattern:
    console: "%clr(%d{HH:mm:ss.SSS}){faint} %clr(%1.-1p) %clr(%-10.10t){faint} %clr(%-35.35logger){cyan} %clr(:){faint} %m%n%wEx"

#
# WTP properties
#
wtp.responder-active-transactions: 9_223_372_036_854_775_807

#
# Benchmark properties
#
benchmark:
  logging-interval: 5000
  message:
    count: 1_000_000
    size: 200
    version: VERSION_1
    class: CLASS_1
  thread-count: 2
  vehicles: 10_000
