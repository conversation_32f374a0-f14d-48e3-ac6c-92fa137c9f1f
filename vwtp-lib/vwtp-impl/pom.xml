<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.volvo.tisp.vwtp</groupId>
    <artifactId>vwtp-lib</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>vwtp-impl</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp.vwtp</groupId>
      <artifactId>vwtp-api</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.vwtp</groupId>
      <artifactId>vwtp-model</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.volvo.connectivity.common</groupId>
      <artifactId>component-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.identifiers</groupId>
      <artifactId>tisp-identifiers</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context-support</artifactId>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
    </dependency>
    <dependency>
      <groupId>io.micrometer</groupId>
      <artifactId>micrometer-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>unit-test-lib</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.assertj</groupId>
      <artifactId>assertj-core</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor.tools</groupId>
      <artifactId>blockhound</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <configuration>
          <cpuCount>fork-join-pool.parallelism</cpuCount>
          <factor>0.67</factor>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>cpu-count</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <systemPropertyVariables>
            <!-- Reduce a number of available threads in {@link ForkJoinPool#commonPool}, to reduce thread conjunction and give tiny priority to reactive {@link Scheduler} pool -->
            <java.util.concurrent.ForkJoinPool.common.maximumSpares>0</java.util.concurrent.ForkJoinPool.common.maximumSpares>
            <java.util.concurrent.ForkJoinPool.common.parallelism>${fork-join-pool.parallelism}</java.util.concurrent.ForkJoinPool.common.parallelism>
          </systemPropertyVariables>
          <printSummary>false</printSummary>
          <argLine>@{argLine} -XX:+AllowRedefinitionToAddDeleteMethods</argLine>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <reportSets>
          <reportSet>
            <reports>
              <report>report</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
    </plugins>
  </reporting>
</project>
