package com.volvo.tisp.vwtp.converter.bytestomodel;

import static org.assertj.core.api.Assertions.*;

import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.AbortPduToModelCommonConverter;
import com.volvo.tisp.vwtp.model.AbortModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.test.PayloadUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Test;

/**
 * Test for {@link NetworkMessageToModelCommonConverter} and {@link AbortPduToModelCommonConverter}
 */
class ByteArrayToAbortModelConverterTest {
  /** Test converting WTP version 1 byte array with minimal values to {@link AbortModel} */
  @Test
  void testConvertWtp1BytesToAbortModelMinimum() {
    final byte[] wtpPduPayload = {0b00100000, 0b00000000, 0b00000000, 0b00000000};
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ABORT);
    assertThat((AbortModel) modelCommon)
        .as("AbortModel")
        .hasFieldOrPropertyWithValue("abortCode", AbortCode.TGW_PROVIDER_UNKNOWN)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 1 byte array with maximum values to {@link AbortModel} */
  @Test
  void testConvertWtp1BytesToAbortModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b00100000, (byte) 0b01111111, (byte) 0b11111111, (byte) 0b00001010
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ABORT);
    assertThat((AbortModel) modelCommon)
        .as("AbortModel")
        .hasFieldOrPropertyWithValue(
            "abortCode", AbortCode.TGW_PROVIDER_NOT_IMPLEMENTED_EXTENDED_SAR)
        .hasFieldOrPropertyWithValue("wtpTid", 32_767L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 1 byte array with minimal values to {@link AbortModel} */
  @Test
  void testConvertWtp2BytesToAbortModelMinimum() {
    final byte[] wtpPduPayload = {
      0b01010001, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ABORT);
    assertThat((AbortModel) modelCommon)
        .as("AbortModel")
        .hasFieldOrPropertyWithValue("abortCode", AbortCode.TGW_USER_UNKNOWN)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("vid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 2 byte array with maximum values to {@link AbortModel} */
  @Test
  void testConvertWtp2BytesToAbortModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b01010001,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b10000000,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ABORT);
    assertThat((AbortModel) modelCommon)
        .as("AbortModel")
        .hasFieldOrPropertyWithValue("abortCode", AbortCode.TGW_USER_UNSUPPORTED_SERVICE_VERSION)
        .hasFieldOrPropertyWithValue("wtpTid", 65_535L)
        .hasFieldOrPropertyWithValue("vid", 4_294_967_295L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }
}
