package com.volvo.tisp.vwtp.configuration;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;

import java.time.Duration;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(TestConfiguration.class)
@TestPropertySource("classpath:property-test.properties")
class PropertiesTest {
  @Autowired private InitiatorProperties initiatorProperties;

  @Autowired private ResponderProperties responderProperties;

  @Test
  void testProperties() {
    final Duration[] timerR = {
      Duration.ofSeconds(10), Duration.ofSeconds(20), Duration.ofSeconds(30)
    };
    final Duration[] timerGR = {
      Duration.ofSeconds(10), Duration.ofSeconds(20), Duration.ofSeconds(30)
    };

    assertEquals(96, initiatorProperties.getVersion1MaxPduPayloadSize());
    assertEquals(92, initiatorProperties.getVersion2MaxPduPayloadSize());
    assertEquals(24576, initiatorProperties.getVersion1MaxPayloadSize());
    assertEquals(23552, initiatorProperties.getVersion2MaxPayloadSize());
    assertEquals(4, initiatorProperties.getGroupSize());
    assertArrayEquals(timerR, initiatorProperties.getTimerR());
    assertArrayEquals(timerGR, initiatorProperties.getTimerGR());

    assertEquals(Duration.ofMillis(15000), responderProperties.getUserAcknowledgementInterval());
    assertEquals(Duration.ofMillis(120000), responderProperties.getTransactionTimeoutInterval());
    assertEquals(Duration.ofMillis(5000), responderProperties.getNackDelayInterval());
  }
}
