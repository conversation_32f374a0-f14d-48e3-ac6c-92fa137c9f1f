package com.volvo.tisp.vwtp.test;

import static org.assertj.core.api.Assertions.*;

import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.TpiType;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.model.AbortModel;
import com.volvo.tisp.vwtp.model.AckModel;
import com.volvo.tisp.vwtp.model.InvokeModel;
import com.volvo.tisp.vwtp.model.NackModel;
import com.volvo.tisp.vwtp.model.PsnTpiModel;
import com.volvo.tisp.vwtp.model.SegInvokeModel;
import java.util.Arrays;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.assertj.core.api.ObjectAssert;

/**
 * Collection of verification routines to help put together Responder tests with {@link
 * TestContainer}
 */
public final class Verify {
  private static final long TIMEOUT = 10000L;

  /**
   * Verifies delivered user message
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userGotUserMessage(final TestContainer testContainer) {
    final Optional<UserMessageDto> optionalUserMessage =
        testContainer.getUserMessageCommunicator().poll(TimeUnit.MILLISECONDS, TIMEOUT);

    assertThat(optionalUserMessage)
        .as("Delivered user message (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            userMessage ->
                assertThat(userMessage)
                    .as("Delivered user message (Message ID: %s)", testContainer.getMessageId())
                    .hasFieldOrPropertyWithValue("address", testContainer.getAddress())
                    .hasFieldOrPropertyWithValue("vehicleId", testContainer.getVid())
                    .hasFieldOrPropertyWithValue(
                        "transactionClass", testContainer.getTransactionClass())
                    .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                    .hasFieldOrPropertyWithValue("payload", testContainer.getTestData()));

    return testContainer;
  }

  /**
   * Verifies abort packet from responder with specified {@link AbortCode}
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param abortCode {@link AbortCode}
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer verifyAbortWithCode(
      final TestContainer testContainer, final AbortCode abortCode) {
    final Optional<NetworkMessageDto> optionalNetworkMessage =
        testContainer.getNetworkMessageCommunicator().poll(TimeUnit.MILLISECONDS, TIMEOUT);

    assertThat(optionalNetworkMessage)
        .as("Network data of Abort PDU (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            networkMessage -> {
              assertThat(networkMessage)
                  .as("NetworkMessageDto (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("address", testContainer.getAddress());
              assertThat(PayloadUtil.convertPayloadToModelCommon(networkMessage.getPayload()))
                  .as("ModelCommon (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("pduType", PduType.ABORT)
                  .isInstanceOfSatisfying(
                      AbortModel.class,
                      abortModel ->
                          assertThat(abortModel)
                              .as("AbortModel (Message ID: %s)", testContainer.getMessageId())
                              .hasFieldOrPropertyWithValue("abortCode", abortCode)
                              .hasFieldOrPropertyWithValue("rid", false)
                              .hasFieldOrPropertyWithValue(
                                  "reverseWtpTid", testContainer.getVwtpTid())
                              .hasFieldOrPropertyWithValue(
                                  "wtpVersion", testContainer.getWtpVersion())
                              .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                              .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
                              .isEmpty());
            });

    return testContainer;
  }

  /**
   * Initiator function that receives and verifies abort packet with {@link
   * AbortCode#TGW_PROVIDER_NO_RESPONSE}
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAbortNoUserResponse(final TestContainer testContainer) {
    return verifyAbortWithCode(testContainer, AbortCode.TGW_PROVIDER_NO_RESPONSE);
  }

  /**
   * Initiator function that receives and verifies abort packet with {@link
   * AbortCode#TGW_PROVIDER_NOT_IMPLEMENTED_CLASS}
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAbortNotImplementedClass2(
      final TestContainer testContainer) {
    return verifyAbortWithCode(testContainer, AbortCode.TGW_PROVIDER_NOT_IMPLEMENTED_CLASS);
  }

  /**
   * Verifies message with acknowledgement
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param ackAssert {@link Consumer} responsible for verifying actual {@link ObjectAssert}
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer verifyAckMessage(
      final TestContainer testContainer, final Consumer<ObjectAssert<AckModel>> ackAssert) {
    final Optional<NetworkMessageDto> optionalNetworkMessage =
        testContainer.getNetworkMessageCommunicator().poll(TimeUnit.MILLISECONDS, TIMEOUT);
    assertThat(optionalNetworkMessage)
        .as("Network data of Ack PDU (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            networkMessage -> {
              assertThat(networkMessage)
                  .as("NetworkMessageDto (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("address", testContainer.getAddress());
              assertThat(PayloadUtil.convertPayloadToModelCommon(networkMessage.getPayload()))
                  .as("ModelCommon (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("pduType", PduType.ACK)
                  .isInstanceOfSatisfying(
                      AckModel.class,
                      ackModel ->
                          ackAssert.accept(
                              assertThat(ackModel)
                                  .as("AckModel (Message ID: %s)", testContainer.getMessageId())
                                  .hasFieldOrPropertyWithValue("rid", false)
                                  .hasFieldOrPropertyWithValue(
                                      "wtpVersion", testContainer.getWtpVersion())
                                  .hasFieldOrPropertyWithValue("vid", testContainer.getVid())));
            });
    return testContainer;
  }

  /**
   * Responder function that receives and verifies acknowledgement packet from initiator contains
   * OTR flag
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotAckWithOtr(final TestContainer testContainer) {
    return verifyAckMessage(
        testContainer,
        ackAssert ->
            ackAssert
                .hasFieldOrPropertyWithValue("otr", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .satisfies(
                    ackModel ->
                        assertThat(ackModel.getTpiArray())
                            .as("TpiCommon array (Message ID: %s)", testContainer.getMessageId())
                            .noneSatisfy(
                                tpiCommon ->
                                    assertThat(tpiCommon)
                                        .as(
                                            "TpiCommon (Message ID: %s)",
                                            testContainer.getMessageId())
                                        .hasFieldOrPropertyWithValue("type", TpiType.PSN))));
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder without PSN
   * TPI
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithoutPsnTpi(final TestContainer testContainer) {
    return verifyAckMessage(
        testContainer,
        ackAssert ->
            ackAssert
                .hasFieldOrPropertyWithValue("otr", false)
                .hasFieldOrPropertyWithValue("reverseWtpTid", testContainer.getVwtpTid())
                .satisfies(
                    ackModel ->
                        assertThat(ackModel.getTpiArray())
                            .as("TpiCommon array (Message ID: %s)", testContainer.getMessageId())
                            .noneSatisfy(
                                tpiCommon ->
                                    assertThat(tpiCommon)
                                        .as(
                                            "TpiCommon (Message ID: %s)",
                                            testContainer.getMessageId())
                                        .hasFieldOrPropertyWithValue("type", TpiType.PSN))));
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with any
   * PSN TPI
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithAnyPsnTpi(final TestContainer testContainer) {
    return verifyAckMessage(
        testContainer,
        ackAssert ->
            ackAssert
                .hasFieldOrPropertyWithValue("otr", false)
                .hasFieldOrPropertyWithValue("reverseWtpTid", testContainer.getVwtpTid())
                .satisfies(
                    ackModel ->
                        assertThat(ackModel.getTpiArray())
                            .as("TpiCommon array (Message ID: %s)", testContainer.getMessageId())
                            .anySatisfy(
                                tpiCommon ->
                                    assertThat(tpiCommon)
                                        .as(
                                            "TpiCommon (Message ID: %s)",
                                            testContainer.getMessageId())
                                        .hasFieldOrPropertyWithValue("type", TpiType.PSN))));
  }

  /**
   * Verifies acknowledgement packet from responder with specified PSN TPI
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param psnTpi packet sequence number in PSN TPI
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer verifyAckWithTpi(
      final TestContainer testContainer, final int psnTpi) {
    return verifyAckMessage(
        testContainer,
        ackAssert ->
            ackAssert
                .hasFieldOrPropertyWithValue("otr", false)
                .hasFieldOrPropertyWithValue("reverseWtpTid", testContainer.getVwtpTid())
                .satisfies(
                    ackModel ->
                        assertThat(ackModel.getTpiArray())
                            .as("TpiCommon array (Message ID: %s)", testContainer.getMessageId())
                            .anySatisfy(
                                tpiCommon ->
                                    assertThat(tpiCommon)
                                        .as(
                                            "TpiCommon (Message ID: %s)",
                                            testContainer.getMessageId())
                                        .hasFieldOrPropertyWithValue("type", TpiType.PSN)
                                        .isInstanceOfSatisfying(
                                            PsnTpiModel.class,
                                            psnTpiModel ->
                                                assertThat(psnTpiModel)
                                                    .as(
                                                        "PsnTpiModel (Message ID: %s)",
                                                        testContainer.getMessageId())
                                                    .hasFieldOrPropertyWithValue(
                                                        "packetSequenceNumber", psnTpi)))));
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with PSN
   * TPI value of "02"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithPsnTpi01(final TestContainer testContainer) {
    return verifyAckWithTpi(testContainer, 1);
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with PSN
   * TPI value of "02"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithPsnTpi02(final TestContainer testContainer) {
    return verifyAckWithTpi(testContainer, 2);
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with PSN
   * TPI value of "02"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithPsnTpi03(final TestContainer testContainer) {
    return verifyAckWithTpi(testContainer, 3);
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with PSN
   * TPI value of "05"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithPsnTpi05(final TestContainer testContainer) {
    return verifyAckWithTpi(testContainer, 5);
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with PSN
   * TPI value of "07"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithPsnTpi07(final TestContainer testContainer) {
    return verifyAckWithTpi(testContainer, 7);
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with PSN
   * TPI value of "08"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithPsnTpi08(final TestContainer testContainer) {
    return verifyAckWithTpi(testContainer, 8);
  }

  /**
   * Initiator function that receives and verifies acknowledgement packet from responder with PSN
   * TPI value of "11"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotAckWithPsnTpi11(final TestContainer testContainer) {
    return verifyAckWithTpi(testContainer, 11);
  }

  /**
   * Verifies negative acknowledgement packet from responder with specified PSN TPI array values
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param psn packet sequence numbers in PSN TPI array
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer verifyNack(final TestContainer testContainer, final int[] psn) {
    final Optional<NetworkMessageDto> optionalNetworkMessage =
        testContainer.getNetworkMessageCommunicator().poll(TimeUnit.MILLISECONDS, TIMEOUT);

    assertThat(optionalNetworkMessage)
        .as(
            "Network data of Nack PDU with PSN array '%s' (Message ID: %s)",
            Arrays.toString(psn), testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            networkMessage -> {
              assertThat(networkMessage)
                  .as("NetworkMessageDto (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("address", testContainer.getAddress());
              assertThat(PayloadUtil.convertPayloadToModelCommon(networkMessage.getPayload()))
                  .as("ModelCommon (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("pduType", PduType.NACK)
                  .isInstanceOfSatisfying(
                      NackModel.class,
                      nackModel ->
                          assertThat(nackModel)
                              .as("NackModel (Message ID: %s)", testContainer.getMessageId())
                              .hasFieldOrPropertyWithValue("missingPsn", psn)
                              .hasFieldOrPropertyWithValue("rid", false)
                              .hasFieldOrPropertyWithValue(
                                  "reverseWtpTid", testContainer.getVwtpTid())
                              .hasFieldOrPropertyWithValue(
                                  "wtpVersion", testContainer.getWtpVersion())
                              .hasFieldOrPropertyWithValue("vid", testContainer.getVid()));
            });

    return testContainer;
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [00]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn00(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {0});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [01]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn01(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {1});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [01]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn02(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {2});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [04]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn04(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {4});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [00, 02]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn00_02(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {0, 2});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [01, 02]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn01_02(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {1, 2});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [00, 01, 02]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn00_01_02(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {0, 1, 2});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [04, 05, 06]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn04_05_06(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {4, 5, 6});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with PSN TPI array value [04, 05]
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsn04_05(final TestContainer testContainer) {
    return verifyNack(testContainer, new int[] {4, 5});
  }

  /**
   * Initiator function that receives and verifies negative acknowledgement packet from responder
   * with maximum possible values in PSN TPI
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorGotNackWithPsnMax(final TestContainer testContainer) {
    final int[] allPossiblePsnValues = new int[255];
    for (int index = 0; index < allPossiblePsnValues.length; index++) {
      allPossiblePsnValues[index] = index;
    }
    return verifyNack(testContainer, allPossiblePsnValues);
  }

  /**
   * Verifies message with Invoke PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param invokeAssert {@link Consumer} responsible for asserting actual {@link InvokeModel}
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer verifyInvokeMessage(
      final TestContainer testContainer, final Consumer<ObjectAssert<InvokeModel>> invokeAssert) {
    final Optional<NetworkMessageDto> optionalNetworkMessage =
        testContainer.getNetworkMessageCommunicator().poll(TimeUnit.MILLISECONDS, TIMEOUT);
    assertThat(optionalNetworkMessage)
        .as("Network data of Invoke PDU (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            networkMessage -> {
              assertThat(networkMessage)
                  .as("NetworkMessageDto (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("address", testContainer.getAddress());
              assertThat(networkMessage.getProperties())
                  .as("NetworkMessageDto properties (Message ID: %s)", testContainer.getMessageId())
                  .containsAllEntriesOf(testContainer.getProperties());
              assertThat(PayloadUtil.convertPayloadToModelCommon(networkMessage.getPayload()))
                  .as("ModelCommon (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("pduType", PduType.INVOKE)
                  .isInstanceOfSatisfying(
                      InvokeModel.class,
                      invokeModel -> {
                        testContainer.setVwtpTid(invokeModel.getWtpTid());
                        invokeAssert.accept(
                            assertThat(invokeModel)
                                .as("InvokeModel (Message ID: %s)", testContainer.getMessageId()));
                      })
                  .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
                  .isEmpty();
            });
    return testContainer;
  }

  /**
   * Responder function that receives and verifies that Invoke is for non segmented message
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotNonSegmentedInvoke(final TestContainer testContainer) {
    return verifyInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", true)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("userAck", true)
                .hasFieldOrPropertyWithValue("tcl", testContainer.getTransactionClass())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("payload", testContainer.getTestData()));
  }

  /**
   * Responder function that receives and verifies that Invoke is for non segmented message with
   * TIDnew
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotNonSegmentedInvokeWithTidNew(
      final TestContainer testContainer) {
    return verifyInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", true)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("wtpTidNew", true)
                .hasFieldOrPropertyWithValue("userAck", true)
                .hasFieldOrPropertyWithValue("tcl", testContainer.getTransactionClass())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("payload", testContainer.getTestData()));
  }

  /**
   * Responder function that receives and verifies that Invoke is for non segmented message without
   * payload
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotNonSegmentedInvokeWithNoPayload(
      final TestContainer testContainer) {
    return verifyInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", true)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("userAck", true)
                .hasFieldOrPropertyWithValue("tcl", testContainer.getTransactionClass())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .extracting("payload", as(InstanceOfAssertFactories.BYTE_ARRAY))
                .isEmpty());
  }

  /**
   * Responder function that receives and verifies that retransmitted Invoke is for non segmented
   * message
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotNonSegmentedInvokeWithRid(
      final TestContainer testContainer) {
    return verifyInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", true)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("userAck", true)
                .hasFieldOrPropertyWithValue("tcl", testContainer.getTransactionClass())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("payload", testContainer.getTestData()));
  }

  /**
   * Responder function that receives and verifies that Invoke is for segmented message
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotInvokeWithGtr(final TestContainer testContainer) {
    return verifyInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", true)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("userAck", true)
                .hasFieldOrPropertyWithValue("tcl", testContainer.getTransactionClass())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("payload", testContainer.getInvokeTestData(0)));
  }

  /**
   * Responder function that receives and verifies that Invoke is retransmitted
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotInvokeWithTtrAndRid(final TestContainer testContainer) {
    return verifyInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("userAck", true)
                .hasFieldOrPropertyWithValue("tcl", testContainer.getTransactionClass())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("payload", testContainer.getInvokeTestData(0)));
  }

  /**
   * Responder function that receives and verifies that Invoke is part of retransmission
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotInvokeWithRid(final TestContainer testContainer) {
    return verifyInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("userAck", true)
                .hasFieldOrPropertyWithValue("tcl", testContainer.getTransactionClass())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("payload", testContainer.getInvokeTestData(0)));
  }

  /**
   * Verifies message with Segmented Invoke PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param segInvokeAssert {@link Consumer} responsible for asserting actual {@link ObjectAssert}
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer verifySegInvokeMessage(
      final TestContainer testContainer,
      final Consumer<ObjectAssert<SegInvokeModel>> segInvokeAssert) {
    final Optional<NetworkMessageDto> optionalNetworkMessage =
        testContainer.getNetworkMessageCommunicator().poll(TimeUnit.MILLISECONDS, TIMEOUT);
    assertThat(optionalNetworkMessage)
        .as("Network data of Segmented Invoke PDU (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            networkMessage -> {
              assertThat(networkMessage)
                  .as("NetworkMessageDto (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("address", testContainer.getAddress());
              assertThat(networkMessage.getProperties())
                  .as("NetworkMessageDto properties (Message ID: %s)", testContainer.getMessageId())
                  .containsAllEntriesOf(testContainer.getProperties());
              assertThat(PayloadUtil.convertPayloadToModelCommon(networkMessage.getPayload()))
                  .as("ModelCommon (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("pduType", PduType.SEGINVOKE)
                  .isInstanceOfSatisfying(
                      SegInvokeModel.class,
                      segInvokeModel ->
                          segInvokeAssert.accept(
                              assertThat(segInvokeModel)
                                  .as(
                                      "SegInvokeModel (Message ID: %s)",
                                      testContainer.getMessageId())));
            });
    return testContainer;
  }

  /**
   * Responder function that receives and verifies Segmented Invoke with PSN 01
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithPsn01(final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 1));
  }

  /**
   * Responder function that receives and verifies retransmitted Segmented Invoke with PSN 01
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithRidAndPsn01(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 1));
  }

  /**
   * Responder function that receives and verifies Segmented Invoke with PSN 02
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithPsn02(final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 2));
  }

  /**
   * Responder function that receives and verifies retransmitted Segmented Invoke with PSN 02
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithRidAndPsn02(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 2));
  }

  /**
   * Responder function that receives and verifies Segmented Invoke with PSN 02 is last in a group
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithTtrAndPsn02(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 2));
  }

  /**
   * Responder function that receives and verifies retransmitted Segmented Invoke with PSN 02 is
   * last in transmission
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithTtrAndRidAndPsn02(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 2));
  }

  /**
   * Responder function that receives and verifies Segmented Invoke with PSN 03
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithPsn03(final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 3));
  }

  /**
   * Responder function that receives and verifies retransmitted Segmented Invoke with PSN 03
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithRidAndPsn03(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 3));
  }

  /**
   * Responder function that receives and verifies Segmented Invoke with PSN 04
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithRidAndPsn04(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 4));
  }

  /**
   * Responder function that receives and verifies Segmented Invoke with PSN 04 is last in a group
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithGtrAndPsn04(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", true)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 4));
  }

  /**
   * Responder function that receives and verifies retransmitted Segmented Invoke with PSN 04 is
   * last in a group
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithGtrAndRidAndPsn04(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", true)
                .hasFieldOrPropertyWithValue("ttr", false)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 4));
  }

  /**
   * Responder function that receives and verifies Segmented Invoke with PSN 05 is last in
   * transmission
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithTtrAndPsn05(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", false)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 5));
  }

  /**
   * Responder function that receives and verifies retransmitted Segmented Invoke with PSN 05 is
   * last in transmission
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderGotSegInvokeWithTtrAndRidAndPsn05(
      final TestContainer testContainer) {
    return verifySegInvokeMessage(
        testContainer,
        invokeAssert ->
            invokeAssert
                .hasFieldOrPropertyWithValue("gtr", false)
                .hasFieldOrPropertyWithValue("ttr", true)
                .hasFieldOrPropertyWithValue("rid", true)
                .hasFieldOrPropertyWithValue("wtpTid", testContainer.getVwtpTid())
                .hasFieldOrPropertyWithValue("wtpVersion", testContainer.getWtpVersion())
                .hasFieldOrPropertyWithValue("vid", testContainer.getVid())
                .hasFieldOrPropertyWithValue("psn", 5));
  }

  /**
   * Verifies received {@link UserStatusDto} message
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param statusAssert {@link Consumer} responsible for asserting actual {@link ObjectAssert}
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer verifyUserStatus(
      final TestContainer testContainer, final Consumer<ObjectAssert<UserStatusDto>> statusAssert) {
    final Optional<UserStatusDto> optionalUserStatus =
        testContainer.getUserStatusCommunicator().poll(TimeUnit.MILLISECONDS, TIMEOUT);
    assertThat(optionalUserStatus)
        .as("User Status Message (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            userStatus -> {
              assertThat(userStatus)
                  .as("UserStatusDto (Message ID: %s)", testContainer.getMessageId())
                  .hasFieldOrPropertyWithValue("address", testContainer.getAddress());
              statusAssert.accept(
                  assertThat(userStatus)
                      .as("UserStatusDto (Message ID: %s)", testContainer.getMessageId()));
            });
    return testContainer;
  }

  /**
   * User function that receives and verifies that delivered status has been received
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userGotDeliveredStatus(final TestContainer testContainer) {
    return verifyUserStatus(
        testContainer, statusAssert -> statusAssert.hasFieldOrPropertyWithValue("delivered", true));
  }

  /**
   * User function that receives and verifies that status about message being not delivered is
   * received
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userGotAbortStatus(final TestContainer testContainer) {
    return verifyUserStatus(
        testContainer,
        statusAssert -> statusAssert.hasFieldOrPropertyWithValue("delivered", false));
  }

  /**
   * User function that receives and verifies that user status with abort code {@link
   * AbortCode#TCE_PROVIDER_MESSAGE_TOO_LARGE} has been received
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userGotMessageTooLargeAbortStatus(final TestContainer testContainer) {
    return verifyUserStatus(
        testContainer,
        statusAssert ->
            statusAssert
                .hasFieldOrPropertyWithValue("delivered", false)
                .hasFieldOrPropertyWithValue(
                    "abortCode", AbortCode.TCE_PROVIDER_MESSAGE_TOO_LARGE));
  }

  /**
   * User function that receives and verifies that user status with abort code {@link
   * AbortCode#TGW_PROVIDER_UNKNOWN} has been received
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userGotProviderUnkonwAbortStatus(final TestContainer testContainer) {
    return verifyUserStatus(
        testContainer,
        statusAssert ->
            statusAssert
                .hasFieldOrPropertyWithValue("delivered", false)
                .hasFieldOrPropertyWithValue("abortCode", AbortCode.TGW_PROVIDER_UNKNOWN));
  }

  /**
   * User function that receives and verifies that user status with abort code {@link
   * AbortCode#TGW_PROVIDER_UNKNOWN} has been received
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userGotProviderTransactionStatusAbortStatus(
      final TestContainer testContainer) {
    return verifyUserStatus(
        testContainer,
        statusAssert ->
            statusAssert
                .hasFieldOrPropertyWithValue("delivered", false)
                .hasFieldOrPropertyWithValue(
                    "abortCode", AbortCode.TGW_PROVIDER_TRANSACTION_TIMEOUT));
  }
}
