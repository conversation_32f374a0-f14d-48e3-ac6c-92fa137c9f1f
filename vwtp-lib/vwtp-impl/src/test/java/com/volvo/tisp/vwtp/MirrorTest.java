package com.volvo.tisp.vwtp;

import static org.assertj.core.api.Assertions.assertThat;

import com.volvo.tisp.test.util.RandomPayload;
import com.volvo.tisp.vwtp.configuration.MirrorTestConfiguration;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.test.TestContainer;
import java.net.URI;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(MirrorTestConfiguration.class)
class MirrorTest {
  private static final String VEHICLE_URI = "udp://*********:4242";

  @Autowired private TestContainer.Builder testContainerBuilder;

  @Test
  void testMirrorInitiatorFlow() {
    final TestContainer testContainer =
        testContainerBuilder
            .withAddressURI(URI.create(VEHICLE_URI))
            .withTestData(RandomPayload.generate(88))
            .withTransactionClass(TransactionClass.CLASS_1)
            .withTid(0L)
            .build();

    final String payload = "Hello123";
    final UserMessageDto userMessage =
        testContainer
            .getUserMessageBuilder()
            .withWtpVersion(WtpVersion.VERSION_2)
            .withPayload(payload.getBytes())
            .build();
    testContainer.getUserMessageCommunicator().send(userMessage);

    final Optional<UserMessageDto> maybeUserMessageDto =
        testContainer.getUserMessageCommunicator().poll(TimeUnit.MILLISECONDS, 10000);
    assertThat(maybeUserMessageDto)
        .as("Optional<UserMessageDto> (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            userMessageDto ->
                assertThat(userMessageDto)
                    .as("UserMessageDto (Message ID: %s)", testContainer.getMessageId())
                    .hasFieldOrPropertyWithValue("address", userMessage.getAddress())
                    .hasFieldOrPropertyWithValue("wtpVersion", userMessage.getWtpVersion())
                    .hasFieldOrPropertyWithValue("payload", userMessage.getPayload()));

    final UserStatusDto userStatus =
        testContainer
            .getUserStatusBuilder()
            .withDelivered(true)
            .withAbortCode(AbortCode.TCE_PROVIDER_UNKNOWN)
            .build();
    testContainer.getUserStatusCommunicator().send(userStatus);

    final Optional<UserStatusDto> maybeUserStatus =
        testContainer.getUserStatusCommunicator().poll(TimeUnit.MILLISECONDS, 10000);
    assertThat(maybeUserStatus)
        .as("Optional<UserStatusDto> (Message ID: %s)", testContainer.getMessageId())
        .isNotEmpty()
        .hasValueSatisfying(
            userStatusDto ->
                assertThat(userStatusDto)
                    .as("UserStatus (Message ID: %s)")
                    .hasFieldOrPropertyWithValue("delivered", true));
  }
}
