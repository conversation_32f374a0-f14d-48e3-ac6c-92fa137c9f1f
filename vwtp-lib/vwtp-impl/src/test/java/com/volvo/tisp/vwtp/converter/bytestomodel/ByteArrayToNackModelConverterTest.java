package com.volvo.tisp.vwtp.converter.bytestomodel;

import static org.assertj.core.api.Assertions.*;

import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.NackPduToModelCommonConverter;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.NackModel;
import com.volvo.tisp.vwtp.test.PayloadUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.assertj.core.api.ObjectAssert;
import org.junit.jupiter.api.Test;

/**
 * Test for {@link NetworkMessageToModelCommonConverter} and {@link NackPduToModelCommonConverter}
 */
class ByteArrayToNackModelConverterTest {
  /** Test converting WTP version 1 byte array with minimal values to {@link NackModel} */
  @Test
  void testConvertWtp1BytesToNackModelMinimum() {
    final byte[] wtpPduPayload = {
      (byte) 0b00111000, (byte) 0b10000000, (byte) 0b00000000, (byte) 0b00000001, (byte) 0b00000000
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.NACK);
    assertThat((NackModel) modelCommon)
        .as("NackModel")
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 32_768L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .hasFieldOrPropertyWithValue("missingPsn", new int[] {0b00000000})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 1 byte array with maximum values to {@link NackModel} */
  @Test
  void testConvertWtp1BytesToNackModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b00111001,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b00000011,
      (byte) 0b01111100,
      (byte) 0b01111101,
      (byte) 0b01111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.NACK);
    ObjectAssert<NackModel> nackAssert =
        assertThat((NackModel) modelCommon)
            .as("NackModel")
            .hasFieldOrPropertyWithValue("rid", true)
            .hasFieldOrPropertyWithValue("wtpTid", 65_535L)
            .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1);
    nackAssert
        .extracting("missingPsn", as(InstanceOfAssertFactories.INT_ARRAY))
        .containsOnlyOnce(0b01111100, 0b01111101, 0b01111111);
    nackAssert.extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY)).isEmpty();
  }

  /** Test converting WTP version 2 byte array with minimal values to {@link NackModel} */
  @Test
  void testConvertWtp2BytesToNackModelMinimum() {
    final byte[] wtpPduPayload = {
      (byte) 0b01100000,
      (byte) 0b10000000,
      (byte) 0b00000000,
      (byte) 0b00000001,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.NACK);
    assertThat((NackModel) modelCommon)
        .as("NackModel")
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 32_768L)
        .hasFieldOrPropertyWithValue("missingPsn", new int[] {0b00000000})
        .hasFieldOrPropertyWithValue("vid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 2 byte array with maximum values to {@link NackModel} */
  @Test
  void testConvertWtp2BytesToNackModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b01100001,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b00000011,
      (byte) 0b11111110,
      (byte) 0b11111101,
      (byte) 0b11111100,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.NACK);
    ObjectAssert<NackModel> nackAssert =
        assertThat((NackModel) modelCommon)
            .as("NackModel")
            .hasFieldOrPropertyWithValue("rid", true)
            .hasFieldOrPropertyWithValue("wtpTid", 65_535L)
            .hasFieldOrPropertyWithValue("vid", 4_294_967_295L)
            .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2);
    nackAssert
        .extracting("missingPsn", as(InstanceOfAssertFactories.INT_ARRAY))
        .containsOnlyOnce(0b11111110, 0b11111101, 0b11111100);
    nackAssert.extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY)).isEmpty();
  }
}
