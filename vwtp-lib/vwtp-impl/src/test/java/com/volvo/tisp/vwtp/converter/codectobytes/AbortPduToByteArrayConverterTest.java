package com.volvo.tisp.vwtp.converter.codectobytes;

import com.volvo.tisp.vwtp.builder.AbortPdu2Builder;
import com.volvo.tisp.vwtp.builder.AbortPduBuilder;
import com.volvo.tisp.vwtp.codec.AbortPdu;
import com.volvo.tisp.vwtp.codec.AbortPdu2;
import com.volvo.tisp.vwtp.constants.AbortCode;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

/** Test for {@link AbortPduToByteArrayConverter} */
class AbortPduToByteArrayConverterTest {
  /** Test generating and converting {@link AbortPdu} with default values */
  @Test
  void testConvertAbortPduDefault() {
    final byte[] expectedPayload = {0b00100000, 0b00000000, 0b00000000, 0b00000000};
    final AbortPdu abortPdu = AbortPduBuilder.builder().buildAbortPdu();
    final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
    Assertions.assertThat(payload).as("AbortPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AbortPdu} with changed values */
  @Test
  void testConvertAbortPdu() {
    final byte[] expectedPayload = {
      (byte) 0b00100001, (byte) 0b01000000, (byte) 0b00000000, (byte) 0b10000000
    };
    final AbortPdu abortPdu =
        AbortPduBuilder.builder()
            .withAbortCode(AbortCode.TCE_USER_UNSUPPORTED_SERVICE_VERSION)
            .withWtpTid(16_384L)
            .buildAbortPdu();
    final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
    Assertions.assertThat(payload).as("AbortPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AbortPdu2} with default values */
  @Test
  void testConvertAbortPdu2Default() {
    final byte[] expectedPayload = {
      0b01010000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000
    };
    final AbortPdu2 abortPdu = AbortPdu2Builder.builder().buildAbortPdu2();
    final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
    Assertions.assertThat(payload).as("AbortPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AbortPdu2} with changed values */
  @Test
  void testConvertAbortPdu2() {
    final byte[] expectedPayload = {
      (byte) 0b01010000,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b00001010,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final AbortPdu2 abortPdu =
        AbortPdu2Builder.builder()
            .withAbortCode(AbortCode.TCE_PROVIDER_NOT_IMPLEMENTED_EXTENDED_SAR)
            .withWtpTidReverseDirection(32_767L)
            .withVid(4_294_967_295L)
            .buildAbortPdu2();
    final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
    Assertions.assertThat(payload).as("AbortPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }
}
