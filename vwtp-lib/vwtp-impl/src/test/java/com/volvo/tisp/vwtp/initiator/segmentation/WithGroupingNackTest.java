package com.volvo.tisp.vwtp.initiator.segmentation;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(TestConfiguration.class)
class WithGroupingNackTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  /** WTP version 1 - single test */
  @Test
  void testWtp1() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        576,
        96,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel transactions test */
  @Test
  void testWtp1WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        576,
        96,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel vehicles test */
  @Test
  void testWtp1WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        576,
        96,
        this::executionSequence);
  }

  /** WTP version 2 - single test */
  @Test
  void testWtp2() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        552,
        92,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel transactions test */
  @Test
  void testWtp2WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        552,
        92,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel vehicles test */
  @Test
  void testWtp2WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        552,
        92,
        this::executionSequence);
  }

  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> future) {
    return future
        .thenApply(Create::userMessage)
        .thenApply(Verify::responderGotInvokeWithGtr)
        .thenApply(Create::responderAckWithPsn00)
        .thenApply(Verify::responderGotSegInvokeWithPsn01)
        .thenApply(Verify::responderGotSegInvokeWithPsn02)
        .thenApply(Verify::responderGotSegInvokeWithPsn03)
        .thenApply(Verify::responderGotSegInvokeWithGtrAndPsn04)
        .thenApply(Create::responderNackWithPsn03)
        .thenApply(Verify::responderGotSegInvokeWithRidAndPsn03)
        .thenApply(Create::responderAckWithPsn04)
        .thenApply(Verify::responderGotSegInvokeWithTtrAndPsn05)
        .thenApply(Create::responderAckWithPsn05)
        .thenApply(Verify::userGotDeliveredStatus)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
