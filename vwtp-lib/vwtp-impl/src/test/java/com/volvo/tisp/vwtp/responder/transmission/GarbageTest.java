package com.volvo.tisp.vwtp.responder.transmission;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/** Verify that WTP responder implementation can handle garbage data without crashing */
@SpringJUnitConfig(TestConfiguration.class)
class GarbageTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  /** WTP version 1 - single test */
  @Test
  void test() {
    TestUtil.runSingleTest(testContainerBuilder, null, null, 0, 0, this::executionSequence);
  }

  /**
   * Test execution sequence
   *
   * @param completableFuture {@link CompletableFuture} with initial {@link TestContainer}
   *     configuration
   * @return {@link CompletableFuture} for this test
   */
  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> completableFuture) {
    return completableFuture
        .thenApply(Create::garbage)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
