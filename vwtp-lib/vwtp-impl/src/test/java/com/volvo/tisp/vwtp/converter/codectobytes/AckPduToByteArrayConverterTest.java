package com.volvo.tisp.vwtp.converter.codectobytes;

import com.volvo.tisp.vwtp.builder.AckPdu2Builder;
import com.volvo.tisp.vwtp.builder.AckPduBuilder;
import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

/** Test for {@link AckPduToByteArrayConverter} */
class AckPduToByteArrayConverterTest {
  /** Test generating and converting {@link AckPdu} with default values */
  @Test
  void testConvertAckPduDefault() {
    final byte[] expectedPayload = {0b00011000, 0b00000000, 0b00000000};
    final AckPdu ackPdu = AckPduBuilder.builder().buildAckPdu();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu);
    Assertions.assertThat(payload).as("AckPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AckPdu} with changed values */
  @Test
  void testConvertAckPdu() {
    final byte[] expectedPayload = {
      0b00011101, 0b01000000, 0b00000000,
    };
    final AckPdu ackPdu =
        AckPduBuilder.builder().withOtr(true).withRid(true).withWtpTid(16_384L).buildAckPdu();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu);
    Assertions.assertThat(payload).as("AckPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AckPdu} with PSN TPI and default values */
  @Test
  void testConvertAckPduWithPsnTpiDefault() {
    final byte[] expectedPayload = {
      (byte) 0b10011000, (byte) 0b00000000, (byte) 0b00000000, (byte) 0b00011001, (byte) 0b00000001
    };
    final AckPdu ackPdu = AckPduBuilder.builder().buildAckPdu();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu, 1L);
    Assertions.assertThat(payload).as("AckPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AckPdu} with PSN TPI and changed values */
  @Test
  void testConvertAckPduWithPsnTpi() {
    final byte[] expectedPayload = {
      (byte) 0b10011101, (byte) 0b11111111, (byte) 0b11111111, (byte) 0b00011001, (byte) 0b11111111
    };
    final AckPdu ackPdu =
        AckPduBuilder.builder()
            .withOtr(true)
            .withRid(true)
            .withWtpTidReverseDirection(32_767L)
            .buildAckPdu();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu, 255L);
    Assertions.assertThat(payload).as("AckPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AckPdu2} with default values */
  @Test
  void testConvertAckPdu2Default() {
    final byte[] expectedPayload = {
      0b01001000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000
    };
    final AckPdu2 ackPdu = AckPdu2Builder.builder().buildAckPdu2();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu);
    Assertions.assertThat(payload).as("AckPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AckPdu2} with changed values */
  @Test
  void testConvertAckPdu2() {
    final byte[] expectedPayload = {
      (byte) 0b01001101,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final AckPdu2 ackPdu =
        AckPdu2Builder.builder()
            .withOtr(true)
            .withRid(true)
            .withWtpTidReverseDirection(32_767L)
            .withVid(4_294_967_295L)
            .buildAckPdu2();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu);
    Assertions.assertThat(payload).as("AckPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AckPdu2} with PSN TPI and default values */
  @Test
  void testConvertAckPdu2WithPsnTpiDefault() {
    final byte[] expectedPayload = {
      (byte) 0b11001000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00011001,
      (byte) 0b00000001
    };
    final AckPdu2 ackPdu = AckPdu2Builder.builder().buildAckPdu2();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu, 1L);
    Assertions.assertThat(payload).as("AckPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link AckPdu2} with PSN TPI and changed values */
  @Test
  void testConvertAckPdu2WithPsnTpi() {
    final byte[] expectedPayload = {
      (byte) 0b11001101,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b00011001,
      (byte) 0b11111111
    };
    final AckPdu2 ackPdu =
        AckPdu2Builder.builder()
            .withOtr(true)
            .withRid(true)
            .withWtpTidReverseDirection(32_767L)
            .withVid(4_294_967_295L)
            .buildAckPdu2();
    final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu, 255L);
    Assertions.assertThat(payload).as("AckPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }
}
