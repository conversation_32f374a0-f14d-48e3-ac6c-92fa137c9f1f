package com.volvo.tisp.vwtp.converter.codectobytes;

import com.volvo.tisp.vwtp.builder.InvokePdu2Builder;
import com.volvo.tisp.vwtp.builder.InvokePduBuilder;
import com.volvo.tisp.vwtp.codec.InvokePdu;
import com.volvo.tisp.vwtp.codec.InvokePdu2;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

/** Test for {@link InvokePduToByteArrayConverter} */
class InvokePduToByteArrayConverterTest {
  /** Test generating and converting {@link InvokePdu} with default values */
  @Test
  void testConvertInvokePduDefault() {
    final byte[] expectedPayload = {0b00001000, 0b00000000, 0b00000000, 0b00000000, 0b00000000};
    final InvokePdu invokePdu = InvokePduBuilder.builder().buildInvokePdu();
    final byte[] payload =
        InvokePduToByteArrayConverter.convert(invokePdu, new byte[] {0b00000000});
    Assertions.assertThat(payload).as("InvokePdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link InvokePdu} with changed values */
  @Test
  void testConvertInvokePdu() {
    final byte[] expectedPayload = {0b00001111, 0b01111111, 0b01111111, 0b00110001, 0b01111111};
    final InvokePdu invokePdu =
        InvokePduBuilder.builder()
            .withGtr(true)
            .withTtr(true)
            .withRid(true)
            .withWtpTid(32_639L)
            .withTidNew(true)
            .withUserAck(true)
            .withTransactionClass(TransactionClass.CLASS_1)
            .buildInvokePdu();
    final byte[] payload =
        InvokePduToByteArrayConverter.convert(invokePdu, new byte[] {0b01111111});
    Assertions.assertThat(payload).as("InvokePdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link InvokePdu2} with default values */
  @Test
  void testConvertInvokePdu2Default() {
    final byte[] expectedPayload = {
      0b01000000,
      0b00000000,
      0b00000000,
      0b01000000,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000
    };
    final InvokePdu2 invokePdu = InvokePdu2Builder.builder().buildInvokePdu2();
    final byte[] payload =
        InvokePduToByteArrayConverter.convert(invokePdu, new byte[] {0b00000000});
    Assertions.assertThat(payload).as("InvokePdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link InvokePdu2} with changed values */
  @Test
  void testConvertInvokePdu2() {
    final byte[] expectedPayload = {
      (byte) 0b01000111,
      (byte) 0b01111111,
      (byte) 0b11111111,
      (byte) 0b01110001,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final InvokePdu2 invokePdu =
        InvokePdu2Builder.builder()
            .withGtr(true)
            .withTtr(true)
            .withRid(true)
            .withWtpTid(32_767L)
            .withTidNew(true)
            .withUserAck(true)
            .withTransactionClass(TransactionClass.CLASS_1)
            .withVid(4_294_967_295L)
            .buildInvokePdu2();
    final byte[] payload =
        InvokePduToByteArrayConverter.convert(invokePdu, new byte[] {(byte) 0b11111111});
    Assertions.assertThat(payload).as("InvokePdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }
}
