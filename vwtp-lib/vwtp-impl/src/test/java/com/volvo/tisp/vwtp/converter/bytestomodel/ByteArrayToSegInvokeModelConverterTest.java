package com.volvo.tisp.vwtp.converter.bytestomodel;

import static org.assertj.core.api.Assertions.*;

import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.InvokePduToModelCommonConverter;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.SegInvokeModel;
import com.volvo.tisp.vwtp.test.PayloadUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Test;

/**
 * Test for {@link NetworkMessageToModelCommonConverter} and segmented {@link
 * InvokePduToModelCommonConverter}
 */
class ByteArrayToSegInvokeModelConverterTest {
  /** Test converting WTP version 1 byte array with minimal values to {@link SegInvokeModel} */
  @Test
  void testConvertWtp1BytesToSegInvokeModelMinimum() {
    final byte[] wtpPduPayload = {0b00101000, 0b00000000, 0b00000000, 0b00000001, 0b00000000};
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.SEGINVOKE);
    assertThat((SegInvokeModel) modelCommon)
        .as("SegInvokeModel")
        .hasFieldOrPropertyWithValue("gtr", false)
        .hasFieldOrPropertyWithValue("ttr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("psn", 1)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .hasFieldOrPropertyWithValue("payload", new byte[] {0b00000000})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 1 byte array with maximum values to {@link SegInvokeModel} */
  @Test
  void testConvertWtp1BytesToSegInvokeModelMmaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b00101111, (byte) 0b01111111, (byte) 0b11111111, (byte) 0b11111111, (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.SEGINVOKE);
    assertThat((SegInvokeModel) modelCommon)
        .as("SegInvokeModel")
        .hasFieldOrPropertyWithValue("gtr", true)
        .hasFieldOrPropertyWithValue("ttr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 32_767L)
        .hasFieldOrPropertyWithValue("psn", 255)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .hasFieldOrPropertyWithValue("payload", new byte[] {(byte) 0b11111111})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 2 byte array with minimal values to {@link SegInvokeModel} */
  @Test
  void testConvertWtp2BytesToSegInvokeModelMinimum() {
    final byte[] wtpPduPayload = {
      0b01011000,
      0b00000000,
      0b00000000,
      0b00000001,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.SEGINVOKE);
    assertThat((SegInvokeModel) modelCommon)
        .as("SegInvokeModel")
        .hasFieldOrPropertyWithValue("gtr", false)
        .hasFieldOrPropertyWithValue("ttr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("psn", 1)
        .hasFieldOrPropertyWithValue("vid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("payload", new byte[] {0b00000000})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 2 byte array with maximum values to {@link SegInvokeModel} */
  @Test
  void testConvertWtp2BytesToSegInvokeModelMmaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b01011111,
      (byte) 0b01111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.SEGINVOKE);
    assertThat((SegInvokeModel) modelCommon)
        .as("SegInvokeModel")
        .hasFieldOrPropertyWithValue("gtr", true)
        .hasFieldOrPropertyWithValue("ttr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 32_767L)
        .hasFieldOrPropertyWithValue("psn", 255)
        .hasFieldOrPropertyWithValue("vid", 4_294_967_295L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("payload", new byte[] {(byte) 0b11111111})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }
}
