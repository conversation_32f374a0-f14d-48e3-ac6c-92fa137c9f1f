package com.volvo.tisp.vwtp.initiator.transmission;

import com.github.benmanes.caffeine.cache.Cache;
import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(TestConfiguration.class)
class TidWrapAroundMessagesTest {
  private static final long INITIAL_LAST_WTP_TID = 32767L;

  @Autowired private Cache<URI, AtomicLong> initiatorLastWtpTidCache;

  @Autowired private TestContainer.Builder testContainerBuilder;

  /**
   * Inserts maximum possible WTP TID to Initiators LastWtpTidCache for the current vehicle to
   * simulate TID wrap around
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  TestContainer populateInitiatorLastWtpTidCache(final TestContainer testContainer) {
    initiatorLastWtpTidCache.put(testContainer.getAddress(), new AtomicLong(INITIAL_LAST_WTP_TID));
    return testContainer;
  }

  /** WTP version 1 - single test */
  @Test
  void testWtp1() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        96,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel vehicles test */
  @Test
  void testWtp1WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        96,
        this::executionSequence);
  }

  /** WTP version 2 - single test */
  @Test
  void testWtp2() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        92,
        92,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel vehicles test */
  @Test
  void testWtp2WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        92,
        92,
        this::executionSequence);
  }

  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> future) {
    return future
        .thenApply(this::populateInitiatorLastWtpTidCache)
        .thenApply(Create::userMessage)
        .thenApply(Verify::responderGotNonSegmentedInvokeWithTidNew)
        .thenApply(Create::responderAck)
        .thenApply(Verify::userGotDeliveredStatus)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
