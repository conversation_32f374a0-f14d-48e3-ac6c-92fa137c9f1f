package com.volvo.tisp.vwtp.initiator.transmission;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.PropertyKey;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(TestConfiguration.class)
@TestPropertySource(properties = PropertyKey.WTP_TIMER_R + "=1,1,1")
class TimeoutTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  /** WTP version 1 - single test */
  @Test
  void testWtp1() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        96,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel transactions test */
  @Test
  void testWtp1WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        96,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel vehicles test */
  @Test
  void testWtp1WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        96,
        this::executionSequence);
  }

  /** WTP version 2 - single test */
  @Test
  void testWtp2() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        92,
        92,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel transactions test */
  @Test
  void testWtp2WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        92,
        92,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel vehicles test */
  @Test
  void testWtp2WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        92,
        92,
        this::executionSequence);
  }

  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> future) {
    return future
        .thenApply(Create::userMessage)
        .thenApply(Verify::responderGotNonSegmentedInvoke)
        .thenApply(Verify::responderGotNonSegmentedInvokeWithRid)
        .thenApply(Verify::responderGotNonSegmentedInvokeWithRid)
        .thenApply(Verify::userGotAbortStatus)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
