package com.volvo.tisp.vwtp.test;

import java.util.Optional;
import java.util.concurrent.TimeUnit;
import reactor.core.publisher.Sinks.EmitFailureHandler;
import reactor.core.publisher.Sinks.EmitResult;

/**
 * Communicator for defining general interface how to communicate with WTP module
 *
 * @param <M> implementation specific message exchange type
 */
public interface Communicator<M> {

  EmitFailureHandler RETRY_NON_SERIALIZED =
      (signalType, emissionResult) -> emissionResult == EmitResult.FAIL_NON_SERIALIZED;

  /**
   * Retrieves a message from the WTP module or waits specified amount of time for message to
   * arrive.
   *
   * @param timeUnit {@link TimeUnit} determining how to interpret the {@code timeout} parameter
   * @param timeout how long to wait before giving up, in units of {@code timeUnit}
   * @return {@link Optional} implementation specific message exchange type
   */
  Optional<M> poll(TimeUnit timeUnit, long timeout);

  /**
   * Sends a message from the WTP module
   *
   * @param message implementation specific message exchange type
   */
  void send(M message);

  /** Clears the message queue */
  void clear();
}
