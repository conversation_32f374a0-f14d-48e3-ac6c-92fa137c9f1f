package com.volvo.tisp.vwtp.initiator;

import static org.junit.jupiter.api.Assertions.assertArrayEquals;

import com.volvo.tisp.vwtp.configuration.InitiatorProperties;
import com.volvo.tisp.vwtp.constants.PropertyKey;
import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(InitiatorProperties.class)
class ConfigurationTest {
  @Autowired private InitiatorProperties initiatorProperties;

  @Test
  void testUserPropertiesOverrides() {
    final Map<String, String> messageProperties = new HashMap<>();

    messageProperties.put(PropertyKey.WTP_TIMER_GR, "1000,2000,5000");
    messageProperties.put(PropertyKey.WTP_TIMER_R, "2000,3000,4000");

    final InitiatorProperties clonedProperties =
        initiatorProperties.cloneWithMessageProperties(messageProperties);

    assertArrayEquals(
        new Duration[] {Duration.ofMillis(1000), Duration.ofMillis(2000), Duration.ofMillis(5000)},
        clonedProperties.getTimerGR());
    assertArrayEquals(
        new Duration[] {Duration.ofMillis(2000), Duration.ofMillis(3000), Duration.ofMillis(4000)},
        clonedProperties.getTimerR());
  }

  @Test
  void testDefaultPropertiesAreUsed() {
    final InitiatorProperties clonedProperties =
        initiatorProperties.cloneWithMessageProperties(new HashMap<>());

    assertArrayEquals(
        new Duration[] {
          Duration.ofMillis(15000),
          Duration.ofMillis(15000),
          Duration.ofMillis(15000),
          Duration.ofMillis(15000)
        },
        clonedProperties.getTimerGR());
    assertArrayEquals(
        new Duration[] {
          Duration.ofMillis(5000),
          Duration.ofMillis(10000),
          Duration.ofMillis(15000),
          Duration.ofMillis(15000),
          Duration.ofMillis(15000)
        },
        clonedProperties.getTimerR());
  }
}
