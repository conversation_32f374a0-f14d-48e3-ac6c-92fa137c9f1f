package com.volvo.tisp.vwtp.test;

import org.assertj.core.presentation.StandardRepresentation;

/**
 * AssertJ object representation handling class extended to display byte primitives and Byte objects
 * as hexadecimal strings
 */
final class ByteToHexRepresentation extends StandardRepresentation {
  static final ByteToHexRepresentation BYTE_TO_HEX_REPRESENTATION = new ByteToHexRepresentation();

  /** Default private constructor */
  private ByteToHexRepresentation() {
    super();
  }

  @Override
  protected String toStringOf(final Number number) {
    if (number instanceof Byte) {
      return String.format("%02X", number);
    }
    return super.toStringOf(number);
  }
}
