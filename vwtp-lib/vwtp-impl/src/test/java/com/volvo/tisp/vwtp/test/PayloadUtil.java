package com.volvo.tisp.vwtp.test;

import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.model.ModelCommon;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import reactor.core.publisher.Mono;
import reactor.core.publisher.SynchronousSink;
import reactor.util.context.Context;
import reactor.util.context.ContextView;
import reactor.util.function.Tuple2;

/** Utility class for payload manipulation */
public class PayloadUtil {
  /**
   * Convert VWTP payload (byte[]) to {@link ModelCommon}
   *
   * @param byteArraypayload byte array with WTP PDU's
   * @return {@link Mono}
   */
  @Nullable
  public static ModelCommon convertPayloadToModelCommon(final @NonNull byte[] byteArraypayload) {
    final NetworkMessageDto networkMessage =
        NetworkMessageDto.builder().withPayload(byteArraypayload).build();
    final DummySink dummySink = new DummySink();
    NetworkMessageToModelCommonConverter.convert(networkMessage, dummySink);
    return dummySink.modelCommon;
  }

  /** Dummy implementation of {@link SynchronousSink} to extract {@link ModelCommon} */
  private static final class DummySink
      implements SynchronousSink<Tuple2<NetworkMessageDto, ModelCommon>> {
    private ModelCommon modelCommon;

    @Override
    public void complete() {}

    /** Delete once deprecated overridden method is removed */
    @Override
    @Deprecated
    public Context currentContext() {
      return null;
    }

    @Override
    public ContextView contextView() {
      return null;
    }

    @Override
    public void error(final Throwable e) {}

    @Override
    public void next(final Tuple2<NetworkMessageDto, ModelCommon> tuple) {
      modelCommon = tuple.getT2();
    }
  }
}
