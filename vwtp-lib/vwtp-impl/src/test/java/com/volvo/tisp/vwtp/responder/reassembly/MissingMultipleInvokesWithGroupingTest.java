package com.volvo.tisp.vwtp.responder.reassembly;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.PropertyKey;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * Verify that WTP responder implementation can handle a missing SegInvoke in re-assembly with
 * grouping.
 *
 * <p>Specification reference: 7.14.3. Procedure for Packet Groups
 */
@SpringJUnitConfig(TestConfiguration.class)
@TestPropertySource(properties = PropertyKey.WTP_TIMER_SN + "=1")
class MissingMultipleInvokesWithGroupingTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  /** WTP version 1 - single test */
  @Test
  void testWtp1() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        64,
        8,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel transactions test */
  @Test
  void testWtp1WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        64,
        8,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel vehicles test */
  @Test
  void testWtp1WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        64,
        8,
        this::executionSequence);
  }

  /** WTP version 2 - single test */
  @Test
  void testWtp2() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        64,
        8,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel transactions test */
  @Test
  void testWtp2WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        64,
        8,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel vehicles test */
  @Test
  void testWtp2WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        64,
        8,
        this::executionSequence);
  }

  /**
   * Test execution sequence
   *
   * @param completableFuture {@link CompletableFuture} with initial {@link TestContainer}
   *     configuration
   * @return {@link CompletableFuture} for this test
   */
  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> completableFuture) {
    return completableFuture
        .thenApply(Create::segInvoke03WithGtr)
        .thenApply(Verify::initiatorGotNackWithPsn00_01_02)
        .thenApply(Create::segInvoke01WithRid)
        .thenApply(Verify::initiatorGotNackWithPsn00_02)
        .thenApply(Create::invokeWithUserAckAndRid)
        .thenApply(Verify::initiatorGotNackWithPsn02)
        .thenApply(Create::segInvoke02WithRid)
        .thenApply(Verify::initiatorGotAckWithPsnTpi03)
        .thenApply(Create::segInvoke07WithTtr)
        .thenApply(Verify::initiatorGotNackWithPsn04_05_06)
        .thenApply(Create::segInvoke06WithRid)
        .thenApply(Verify::initiatorGotNackWithPsn04_05)
        .thenApply(Create::segInvoke05WithRid)
        .thenApply(Verify::initiatorGotNackWithPsn04)
        .thenApply(Create::segInvoke04WithRid)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::initiatorGotAckWithPsnTpi07)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
