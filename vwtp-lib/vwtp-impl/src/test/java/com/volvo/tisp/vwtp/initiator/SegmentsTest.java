package com.volvo.tisp.vwtp.initiator;

import com.volvo.tisp.test.util.RandomPayload;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import java.net.URI;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;
import java.util.stream.IntStream;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class SegmentsTest {
  private static final String INITIATOR_URI = "udp://10.0.0.1:4242";
  private static final String MESSAGE_ID = UUID.randomUUID().toString();
  private static final int PAYLOAD_SIZE = 2048;

  @Test
  void test() {
    final InitiatorSegments segments = createInitiatorSegments();

    final int expectedMaxPsn = (int) Math.floor((double) PAYLOAD_SIZE / 250);

    Assertions.assertThat(segments.getMinPsnValue()).isZero();
    Assertions.assertThat(segments.getMaxPsnValue()).isEqualTo(expectedMaxPsn);
    Assertions.assertThat(segments.getMaxSegmentsInGroup()).isEqualTo(8);

    Assertions.assertThat(segments.isLastGroup(0)).isFalse();
    Assertions.assertThat(segments.isLastGroup(expectedMaxPsn)).isTrue();

    Assertions.assertThat(segments.getFirstSegment().getPsn()).isZero();
    Assertions.assertThat(segments.getLastSegment().getPsn()).isEqualTo(expectedMaxPsn);

    Assertions.assertThat(
            segments
                .getSegments(
                    IntStream.rangeClosed(segments.getMinPsnValue(), segments.getMaxPsnValue())
                        .toArray())
                .size())
        .isEqualTo(expectedMaxPsn + 1);

    Assertions.assertThat(segments.getGroup(0, 7).size()).isEqualTo(8);
    Assertions.assertThat(segments.getGroup(0).size()).isEqualTo(8);
  }

  private static InitiatorSegments createInitiatorSegments() {
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(PAYLOAD_SIZE), 250, 8);

    return new InitiatorSegments(segments, 8);
  }

  private static UserMessageDto createUserMessageDto(final int payloadSize) {
    return UserMessageDto.builder()
        .withAddress(URI.create(INITIATOR_URI))
        .withWtpVersion(WtpVersion.VERSION_1)
        .withTransactionClass(TransactionClass.CLASS_1)
        .withMessageId(MESSAGE_ID)
        .withPayload(payloadSize == 0 ? new byte[] {} : RandomPayload.generate(payloadSize))
        .withProperties(Collections.emptyMap())
        .build();
  }
}
