package com.volvo.tisp.vwtp.configuration;

import com.volvo.tisp.flow.FlowBalancer;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;

/** Spring test configuration for mirror tests */
@Configuration
@Import(TestConfiguration.class)
public class MirrorTestConfiguration {
  /**
   * Exposing "initiatorNetworkMessageFlow" as "outgoingControllerNetworkMessageFlowForInitiator"
   *
   * @param initiatorNetworkMessageFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean
  protected FlowComposer<NetworkMessageDto, Void> outgoingControllerNetworkMessageFlowForInitiator(
      @Lazy final FlowComposer<NetworkMessageDto, Void> initiatorNetworkMessageFlow) {
    return initiatorNetworkMessageFlow;
  }

  /**
   * Exposing "responderNetworkMessageFlow" as "outgoingControllerNetworkMessageFlowForResponder"
   *
   * @param responderNetworkMessageFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean
  protected FlowComposer<NetworkMessageDto, Void> outgoingControllerNetworkMessageFlowForResponder(
      @Lazy final FlowComposer<NetworkMessageDto, Void> responderNetworkMessageFlow) {
    return responderNetworkMessageFlow;
  }

  /**
   * Exposing "outgoingNetworkMessageFlow" as "outgoingInitiatorNetworkMessageFlow" and
   * "outgoingResponderNetworkMessageFlow"
   *
   * @param balancedNetworkMessageFlow instance of {@link FlowBalancer}
   * @return instance of {@link FlowComposer}
   */
  @Bean({"outgoingInitiatorNetworkMessageFlow", "outgoingResponderNetworkMessageFlow"})
  protected FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow(
      final FlowBalancer<NetworkMessageDto, Void> balancedNetworkMessageFlow) {
    return balancedNetworkMessageFlow;
  }
}
