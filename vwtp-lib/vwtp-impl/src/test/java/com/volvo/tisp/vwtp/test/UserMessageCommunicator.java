package com.volvo.tisp.vwtp.test;

import com.volvo.tisp.flow.FlowBalancer;
import com.volvo.tisp.vwtp.controller.NetworkMessageFlow;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Sinks;
import reactor.util.Logger;
import reactor.util.Loggers;

/** Communicator to interface {@link NetworkMessageFlow} from the WTP user side */
class UserMessageCommunicator implements Communicator<UserMessageDto> {
  private static final Logger logger = Loggers.getLogger(UserMessageCommunicator.class);

  private final String queueName;
  private final BlockingQueue<UserMessageDto> userMessageQueue;
  private final Sinks.Many<UserMessageDto> userMessageFluxSink;

  private UserMessageCommunicator(final Builder builder) {
    userMessageQueue = builder.userMessageQueue;
    queueName = builder.queueName;
    userMessageFluxSink = builder.userMessageFluxSink;
  }

  @Override
  public Optional<UserMessageDto> poll(final TimeUnit timeUnit, final long timeout) {
    logger.debug("=== Starting poll on user message queue {} ===", queueName);
    try {
      return Optional.ofNullable(userMessageQueue.poll(timeout, timeUnit));
    } catch (final InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException(e);
    } finally {
      logger.debug("=== Ending poll on user message queue {} ===", queueName);
    }
  }

  @Override
  public void send(final UserMessageDto userMessage) {
    logger.debug("=== Sending DOWN to WTP module ===⏎\n\n{}\n", userMessage);
    userMessageFluxSink.emitNext(userMessage, RETRY_NON_SERIALIZED);
  }

  @Override
  public void clear() {
    userMessageQueue.clear();
  }

  /** Builder for {@link UserStatusCommunicator} */
  @Component
  static class Builder
      implements org.apache.commons.lang3.builder.Builder<UserMessageCommunicator>, DisposableBean {
    private final Sinks.Many<UserMessageDto> userMessageFluxSink;
    private String queueName;
    private BlockingQueue<UserMessageDto> userMessageQueue;

    @Autowired
    protected Builder(final FlowBalancer<UserMessageDto, Void> balancedUserMessageFlow) {
      userMessageFluxSink = Sinks.many().unicast().onBackpressureError();
      userMessageFluxSink
          .asFlux()
          .transform(balancedUserMessageFlow)
          .subscribe(null, throwable -> logger.error(throwable.getMessage(), throwable));
    }

    Builder withQueueName(final String queueName) {
      this.queueName = queueName;
      return this;
    }

    Builder withUserStatusQueue(final BlockingQueue<UserMessageDto> userMessageQueue) {
      this.userMessageQueue = userMessageQueue;
      return this;
    }

    @Override
    public UserMessageCommunicator build() {
      return new UserMessageCommunicator(this);
    }

    @Override
    public void destroy() {
      userMessageFluxSink.emitComplete(RETRY_NON_SERIALIZED);
    }
  }
}
