package com.volvo.tisp.vwtp.converter.codectobytes;

import com.volvo.tisp.vwtp.builder.SegInvokePdu2Builder;
import com.volvo.tisp.vwtp.builder.SegInvokePduBuilder;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

/** Test for {@link SegInvokePduToByteArrayConverter} */
class SegInvokePduToByteArrayConverterTest {
  /** Test generating and converting {@link SegInvokePdu} with default values */
  @Test
  void testConvertSegInvokePduDefault() {
    final byte[] expectedPayload = {0b00101000, 0b00000000, 0b00000000, 0b00000001, 0b00000000};
    final SegInvokePdu segInvokePdu = SegInvokePduBuilder.builder().buildSegInvokePdu();
    final byte[] payload =
        SegInvokePduToByteArrayConverter.convert(segInvokePdu, new byte[] {0b00000000});
    Assertions.assertThat(payload)
        .as("SegInvokePdu payload")
        .isNotEmpty()
        .isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link SegInvokePdu} with changed values */
  @Test
  void testConvertSegInvokePdu() {
    final byte[] expectedPayload = {0b00101111, 0b01111111, 0b01111111, 0b01111111, 0b01111111};
    final SegInvokePdu segInvokePdu =
        SegInvokePduBuilder.builder()
            .withGtr(true)
            .withTtr(true)
            .withRid(true)
            .withWtpTid(32_639L)
            .withPsn(127L)
            .buildSegInvokePdu();
    final byte[] payload =
        SegInvokePduToByteArrayConverter.convert(segInvokePdu, new byte[] {0b01111111});
    Assertions.assertThat(payload)
        .as("SegInvokePdu payload")
        .isNotEmpty()
        .isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link SegInvokePdu2} with default values */
  @Test
  void testConvertSegInvokePdu2Default() {
    final byte[] expectedPayload = {
      0b01011000,
      0b00000000,
      0b00000000,
      0b00000001,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000
    };
    final SegInvokePdu2 segInvokePdu = SegInvokePdu2Builder.builder().buildSegInvokePdu2();
    final byte[] payload =
        SegInvokePduToByteArrayConverter.convert(segInvokePdu, new byte[] {0b00000000});
    Assertions.assertThat(payload)
        .as("SegInvokePdu2 payload")
        .isNotEmpty()
        .isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link SegInvokePdu2} with changed values */
  @Test
  void testConvertSegInvokePdu2() {
    final byte[] expectedPayload = {
      (byte) 0b01011111,
      (byte) 0b01111111,
      (byte) 0b11111111,
      (byte) 0b01111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final SegInvokePdu2 segInvokePdu =
        SegInvokePdu2Builder.builder()
            .withGtr(true)
            .withTtr(true)
            .withRid(true)
            .withWtpTid(32_767L)
            .withVid(4_294_967_295L)
            .withPsn(127L)
            .buildSegInvokePdu2();
    final byte[] payload =
        SegInvokePduToByteArrayConverter.convert(segInvokePdu, new byte[] {(byte) 0b11111111});
    Assertions.assertThat(payload)
        .as("SegInvokePdu2 payload")
        .isNotEmpty()
        .isEqualTo(expectedPayload);
  }
}
