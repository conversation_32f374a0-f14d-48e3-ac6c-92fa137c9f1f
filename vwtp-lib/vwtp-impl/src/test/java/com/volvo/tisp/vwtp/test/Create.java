package com.volvo.tisp.vwtp.test;

import com.volvo.tisp.vwtp.builder.AbortPdu2Builder;
import com.volvo.tisp.vwtp.builder.AbortPduBuilder;
import com.volvo.tisp.vwtp.builder.AckPdu2Builder;
import com.volvo.tisp.vwtp.builder.AckPduBuilder;
import com.volvo.tisp.vwtp.builder.InvokePdu2Builder;
import com.volvo.tisp.vwtp.builder.InvokePduBuilder;
import com.volvo.tisp.vwtp.builder.NackPdu2Builder;
import com.volvo.tisp.vwtp.builder.NackPduBuilder;
import com.volvo.tisp.vwtp.builder.SegInvokePdu2Builder;
import com.volvo.tisp.vwtp.builder.SegInvokePduBuilder;
import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import com.volvo.tisp.vwtp.codec.InvokePdu;
import com.volvo.tisp.vwtp.codec.InvokePdu2;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.codectobytes.AbortPduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.AckPduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.InvokePduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.NackPduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.SegInvokePduToByteArrayConverter;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import java.nio.charset.StandardCharsets;

/**
 * Collection of Protocol Data Units from Initiator to put together Responder tests with {@link
 * TestContainer}
 */
public final class Create {
  /**
   * Wraps protocol data unit into a {@link NetworkMessageDto} and sends it to {@link TestContainer}
   *
   * @param testContainer instance of {@link TestContainer}
   * @param protocolDataUnit protocol data unit
   * @return instance of {@link TestContainer}
   */
  private static TestContainer pdu(
      final TestContainer testContainer, final byte[] protocolDataUnit) {
    testContainer
        .getNetworkMessageCommunicator()
        .send(testContainer.getNetworkMessageBuilder().withPayload(protocolDataUnit).build());
    return testContainer;
  }

  /**
   * Sends "garbage"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer garbage(final TestContainer testContainer) {
    return pdu(testContainer, "garbage".getBytes(StandardCharsets.UTF_8));
  }

  /**
   * Sends "0xFFFFFF"
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer corruptAck(final TestContainer testContainer) {
    return pdu(testContainer, new byte[] {(byte) 0xff, (byte) 0xff, (byte) 0xff});
  }

  /**
   * Sends Invoke PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param gtr group trailer
   * @param ttr transmission trailer
   * @param rid retransmission indicator
   * @param tnew TID new
   * @param uac user ACK
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer invoke(
      final TestContainer testContainer,
      final boolean gtr,
      final boolean ttr,
      final boolean rid,
      final boolean tnew,
      final boolean uac) {
    final byte[] wtpServiceDataUnit = testContainer.getInvokeTestData(0);

    if (testContainer.getWtpVersion() == WtpVersion.VERSION_1) {
      final InvokePdu invokePdu =
          InvokePduBuilder.builder()
              .withGtr(gtr)
              .withTtr(ttr)
              .withRid(rid)
              .withWtpTid(testContainer.getVwtpTid())
              .withTidNew(tnew)
              .withUserAck(uac)
              .withTransactionClass(testContainer.getTransactionClass())
              .buildInvokePdu();
      return pdu(
          testContainer, InvokePduToByteArrayConverter.convert(invokePdu, wtpServiceDataUnit));
    }
    final InvokePdu2 invokePdu =
        InvokePdu2Builder.builder()
            .withGtr(gtr)
            .withTtr(ttr)
            .withRid(rid)
            .withWtpTid(testContainer.getVwtpTid())
            .withTidNew(tnew)
            .withUserAck(uac)
            .withTransactionClass(testContainer.getTransactionClass())
            .withVid(testContainer.getVid())
            .buildInvokePdu2();
    return pdu(testContainer, InvokePduToByteArrayConverter.convert(invokePdu, wtpServiceDataUnit));
  }

  /**
   * Sends Invoke PDU with TTR flag set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithTtr(final TestContainer testContainer) {
    return invoke(testContainer, false, true, false, false, false);
  }

  /**
   * Sends Invoke PDU with TTR and RID flag set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithTtrAndRid(final TestContainer testContainer) {
    return invoke(testContainer, false, true, true, false, false);
  }

  /**
   * Sends Invoke PDU with GTR and U/P flags set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithGtrAndUserAck(final TestContainer testContainer) {
    return invoke(testContainer, true, false, false, false, true);
  }

  /**
   * Sends Invoke PDU with TTR and U/P flags set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithTtrAndUserAck(final TestContainer testContainer) {
    return invoke(testContainer, false, true, false, false, true);
  }

  /**
   * Sends Invoke PDU with TTR, RID and U/P flags set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithTtrRidAndUserAck(final TestContainer testContainer) {
    return invoke(testContainer, false, true, true, false, true);
  }

  /**
   * Sends Invoke PDU with TTR, TIDnew and U/P flags set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithTtrTidNewAndUserAck(final TestContainer testContainer) {
    return invoke(testContainer, false, true, false, true, true);
  }

  /**
   * Sends Invoke PDU with U/P flag set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithUserAck(final TestContainer testContainer) {
    return invoke(testContainer, false, false, false, false, true);
  }

  /**
   * Sends Invoke PDU with U/P flag and RID set to true
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer invokeWithUserAckAndRid(final TestContainer testContainer) {
    return invoke(testContainer, false, false, true, false, true);
  }

  /**
   * Sends Segmented Invoke PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param psn packet sequence number
   * @param gtr group trailer
   * @param ttr transmission trailer
   * @param rid retransmission indicator
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer segInvoke(
      final TestContainer testContainer,
      final int psn,
      final boolean gtr,
      final boolean ttr,
      final boolean rid) {
    final byte[] wtpServiceDataUnit = testContainer.getInvokeTestData(psn);

    if (testContainer.getWtpVersion() == WtpVersion.VERSION_1) {
      final SegInvokePdu segInvokePdu =
          SegInvokePduBuilder.builder()
              .withGtr(gtr)
              .withTtr(ttr)
              .withRid(rid)
              .withWtpTid(testContainer.getVwtpTid())
              .withPsn(psn)
              .buildSegInvokePdu();
      return pdu(
          testContainer,
          SegInvokePduToByteArrayConverter.convert(segInvokePdu, wtpServiceDataUnit));
    }
    final SegInvokePdu2 segInvokePdu =
        SegInvokePdu2Builder.builder()
            .withGtr(gtr)
            .withTtr(ttr)
            .withRid(rid)
            .withWtpTid(testContainer.getVwtpTid())
            .withPsn(psn)
            .withVid(testContainer.getVid())
            .buildSegInvokePdu2();
    return pdu(
        testContainer, SegInvokePduToByteArrayConverter.convert(segInvokePdu, wtpServiceDataUnit));
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"01"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke01(final TestContainer testContainer) {
    return segInvoke(testContainer, 1, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"01"</em> and GTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke01WithGtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 1, true, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"01"</em>, GTR and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke01WithGtrAndRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 1, true, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"01"</em> and TTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke01WithTtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 1, false, true, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"01"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke01WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 1, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"02"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke02(final TestContainer testContainer) {
    return segInvoke(testContainer, 2, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"02"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke02WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 2, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"02"</em> and GTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke02WithGtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 2, true, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"02"</em>, GTR and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke02WithGtrAndRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 2, true, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"03"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke03(final TestContainer testContainer) {
    return segInvoke(testContainer, 3, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"03"</em> and GTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke03WithGtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 3, true, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"03"</em> and TTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke03WithTtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 3, false, true, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"03"</em> TTR and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke03WithTtrAndRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 3, false, true, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"03"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke03WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 3, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"04"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke04(final TestContainer testContainer) {
    return segInvoke(testContainer, 4, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"04"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke04WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 4, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"05"</em> and GTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke05WithGtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 5, true, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"05"</em>, GTR and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke05WithGtrAndRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 5, true, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"05"</em> and TTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke05WithTtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 5, false, true, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"05"</em>, TTR and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke05WithTtrAndRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 5, false, true, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"05"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke05WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 5, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"06"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke06(final TestContainer testContainer) {
    return segInvoke(testContainer, 6, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"06"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke06WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 6, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"07"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke07(final TestContainer testContainer) {
    return segInvoke(testContainer, 7, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"07"</em> and TTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke07WithTtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 7, false, true, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"07"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke07WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 7, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"08"</em> and GTR
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke08WithGtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 8, true, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"08"</em>, GTR and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke08WithGtrAndRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 8, true, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"09"</em> and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke09(final TestContainer testContainer) {
    return segInvoke(testContainer, 9, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"09"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke09WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 9, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"10"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface and RID
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke10(final TestContainer testContainer) {
    return segInvoke(testContainer, 10, false, false, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"10"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke10WithRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 10, false, false, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"11"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke11WithTtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 11, false, true, false);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"11"</em>, TTR and RID
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke11WithTtrAndRid(final TestContainer testContainer) {
    return segInvoke(testContainer, 11, false, true, true);
  }

  /**
   * Sends Segmented Invoke PDU with Packet Sequence Number number <em>"05"</em>
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer segInvoke255WithTtr(final TestContainer testContainer) {
    return segInvoke(testContainer, 255, false, true, false);
  }

  /**
   * Sends Abort PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param abortCode {@link AbortCode}
   * @param reverseTid indicates if abort is from responder and tid needs to be reversed
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer abort(
      final TestContainer testContainer, final AbortCode abortCode, final boolean reverseTid) {
    if (testContainer.getWtpVersion() == WtpVersion.VERSION_1) {
      if (reverseTid) {
        return pdu(
            testContainer,
            AbortPduToByteArrayConverter.convert(
                AbortPduBuilder.builder()
                    .withAbortCode(abortCode)
                    .withWtpTidReverseDirection(testContainer.getVwtpTid())
                    .buildAbortPdu()));
      }
      return pdu(
          testContainer,
          AbortPduToByteArrayConverter.convert(
              AbortPduBuilder.builder()
                  .withAbortCode(abortCode)
                  .withWtpTid(testContainer.getVwtpTid())
                  .buildAbortPdu()));
    }

    if (reverseTid) {
      return pdu(
          testContainer,
          AbortPduToByteArrayConverter.convert(
              AbortPdu2Builder.builder()
                  .withAbortCode(abortCode)
                  .withWtpTidReverseDirection(testContainer.getVwtpTid())
                  .withVid(testContainer.getVid())
                  .buildAbortPdu2()));
    }

    return pdu(
        testContainer,
        AbortPduToByteArrayConverter.convert(
            AbortPdu2Builder.builder()
                .withAbortCode(abortCode)
                .withWtpTid(testContainer.getVwtpTid())
                .withVid(testContainer.getVid())
                .buildAbortPdu2()));
  }

  /**
   * Sends Abort PDU from initiator with abort type PROVIDER abort reason UNKNOWN
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer initiatorAbortProviderUnknown(final TestContainer testContainer) {
    return abort(testContainer, AbortCode.TCE_PROVIDER_UNKNOWN, false);
  }

  /**
   * Sends Abort PDU from responder with abort type PROVIDER abort reason UNKNOWN
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAbortProviderUnknown(final TestContainer testContainer) {
    return abort(testContainer, AbortCode.TCE_PROVIDER_UNKNOWN, true);
  }

  /**
   * Sends Abort PDU from responder with abort type PROVIDER abort reason TRANSACTION TIMEOUT
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAbortProviderTransactionTimeout(
      final TestContainer testContainer) {
    return abort(testContainer, AbortCode.TGW_PROVIDER_TRANSACTION_TIMEOUT, true);
  }

  /**
   * Sends {@link UserMessageDto}
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userMessage(final TestContainer testContainer) {
    testContainer
        .getUserMessageCommunicator()
        .send(
            testContainer
                .getUserMessageBuilder()
                .withMessageId(testContainer.getMessageId())
                .withProperties(testContainer.getProperties())
                .withAddress(testContainer.getAddress())
                .withPayload(testContainer.getTestData())
                .withVehicleId(testContainer.getVid())
                .withTransactionClass(testContainer.getTransactionClass())
                .withWtpVersion(testContainer.getWtpVersion())
                .build());
    return testContainer;
  }

  /**
   * Sends {@link UserStatusDto}
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param delivered true if delivered
   * @param abortCode {@link AbortCode} for reply
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer userStatus(
      final TestContainer testContainer, final boolean delivered, final AbortCode abortCode) {
    final UserStatusDto status =
        testContainer
            .getUserStatusBuilder()
            .withDelivered(delivered)
            .withAbortCode(abortCode)
            .withMessageId(testContainer.getMessageId())
            .withAddress(testContainer.getAddress())
            .build();
    testContainer.getUserStatusCommunicator().send(status);
    return testContainer;
  }

  /**
   * Replies with {@link UserStatusDto} delivered
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userStatusDelivered(final TestContainer testContainer) {
    return userStatus(testContainer, true, AbortCode.TCE_PROVIDER_UNKNOWN);
  }

  /**
   * Replies with {@link UserStatusDto} aborted
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer userStatusNoUserResponse(final TestContainer testContainer) {
    return userStatus(testContainer, false, AbortCode.TCE_PROVIDER_NO_RESPONSE);
  }

  /**
   * Sends Acknowledgment PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param otr outstanding transaction indicator
   * @param psnTpiData packet sequence number to be attached as transport information item
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer ack(
      final TestContainer testContainer, final boolean otr, final int psnTpiData) {
    if (testContainer.getWtpVersion() == WtpVersion.VERSION_1) {
      final AckPdu ackPdu =
          AckPduBuilder.builder()
              .withOtr(otr)
              .withRid(false)
              .withWtpTidReverseDirection(testContainer.getVwtpTid())
              .buildAckPdu();
      if (psnTpiData < 0) {
        return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu));
      }
      return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu, psnTpiData));
    }
    final AckPdu2 ackPdu =
        AckPdu2Builder.builder()
            .withOtr(otr)
            .withRid(false)
            .withWtpTidReverseDirection(testContainer.getVwtpTid())
            .buildAckPdu2();
    if (psnTpiData < 0) {
      return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu));
    }
    return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu, psnTpiData));
  }

  /**
   * Sends Acknowledgment PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param otr outstanding transaction indicator
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer ack(final TestContainer testContainer, final boolean otr) {
    return ack(testContainer, otr, -1);
  }

  /**
   * Sends Acknowledgment PDU from responder
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAck(final TestContainer testContainer) {
    return ack(testContainer, false);
  }

  /**
   * Sends Acknowledgment PDU from responder with PSN 00
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithPsn00(final TestContainer testContainer) {
    return ack(testContainer, false, 0);
  }

  /**
   * Sends Acknowledgment PDU from responder with OTR and PSN 00
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithOtrAndPsn00(final TestContainer testContainer) {
    return ack(testContainer, true, 0);
  }

  /**
   * Sends Acknowledgment PDU from responder with PSN 02
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithPsn02(final TestContainer testContainer) {
    return ack(testContainer, false, 2);
  }

  /**
   * Sends Acknowledgment PDU from responder with PSN 04
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithPsn04(final TestContainer testContainer) {
    return ack(testContainer, false, 4);
  }

  /**
   * Sends Acknowledgment PDU from responder with PSN 05
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithPsn05(final TestContainer testContainer) {
    return ack(testContainer, false, 5);
  }

  /**
   * Sends Acknowledgment PDU from responder with PSN 09
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithPsn09(final TestContainer testContainer) {
    return ack(testContainer, false, 9);
  }

  /**
   * Sends Acknowledgment PDU with reversed WTP version
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param otr outstanding transaction indicator
   * @param psnTpiData packet sequence number to be attached as transport information item
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer ackWithOpositeWtpVersion(
      final TestContainer testContainer, final boolean otr, final int psnTpiData) {
    if (testContainer.getWtpVersion() == WtpVersion.VERSION_2) {
      final AckPdu ackPdu =
          AckPduBuilder.builder()
              .withOtr(otr)
              .withRid(false)
              .withWtpTidReverseDirection(testContainer.getVwtpTid())
              .buildAckPdu();
      if (psnTpiData < 0) {
        return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu));
      }
      return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu, psnTpiData));
    }
    final AckPdu2 ackPdu =
        AckPdu2Builder.builder()
            .withOtr(otr)
            .withRid(false)
            .withWtpTidReverseDirection(testContainer.getVwtpTid())
            .buildAckPdu2();
    if (psnTpiData < 0) {
      return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu));
    }
    return pdu(testContainer, AckPduToByteArrayConverter.convert(ackPdu, psnTpiData));
  }

  /**
   * Sends Acknowledgment PDU from responder with reversed WTP version
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithOpositeWtpVersion(final TestContainer testContainer) {
    return ackWithOpositeWtpVersion(testContainer, false, -1);
  }

  /**
   * Sends Acknowledgment PDU from responder with reversed WTP version and PSN 02
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderAckWithOpositeWtpVersionAndPsn02(
      final TestContainer testContainer) {
    return ackWithOpositeWtpVersion(testContainer, false, 2);
  }

  /**
   * Sends Negative Acknowledgment PDU
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @param missingPsn array of missing packet sequence numbers
   * @return {@link TestContainer} as defined in functional interface
   */
  private static TestContainer nack(final TestContainer testContainer, final long... missingPsn) {
    if (testContainer.getWtpVersion() == WtpVersion.VERSION_1) {
      return pdu(
          testContainer,
          NackPduToByteArrayConverter.convert(
              NackPduBuilder.builder()
                  .withRid(false)
                  .withWtpTidReverseDirection(testContainer.getVwtpTid())
                  .withMissingPsn(missingPsn)
                  .buildNackPdu()));
    }
    return pdu(
        testContainer,
        NackPduToByteArrayConverter.convert(
            NackPdu2Builder.builder()
                .withRid(false)
                .withWtpTidReverseDirection(testContainer.getVwtpTid())
                .withMissingPsn(missingPsn)
                .buildNackPdu2()));
  }

  /**
   * Sends Negative Acknowledgment PDU from responder with empty PSN array
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderNackWithEmptyPsnArray(final TestContainer testContainer) {
    return nack(testContainer);
  }

  /**
   * Sends Negative Acknowledgment PDU from responder with PSN 00
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderNackWithPsn00(final TestContainer testContainer) {
    return nack(testContainer, 0L);
  }

  /**
   * Sends Negative Acknowledgment PDU from responder with PSN 02
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderNackWithPsn02(final TestContainer testContainer) {
    return nack(testContainer, 2L);
  }

  /**
   * Sends Negative Acknowledgment PDU from responder with PSN 03
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderNackWithPsn03(final TestContainer testContainer) {
    return nack(testContainer, 3L);
  }

  /**
   * Sends Negative Acknowledgment PDU from responder with PSN 02 and PSN 03
   *
   * @param testContainer {@link TestContainer} as defined in functional interface
   * @return {@link TestContainer} as defined in functional interface
   */
  public static TestContainer responderNackWithPsn02_03(final TestContainer testContainer) {
    return nack(testContainer, 2L, 3L);
  }
}
