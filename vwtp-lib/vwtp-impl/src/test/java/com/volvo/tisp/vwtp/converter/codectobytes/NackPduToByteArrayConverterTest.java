package com.volvo.tisp.vwtp.converter.codectobytes;

import com.volvo.tisp.vwtp.builder.NackPdu2Builder;
import com.volvo.tisp.vwtp.builder.NackPduBuilder;
import com.volvo.tisp.vwtp.codec.NackPdu;
import com.volvo.tisp.vwtp.codec.NackPdu2;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

/** Test for {@link NackPduToByteArrayConverter} */
class NackPduToByteArrayConverterTest {
  /** Test generating and converting {@link NackPdu} with default values */
  @Test
  void testConvertNackPduDefault() {
    final byte[] expectedPayload = {0b00111000, 0b00000000, 0b00000000, 0b00000000};
    final NackPdu nackPdu = NackPduBuilder.builder().buildNackPdu();
    final byte[] payload = NackPduToByteArrayConverter.convert(nackPdu);
    Assertions.assertThat(payload).as("NackPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link NackPdu} with changed values */
  @Test
  void testConvertNackPdu() {
    final byte[] expectedPayload = {
      0b00111001, 0b01111111, 0b01111111, 0b00000011, 0b01111100, 0b01111101, 0b01111100
    };
    final NackPdu nackPdu =
        NackPduBuilder.builder()
            .withRid(true)
            .withWtpTid(32_639L)
            .withMissingPsn(0b01111100, 0b01111101, 0b01111100)
            .buildNackPdu();
    final byte[] payload = NackPduToByteArrayConverter.convert(nackPdu);
    Assertions.assertThat(payload).as("NackPdu payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link NackPdu2} with default values */
  @Test
  void testConvertNackPdu2Default() {
    final byte[] expectedPayload = {
      0b01100000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000
    };
    final NackPdu2 nackPdu = NackPdu2Builder.builder().buildNackPdu2();
    final byte[] payload = NackPduToByteArrayConverter.convert(nackPdu);
    Assertions.assertThat(payload).as("NackPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }

  /** Test generating and converting {@link NackPdu2} with changed values */
  @Test
  void testConvertNackPdu2() {
    final byte[] expectedPayload = {
      (byte) 0b01100001,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b00000011,
      (byte) 0b01111110,
      (byte) 0b01111101,
      (byte) 0b01111101,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final NackPdu2 nackPdu =
        NackPdu2Builder.builder()
            .withRid(true)
            .withWtpTidReverseDirection(32_767L)
            .withMissingPsn(0b01111110, 0b01111101, 0b01111101)
            .withVid(4_294_967_295L)
            .buildNackPdu2();
    final byte[] payload = NackPduToByteArrayConverter.convert(nackPdu);
    Assertions.assertThat(payload).as("NackPdu2 payload").isNotEmpty().isEqualTo(expectedPayload);
  }
}
