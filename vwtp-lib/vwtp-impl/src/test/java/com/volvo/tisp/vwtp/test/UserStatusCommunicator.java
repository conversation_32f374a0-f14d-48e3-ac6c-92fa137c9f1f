package com.volvo.tisp.vwtp.test;

import com.volvo.tisp.flow.FlowBalancer;
import com.volvo.tisp.vwtp.controller.NetworkMessageFlow;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Sinks;
import reactor.util.Logger;
import reactor.util.Loggers;

/** Communicator to send and receive message delivery statuses to {@link NetworkMessageFlow} */
final class UserStatusCommunicator implements Communicator<UserStatusDto> {
  private static final Logger logger = Loggers.getLogger(UserMessageCommunicator.class);

  private final String queueName;
  private final BlockingQueue<UserStatusDto> userStatusQueue;
  private final Sinks.Many<UserStatusDto> userStatusFluxSink;

  private UserStatusCommunicator(final Builder builder) {
    userStatusQueue = builder.userStatusQueue;
    queueName = builder.queueName;
    userStatusFluxSink = builder.userStatusFluxSink;
  }

  @Override
  public Optional<UserStatusDto> poll(final TimeUnit timeUnit, final long timeout) {
    logger.debug("=== Starting poll on user status queue {} ===", queueName);
    try {
      return Optional.ofNullable(userStatusQueue.poll(timeout, timeUnit));
    } catch (final InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException(e);
    } finally {
      logger.debug("=== Ending poll on user status queue {} ===", queueName);
    }
  }

  @Override
  public void send(final UserStatusDto statusMessage) {
    logger.debug("=== Sending DOWN to WTP module ===⏎\n\n{}\n", statusMessage);
    userStatusFluxSink.emitNext(statusMessage, RETRY_NON_SERIALIZED);
  }

  @Override
  public void clear() {
    userStatusQueue.clear();
  }

  /** Builder for {@link UserStatusCommunicator} */
  @Component
  static class Builder
      implements org.apache.commons.lang3.builder.Builder<UserStatusCommunicator>, DisposableBean {
    private final Sinks.Many<UserStatusDto> userStatusFluxSink;
    private BlockingQueue<UserStatusDto> userStatusQueue;
    private String queueName;

    @Autowired
    protected Builder(final FlowBalancer<UserStatusDto, Void> balancedUserStatusFlow) {
      userStatusFluxSink = Sinks.many().unicast().onBackpressureError();
      userStatusFluxSink
          .asFlux()
          .transform(balancedUserStatusFlow)
          .subscribe(null, throwable -> logger.error(throwable.getMessage(), throwable));
    }

    Builder withQueueName(final String queueName) {
      this.queueName = queueName;
      return this;
    }

    Builder withUserStatusQueue(final BlockingQueue<UserStatusDto> userStatusQueue) {
      this.userStatusQueue = userStatusQueue;
      return this;
    }

    @Override
    public UserStatusCommunicator build() {
      return new UserStatusCommunicator(this);
    }

    @Override
    public void destroy() {
      userStatusFluxSink.emitComplete(RETRY_NON_SERIALIZED);
    }
  }
}
