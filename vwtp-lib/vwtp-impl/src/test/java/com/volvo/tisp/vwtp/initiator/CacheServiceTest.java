package com.volvo.tisp.vwtp.initiator;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import java.net.URI;
import java.util.concurrent.atomic.AtomicLong;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(TestConfiguration.class)
class CacheServiceTest {
  private static final URI SMS_46705424242 = URI.create("sms://46705424242");

  @Autowired private CacheService cacheService;

  @Test
  void testInitiatorLastWtpTid() {
    final AtomicLong wtpTidRead = cacheService.initiatorReadOrCreateLastWtpTid(SMS_46705424242);
    Assertions.assertThat(wtpTidRead).isNotNull();

    final AtomicLong lastWtpTid = cacheService.initiatorReadOrCreateLastWtpTid(SMS_46705424242);
    Assertions.assertThat(lastWtpTid).isEqualTo(wtpTidRead);
  }
}
