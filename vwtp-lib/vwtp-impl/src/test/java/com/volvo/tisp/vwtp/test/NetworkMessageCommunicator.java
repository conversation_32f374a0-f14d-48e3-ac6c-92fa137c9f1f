package com.volvo.tisp.vwtp.test;

import com.volvo.tisp.flow.FlowBalancer;
import com.volvo.tisp.vwtp.controller.NetworkMessageFlow;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import java.util.Optional;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Sinks;
import reactor.util.Logger;
import reactor.util.Loggers;

/** Communicator to interface {@link NetworkMessageFlow} from the network side */
class NetworkMessageCommunicator implements Communicator<NetworkMessageDto> {
  private static final Logger logger = Loggers.getLogger(NetworkMessageCommunicator.class);

  private final String queueName;
  private final BlockingQueue<NetworkMessageDto> networkMessageQueue;
  private final Sinks.Many<NetworkMessageDto> networkMessageFluxSink;

  private NetworkMessageCommunicator(final Builder builder) {
    networkMessageQueue = builder.networkMessageQueue;
    queueName = builder.queueName;
    networkMessageFluxSink = builder.networkMessageFluxSink;
  }

  @Override
  public Optional<NetworkMessageDto> poll(final TimeUnit timeUnit, final long timeout) {
    logger.debug("=== Starting poll on network message queue {} ===", queueName);
    try {
      return Optional.ofNullable(networkMessageQueue.poll(timeout, timeUnit));
    } catch (final InterruptedException e) {
      Thread.currentThread().interrupt();
      throw new RuntimeException(e);
    } finally {
      logger.debug("=== Ending poll on network message queue {} ===", queueName);
    }
  }

  @Override
  public void send(final NetworkMessageDto networkMessage) {
    logger.debug("=== Sending UP to WTP module ===⏎\n\n{}\n", networkMessage);
    networkMessageFluxSink.emitNext(networkMessage, RETRY_NON_SERIALIZED);
  }

  @Override
  public void clear() {
    networkMessageQueue.clear();
  }

  /** Builder for {@link NetworkMessageCommunicator} */
  @Component
  static class Builder
      implements org.apache.commons.lang3.builder.Builder<NetworkMessageCommunicator>,
          DisposableBean {
    private final Sinks.Many<NetworkMessageDto> networkMessageFluxSink;
    private String queueName;
    private BlockingQueue<NetworkMessageDto> networkMessageQueue;

    @Autowired
    protected Builder(final FlowBalancer<NetworkMessageDto, Void> balancedNetworkMessageFlow) {
      networkMessageFluxSink = Sinks.many().unicast().onBackpressureError();
      networkMessageFluxSink
          .asFlux()
          .transform(balancedNetworkMessageFlow)
          .subscribe(null, throwable -> logger.error(throwable.getMessage(), throwable));
    }

    Builder withQueueName(final String queueName) {
      this.queueName = queueName;
      return this;
    }

    Builder withNetworkMessageQueue(final BlockingQueue<NetworkMessageDto> networkMessageQueue) {
      this.networkMessageQueue = networkMessageQueue;
      return this;
    }

    @Override
    public NetworkMessageCommunicator build() {
      return new NetworkMessageCommunicator(this);
    }

    @Override
    public void destroy() {
      networkMessageFluxSink.emitComplete(RETRY_NON_SERIALIZED);
    }
  }
}
