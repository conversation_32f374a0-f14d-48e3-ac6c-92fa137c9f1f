package com.volvo.tisp.vwtp.responder.transmission;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/** Verify that WTP responder implementation can handle TIDnew flag properly */
@SpringJUnitConfig(TestConfiguration.class)
class TidNewTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  /** WTP version 1 - single test */
  @Test
  void testWtp1() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        64,
        64,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel vehicles test */
  @Test
  void testWtp1WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        64,
        64,
        this::executionSequence);
  }

  /** WTP version 2 - single test */
  @Test
  void testWtp2() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        64,
        64,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel vehicles test */
  @Test
  void testWtp2WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        64,
        64,
        this::executionSequence);
  }

  private static TestContainer setWtpTid_0x2000(final TestContainer testContainer) {
    testContainer.setVwtpTid(0x2000L);
    return testContainer;
  }

  private static TestContainer setWtpTid_0x5FFF(final TestContainer testContainer) {
    testContainer.setVwtpTid(0x5FFFL);
    return testContainer;
  }

  /**
   * Test execution sequence
   *
   * @param completableFuture {@link CompletableFuture} with initial {@link TestContainer}
   *     configuration
   * @return {@link CompletableFuture} for this test
   */
  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> completableFuture) {
    return completableFuture
        .thenApply(TidNewTest::setWtpTid_0x2000)
        .thenApply(Create::invokeWithTtrAndUserAck)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::initiatorGotAckWithoutPsnTpi)
        .thenApply(TidNewTest::setWtpTid_0x5FFF)
        .thenApply(Create::invokeWithTtrAndUserAck)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::initiatorGotAckWithoutPsnTpi)
        // Now pretend that device restarted with WTP TID 0x2000 and fired TIDnew
        .thenApply(TidNewTest::setWtpTid_0x2000)
        .thenApply(Create::invokeWithTtrTidNewAndUserAck)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::initiatorGotAckWithoutPsnTpi)
        .thenApply(TidNewTest::setWtpTid_0x5FFF)
        .thenApply(Create::invokeWithTtrAndUserAck)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::initiatorGotAckWithoutPsnTpi)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
