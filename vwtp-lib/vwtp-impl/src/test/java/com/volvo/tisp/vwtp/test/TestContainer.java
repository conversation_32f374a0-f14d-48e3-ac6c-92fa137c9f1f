package com.volvo.tisp.vwtp.test;

import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto.NetworkMessageDtoBuilder;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto.UserMessageDtoBuilder;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto.UserStatusDtoBuilder;
import java.net.URI;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import org.apache.commons.lang3.ArrayUtils;
import org.assertj.core.api.Assertions;
import org.assertj.core.api.SoftAssertions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

/** Test execution helper to for testing VWTP implementation */
public final class TestContainer {
  private static final int QUEUE_CAPACITY = 1024;
  private static final String SEQUENCE_ID_PATTERN = "SEQ#%06d#%s";

  private final Communicator<NetworkMessageDto> networkMessageCommunicator;
  private final Communicator<UserMessageDto> userMessageCommunicator;
  private final Communicator<UserStatusDto> userStatusCommunicator;
  private final NetworkMessageDtoBuilder networkMessageBuilder;
  private final UserMessageDtoBuilder userMessageBuilder;
  private final UserStatusDtoBuilder userStatusBuilder;
  private final Map<String, String> properties;
  private final URI adress;
  private final String messageId;
  private final TransactionClass transactionClass;
  private final WtpVersion wtpVersion;
  private final byte[] testData;
  private final int segmentSize;
  private long tid;
  private final long vid;

  private TestContainer(final Builder builder) {
    adress = builder.address;
    messageId = String.format(SEQUENCE_ID_PATTERN, builder.counter.getAndIncrement(), adress);

    final BlockingQueue<NetworkMessageDto> networkMessageQueue =
        new ArrayBlockingQueue<>(QUEUE_CAPACITY);
    builder.networkMessageQueueMap.put(messageId, networkMessageQueue);
    final BlockingQueue<UserMessageDto> userMessageQueue = new ArrayBlockingQueue<>(QUEUE_CAPACITY);
    builder.userMessageQueueMap.put(messageId, userMessageQueue);
    final BlockingQueue<UserStatusDto> userStatusQueue = new ArrayBlockingQueue<>(QUEUE_CAPACITY);
    builder.userStatusQueueMap.put(messageId, userStatusQueue);

    networkMessageCommunicator =
        builder
            .networkMessageCommunicatorBuilder
            .withQueueName(messageId)
            .withNetworkMessageQueue(networkMessageQueue)
            .build();
    userMessageCommunicator =
        builder
            .userMessageCommunicatorBuilder
            .withQueueName(messageId)
            .withUserStatusQueue(userMessageQueue)
            .build();
    userStatusCommunicator =
        builder
            .userStatusCommunicatorBuilder
            .withQueueName(messageId)
            .withUserStatusQueue(userStatusQueue)
            .build();
    networkMessageBuilder =
        NetworkMessageDto.builder().withAddress(adress).withMessageId(messageId);
    userMessageBuilder =
        UserMessageDto.builder()
            .withAddress(adress)
            .withWtpVersion(builder.wtpVersion)
            .withTransactionClass(builder.transactionClass)
            .withMessageId(messageId);
    userStatusBuilder = UserStatusDto.builder().withMessageId(messageId);
    properties =
        IntStream.range(1, 4)
            .mapToObj(Integer::toString)
            .collect(Collectors.toMap(seq -> "key#" + seq, seq -> seq + "#" + messageId));
    transactionClass = builder.transactionClass;
    wtpVersion = builder.wtpVersion;
    testData = builder.testData;
    segmentSize = builder.segmentSize;
    tid = builder.tid;
    vid = builder.vid;
  }

  /**
   * @return network message {@link Communicator}
   */
  public Communicator<NetworkMessageDto> getNetworkMessageCommunicator() {
    return networkMessageCommunicator;
  }

  /**
   * @return user message {@link Communicator}
   */
  public Communicator<UserMessageDto> getUserMessageCommunicator() {
    return userMessageCommunicator;
  }

  /**
   * @return {@link Communicator}
   */
  public Communicator<UserStatusDto> getUserStatusCommunicator() {
    return userStatusCommunicator;
  }

  /**
   * @return {@link NetworkMessageDtoBuilder}
   */
  public NetworkMessageDtoBuilder getNetworkMessageBuilder() {
    return networkMessageBuilder;
  }

  /**
   * @return {@link UserMessageDtoBuilder}
   */
  public UserMessageDtoBuilder getUserMessageBuilder() {
    return userMessageBuilder;
  }

  /**
   * @return {@link UserStatusDtoBuilder}
   */
  public UserStatusDtoBuilder getUserStatusBuilder() {
    return userStatusBuilder;
  }

  /**
   * @return message properties
   */
  public Map<String, String> getProperties() {
    return properties;
  }

  /**
   * @return vehicle address
   */
  public URI getAddress() {
    return adress;
  }

  /**
   * @return Message ID
   */
  public String getMessageId() {
    return messageId;
  }

  /**
   * @return transaction class
   */
  public TransactionClass getTransactionClass() {
    return transactionClass;
  }

  /**
   * @return WTP version
   */
  public WtpVersion getWtpVersion() {
    return wtpVersion;
  }

  /**
   * @return test data
   */
  public byte[] getTestData() {
    return testData;
  }

  /**
   * @return segment size
   */
  public int getSegmentSize() {
    return segmentSize;
  }

  /**
   * @return WTP transaction ID
   */
  public long getVwtpTid() {
    return tid;
  }

  /**
   * @param tid WTP transaction ID
   */
  public void setVwtpTid(final long tid) {
    this.tid = tid;
  }

  /**
   * @return vehicle ID
   */
  public long getVid() {
    return wtpVersion == WtpVersion.VERSION_2 ? vid : 0;
  }

  /**
   * @param packetSequenceNumber packet sequence number
   * @return test data for specified invoke number
   */
  public byte[] getInvokeTestData(final int packetSequenceNumber) {
    final int sduStart = packetSequenceNumber * segmentSize;
    return Arrays.copyOfRange(testData, sduStart, sduStart + segmentSize);
  }

  /**
   * Clears all queues of the {@link TestContainer} instance
   *
   * @return instance of {@link TestContainer}
   */
  public TestContainer emptyCommunicators() {
    networkMessageCommunicator.clear();
    userMessageCommunicator.clear();
    userStatusCommunicator.clear();
    return this;
  }

  /**
   * Fails with assertion if any of communicator queues are not empty
   *
   * @return instance of {@link TestContainer}
   */
  public TestContainer assertCommunicatorsAreEmpty() {
    SoftAssertions.assertSoftly(
        softAssertion -> {
          Assertions.assertThat(networkMessageCommunicator.poll(TimeUnit.MILLISECONDS, 0))
              .as("Network message communicator - %s", messageId)
              .isEmpty();
          Assertions.assertThat(userMessageCommunicator.poll(TimeUnit.MILLISECONDS, 0))
              .as("User message communicator - %s", messageId)
              .isEmpty();
          Assertions.assertThat(userStatusCommunicator.poll(TimeUnit.MILLISECONDS, 0))
              .as("User status communicator - %s", messageId)
              .isEmpty();
        });
    return emptyCommunicators();
  }

  /** Builder for {@link UserMessageCommunicator} */
  @Component
  public static final class Builder
      implements org.apache.commons.lang3.builder.Builder<TestContainer> {
    private static final Logger logger = LoggerFactory.getLogger(Builder.class);
    private static final URI DUMMY_ADDRESS = URI.create("udp://localhost:9999");

    private final Map<String, BlockingQueue<NetworkMessageDto>> networkMessageQueueMap =
        new HashMap<>();
    private final Map<String, BlockingQueue<UserMessageDto>> userMessageQueueMap = new HashMap<>();
    private final Map<String, BlockingQueue<UserStatusDto>> userStatusQueueMap = new HashMap<>();
    private final NetworkMessageCommunicator.Builder networkMessageCommunicatorBuilder;
    private final UserMessageCommunicator.Builder userMessageCommunicatorBuilder;
    private final UserStatusCommunicator.Builder userStatusCommunicatorBuilder;
    private final AtomicInteger counter = new AtomicInteger();
    private URI address = DUMMY_ADDRESS;
    private TransactionClass transactionClass = TransactionClass.CLASS_0;
    private WtpVersion wtpVersion = WtpVersion.VERSION_1;
    private byte[] testData = ArrayUtils.EMPTY_BYTE_ARRAY;
    private int segmentSize = 16;
    private long tid = 0;
    private long vid = 0;

    /**
     * Default constructor
     *
     * @param networkMessageCommunicatorBuilder {@link NetworkMessageCommunicator.Builder}
     * @param userMessageCommunicatorBuilder {@link UserMessageCommunicator.Builder}
     * @param userStatusCommunicatorBuilder {@link UserStatusCommunicator.Builder}
     */
    @Autowired
    @Lazy
    private Builder(
        final NetworkMessageCommunicator.Builder networkMessageCommunicatorBuilder,
        final UserMessageCommunicator.Builder userMessageCommunicatorBuilder,
        final UserStatusCommunicator.Builder userStatusCommunicatorBuilder) {
      this.networkMessageCommunicatorBuilder = networkMessageCommunicatorBuilder;
      this.userMessageCommunicatorBuilder = userMessageCommunicatorBuilder;
      this.userStatusCommunicatorBuilder = userStatusCommunicatorBuilder;
    }

    public void networkMessageConsumer(final NetworkMessageDto networkMessage) {
      logger.debug(
          "=== Adding to network queue {} ===⏎\n\n{}\n",
          networkMessage.getMessageId(),
          networkMessage);
      networkMessageQueueMap.get(networkMessage.getMessageId()).add(networkMessage);
    }

    public void userMessageConsumer(final UserMessageDto userMessage) {
      logger.debug(
          "=== Adding to user message queue {} ===⏎\n\n{}\n",
          userMessage.getMessageId(),
          userMessage);
      userMessageQueueMap.get(userMessage.getMessageId()).add(userMessage);
    }

    public void userStatusConsumer(final UserStatusDto userStatus) {
      logger.debug(
          "=== Adding to user status queue {} ===⏎\n\n{}\n", userStatus.getMessageId(), userStatus);
      userStatusQueueMap.get(userStatus.getMessageId()).add(userStatus);
    }

    /**
     * Builder method to build {@link TestContainer} for specific adress
     *
     * @param address vehicle URI address
     * @return {@link TestContainer.Builder}
     */
    public Builder withAddressURI(final URI address) {
      this.address = address;
      return this;
    }

    /**
     * Builder method to build {@link TestContainer} with specific transaction class
     *
     * @param transactionClass transaction class
     * @return {@link TestContainer.Builder}
     */
    public Builder withTransactionClass(final TransactionClass transactionClass) {
      this.transactionClass = transactionClass;
      return this;
    }

    /**
     * Builder method to build {@link TestContainer} with specific WTP version
     *
     * @param wtpVersion WTP version
     * @return {@link TestContainer.Builder}
     */
    public Builder withWtpVersion(final WtpVersion wtpVersion) {
      this.wtpVersion = wtpVersion;
      return this;
    }

    /**
     * Builder method to build {@link TestContainer} with specific test data
     *
     * @param testData test data
     * @return {@link TestContainer.Builder}
     */
    public Builder withTestData(final byte[] testData) {
      this.testData = testData;
      return this;
    }

    /**
     * Builder method to build {@link TestContainer} with specific segment size
     *
     * @param segmentSize segment size
     * @return {@link TestContainer.Builder}
     */
    public Builder withSegmentSize(final int segmentSize) {
      this.segmentSize = segmentSize;
      return this;
    }

    /**
     * Builder method to build {@link TestContainer} with specific starting TID
     *
     * @param tid starting transaction identifier
     * @return {@link TestContainer.Builder}
     */
    public Builder withTid(final long tid) {
      this.tid = tid;
      return this;
    }

    /**
     * Builder method to build {@link TestContainer} with specific vehicle ID
     *
     * @param vid vehicle ID
     * @return {@link TestContainer.Builder}
     */
    public Builder withVid(final long vid) {
      this.vid = vid;
      return this;
    }

    @Override
    public TestContainer build() {
      return new TestContainer(this);
    }
  }
}
