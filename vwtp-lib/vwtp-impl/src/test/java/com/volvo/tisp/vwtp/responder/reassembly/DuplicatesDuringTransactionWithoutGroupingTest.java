package com.volvo.tisp.vwtp.responder.reassembly;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * Verify that WTP responder implementation can handle re-assembly without grouping having out of
 * order segments with duplicates during transaction.
 *
 * <p>Specification reference: 7.14. Segmentation and Re-assembly
 */
@SpringJUnitConfig(TestConfiguration.class)
class DuplicatesDuringTransactionWithoutGroupingTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  /** WTP version 1 - single test */
  @Test
  void testWtp1() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        16,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel transactions test */
  @Test
  void testWtp1WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        16,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel vehicles test */
  @Test
  void testWtp1WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        16,
        this::executionSequence);
  }

  /** WTP version 2 - single test */
  @Test
  void testWtp2() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        96,
        16,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel transactions test */
  @Test
  void testWtp2WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        96,
        16,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel vehicles test */
  @Test
  void testWtp2WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        96,
        16,
        this::executionSequence);
  }

  /**
   * Test execution sequence
   *
   * @param completableFuture {@link CompletableFuture} with initial {@link TestContainer}
   *     configuration
   * @return {@link CompletableFuture} for this test
   */
  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> completableFuture) {
    return completableFuture
        .thenApply(Create::segInvoke02)
        .thenApply(Create::invokeWithUserAck)
        .thenApply(Create::invokeWithUserAck)
        .thenApply(Create::segInvoke01)
        .thenApply(Create::segInvoke04)
        .thenApply(Create::segInvoke03)
        .thenApply(Create::segInvoke01)
        .thenApply(Create::segInvoke02)
        .thenApply(Create::segInvoke04)
        .thenApply(Create::segInvoke05WithTtr)
        .thenApply(Create::segInvoke03)
        .thenApply(Create::segInvoke05WithTtr)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::initiatorGotAckWithPsnTpi05)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
