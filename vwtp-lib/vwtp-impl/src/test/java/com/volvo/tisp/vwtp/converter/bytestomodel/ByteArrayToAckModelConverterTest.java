package com.volvo.tisp.vwtp.converter.bytestomodel;

import static org.assertj.core.api.Assertions.*;

import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.TpiType;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.AckPduToModelCommonConverter;
import com.volvo.tisp.vwtp.model.AckModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.PsnTpiModel;
import com.volvo.tisp.vwtp.test.PayloadUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Test;

/**
 * Test for {@link NetworkMessageToModelCommonConverter} and {@link AckPduToModelCommonConverter}
 */
class ByteArrayToAckModelConverterTest {
  /** Test converting WTP version 1 byte array with minimal values to {@link AckModel} */
  @Test
  void testConvertWtp1BytesToAckModelMinimum() {
    final byte[] wtpPduPayload = {0b00011000, 0b00000000, 0b00000000};
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 1 byte array with maximum values to {@link AckModel} */
  @Test
  void testConvertWtp1BytesToAckModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b00011101, (byte) 0b01111111, (byte) 0b11111111,
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 32_767L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /**
   * Test converting WTP version 1 byte array with minimal values and PSN TPI to {@link AckModel}
   */
  @Test
  void testConvertWtp1BytesToAckModelWithPsnTpiMinimum() {
    final byte[] wtpPduPayload = {
      (byte) 0b10011000, (byte) 0b00000000, (byte) 0b00000000, (byte) 0b00011001, (byte) 0b00000001
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1);
    assertThat(modelCommon.getTpiArray())
        .as("TpiCommon array")
        .isNotEmpty()
        .allSatisfy(
            tpiCommon -> {
              assertThat(tpiCommon)
                  .as("TpiCommon")
                  .hasFieldOrPropertyWithValue("type", TpiType.PSN);
              assertThat((PsnTpiModel) tpiCommon)
                  .as("PsnTpiModel")
                  .hasFieldOrPropertyWithValue("packetSequenceNumber", 1);
            });
  }

  /**
   * Test converting WTP version 1 byte array with maximum values and PSN TPI to {@link AckModel}
   */
  @Test
  void testConvertWtp1BytesToAckModelWithPsnTpiMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b10011101, (byte) 0b11111111, (byte) 0b11111111, (byte) 0b00011001, (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 65_535L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1);
    assertThat(modelCommon.getTpiArray())
        .as("TpiCommon array")
        .isNotEmpty()
        .allSatisfy(
            tpiCommon -> {
              assertThat(tpiCommon)
                  .as("TpiCommon")
                  .hasFieldOrPropertyWithValue("type", TpiType.PSN);
              assertThat((PsnTpiModel) tpiCommon)
                  .as("PsnTpiModel")
                  .hasFieldOrPropertyWithValue("packetSequenceNumber", 255);
            });
  }

  /** Test converting WTP version 2 byte array with minimal values to {@link AckModel} */
  @Test
  void testConvertWtp2BytesToAckModelMinimum() {
    final byte[] wtpPduPayload = {
      0b01001000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000, 0b00000000
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("vid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 2 byte array with maximum values to {@link AckModel} */
  @Test
  void testConvertWtp2BytesToAckModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b01001101,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 65_535L)
        .hasFieldOrPropertyWithValue("vid", 4_294_967_295L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /**
   * Test converting WTP version 2 byte array with minimal values and PSN TPI to {@link AckModel}
   */
  @Test
  void testConvertWtp2BytesToAckModelWithPsnTpiMinimum() {
    final byte[] wtpPduPayload = {
      (byte) 0b11001000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00000000,
      (byte) 0b00011001,
      (byte) 0b00000001
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("vid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2);
    assertThat(modelCommon.getTpiArray())
        .as("TpiCommon array")
        .isNotEmpty()
        .allSatisfy(
            tpiCommon -> {
              assertThat(tpiCommon)
                  .as("TpiCommon")
                  .hasFieldOrPropertyWithValue("type", TpiType.PSN);
              assertThat((PsnTpiModel) tpiCommon)
                  .as("PsnTpiModel")
                  .hasFieldOrPropertyWithValue("packetSequenceNumber", 1);
            });
  }

  /**
   * Test converting WTP version 2 byte array with maximum values and PSN TPI to {@link AckModel}
   */
  @Test
  void testConvertWtp2BytesToAckModelWithPsnTpiMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b11001101,
      (byte) 0b01111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b00011001,
      (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.ACK);
    assertThat((AckModel) modelCommon)
        .as("AckModel")
        .hasFieldOrPropertyWithValue("otr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 32_767L)
        .hasFieldOrPropertyWithValue("vid", 4_294_967_295L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2);
    assertThat(modelCommon.getTpiArray())
        .as("TpiCommon array")
        .isNotEmpty()
        .allSatisfy(
            tpiCommon -> {
              assertThat(tpiCommon)
                  .as("TpiCommon")
                  .hasFieldOrPropertyWithValue("type", TpiType.PSN);
              assertThat((PsnTpiModel) tpiCommon)
                  .as("PsnTpiModel")
                  .hasFieldOrPropertyWithValue("packetSequenceNumber", 255);
            });
  }
}
