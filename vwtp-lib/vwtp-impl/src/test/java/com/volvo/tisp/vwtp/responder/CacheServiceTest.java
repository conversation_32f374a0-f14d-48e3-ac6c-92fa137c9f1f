package com.volvo.tisp.vwtp.responder;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.model.InvokeModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import java.net.URI;
import java.util.Collections;
import java.util.Map;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(TestConfiguration.class)
class CacheServiceTest {
  private static final URI SMS_46705424242 = URI.create("sms://46705424242");
  private static Long WTP_TID = 0L;
  private static Long WTP_VID = 255L;
  private static String MESSAGE_ID = "YOLO";
  private static Map<String, String> TEST_PROPERTIES =
      Collections.singletonMap("test", "test_value");

  @Autowired private CacheService cacheService;

  @Test
  void testResponderWtpTransaction() {
    NetworkMessageDto networkMessage =
        NetworkMessageDto.builder()
            .withAddress(SMS_46705424242)
            .withMessageId(MESSAGE_ID)
            .withPayload(null)
            .withProperties(TEST_PROPERTIES)
            .build();
    ModelCommon modelCommon =
        InvokeModel.builder()
            .withWtpVersion(WtpVersion.VERSION_2)
            .withVid(WTP_VID)
            .withWtpTid(WTP_TID)
            .build();

    final Transaction wtCreate =
        cacheService.responderCreateWtpTransaction(networkMessage, modelCommon);
    Assertions.assertThat(wtCreate).isNotNull();

    final Transaction wtRead = cacheService.responderReadWtpTransaction(SMS_46705424242, WTP_TID);
    Assertions.assertThat(wtRead)
        .isNotNull()
        .hasFieldOrPropertyWithValue("address", SMS_46705424242)
        .hasFieldOrPropertyWithValue("messageId", MESSAGE_ID)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("wtpTid", WTP_TID)
        .hasFieldOrPropertyWithValue("vehicleId", WTP_VID)
        .hasFieldOrPropertyWithValue("properties", TEST_PROPERTIES);
  }

  @Test
  void testResponderTidCorrelation() {
    final Transaction transaction =
        new Transaction(
            WtpVersion.VERSION_1, 0, SMS_46705424242, MESSAGE_ID, WTP_TID, Collections.emptyMap());

    cacheService.responderCreateMessageIdCorrelation(transaction);

    final Transaction wtRead = cacheService.responderReadWtpTransaction(MESSAGE_ID);
    Assertions.assertThat(wtRead).isNotNull();
  }
}
