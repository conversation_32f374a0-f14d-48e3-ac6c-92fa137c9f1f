package com.volvo.tisp.vwtp.converter.bytestomodel;

import static org.assertj.core.api.Assertions.*;

import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.InvokePduToModelCommonConverter;
import com.volvo.tisp.vwtp.model.InvokeModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.test.PayloadUtil;
import org.assertj.core.api.InstanceOfAssertFactories;
import org.junit.jupiter.api.Test;

/**
 * Test for {@link NetworkMessageToModelCommonConverter} and {@link InvokePduToModelCommonConverter}
 */
class ByteArrayToInvokeModelConverterTest {
  /** Test converting WTP version 1 byte array with minimal values to {@link InvokeModel} */
  @Test
  void testConvertWtp1BytesToInvokeModelMinimum() {
    final byte[] wtpPduPayload = {0b00001000, 0b00000000, 0b00000000, 0b00000000, 0b00000000};
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.INVOKE);
    assertThat((InvokeModel) modelCommon)
        .as("InvokeModel")
        .hasFieldOrPropertyWithValue("gtr", false)
        .hasFieldOrPropertyWithValue("ttr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .hasFieldOrPropertyWithValue("wtpTidNew", false)
        .hasFieldOrPropertyWithValue("userAck", false)
        .hasFieldOrPropertyWithValue("tcl", TransactionClass.CLASS_0)
        .hasFieldOrPropertyWithValue("payload", new byte[] {0b00000000})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 1 byte array with maximum values to {@link InvokeModel} */
  @Test
  void testConvertWtp1TBytesoInvokeModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b00001111, (byte) 0b01111111, (byte) 0b11111111, (byte) 0b00110010, (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.INVOKE);
    assertThat((InvokeModel) modelCommon)
        .as("InvokeModel")
        .hasFieldOrPropertyWithValue("gtr", true)
        .hasFieldOrPropertyWithValue("ttr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 32_767L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_1)
        .hasFieldOrPropertyWithValue("wtpTidNew", true)
        .hasFieldOrPropertyWithValue("userAck", true)
        .hasFieldOrPropertyWithValue("tcl", TransactionClass.CLASS_2)
        .hasFieldOrPropertyWithValue("payload", new byte[] {(byte) 0b11111111})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 2 byte array with minimal values to {@link InvokeModel} */
  @Test
  void testConvertWtp2BytesToInvokeModel2Minimum() {
    final byte[] wtpPduPayload = {
      0b01000000,
      0b00000000,
      0b00000000,
      0b01000000,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000,
      0b00000000
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.INVOKE);
    assertThat((InvokeModel) modelCommon)
        .as("InvokeModel")
        .hasFieldOrPropertyWithValue("gtr", false)
        .hasFieldOrPropertyWithValue("ttr", false)
        .hasFieldOrPropertyWithValue("rid", false)
        .hasFieldOrPropertyWithValue("wtpTid", 0L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("wtpTidNew", false)
        .hasFieldOrPropertyWithValue("userAck", false)
        .hasFieldOrPropertyWithValue("tcl", TransactionClass.CLASS_0)
        .hasFieldOrPropertyWithValue("vid", 0L)
        .hasFieldOrPropertyWithValue("payload", new byte[] {0b00000000})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }

  /** Test converting WTP version 2 byte array with maximum values to {@link InvokeModel} */
  @Test
  void testConvertWtp2BytesToInvokeModelMaximum() {
    final byte[] wtpPduPayload = {
      (byte) 0b01000111,
      (byte) 0b01111111,
      (byte) 0b11111111,
      (byte) 0b01110010,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111,
      (byte) 0b11111111
    };
    final ModelCommon modelCommon = PayloadUtil.convertPayloadToModelCommon(wtpPduPayload);
    assertThat(modelCommon)
        .as("ModelCommon")
        .isNotNull()
        .hasFieldOrPropertyWithValue("pduType", PduType.INVOKE);
    assertThat((InvokeModel) modelCommon)
        .as("InvokeModel")
        .hasFieldOrPropertyWithValue("gtr", true)
        .hasFieldOrPropertyWithValue("ttr", true)
        .hasFieldOrPropertyWithValue("rid", true)
        .hasFieldOrPropertyWithValue("wtpTid", 32_767L)
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("wtpTidNew", true)
        .hasFieldOrPropertyWithValue("userAck", true)
        .hasFieldOrPropertyWithValue("tcl", TransactionClass.CLASS_2)
        .hasFieldOrPropertyWithValue("vid", 4_294_967_295L)
        .hasFieldOrPropertyWithValue("payload", new byte[] {(byte) 0b11111111})
        .extracting("tpiArray", as(InstanceOfAssertFactories.ARRAY))
        .isEmpty();
  }
}
