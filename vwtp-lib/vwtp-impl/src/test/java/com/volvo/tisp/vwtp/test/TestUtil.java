package com.volvo.tisp.vwtp.test;

import com.volvo.tisp.test.util.RandomPayload;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import java.net.URI;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionException;
import java.util.function.Function;
import java.util.stream.IntStream;
import java.util.stream.Stream;

/** Utility helper functions for testing */
public final class TestUtil {
  private TestUtil() {}

  private static String URI_FORMAT = "udp://%d.%d.%d.%d:9999";
  private static long vehicleIdentifier = 0x1000000L;

  /**
   * Convenience method to wait for {@link CompletableFuture} to complete and handle exceptions
   *
   * @param completableFuture cause of failure
   */
  static void joinAndHandleExceptions(final CompletableFuture<?> completableFuture) {
    try {
      completableFuture.join();
    } catch (final CompletionException exception) {
      final Throwable cause = exception.getCause();
      if (cause instanceof Error) {
        throw (Error) cause;
      } else if (cause instanceof RuntimeException) {
        throw (RuntimeException) cause;
      } else {
        throw exception;
      }
    }
  }

  /**
   * Converts vehicle identifier to UDP URI with IP address
   *
   * @param vehicleIdentifier vehicle identifier
   * @return vehicle identifier {@link URI}
   */
  private static URI convertToUri(final long vehicleIdentifier) {
    return URI.create(
        String.format(
            URI_FORMAT,
            vehicleIdentifier >> 24 & 0xFF,
            vehicleIdentifier >> 16 & 0xFF,
            vehicleIdentifier >> 8 & 0xFF,
            vehicleIdentifier & 0xFF));
  }

  /**
   * Utility to execute a single test with vehicle number 255.
   *
   * @param testContainerBuilder instance of {@link TestContainer.Builder}
   * @param transactionClass transaction class
   * @param wtpVersion WTP version
   * @param randomPayloadSize size of random payload size in bytes
   * @param segmentSize size of a (segmented) invoke PDU payload
   * @param testSequence a function containing test execution sequence
   */
  public static void runSingleTest(
      final TestContainer.Builder testContainerBuilder,
      final TransactionClass transactionClass,
      final WtpVersion wtpVersion,
      final int randomPayloadSize,
      final int segmentSize,
      final Function<CompletableFuture<TestContainer>, CompletableFuture<TestContainer>>
          testSequence) {
    final TestContainer testContainer =
        testContainerBuilder
            .withAddressURI(convertToUri(++vehicleIdentifier))
            .withTransactionClass(transactionClass)
            .withWtpVersion(wtpVersion)
            .withTestData(RandomPayload.generate(randomPayloadSize))
            .withSegmentSize(segmentSize)
            .withTid(100L)
            .withVid(vehicleIdentifier)
            .build();

    Stream.of(testContainer)
        .map(CompletableFuture::completedFuture)
        .map(testSequence)
        .forEach(TestUtil::joinAndHandleExceptions);
  }

  /**
   * Utility to execute a test with 5 parallel transactions with vehicle number 254.
   *
   * @param testContainerBuilder instance of {@link TestContainer.Builder}
   * @param transactionClass transaction class
   * @param wtpVersion WTP version
   * @param randomPayloadSize size of random payload size in bytes
   * @param segmentSize size of a (segmented) invoke PDU payload
   * @param testSequence a function containing test execution sequence
   */
  public static void runParallelTransactionsTest(
      final TestContainer.Builder testContainerBuilder,
      final TransactionClass transactionClass,
      final WtpVersion wtpVersion,
      final int randomPayloadSize,
      final int segmentSize,
      final Function<CompletableFuture<TestContainer>, CompletableFuture<TestContainer>>
          testSequence) {
    testContainerBuilder
        .withAddressURI(convertToUri(++vehicleIdentifier))
        .withTransactionClass(transactionClass)
        .withWtpVersion(wtpVersion)
        .withSegmentSize(segmentSize)
        .withVid(vehicleIdentifier);

    IntStream.of(10, 50, 40, 20, 30)
        .mapToObj(
            index ->
                testContainerBuilder
                    .withTestData(RandomPayload.generate(randomPayloadSize))
                    .withTid(index)
                    .build())
        .toList()
        .parallelStream()
        .map(CompletableFuture::completedFuture)
        .map(testSequence)
        .forEach(TestUtil::joinAndHandleExceptions);
  }

  /**
   * Utility to execute a mirror test that sends collection of messages starting with size zero to
   * maximum allowed.
   *
   * @param testContainerBuilder instance of {@link TestContainer.Builder}
   * @param transactionClass transaction class
   * @param wtpVersion WTP version
   * @param testSequence a function containing test execution sequence
   */
  public static void runMirrorTest(
      final TestContainer.Builder testContainerBuilder,
      final TransactionClass transactionClass,
      final WtpVersion wtpVersion,
      final Function<CompletableFuture<TestContainer>, CompletableFuture<TestContainer>>
          testSequence) {
    testContainerBuilder
        .withAddressURI(convertToUri(++vehicleIdentifier))
        .withTransactionClass(transactionClass)
        .withWtpVersion(wtpVersion)
        .withVid(vehicleIdentifier);
    int maximumPayloadSize = 62_974;
    if (wtpVersion == WtpVersion.VERSION_2) {
      maximumPayloadSize = 61_952;
    }

    Stream.of(testContainerBuilder.withTestData(new byte[] {}).build())
        .map(CompletableFuture::completedFuture)
        .map(testSequence)
        .forEach(TestUtil::joinAndHandleExceptions);

    IntStream.concat(
            IntStream.iterate(0, index -> index + 6195).limit(10), IntStream.of(maximumPayloadSize))
        .mapToObj(index -> testContainerBuilder.withTestData(RandomPayload.generate(index)).build())
        .toList()
        .parallelStream()
        .map(CompletableFuture::completedFuture)
        .map(testSequence)
        .forEach(TestUtil::joinAndHandleExceptions);
  }

  /**
   * Utility to execute a test with 5 parallel vehicles with numbers from 1 to 5.
   *
   * @param testContainerBuilder instance of {@link TestContainer.Builder}
   * @param transactionClass transaction class
   * @param wtpVersion WTP version
   * @param randomPayloadSize size of random payload size in bytes
   * @param segmentSize size of a (segmented) invoke PDU payload
   * @param testSequence a function containing test execution sequence
   */
  public static void runParallelVehiclesTest(
      final TestContainer.Builder testContainerBuilder,
      final TransactionClass transactionClass,
      final WtpVersion wtpVersion,
      final int randomPayloadSize,
      final int segmentSize,
      final Function<CompletableFuture<TestContainer>, CompletableFuture<TestContainer>>
          testSequence) {
    testContainerBuilder
        .withTransactionClass(transactionClass)
        .withWtpVersion(wtpVersion)
        .withSegmentSize(segmentSize)
        .withTid(1000L);

    Stream.generate(
            () ->
                testContainerBuilder
                    .withAddressURI(convertToUri(++vehicleIdentifier))
                    .withTestData(RandomPayload.generate(randomPayloadSize))
                    .withVid(vehicleIdentifier)
                    .build())
        .limit(5)
        .toList()
        .parallelStream()
        .map(CompletableFuture::completedFuture)
        .map(testSequence)
        .forEach(TestUtil::joinAndHandleExceptions);
  }
}
