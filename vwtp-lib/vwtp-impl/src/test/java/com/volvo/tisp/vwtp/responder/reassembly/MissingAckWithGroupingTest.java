package com.volvo.tisp.vwtp.responder.reassembly;

import com.volvo.tisp.vwtp.configuration.TestConfiguration;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

/**
 * Verify that WTP responder implementation can handle re-sending Acknowledgment if it was lost
 * during re-assembly with grouping.
 */
@SpringJUnitConfig(TestConfiguration.class)
class MissingAckWithGroupingTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  /** WTP version 1 - single test */
  @Test
  void testWtp1() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        8,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel transactions test */
  @Test
  void testWtp1WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        8,
        this::executionSequence);
  }

  /** WTP version 1 - with parallel vehicles test */
  @Test
  void testWtp1WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        96,
        8,
        this::executionSequence);
  }

  /** WTP version 2 - single test */
  @Test
  void testWtp2() {
    TestUtil.runSingleTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        96,
        8,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel transactions test */
  @Test
  void testWtp2WithParallelTransactions() {
    TestUtil.runParallelTransactionsTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        96,
        8,
        this::executionSequence);
  }

  /** WTP version 2 - with parallel vehicles test */
  @Test
  void testWtp2WithParallelVehicles() {
    TestUtil.runParallelVehiclesTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        96,
        8,
        this::executionSequence);
  }

  /**
   * Test execution sequence
   *
   * @param completableFuture {@link CompletableFuture} with initial {@link TestContainer}
   *     configuration
   * @return {@link CompletableFuture} for this test
   */
  private CompletableFuture<TestContainer> executionSequence(
      final CompletableFuture<TestContainer> completableFuture) {
    return completableFuture
        .thenApply(Create::invokeWithUserAck)
        .thenApply(Create::segInvoke01)
        .thenApply(Create::segInvoke02WithGtr)
        .thenApply(Verify::initiatorGotAckWithPsnTpi02)
        // Pretend acknowledgement was not received
        .thenApply(Create::segInvoke02WithGtrAndRid)
        .thenApply(Verify::initiatorGotAckWithPsnTpi02)
        .thenApply(Create::segInvoke03)
        .thenApply(Create::segInvoke04)
        .thenApply(Create::segInvoke05WithGtr)
        .thenApply(Verify::initiatorGotAckWithPsnTpi05)
        // Pretend acknowledgement was not received
        .thenApply(Create::segInvoke05WithGtrAndRid)
        .thenApply(Verify::initiatorGotAckWithPsnTpi05)
        .thenApply(Create::segInvoke06)
        .thenApply(Create::segInvoke07)
        .thenApply(Create::segInvoke08WithGtr)
        .thenApply(Verify::initiatorGotAckWithPsnTpi08)
        // Pretend acknowledgement was not received
        .thenApply(Create::segInvoke08WithGtrAndRid)
        .thenApply(Verify::initiatorGotAckWithPsnTpi08)
        .thenApply(Create::segInvoke09)
        .thenApply(Create::segInvoke10)
        .thenApply(Create::segInvoke11WithTtr)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::initiatorGotAckWithPsnTpi11)
        // Pretend acknowledgement was not received
        .thenApply(Create::segInvoke11WithTtrAndRid)
        .thenApply(Verify::initiatorGotAckWithPsnTpi11)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
