package com.volvo.tisp.vwtp.responder;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.Test;

class ResponderUtilsTest {
  @Test
  void createMessageIdEmptyString() {
    final String orReuseMessageId = ResponderUtils.createOrReuseMessageId("");
    assertNotEquals("", orReuseMessageId);
  }

  @Test
  void reuseMessageId() {
    final String orReuseMessageId = ResponderUtils.createOrReuseMessageId("2");
    assertEquals("2", orReuseMessageId);
  }

  @Test
  void createMessageIdExistingIsNull() {
    final String orReuseMessageId = ResponderUtils.createOrReuseMessageId("");
    assertNotEquals(null, orReuseMessageId);
  }
}
