package com.volvo.tisp.vwtp;

import com.volvo.tisp.vwtp.configuration.MirrorTestConfiguration;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.test.Create;
import com.volvo.tisp.vwtp.test.TestContainer;
import com.volvo.tisp.vwtp.test.TestUtil;
import com.volvo.tisp.vwtp.test.Verify;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit.jupiter.SpringJUnitConfig;

@SpringJUnitConfig(MirrorTestConfiguration.class)
@TestPropertySource(properties = "wtp.mtu=250")
class ExtremePayloadsMirrorTest {
  @Autowired private TestContainer.Builder testContainerBuilder;

  @Test
  void testExtremePayloadsMirrorWtpVersionOne() {
    TestUtil.runMirrorTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_1,
        this::executeTransactionFlow);
  }

  @Test
  void testExtremePayloadsMirrorWtpVersionTwo() {
    TestUtil.runMirrorTest(
        testContainerBuilder,
        TransactionClass.CLASS_1,
        WtpVersion.VERSION_2,
        this::executeTransactionFlow);
  }

  /**
   * Test execution sequence
   *
   * @param future {@link CompletableFuture} with initial {@link TestContainer} configuration
   * @return {@link CompletableFuture} for this test
   */
  private CompletableFuture<TestContainer> executeTransactionFlow(
      final CompletableFuture<TestContainer> future) {
    return future
        .thenApply(Create::userMessage)
        .thenApply(Verify::userGotUserMessage)
        .thenApply(Create::userStatusDelivered)
        .thenApply(Verify::userGotDeliveredStatus)
        .thenApply(TestContainer::assertCommunicatorsAreEmpty);
  }
}
