package com.volvo.tisp.vwtp.configuration;

import com.volvo.tisp.flow.FlowBalancer;
import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.test.TestContainer;
import java.util.List;
import java.util.stream.Stream;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.PropertySource;
import reactor.blockhound.BlockHound;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;

/** Spring test configuration */
@Configuration
@PropertySource("classpath:unit-tests.properties")
@Import({ControllerConfiguration.class, InitiatorConfiguration.class, ResponderConfiguration.class})
@ComponentScan("com.volvo.tisp.vwtp.test")
public class TestConfiguration implements DisposableBean {
  private final List<Scheduler> flowSchedulers;

  protected TestConfiguration() {
    final int availableProcessors = Runtime.getRuntime().availableProcessors();
    /** Use all available cores for unit testing */
    flowSchedulers =
        Stream.generate(() -> Schedulers.newSingle("test")).limit(availableProcessors).toList();
    BlockHound.install(
        builder ->
            builder
                .allowBlockingCallsInside("org.slf4j.simple.SimpleLogger", "write")
                .allowBlockingCallsInside(
                    "com.volvo.connectivity.metric.OperatorMetrics", "loadSubnetsConfiguration")
                .allowBlockingCallsInside(
                    "com.volvo.tisp.vwtp.test.TestContainer$Builder", "networkMessageConsumer")
                .allowBlockingCallsInside(
                    "com.volvo.tisp.vwtp.test.TestContainer$Builder", "userMessageConsumer")
                .allowBlockingCallsInside(
                    "com.volvo.tisp.vwtp.test.TestContainer$Builder", "userStatusConsumer"));
  }

  @Bean
  protected List<Scheduler> flowSchedulers() {
    return flowSchedulers;
  }

  @Bean
  protected FlowBalancer<NetworkMessageDto, Void> balancedNetworkMessageFlow(
      final FlowComposer<NetworkMessageDto, Void> controllerNetworkMessageFlow) {
    return new FlowBalancer<>(controllerNetworkMessageFlow, flowSchedulers);
  }

  @Bean
  protected FlowBalancer<UserStatusDto, Void> balancedUserStatusFlow(
      final FlowComposer<UserStatusDto, Void> responderUserStatusFlow) {
    return new FlowBalancer<>(responderUserStatusFlow, flowSchedulers);
  }

  @Bean
  protected FlowBalancer<UserMessageDto, Void> balancedUserMessageFlow(
      final FlowComposer<UserMessageDto, Void> initiatorUserMessageFlow) {
    return new FlowBalancer<>(initiatorUserMessageFlow, flowSchedulers);
  }

  /**
   * Exposing "initiatorNetworkMessageFlow" as "outgoingControllerNetworkMessageFlowForInitiator"
   *
   * @param initiatorNetworkMessageFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean
  protected FlowComposer<NetworkMessageDto, Void> outgoingControllerNetworkMessageFlowForInitiator(
      final FlowComposer<NetworkMessageDto, Void> initiatorNetworkMessageFlow) {
    return initiatorNetworkMessageFlow;
  }

  /**
   * Exposing "responderNetworkMessageFlow" as "outgoingControllerNetworkMessageFlowForResponder"
   *
   * @param responderNetworkMessageFlow instance of {@link FlowComposer}
   * @return instance of {@link FlowComposer}
   */
  @Bean
  protected FlowComposer<NetworkMessageDto, Void> outgoingControllerNetworkMessageFlowForResponder(
      final FlowComposer<NetworkMessageDto, Void> responderNetworkMessageFlow) {
    return responderNetworkMessageFlow;
  }

  @Bean({"outgoingInitiatorNetworkMessageFlow", "outgoingResponderNetworkMessageFlow"})
  protected FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow(
      final TestContainer.Builder builder) {
    return flux -> flux.doOnNext(builder::networkMessageConsumer).then();
  }

  @Bean
  protected FlowComposer<UserMessageDto, Void> outgoingResponderUserMessageFlow(
      final TestContainer.Builder builder) {
    return flux -> flux.doOnNext(builder::userMessageConsumer).then();
  }

  @Bean
  protected FlowComposer<UserStatusDto, Void> outgoingInitiatorUserStatusFlow(
      final TestContainer.Builder builder) {
    return flux -> flux.doOnNext(builder::userStatusConsumer).then();
  }

  @Override
  public void destroy() {
    for (final Scheduler flowScheduler : flowSchedulers) {
      flowScheduler.dispose();
    }
  }
}
