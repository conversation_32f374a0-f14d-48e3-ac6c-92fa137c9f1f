package com.volvo.tisp.vwtp.initiator;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.volvo.tisp.test.util.RandomPayload;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import java.net.URI;
import java.util.Collections;
import java.util.Map;
import java.util.UUID;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;

class PayloadSplitterTest {
  private static final String INITIATOR_URI = "udp://10.0.0.1:4242";
  private static final String MESSAGE_ID = UUID.randomUUID().toString();

  @Test
  void testSplit512bytes() {
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(512), 250, 8);

    assertEquals(3, segments.size());
    assertEquals(250, segments.get(0).getPayload().length);
    assertEquals(250, segments.get(1).getPayload().length);
    assertEquals(12, segments.get(2).getPayload().length);
  }

  @Test
  void testSplit500bytes() {
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(500), 250, 8);

    assertEquals(2, segments.size());
    assertEquals(250, segments.get(0).getPayload().length);
    assertEquals(250, segments.get(1).getPayload().length);
  }

  @Test
  void testSplit249bytes() {
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(249), 250, 8);

    assertEquals(1, segments.size());
    assertEquals(249, segments.get(0).getPayload().length);
  }

  @Test
  void testSplit251bytes() {
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(251), 250, 8);

    assertEquals(2, segments.size());
    assertEquals(250, segments.get(0).getPayload().length);
    assertEquals(1, segments.get(1).getPayload().length);
  }

  @Test
  void testSplit250bytes() {
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(250), 250, 8);

    assertEquals(1, segments.size());
    assertEquals(250, segments.get(0).getPayload().length);
  }

  @Test
  void testSplit0bytes() {
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(0), 250, 8);

    assertEquals(1, segments.size());
  }

  @Test
  void testSplitWith7Segments() {
    final Map<Integer, Segment> segmentList =
        InitiatorUtils.createSegments(createUserMessageDto(250 * 7), 250, 8);

    for (int index = 0; index < segmentList.size(); index++) {
      if (index == 0) {
        Assertions.assertThat(segmentList.get(index).isLastInGroup()).isTrue();
      } else {
        Assertions.assertThat(segmentList.get(index).isLastInGroup()).isFalse();
      }
    }
  }

  @Test
  void testSplitWith8Segments() {
    final int nrOfSegs = 8;
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(250 * nrOfSegs), 250, 8);

    for (int index = 0; index < segments.size(); index++) {
      if (index == 0) {
        Assertions.assertThat(segments.get(index).isLastInGroup()).isTrue();
      } else if (index == 7) {
        Assertions.assertThat(segments.get(index).isLastSegment()).isTrue();
      } else {
        Assertions.assertThat(segments.get(index).isLastInGroup()).isFalse();
        Assertions.assertThat(segments.get(index).isLastSegment()).isFalse();
      }
    }
  }

  @Test
  void testSplitWith9Segments() {
    final int nrOfSegs = 9;
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(250 * nrOfSegs), 250, 8);

    for (int index = 0; index < segments.size(); index++) {
      if (index == 0) {
        Assertions.assertThat(segments.get(index).isLastInGroup()).isTrue();
      } else if (index == 8) {
        Assertions.assertThat(segments.get(index).isLastSegment()).isTrue();
      } else {
        Assertions.assertThat(segments.get(index).isLastInGroup()).isFalse();
        Assertions.assertThat(segments.get(index).isLastSegment()).isFalse();
      }
    }
  }

  @Test
  void testSplitWith10Segments() {
    final int nrOfSegs = 10;
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(250 * nrOfSegs), 250, 8);

    for (int index = 0; index < segments.size(); index++) {
      if (index == 0) {
        Assertions.assertThat(segments.get(index).isLastInGroup()).isTrue();
      } else if (index == 8) {
        Assertions.assertThat(segments.get(index).isLastInGroup()).isTrue();
      } else if (index == 9) {
        Assertions.assertThat(segments.get(index).isLastSegment()).isTrue();
      } else {
        Assertions.assertThat(segments.get(index).isLastInGroup()).isFalse();
        Assertions.assertThat(segments.get(index).isLastSegment()).isFalse();
      }
    }
  }

  @Test
  void testSplitWithTooManySegments() {
    final int nrOfSegs = 300;
    final Map<Integer, Segment> segments =
        InitiatorUtils.createSegments(createUserMessageDto(250 * nrOfSegs), 250, 8);
    Assertions.assertThat(segments.size()).isEqualTo(300);
  }

  private static UserMessageDto createUserMessageDto(final int payloadSize) {
    return UserMessageDto.builder()
        .withAddress(URI.create(INITIATOR_URI))
        .withWtpVersion(WtpVersion.VERSION_1)
        .withTransactionClass(TransactionClass.CLASS_1)
        .withMessageId(MESSAGE_ID)
        .withPayload(payloadSize == 0 ? new byte[] {} : RandomPayload.generate(payloadSize))
        .withProperties(Collections.emptyMap())
        .build();
  }
}
