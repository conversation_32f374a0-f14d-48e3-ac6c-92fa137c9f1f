package com.volvo.tisp.vwtp.initiator;

import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

public final class Segment {
  /** Packet Sequence Number 0 -> implies an InvokePDU 1 .. n -> implies a SegInvokePdu */
  private final int psn;

  /** This is the User Data carried by the WTP protocol */
  private final byte[] payload;

  /**
   * For Segmentation w/ Packet Groups, this implies the GTR flag is to be set for the last segment
   * in the group
   */
  private final boolean lastInGroup;

  /** For Segmentation, this will ge the TTR flag that is set for the last segment */
  private final boolean lastSegment;

  public Segment(
      final int psn, final byte[] payload, final boolean lastInGroup, final boolean lastSegment) {
    this.psn = psn;
    this.payload = payload;
    this.lastInGroup = lastInGroup;
    this.lastSegment = lastSegment;
  }

  public byte[] getPayload() {
    return payload;
  }

  public int getPsn() {
    return psn;
  }

  public boolean isLastInGroup() {
    return lastInGroup;
  }

  public boolean isLastSegment() {
    return lastSegment;
  }

  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }

    if (o instanceof Segment segment) {
      return new EqualsBuilder()
          .append(psn, segment.psn)
          .append(payload, segment.payload)
          .append(lastInGroup, segment.lastInGroup)
          .append(lastSegment, segment.lastSegment)
          .isEquals();
    } else {
      return false;
    }
  }

  @Override
  public int hashCode() {
    return new HashCodeBuilder(17, 37)
        .append(psn)
        .append(payload)
        .append(lastInGroup)
        .append(lastSegment)
        .toHashCode();
  }

  @Override
  public String toString() {
    return "Segment{"
        + "psn="
        + psn
        + ", payload=..."
        + ", lastInGroup="
        + lastInGroup
        + ", lastSegment="
        + lastSegment
        + '}';
  }
}
