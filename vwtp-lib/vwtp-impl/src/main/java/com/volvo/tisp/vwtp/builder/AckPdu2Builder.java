package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.OtrStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.VidStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidReverseDirectionStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.AckPdu2;

public interface AckPdu2Builder<B>
    extends OtrStep<B>, RidStep<B>, WtpTidStep<B>, WtpTidReverseDirectionStep<B>, VidStep<B> {
  AckPdu2 buildAckPdu2();

  static <T extends AckPdu2Builder<T>> AckPdu2Builder<T> builder() {
    return new PduBuilder<>();
  }
}
