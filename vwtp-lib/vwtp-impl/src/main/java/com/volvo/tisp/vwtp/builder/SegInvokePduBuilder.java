package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.PsnStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.TrailerStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;

public interface SegInvokePduBuilder<B>
    extends TrailerStep<B>, RidStep<B>, WtpTidStep<B>, PsnStep<B> {
  SegInvokePdu buildSegInvokePdu();

  static <T extends SegInvokePduBuilder<T>> SegInvokePduBuilder<T> builder() {
    return new PduBuilder<>();
  }
}
