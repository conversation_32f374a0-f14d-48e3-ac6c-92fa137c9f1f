package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.InvokeStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.TrailerStep;
import com.volvo.tisp.vwtp.builder.step.VidStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.InvokePdu2;

public interface InvokePdu2Builder<B>
    extends TrailerStep<B>, RidStep<B>, WtpTidStep<B>, InvokeStep<B>, VidStep<B> {
  InvokePdu2 buildInvokePdu2();

  static <T extends InvokePdu2Builder<T>> InvokePdu2Builder<T> builder() {
    return new PduBuilder<>();
  }
}
