package com.volvo.tisp.vwtp.model;

import com.volvo.tisp.vwtp.constants.TpiType;

/**
 * 8.4.5: Internal representation of Packet Sequence Number (PSN) Transport Information Item (TPI)
 * model.
 */
public final class PsnTpiModel implements TpiCommon {
  private final int packetSequenceNumber;

  private PsnTpiModel(final Builder builder) {
    packetSequenceNumber = builder.packetSequenceNumber;
  }

  /**
   * @return {@link Builder}
   */
  public static Builder builder() {
    return new Builder();
  }

  @Override
  public TpiType getType() {
    return TpiType.PSN;
  }

  /**
   * @return Packet Sequence Number (PSN)
   */
  public int getPacketSequenceNumber() {
    return packetSequenceNumber;
  }

  @Override
  public String toString() {
    return "PsnTpiModel{"
        + "type="
        + getType()
        + ", packetSequenceNumber="
        + packetSequenceNumber
        + ", hashCode="
        + Integer.toHexString(hashCode())
        + '}';
  }

  /** Builder for {@link PsnTpiModel} */
  public static class Builder implements org.apache.commons.lang3.builder.Builder<PsnTpiModel> {
    private int packetSequenceNumber;

    /**
     * @param packetSequenceNumber Packet Sequence Number (PSN)
     * @return {@link Builder}
     */
    public Builder withPacketSequenceNumber(final int packetSequenceNumber) {
      this.packetSequenceNumber = packetSequenceNumber;
      return this;
    }

    @Override
    public PsnTpiModel build() {
      return new PsnTpiModel(this);
    }
  }
}
