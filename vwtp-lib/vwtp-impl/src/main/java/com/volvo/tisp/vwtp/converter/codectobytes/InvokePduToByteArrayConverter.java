package com.volvo.tisp.vwtp.converter.codectobytes;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.codec.ASNSequence;
import com.volvo.tisp.vwtp.codec.InvokePdu;
import com.volvo.tisp.vwtp.codec.InvokePdu2;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu_pdu;

/** Converter utility for converting {@link InvokePdu} or {@link InvokePdu2} into byte[] */
public class InvokePduToByteArrayConverter {
  /** Private constructor to prevent instantiation of utility class */
  private InvokePduToByteArrayConverter() {}

  /**
   * Convert {@link InvokePdu} and SRP data to byte array
   *
   * @param invokePdu {@link InvokePdu}
   * @param payload SRP data
   * @return byte[]
   */
  public static byte[] convert(final InvokePdu invokePdu, final byte[] payload) {
    validateInvokePduAndPayload(invokePdu, payload);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setInvokePdu(invokePdu);

    return WtpPduToByteArrayConverter.convert(pdu, 4, payload);
  }

  /**
   * Convert {@link InvokePdu2} and SRP data to byte array
   *
   * @param invokePdu {@link InvokePdu2}
   * @param payload SRP data
   * @return byte[]
   */
  public static byte[] convert(final InvokePdu2 invokePdu, final byte[] payload) {
    validateInvokePduAndPayload(invokePdu, payload);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setInvokePdu2(invokePdu);

    return WtpPduToByteArrayConverter.convert(pdu, 8, payload);
  }

  /**
   * @param invokePdu {@link InvokePdu} or {@link InvokePdu2}
   * @param payload SRP data
   */
  private static void validateInvokePduAndPayload(
      final ASNSequence invokePdu, final byte[] payload) {
    Preconditions.checkNotNull(invokePdu, "Invoke PDU must not be null");
    Preconditions.checkNotNull(payload, "Invoke PDU (SRP) payload must not be null");
  }
}
