package com.volvo.tisp.vwtp.converter.codectobytes;

import com.volvo.tisp.vwtp.codec.ASNException;
import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import com.volvo.tisp.vwtp.codec.NackPdu;
import com.volvo.tisp.vwtp.codec.NackPdu2;
import com.volvo.tisp.vwtp.codec.PERStream;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;
import com.volvo.tisp.vwtp.codec.TpiPdu;
import com.volvo.tisp.vwtp.codec.TpiPdu_pdu;
import com.volvo.tisp.vwtp.codec.TpiShortPdu;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu_pdu;
import com.volvo.tisp.vwtp.constants.TpiType;
import com.volvo.tisp.vwtp.exception.WtpRuntimeException;

/** Converter utility for converting {@WirelessTransactionPdu_pdu} into byte[] */
final class WtpPduToByteArrayConverter {
  /** Private constructor to prevent instantiation of utility class */
  private WtpPduToByteArrayConverter() {}

  /**
   * @param pdu {@link AckPdu} or {@link AckPdu2} wrapped inside {@link WirelessTransactionPdu_pdu}
   * @param pduLength length in bytes of (@link WirelessTransactionPdu) with {@link AckPdu} or
   *     {@link AckPdu2} wrapped inside
   * @param psnTpiData PSN TPI data representing packet sequence number to be acknowledged in SAR
   * @return byte[]
   */
  static byte[] convert(
      final WirelessTransactionPdu_pdu pdu, final int pduLength, final long psnTpiData) {
    try {
      final WirelessTransactionPdu wtpPdu = new WirelessTransactionPdu();
      wtpPdu.setPdu(pdu);
      wtpPdu.setCon(true);

      final TpiShortPdu tpiShortPdu = new TpiShortPdu();
      tpiShortPdu.setData(new byte[] {(byte) psnTpiData});
      final TpiPdu_pdu tpi = new TpiPdu_pdu();
      tpi.setShort(tpiShortPdu);

      final TpiPdu tpiPdu = new TpiPdu();
      tpiPdu.setCon(false);
      tpiPdu.setTpiId(TpiType.PSN.getValue());
      tpiPdu.setPdu(tpi);

      final PERStream perStream = new PERStream(pduLength + 2);
      wtpPdu.encode(perStream);
      tpiPdu.encode(perStream);
      return perStream.getBuffer();
    } catch (final ASNException e) {
      throw new WtpRuntimeException(e);
    }
  }

  /**
   * @param pdu {@link AckPdu}, {@link AckPdu2}, {@link NackPdu}, {@link NackPdu2} wrapped inside
   *     {@link WirelessTransactionPdu_pdu}
   * @param pduLength length in bytes of (@link WirelessTransactionPdu) with {@link AckPdu} or
   *     {@link AckPdu2} wrapped inside
   * @return byte[]
   */
  static byte[] convert(final WirelessTransactionPdu_pdu pdu, final int pduLength) {
    try {
      final WirelessTransactionPdu wtpPdu = new WirelessTransactionPdu();
      wtpPdu.setPdu(pdu);
      wtpPdu.setCon(false);
      final PERStream perStream = new PERStream(pduLength);
      wtpPdu.encode(perStream);
      return perStream.getBuffer();
    } catch (final ASNException e) {
      throw new WtpRuntimeException(e);
    }
  }

  /**
   * @param pdu {@link SegInvokePdu} or {@link SegInvokePdu2} wrapped inside {@link
   *     WirelessTransactionPdu_pdu}
   * @param pduLength length in bytes of (@link WirelessTransactionPdu) with {@link SegInvokePdu} or
   *     {@link SegInvokePdu2} wrapped inside
   * @param payload PSN TPI data representing packet sequence number to be acknowledged in SAR
   * @return byte[]
   */
  static byte[] convert(
      final WirelessTransactionPdu_pdu pdu, final int pduLength, final byte[] payload) {
    try {
      final WirelessTransactionPdu wtpPdu = new WirelessTransactionPdu();
      wtpPdu.setPdu(pdu);
      wtpPdu.setCon(false);
      final PERStream perStream = new PERStream(pduLength);
      wtpPdu.encode(perStream);
      perStream.rawAppend(payload);
      return perStream.getBuffer();
    } catch (final ASNException e) {
      throw new WtpRuntimeException(e);
    }
  }
}
