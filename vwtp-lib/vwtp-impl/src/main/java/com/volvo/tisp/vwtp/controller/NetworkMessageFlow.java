package com.volvo.tisp.vwtp.controller;

import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.GroupedFlux;
import reactor.util.Logger;
import reactor.util.Loggers;

/** VWTP Controller implementation */
@Component("controllerNetworkMessageFlow")
public class NetworkMessageFlow implements FlowComposer<NetworkMessageDto, Void> {
  private static final Logger logger = Loggers.getLogger(NetworkMessageFlow.class);

  private final FlowComposer<NetworkMessageDto, Void> outgoingResponderFlow;
  private final FlowComposer<NetworkMessageDto, Void> outgoingInitiatorFlow;

  /**
   * @param outgoingResponderFlow instance of {@link FlowComposer} &lt;{@link NetworkMessageDto},
   *     {@link Void}&gt;
   * @param outgoingInitiatorFlow instance of {@link FlowComposer} &lt;{@link NetworkMessageDto},
   *     {@link Void}&gt;
   */
  public NetworkMessageFlow(
      @Qualifier("outgoingControllerNetworkMessageFlowForResponder")
          final FlowComposer<NetworkMessageDto, Void> outgoingResponderFlow,
      @Qualifier("outgoingControllerNetworkMessageFlowForInitiator")
          final FlowComposer<NetworkMessageDto, Void> outgoingInitiatorFlow) {
    this.outgoingResponderFlow = outgoingResponderFlow;
    this.outgoingInitiatorFlow = outgoingInitiatorFlow;
  }

  @Override
  public Publisher<Void> apply(final Flux<NetworkMessageDto> flux) {
    logger.info("Composing reactive flow");
    return flux.groupBy(this::isMessageForResponder)
        .flatMap(this::separateMessagesForResponderAndForInitiator);
  }

  /**
   * Grouping operator indicating if message is for Initiator or for Responder
   *
   * @param networkMessage instance of {@link NetworkMessageDto}
   * @return true if message is for Responder false if for Initiator
   */
  private boolean isMessageForResponder(final NetworkMessageDto networkMessage) {
    final byte tidFirstByte = networkMessage.getPayload()[1];
    return 0 == (tidFirstByte & 0x80);
  }

  /**
   * Subscription operator that composes Responder and Initiator messages with corresponding {@link
   * FlowComposer}
   *
   * @param groupedFlux of {@link NetworkMessageDto} intended either for Responder or Initiator
   * @return instance of {@link Publisher}&lt;{@link Void}&gt; which completes when the composed
   *     {@link Flux} completes
   */
  private Publisher<Void> separateMessagesForResponderAndForInitiator(
      final GroupedFlux<Boolean, NetworkMessageDto> groupedFlux) {
    if (groupedFlux.key().booleanValue()) {
      return groupedFlux.transform(outgoingResponderFlow);
    } else {
      return groupedFlux.transform(outgoingInitiatorFlow);
    }
  }
}
