package com.volvo.tisp.vwtp.responder;

import static com.volvo.tisp.vwtp.constants.WtpConstants.MAX_NUMBER_OF_SEGMENTS;

import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.TransactionStatus;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.model.InvokeCommon;
import com.volvo.tisp.vwtp.model.SegInvokeModel;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tags;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.atomic.AtomicReferenceArray;
import java.util.concurrent.atomic.LongAdder;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import reactor.core.Disposable;
import reactor.core.Disposable.Swap;
import reactor.core.Disposables;

/** Represents a state of a single VWTP responder transaction */
public final class Transaction {
  private static final Disposable DISPOSED = Disposables.disposed();
  private static final AtomicReferenceArray<byte[]> EMPTY_SEGMENTS = new AtomicReferenceArray<>(0);
  private static final InvokeCommon INITIAL_INVOKE = SegInvokeModel.builder().withPsn(-1).build();
  private static final LongAdder inprogressCounter =
      Metrics.gauge(
          MetricNames.RESPONDER_ACTIVE_TRANSACTIONS,
          Tags.of(MetricNames.STATUS_TAG, TransactionStatus.INPROGRESS.name()),
          new LongAdder());
  private static final LongAdder userwaitCounter =
      Metrics.gauge(
          MetricNames.RESPONDER_ACTIVE_TRANSACTIONS,
          Tags.of(MetricNames.STATUS_TAG, TransactionStatus.USERWAIT.name()),
          new LongAdder());

  private final URI address;
  private final String messageId;
  private final long wtpTid;
  private final long vehicleId;
  private final WtpVersion wtpVersion;
  private final Map<String, String> properties;
  private final Swap timerHandleContainer = Disposables.swap();
  private AbortCode abortCode = null;
  private TransactionClass transactionClass = TransactionClass.CLASS_1;
  private volatile boolean userAckRequired = false;
  private volatile long userWaitTimestamp = 0;
  private volatile int previousTrailerPsn = -1;
  private final AtomicReference<TransactionStatus> status =
      new AtomicReference<>(TransactionStatus.INPROGRESS);
  private final AtomicReference<InvokeCommon> highestTrailer =
      new AtomicReference<>(INITIAL_INVOKE);
  private final AtomicInteger highestAcknowledgedTrailerPsn = new AtomicInteger(-1);
  private final AtomicInteger firstMissingSegmentPsn = new AtomicInteger();
  private AtomicReferenceArray<byte[]> segments =
      new AtomicReferenceArray<>(MAX_NUMBER_OF_SEGMENTS);

  public Transaction(
      final WtpVersion wtpVersion,
      final long vehicleId,
      final URI address,
      final String messageId,
      final long wtpTid,
      final Map<String, String> properties) {
    this.wtpVersion = wtpVersion;
    this.vehicleId = vehicleId;
    this.address = address;
    this.messageId = messageId;
    this.wtpTid = wtpTid;
    this.properties = properties;
    timerHandleContainer.replace(DISPOSED);
    inprogressCounter.increment();
  }

  /**
   * @return {@link AbortCode}
   */
  @Nullable
  public AbortCode getAbortCode() {
    return abortCode;
  }

  /**
   * Set {@link AbortCode} and mark transaction as {@link TransactionStatus#COMPLETED}
   *
   * @param abortCode new {@link AbortCode}
   */
  public void setAbortCode(@NonNull final AbortCode abortCode) {
    this.abortCode = abortCode;
  }

  /**
   * @return whenever current {@link TransactionStatus}
   */
  @NonNull
  public TransactionStatus getStatus() {
    return status.get();
  }

  /**
   * Mark transaction as {@link TransactionStatus#COMPLETED}
   *
   * @return previous {@link TransactionStatus}
   */
  public TransactionStatus markCompleted() {
    disposeActiveTimer();
    final TransactionStatus transactionStatus = status.getAndSet(TransactionStatus.COMPLETED);
    if (transactionStatus == TransactionStatus.INPROGRESS) {
      inprogressCounter.decrement();
    } else if (transactionStatus == TransactionStatus.USERWAIT) {
      userwaitCounter.decrement();
    }
    return transactionStatus;
  }

  /**
   * Mark transaction as {@link TransactionStatus#USERWAIT}
   *
   * @return previous {@link TransactionStatus}
   */
  public TransactionStatus markUserAcknowledgement() {
    final TransactionStatus previousStatus =
        status.getAndUpdate(
            currentStatus -> {
              if (currentStatus == TransactionStatus.INPROGRESS) {
                return TransactionStatus.USERWAIT;
              }
              return currentStatus;
            });
    if (previousStatus == TransactionStatus.INPROGRESS) {
      inprogressCounter.decrement();
      userwaitCounter.increment();
      userWaitTimestamp = System.currentTimeMillis();
    }
    return previousStatus;
  }

  /**
   * @return whenever user acknowledgment is required for this transaction
   */
  public boolean isUserAckRequired() {
    return userAckRequired;
  }

  /**
   * @param userAckRequired set when user acknowledgment is required for this transaction
   */
  public void setUserAckRequired(final boolean userAckRequired) {
    this.userAckRequired = userAckRequired;
  }

  /** Disposes current transaction timer (if any) */
  public void disposeActiveTimer() {
    timerHandleContainer.get().dispose();
  }

  /**
   * Atomically set the timer {@link Disposable} on this transaction and dispose the previous timer
   * (if any).
   *
   * @param timerDisposable the {@link Disposable} to set, may be null
   * @return true if the operation succeeded, false if the container has been disposed
   */
  public boolean updateActiveTimer(@NonNull final Disposable timerDisposable) {
    return timerHandleContainer.update(timerDisposable);
  }

  /**
   * @return the integer with latest trailer packet sequence number
   */
  public InvokeCommon getHighestTrailer() {
    return highestTrailer.get();
  }

  /**
   * @return the integer with previous trailer packet sequence number
   */
  public int getPreviousTrailerPsn() {
    return previousTrailerPsn;
  }

  /**
   * @param invokeCommon instance of {@link InvokeCommon}
   * @return true if permission to acknowledge trailer has been granted
   */
  public boolean requestPermissionToAckTrailer(final InvokeCommon invokeCommon) {
    final int highestTrailerPsn = highestTrailer.get().getPsn();
    return invokeCommon.getPsn() == highestTrailerPsn && invokeCommon.isTrailerRid()
        || invokeCommon.getPsn() > previousTrailerPsn
            && highestTrailerPsn
                > highestAcknowledgedTrailerPsn.getAndAccumulate(
                    highestTrailerPsn, this::returnBiggerInteger);
  }

  /**
   * Decrements highest acknowledged trailer packet sequence number if retransmission of the last
   * trailer has been received. In cases where acknowledgment was lost and GTR or TTR with RID
   * received.
   *
   * @param psn packet sequence number of the {@link InvokeCommon} retransmitted trailer
   * @return true if highestAcknowledgedTrailerPsn has been decremented
   */
  public boolean decrementIfRetransmissionOfLastTrailerReceived(final int psn) {
    return highestAcknowledgedTrailerPsn.compareAndSet(psn, psn - 1);
  }

  /**
   * @param invokeCommon instance of {@link InvokeCommon}
   * @return previous segment value
   */
  @Nullable
  public byte[] setSegmentAndGetPrevious(final InvokeCommon invokeCommon) {
    final AtomicReferenceArray<byte[]> segmentsReference = segments;
    if (segmentsReference != EMPTY_SEGMENTS) {
      final int invokePsn = invokeCommon.getPsn();
      final byte[] previousPayload =
          segmentsReference.getAndSet(invokePsn, invokeCommon.getPayload());
      if (invokeCommon.isTrailer()) {
        final int trailerPsn =
            highestTrailer.getAndAccumulate(invokeCommon, this::returnHigherInvokeCommon).getPsn();
        if (trailerPsn < invokePsn) {
          previousTrailerPsn = trailerPsn;
        }
      }
      return previousPayload;
    }
    return ArrayUtils.EMPTY_BYTE_ARRAY;
  }

  /**
   * @return true if any segment is missing within {@link Transaction#firstMissingSegmentPsn} and
   *     {@link Transaction#highestTrailer}
   */
  public boolean isSegmentMissing() {
    final AtomicReferenceArray<byte[]> segmentsReference = segments;
    if (segmentsReference != EMPTY_SEGMENTS) {
      int index = firstMissingSegmentPsn.get();
      for (; index <= highestTrailer.get().getPsn(); index++) {
        if (segmentsReference.get(index) == null) {
          firstMissingSegmentPsn.accumulateAndGet(index, this::returnBiggerInteger);
          return true;
        }
      }
      firstMissingSegmentPsn.accumulateAndGet(index, this::returnBiggerInteger);
    }
    return false;
  }

  private int returnBiggerInteger(final int current, final int provided) {
    return provided > current ? provided : current;
  }

  private InvokeCommon returnHigherInvokeCommon(
      final InvokeCommon current, final InvokeCommon provided) {
    return provided.getPsn() > current.getPsn() ? provided : current;
  }

  /**
   * @return array with missing packet sequence numbers
   */
  @NonNull
  public long[] getMissingPacketSequenceNumbers() {
    long[] returnArray = ArrayUtils.EMPTY_LONG_ARRAY;
    final int highestTrailerPsnValue = highestTrailer.get().getPsn();
    final int firstMissingSegmentPsnValue = firstMissingSegmentPsn.get();
    if (highestTrailerPsnValue > firstMissingSegmentPsnValue) {
      final int initialPsnArrayLenth = highestTrailerPsnValue - firstMissingSegmentPsnValue;
      int psnArrayIndex = 0;
      final long[] psnArray = new long[initialPsnArrayLenth];
      for (int index = firstMissingSegmentPsnValue; index < highestTrailerPsnValue; index++) {
        if (segments.get(index) == null) {
          psnArray[psnArrayIndex++] = index;
        }
      }

      returnArray = new long[psnArrayIndex];
      System.arraycopy(psnArray, 0, returnArray, 0, psnArrayIndex);
    }
    return returnArray;
  }

  /**
   * Retrieves a fully assembled payload
   *
   * <p>Make sure to check if there are no missing segments ({@link #isSegmentMissing()}) before
   * attempting assembly
   *
   * @return assembled payload
   * @throws ArrayIndexOutOfBoundsException if transaction has missing segments and payload can not
   *     be assembled
   */
  @NonNull
  public byte[] getAssembledPayload() {
    int lengthOfPayloadArray = 0;
    final int highestTrailerPsnValue = highestTrailer.get().getPsn();
    for (int index = 0; index <= highestTrailerPsnValue; index++) {
      lengthOfPayloadArray += segments.get(index).length;
    }

    int startingPosition = 0;
    final byte[] payload = new byte[lengthOfPayloadArray];
    int index = 0;
    for (; index <= highestTrailerPsnValue; index++) {
      final byte[] segmentPayload = segments.get(index);
      System.arraycopy(segmentPayload, 0, payload, startingPosition, segmentPayload.length);
      startingPosition += segmentPayload.length;
    }

    /** Transaction payload is no longer required from this point */
    segments = EMPTY_SEGMENTS;
    return payload;
  }

  public URI getAddress() {
    return address;
  }

  public String getMessageId() {
    return messageId;
  }

  public long getWtpTid() {
    return wtpTid;
  }

  public long getVehicleId() {
    return vehicleId;
  }

  public WtpVersion getWtpVersion() {
    return wtpVersion;
  }

  public TransactionClass getTransactionClass() {
    return transactionClass;
  }

  public void setTransactionClass(final TransactionClass transactionClass) {
    this.transactionClass = transactionClass;
  }

  public Map<String, String> getProperties() {
    return properties;
  }

  /**
   * @return {@link System#currentTimeMillis()} of the moment then transaction entered {@link
   *     TransactionStatus#USERWAIT}
   */
  public long getUserWaitTimestamp() {
    return userWaitTimestamp;
  }

  /**
   * @return total number of transactions with {@link TransactionStatus#INPROGRESS}
   */
  public static long getInprogressCounter() {
    return inprogressCounter.sum();
  }

  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }

    if (o == null || getClass() != o.getClass()) {
      return false;
    }

    final Transaction that = (Transaction) o;

    return new EqualsBuilder()
        .append(address, that.address)
        .append(wtpTid, that.wtpTid)
        .append(messageId, that.messageId)
        .isEquals();
  }

  @Override
  public int hashCode() {
    return new HashCodeBuilder(17, 37)
        .append(address)
        .append(wtpTid)
        .append(messageId)
        .toHashCode();
  }

  @Override
  public String toString() {
    return "Transaction{"
        + "messageId="
        + messageId
        + ", status="
        + status
        + ", wtpTid="
        + wtpTid
        + ", address="
        + address
        + ", vehicleId="
        + vehicleId
        + ", wtpVersion="
        + wtpVersion
        + ", transactionClass="
        + transactionClass
        + '}';
  }
}
