package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.PsnStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.TrailerStep;
import com.volvo.tisp.vwtp.builder.step.VidStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;

public interface SegInvokePdu2Builder<B>
    extends TrailerStep<B>, RidStep<B>, WtpTidStep<B>, PsnStep<B>, VidStep<B> {
  SegInvokePdu2 buildSegInvokePdu2();

  static <T extends SegInvokePdu2Builder<T>> SegInvokePdu2Builder<T> builder() {
    return new PduBuilder<>();
  }
}
