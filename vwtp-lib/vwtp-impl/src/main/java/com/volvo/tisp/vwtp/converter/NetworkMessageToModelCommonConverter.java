package com.volvo.tisp.vwtp.converter;

import com.volvo.tisp.vwtp.codec.ASNException;
import com.volvo.tisp.vwtp.codec.PERStream;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu_pdu;
import com.volvo.tisp.vwtp.converter.codectomodel.AbortPduToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.AckPduToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.InvokePduToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.NackPduToModelCommonConverter;
import com.volvo.tisp.vwtp.converter.codectomodel.PERStreamToTpiCommonArray;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.TpiCommon;
import com.volvo.tisp.vwtp.util.MetricNames;
import com.volvo.tisp.vwtp.util.ResponderMetrics;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.SynchronousSink;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/** Converter utility for converting WTP payload (byte[]) to {@link ModelCommon} */
public final class NetworkMessageToModelCommonConverter {
  private static final Logger logger =
      LoggerFactory.getLogger(NetworkMessageToModelCommonConverter.class);
  private static final Counter incomingPduSizes =
      Counter.builder(MetricNames.INCOMING_PDU_SIZES).register(Metrics.globalRegistry);

  /**
   * Converts {@link NetworkMessageDto} to {@link ModelCommon} and discards messages that failed
   * ASN1 parsing
   *
   * @param networkMessage {@link NetworkMessageDto}
   * @param sink {@link SynchronousSink} that is used to advance message further into a flow if it
   *     is a valid {@link ModelCommon}
   */
  public static void convert(
      final NetworkMessageDto networkMessage,
      final SynchronousSink<Tuple2<NetworkMessageDto, ModelCommon>> sink) {
    TpiCommon[] tpiArray = TpiCommon.EMPTY;

    try {
      final PERStream perStream = new PERStream(networkMessage.getPayload());
      final WirelessTransactionPdu wirelessTransactionPdu = new WirelessTransactionPdu();
      wirelessTransactionPdu.decode(perStream);

      if (wirelessTransactionPdu.getCon()) {
        tpiArray = PERStreamToTpiCommonArray.convert(perStream);
      }

      switch (wirelessTransactionPdu.getPdu().getChoice()) {
        case WirelessTransactionPdu_pdu.E_INVOKEPDU:
          sink.next(
              Tuples.of(
                  networkMessage,
                  InvokePduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getInvokePdu(),
                      perStream.getUnusedBufferPart())));
          break;
        case WirelessTransactionPdu_pdu.E_ACKPDU:
          sink.next(
              Tuples.of(
                  networkMessage,
                  AckPduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getAckPdu(), tpiArray)));
          break;
        case WirelessTransactionPdu_pdu.E_ABORTPDU:
          sink.next(
              Tuples.of(
                  networkMessage,
                  AbortPduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getAbortPdu())));
          break;
        case WirelessTransactionPdu_pdu.E_SEGINVOKEPDU:
          sink.next(
              Tuples.of(
                  networkMessage,
                  InvokePduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getSegInvokePdu(),
                      perStream.getUnusedBufferPart())));
          break;
        case WirelessTransactionPdu_pdu.E_NACKPDU:
          sink.next(
              Tuples.of(
                  networkMessage,
                  NackPduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getNackPdu())));
          break;
        case WirelessTransactionPdu_pdu.E_INVOKEPDU2:
          sink.next(
              Tuples.of(
                  networkMessage,
                  InvokePduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getInvokePdu2(),
                      perStream.getUnusedBufferPart())));
          break;
        case WirelessTransactionPdu_pdu.E_ACKPDU2:
          sink.next(
              Tuples.of(
                  networkMessage,
                  AckPduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getAckPdu2(), tpiArray)));
          break;
        case WirelessTransactionPdu_pdu.E_ABORTPDU2:
          sink.next(
              Tuples.of(
                  networkMessage,
                  AbortPduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getAbortPdu2())));
          break;
        case WirelessTransactionPdu_pdu.E_SEGINVOKEPDU2:
          sink.next(
              Tuples.of(
                  networkMessage,
                  InvokePduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getSegInvokePdu2(),
                      perStream.getUnusedBufferPart())));
          break;
        case WirelessTransactionPdu_pdu.E_NACKPDU2:
          sink.next(
              Tuples.of(
                  networkMessage,
                  NackPduToModelCommonConverter.convert(
                      wirelessTransactionPdu.getPdu().getNackPdu2())));
          break;
        default:
          incomingPduSizes.increment(networkMessage.getPayload().length);
          ResponderMetrics.incrementIncomingBadPdu(networkMessage.getAddress());
          logger.warn(
              "Unsupported VWTP PDU type: {}.", wirelessTransactionPdu.getPdu().getChoice());
          break;
      }
    } catch (final ASNException | RuntimeException e) {
      incomingPduSizes.increment(networkMessage.getPayload().length);
      ResponderMetrics.incrementIncomingBadPdu(networkMessage.getAddress());
      logger.warn("Converting datagram (byte[]) to ModelCommon (VWTP PDU) failed.");
    }
  }

  private NetworkMessageToModelCommonConverter() {
    throw new IllegalStateException();
  }
}
