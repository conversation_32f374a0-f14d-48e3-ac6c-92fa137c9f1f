package com.volvo.tisp.vwtp.converter.codectobytes;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.codec.ASNSequence;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu_pdu;

/** Converter utility for converting {@link SegInvokePdu} or {@link SegInvokePdu2} into byte[] */
public class SegInvokePduToByteArrayConverter {
  /** Private constructor to prevent instantiation of utility class */
  private SegInvokePduToByteArrayConverter() {}

  /**
   * Convert {@link SegInvokePdu} and SRP data to byte array
   *
   * @param segInvokePdu {@link SegInvokePdu}
   * @param payload SRP data
   * @return byte[]
   */
  public static byte[] convert(final SegInvokePdu segInvokePdu, final byte[] payload) {
    validateSegInvokePduAndPayload(segInvokePdu, payload);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setSegInvokePdu(segInvokePdu);

    return WtpPduToByteArrayConverter.convert(pdu, 4, payload);
  }

  /**
   * Convert {@link SegInvokePdu2} and SRP data to byte array
   *
   * @param segInvokePdu {@link SegInvokePdu2}
   * @param payload SRP data
   * @return byte[]
   */
  public static byte[] convert(final SegInvokePdu2 segInvokePdu, final byte[] payload) {
    validateSegInvokePduAndPayload(segInvokePdu, payload);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setSegInvokePdu2(segInvokePdu);

    return WtpPduToByteArrayConverter.convert(pdu, 8, payload);
  }

  /**
   * @param invokePdu {@link SegInvokePdu} or {@link SegInvokePdu2}
   * @param payload SRP data
   */
  private static void validateSegInvokePduAndPayload(
      final ASNSequence invokePdu, final byte[] payload) {
    Preconditions.checkNotNull(invokePdu, "Segmented Invoke PDU must not be null");
    Preconditions.checkNotNull(payload, "Segmented Invoke PDU (SRP) payload must not be null");
  }
}
