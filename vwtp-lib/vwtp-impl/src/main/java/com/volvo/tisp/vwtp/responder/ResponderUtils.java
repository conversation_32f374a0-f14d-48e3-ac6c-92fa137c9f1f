package com.volvo.tisp.vwtp.responder;

import com.google.common.base.Strings;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.vwtp.builder.AbortPdu2Builder;
import com.volvo.tisp.vwtp.builder.AbortPduBuilder;
import com.volvo.tisp.vwtp.builder.AckPdu2Builder;
import com.volvo.tisp.vwtp.builder.AckPduBuilder;
import com.volvo.tisp.vwtp.builder.NackPdu2Builder;
import com.volvo.tisp.vwtp.builder.NackPduBuilder;
import com.volvo.tisp.vwtp.codec.AbortPdu;
import com.volvo.tisp.vwtp.codec.AbortPdu2;
import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import com.volvo.tisp.vwtp.codec.NackPdu;
import com.volvo.tisp.vwtp.codec.NackPdu2;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.codectobytes.AbortPduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.AckPduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.NackPduToByteArrayConverter;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto.NetworkMessageDtoBuilder;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.model.InvokeCommon;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.util.MetricNames;
import com.volvo.tisp.vwtp.util.ResponderMetrics;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.binder.BaseUnits;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import reactor.util.Logger;
import reactor.util.Loggers;

/** Responder utilities */
public final class ResponderUtils {
  private static final Logger logger = Loggers.getLogger(ResponderUtils.class);
  private static final DistributionSummary messageSegmentCountDistribution =
      DistributionSummary.builder(MetricNames.RESPONDER_OUTGOING_MESSAGES_SEGMENTS)
          .description("Distribution of number of sements in an outgoing message")
          .maximumExpectedValue(256d)
          .baseUnit("segments")
          .register(Metrics.globalRegistry);
  private static final DistributionSummary messagePayloadsDistribution =
      DistributionSummary.builder(MetricNames.RESPONDER_OUTGOING_MESSAGES_PAYLOADS)
          .description("Distribution of outgoing user message payload sizes")
          .baseUnit(BaseUnits.BYTES)
          .register(Metrics.globalRegistry);
  private static final Counter segmentedMessagesCount =
      Counter.builder(MetricNames.RESPONDER_OUTGOING_MESSAGES_SEGMENTED)
          .description("Count of outgoing segmented user messages")
          .register(Metrics.globalRegistry);
  private static final Counter outgoingUserMessageCounter =
      Counter.builder(MetricNames.RESPONDER_OUTGOING_MESSAGES)
          .description("Count of outgoing user messages")
          .register(Metrics.globalRegistry);
  private static final Counter outgoingPduSizes =
      Counter.builder(MetricNames.OUTGOING_PDU_SIZES)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_RESPONDER)
          .register(Metrics.globalRegistry);

  /** Private constructor to prevent initialization */
  private ResponderUtils() {
    throw new IllegalStateException();
  }

  /**
   * Creates {@link AckPdu} or {@link AckPdu2} wrapped inside {@link NetworkMessageDto}
   *
   * @param transaction {@link Transaction}
   * @return {@link NetworkMessageDto}
   */
  public static NetworkMessageDto createAcknowledgement(final Transaction transaction) {
    final InvokeCommon highestTrailer = transaction.getHighestTrailer();
    final NetworkMessageDto networkMessage;
    final NetworkMessageDtoBuilder networkMessageBuilder =
        NetworkMessageDto.builder()
            .withAddress(transaction.getAddress())
            .withProperties(transaction.getProperties())
            .withMessageId(transaction.getMessageId());

    if (transaction.getWtpVersion() == WtpVersion.VERSION_2) {
      final AckPdu2 ackPdu =
          AckPdu2Builder.builder()
              .withOtr(false)
              .withRid(false)
              .withWtpTidReverseDirection(transaction.getWtpTid())
              .withVid(transaction.getVehicleId())
              .buildAckPdu2();
      if (highestTrailer.getPsn() > 0) {
        networkMessage =
            networkMessageBuilder
                .withPayload(AckPduToByteArrayConverter.convert(ackPdu, highestTrailer.getPsn()))
                .build();
      } else {
        networkMessage =
            networkMessageBuilder.withPayload(AckPduToByteArrayConverter.convert(ackPdu)).build();
      }
    } else {
      final AckPdu ackPdu =
          AckPduBuilder.builder()
              .withOtr(false)
              .withRid(false)
              .withWtpTidReverseDirection(transaction.getWtpTid())
              .buildAckPdu();
      if (highestTrailer.getPsn() > 0) {
        networkMessage =
            networkMessageBuilder
                .withPayload(AckPduToByteArrayConverter.convert(ackPdu, highestTrailer.getPsn()))
                .build();
      } else {
        networkMessage =
            networkMessageBuilder.withPayload(AckPduToByteArrayConverter.convert(ackPdu)).build();
      }
    }
    outgoingPduSizes.increment(networkMessage.getPayload().length);
    ResponderMetrics.incrementOutgoingPdu(PduType.ACK, networkMessage.getAddress());
    return networkMessage;
  }

  /**
   * Creates {@link NackPdu} or {@link NackPdu2} wrapped inside {@link NetworkMessageDto}
   *
   * @param transaction {@link Transaction}
   * @return {@link NetworkMessageDto}
   */
  @Nullable
  public static NetworkMessageDto createNegativeAcknowledgement(final Transaction transaction) {
    final long[] missingPacketSequenceNumbers = transaction.getMissingPacketSequenceNumbers();
    if (missingPacketSequenceNumbers.length > 0) {
      final NetworkMessageDto networkMessage;
      final NetworkMessageDtoBuilder networkMessageBuilder =
          NetworkMessageDto.builder()
              .withAddress(transaction.getAddress())
              .withProperties(transaction.getProperties())
              .withMessageId(transaction.getMessageId());

      if (transaction.getWtpVersion() == WtpVersion.VERSION_2) {
        final NackPdu2 nackPdu =
            NackPdu2Builder.builder()
                .withWtpTidReverseDirection(transaction.getWtpTid())
                .withMissingPsn(missingPacketSequenceNumbers)
                .withVid(transaction.getVehicleId())
                .buildNackPdu2();
        networkMessage =
            networkMessageBuilder.withPayload(NackPduToByteArrayConverter.convert(nackPdu)).build();
      } else {
        final NackPdu nackPdu =
            NackPduBuilder.builder()
                .withWtpTidReverseDirection(transaction.getWtpTid())
                .withMissingPsn(missingPacketSequenceNumbers)
                .buildNackPdu();
        networkMessage =
            networkMessageBuilder.withPayload(NackPduToByteArrayConverter.convert(nackPdu)).build();
      }
      outgoingPduSizes.increment(networkMessage.getPayload().length);
      ResponderMetrics.incrementOutgoingPdu(PduType.NACK, networkMessage.getAddress());
      return networkMessage;
    }
    logger.debug("Aborted NackTimer for {}", transaction);
    return null;
  }

  /**
   * Creates {@link AbortPdu} or {@link AbortPdu2} wrapped inside {@link NetworkMessageDto}
   *
   * @param transaction {@link Transaction}
   * @param abortCode {@link AbortCode}
   * @return {@link NetworkMessageDto}
   */
  public static NetworkMessageDto createAbort(
      @NonNull final Transaction transaction, @NonNull final AbortCode abortCode) {
    transaction.setAbortCode(abortCode);
    final NetworkMessageDto networkMessage;
    final NetworkMessageDtoBuilder networkMessageBuilder =
        NetworkMessageDto.builder()
            .withAddress(transaction.getAddress())
            .withProperties(transaction.getProperties())
            .withMessageId(transaction.getMessageId());

    if (transaction.getWtpVersion() == WtpVersion.VERSION_2) {
      final AbortPdu2 abortPdu =
          AbortPdu2Builder.builder()
              .withAbortCode(abortCode)
              .withWtpTidReverseDirection(transaction.getWtpTid())
              .withVid(transaction.getVehicleId())
              .buildAbortPdu2();
      networkMessage =
          networkMessageBuilder.withPayload(AbortPduToByteArrayConverter.convert(abortPdu)).build();
    } else {
      final AbortPdu abortPdu =
          AbortPduBuilder.builder()
              .withAbortCode(abortCode)
              .withWtpTidReverseDirection(transaction.getWtpTid())
              .buildAbortPdu();
      networkMessage =
          networkMessageBuilder.withPayload(AbortPduToByteArrayConverter.convert(abortPdu)).build();
    }
    outgoingPduSizes.increment(networkMessage.getPayload().length);
    ResponderMetrics.incrementOutgoingPdu(PduType.ABORT, networkMessage.getAddress());
    ResponderMetrics.incrementOutgoingAbort(abortCode);
    return networkMessage;
  }

  /**
   * Creates {@link AbortPdu} or {@link AbortPdu2} wrapped inside {@link NetworkMessageDto}
   *
   * @param referenceNetworkMessage instance of {@link NetworkMessageDto}
   * @param modelCommon instance of {@link ModelCommon}
   * @param abortCode instance of {@link AbortCode}
   * @return {@link NetworkMessageDto}
   */
  static NetworkMessageDto createAbort(
      final NetworkMessageDto referenceNetworkMessage,
      final ModelCommon modelCommon,
      final AbortCode abortCode) {
    final NetworkMessageDto networkMessage;
    final NetworkMessageDtoBuilder networkMessageBuilder =
        NetworkMessageDto.builder()
            .withAddress(referenceNetworkMessage.getAddress())
            .withProperties(referenceNetworkMessage.getProperties())
            .withMessageId(referenceNetworkMessage.getMessageId());

    if (modelCommon.getWtpVersion() == WtpVersion.VERSION_2) {
      final AbortPdu2 abortPdu =
          AbortPdu2Builder.builder()
              .withAbortCode(abortCode)
              .withWtpTidReverseDirection(modelCommon.getWtpTid())
              .withVid(modelCommon.getVid())
              .buildAbortPdu2();
      final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    } else {
      final AbortPdu abortPdu =
          AbortPduBuilder.builder()
              .withAbortCode(abortCode)
              .withWtpTidReverseDirection(modelCommon.getWtpTid())
              .buildAbortPdu();
      final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    }
    outgoingPduSizes.increment(networkMessage.getPayload().length);
    ResponderMetrics.incrementOutgoingPdu(PduType.ABORT, networkMessage.getAddress());
    ResponderMetrics.incrementOutgoingAbort(abortCode);
    return networkMessage;
  }

  /**
   * @param existingMessageId current Message ID
   * @return existingMessageId or new generated Message ID if existingMessageId is null or empty
   */
  public static String createOrReuseMessageId(final @Nullable String existingMessageId) {
    if (Strings.isNullOrEmpty(existingMessageId)) {
      return TrackingIdentifier.create().toString();
    }
    return existingMessageId;
  }

  /**
   * Creates {@link UserMessageDto} with assembled payload inside
   *
   * @param transaction {@link Transaction}
   * @return {@link NetworkMessageDto}
   */
  public static UserMessageDto createUserMessage(final Transaction transaction) {
    final UserMessageDto userMessage =
        UserMessageDto.builder()
            .withAddress(transaction.getAddress())
            .withMessageId(transaction.getMessageId())
            .withTransactionClass(transaction.getTransactionClass())
            .withWtpVersion(transaction.getWtpVersion())
            .withVehicleId(transaction.getVehicleId())
            .withPayload(transaction.getAssembledPayload())
            .withProperties(transaction.getProperties())
            .build();
    final int numberOfSegments = transaction.getHighestTrailer().getPsn() + 1;
    messageSegmentCountDistribution.record(numberOfSegments);
    if (numberOfSegments > 1) {
      segmentedMessagesCount.increment();
    }
    outgoingUserMessageCounter.increment();
    messagePayloadsDistribution.record(userMessage.getPayload().length);
    return userMessage;
  }
}
