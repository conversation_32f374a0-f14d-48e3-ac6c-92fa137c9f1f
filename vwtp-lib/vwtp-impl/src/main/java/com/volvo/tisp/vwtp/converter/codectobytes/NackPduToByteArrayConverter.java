package com.volvo.tisp.vwtp.converter.codectobytes;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.codec.ASNSequence;
import com.volvo.tisp.vwtp.codec.NackPdu;
import com.volvo.tisp.vwtp.codec.NackPdu2;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu_pdu;

/** Converter utility for converting {@link NackPdu} or {@link NackPdu2} into byte[] */
public class NackPduToByteArrayConverter {
  /** Private constructor to prevent instantiation of utility class */
  private NackPduToByteArrayConverter() {}

  /**
   * Convert {@link NackPdu} to byte array
   *
   * @param nackPdu {@link NackPdu}
   * @return byte[]
   */
  public static byte[] convert(final NackPdu nackPdu) {
    validateNackPdu(nackPdu);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setNackPdu(nackPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 4 + nackPdu.getMp().m_Array.size());
  }

  /**
   * Convert {@link NackPdu2} to byte array
   *
   * @param nackPdu {@link NackPdu2}
   * @return byte[]
   */
  public static byte[] convert(final NackPdu2 nackPdu) {
    validateNackPdu(nackPdu);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setNackPdu2(nackPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 8 + nackPdu.getMp().m_Array.size());
  }

  /**
   * @param nackPdu {@link NackPdu} or {@link NackPdu2}
   */
  private static void validateNackPdu(final ASNSequence nackPdu) {
    Preconditions.checkNotNull(nackPdu, "NackPdu must not be null");
  }
}
