package com.volvo.tisp.vwtp.converter.codectobytes;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.codec.ASNSequence;
import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu_pdu;

/** Converter utility for converting {@link AckPdu} or {@link AckPdu2} into byte[] */
public class AckPduToByteArrayConverter {
  /** Private constructor to prevent instantiation of utility class */
  private AckPduToByteArrayConverter() {}

  /**
   * Convert {@link AckPdu} to byte array
   *
   * @param ackPdu {@link AckPdu}
   * @param psnTpiData PSN TPI data representing packet sequence number to be acknowledged in SAR
   * @return byte[]
   */
  public static byte[] convert(final AckPdu ackPdu, final long psnTpiData) {
    validateAckPduAndPsn(ackPdu, psnTpiData);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setAckPdu(ackPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 3, psnTpiData);
  }

  /**
   * Convert {@link AckPdu} to byte array
   *
   * @param ackPdu {@link AckPdu}
   * @return byte[]
   */
  public static byte[] convert(final AckPdu ackPdu) {
    validateAckPdu(ackPdu);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setAckPdu(ackPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 3);
  }

  /**
   * Convert {@link AckPdu2} to byte array
   *
   * @param ackPdu {@link AckPdu2}
   * @param psnTpiData PSN TPI data representing packet sequence number to be acknowledged in SAR
   * @return byte[]
   */
  public static byte[] convert(final AckPdu2 ackPdu, final long psnTpiData) {
    validateAckPduAndPsn(ackPdu, psnTpiData);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setAckPdu2(ackPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 7, psnTpiData);
  }

  /**
   * Convert {@link AckPdu2} to byte array
   *
   * @param ackPdu {@link AckPdu2}
   * @return byte[]
   */
  public static byte[] convert(final AckPdu2 ackPdu) {
    validateAckPdu(ackPdu);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setAckPdu2(ackPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 7);
  }

  /**
   * @param ackPdu {@link AckPdu} or {@link AckPdu2}
   */
  private static void validateAckPdu(final ASNSequence ackPdu) {
    Preconditions.checkNotNull(ackPdu, "AckPdu must not be null");
  }

  /**
   * @param ackPdu {@link AckPdu} or {@link AckPdu2}
   * @param psnTpiData PSN TPI data representing packet sequence number to be acknowledged in SAR
   */
  private static void validateAckPduAndPsn(final ASNSequence ackPdu, final long psnTpiData) {
    validateAckPdu(ackPdu);
    Preconditions.checkArgument(
        psnTpiData >= 0 && psnTpiData < 256, "PSN TPI data must between [1 .. 255]");
  }
}
