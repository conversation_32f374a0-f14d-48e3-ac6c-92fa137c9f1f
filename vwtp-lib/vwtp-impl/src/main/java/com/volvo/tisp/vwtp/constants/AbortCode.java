package com.volvo.tisp.vwtp.constants;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Abort code enumeration
 *
 * <p>8.3.4.1. Abort type
 */
public enum AbortCode {
  /** A generic error code indicating an unexpected provider error. */
  TCE_PROVIDER_UNKNOWN(0x0L, 0x0L, "/WTP/TCE/ABORT/UNKNOWN", "abort-00-tce-provider-unknown"),
  TGW_PROVIDER_UNKNOWN(0x0L, 0x0L, "/WTP/TGW/ABORT/UNKNOWN", "abort-00-tgw-provider-unknown"),

  /** The received PDU could not be interpreted. The structure MAY be wrong. */
  TCE_PROVIDER_PROTOCOL_ERROR(
      0x0L, 0x1L, "/WTP/TCE/ABORT/PROTOCOL_ERROR", "abort-01-tce-provider-protocol-error"),
  TGW_PROVIDER_PROTOCOL_ERROR(
      0x0L, 0x1L, "/WTP/TGW/ABORT/PROTOCOL_ERROR", "abort-01-tgw-provider-protocol-error"),

  /** Only used by the Initiator as a negative result to the TID verification. */
  TCE_PROVIDER_INVALID_WTP_TID(
      0x0L, 0x2L, "/WTP/TCE/ABORT/INVALID_WTP_TID", "abort-02-tce-provider-invalid-tid"),
  TGW_PROVIDER_INVALID_WTP_TID(
      0x0L, 0x2L, "/WTP/TGW/ABORT/INVALID_WTP_TID", "abort-02-tgw-provider-invalid-tid"),

  /** The transaction could not be completed since the transaction class is not supported. */
  TCE_PROVIDER_NOT_IMPLEMENTED_CLASS(
      0x0L,
      0x3L,
      "/WTP/TCE/ABORT/CLASS_NOT_IMPLEMENTED",
      "abort-03-tce-provider-not-implemented-class"),
  TGW_PROVIDER_NOT_IMPLEMENTED_CLASS(
      0x0L,
      0x3L,
      "/WTP/TGW/ABORT/CLASS_NOT_IMPLEMENTED",
      "abort-03-tgw-provider-not-implemented-class"),

  /** The transaction could not be completed since the Responder does not support SAR. */
  TCE_PROVIDER_NOT_IMPLEMENTED_SAR(
      0x0L,
      0x4L,
      "/WTP/TCE/ABORT/SAR_NOT_IMPLEMENTED",
      "abort-04-tce-provider-not-implemented-sar"),
  TGW_PROVIDER_NOT_IMPLEMENTED_SAR(
      0x0L,
      0x4L,
      "/WTP/TGW/ABORT/SAR_NOT_IMPLEMENTED",
      "abort-04-tgw-provider-not-implemented-sar"),

  /**
   * The transaction could not be completed since the Responder does not support User
   * acknowledgments.
   */
  TCE_PROVIDER_NOT_IMPLEMENTED_USER_ACK(
      0x0L,
      0x5L,
      "/WTP/TCE/ABORT/USER_ACK_NOT_IMPLEMENTED",
      "abort-05-tce-provider-not-implemented-user-ack"),
  TGW_PROVIDER_NOT_IMPLEMENTED_USER_ACK(
      0x0L,
      0x5L,
      "/WTP/TGW/ABORT/USER_ACK_NOT_IMPLEMENTED",
      "abort-05-tgw-provider-not-implemented-user-ack"),

  /** Current version is 1. The initiator requested a different version that is not supported. */
  TCE_PROVIDER_NOT_IMPLEMENTED_WTP_VERSION(
      0x0L,
      0x6L,
      "/WTP/TCE/ABORT/WTP_VERSION_NOT_IMPLEMENTED",
      "abort-06-tce-provider-not-implemented-wtp-version"),
  TGW_PROVIDER_NOT_IMPLEMENTED_WTP_VERSION(
      0x0L,
      0x6L,
      "/WTP/TGW/ABORT/WTP_VERSION_NOT_IMPLEMENTED",
      "abort-06-tgw-provider-not-implemented-wtp-version"),

  /** Due to an overload situation the transaction can not be completed. */
  TCE_PROVIDER_CAPACITY_EXCEEDED(
      0x0L, 0x7L, "/WTP/TCE/ABORT/CAPACITY_EXCEEDED", "abort-07-tce-provider-capacity-exceeded"),
  TGW_PROVIDER_CAPACITY_EXCEEDED(
      0x0L, 0x7L, "/WTP/TGW/ABORT/CAPACITY_EXCEEDED", "abort-07-tgw-provider-capacity-exceeded"),

  /** A User acknowledgement was requested but the WTP user did not respond. */
  TCE_PROVIDER_NO_RESPONSE(
      0x0L, 0x8L, "/WTP/TCE/ABORT/NO_RESPONSE_RECEIVED", "abort-08-tce-provider-no-response"),
  TGW_PROVIDER_NO_RESPONSE(
      0x0L, 0x8L, "/WTP/TGW/ABORT/RECEIVED_NO_RESPONSE_ABORT", "abort-08-tgw-provider-no-response"),

  /**
   * Due to a message size bigger than the capabilities of the receiver the transaction cannot be
   * completed.
   */
  TCE_PROVIDER_MESSAGE_TOO_LARGE(
      0x0L, 0x9L, "/WTP/TCE/ABORT/MESSAGE_TOO_LARGE", "abort-09-tce-provider-message-too-large"),
  TGW_PROVIDER_MESSAGE_TOO_LARGE(
      0x0L, 0x9L, "/WTP/TGW/ABORT/MESSAGE_TOO_LARGE", "abort-09-tgw-provider-message-too-large"),

  /** The transaction could not be completed since the Responder does not support extended SAR. */
  TCE_PROVIDER_NOT_IMPLEMENTED_EXTENDED_SAR(
      0x0L,
      0xAL,
      "/WTP/TCE/ABORT/EXTENDED_SAR_NOT_IMPLEMENTED",
      "abort-0A-tce-provider-not-implemented-extended-sar"),
  TGW_PROVIDER_NOT_IMPLEMENTED_EXTENDED_SAR(
      0x0L,
      0xAL,
      "/WTP/TGW/ABORT/EXTENDED_SAR_NOT_IMPLEMENTED",
      "abort-0A-tgw-provider-not-implemented-extended-sar"),

  /** The WTP Provider on the TGW has aborted the Transaction due to a Timeout. */
  TGW_PROVIDER_TRANSACTION_TIMEOUT(
      0x0L, 0x40, "/WTP/TGW/ABORT/PROVIDER_TIMEOUT", "abort-40-tgw-provider-timeout"),

  /** A generic error code indicating an unexpected user error. */
  TCE_USER_UNKNOWN(0x1L, 0x0L, "/WTP/TCE/USER_ABORT/UNKNOWN", "abort-00-tce-user-unknown"),
  TGW_USER_UNKNOWN(0x1L, 0x0L, "/WTP/TGW/USER_ABORT/UNKNOWN", "abort-00-tgw-user-unknown"),

  /**
   * The user did not support the SWAP service or version in service data unit. On-board software
   * can not continue processing.
   */
  TCE_USER_UNSUPPORTED_SERVICE_VERSION(
      0x1L,
      0x80L,
      "/WTP/TCE/USER_ABORT/UNSUPPORTED_SERVICE_VERSION",
      "abort-80-tce-user-unsupported-service-version"),
  TGW_USER_UNSUPPORTED_SERVICE_VERSION(
      0x1L,
      0x80L,
      "/WTP/TGW/USER_ABORT/UNSUPPORTED_SERVICE_VERSION",
      "abort-80-tgw-user-unsupported-service-version");

  private static final Logger logger = LoggerFactory.getLogger(AbortCode.class);

  private final long type;
  private final long reason;
  private final String integrationName;
  private final String metricName;

  AbortCode(
      final long type, final long reason, final String integrationName, final String metricName) {
    this.type = type;
    this.reason = reason;
    this.integrationName = integrationName;
    this.metricName = metricName;
  }

  /**
   * @return abort type
   *     <pre>
   *  0x00 - user
   *  0x01 - provider
   *         </pre>
   */
  public long getType() {
    return type;
  }

  /**
   * @return abort reason
   */
  public long getReason() {
    return reason;
  }

  public String getIntegrationName() {
    return integrationName;
  }

  public String getMetricName() {
    return metricName;
  }

  /**
   * Generate {@link AbortCode} from abort type and abort reason
   *
   * @param abortType abort type
   * @param abortReason abort reason
   * @return instance of {@link AbortCode}
   */
  public static AbortCode fromValue(final long abortType, final long abortReason) {
    switch ((int) abortType) {
      case 0x0:
        switch ((int) abortReason) {
          case 0x0:
            return AbortCode.TGW_PROVIDER_UNKNOWN;
          case 0x1:
            return AbortCode.TGW_PROVIDER_PROTOCOL_ERROR;
          case 0x2:
            return AbortCode.TGW_PROVIDER_INVALID_WTP_TID;
          case 0x3:
            return AbortCode.TGW_PROVIDER_NOT_IMPLEMENTED_CLASS;
          case 0x4:
            return AbortCode.TGW_PROVIDER_NOT_IMPLEMENTED_SAR;
          case 0x5:
            return AbortCode.TGW_PROVIDER_NOT_IMPLEMENTED_USER_ACK;
          case 0x6:
            return AbortCode.TGW_PROVIDER_NOT_IMPLEMENTED_WTP_VERSION;
          case 0x7:
            return AbortCode.TGW_PROVIDER_CAPACITY_EXCEEDED;
          case 0x8:
            return AbortCode.TGW_PROVIDER_NO_RESPONSE;
          case 0x9:
            return AbortCode.TGW_PROVIDER_MESSAGE_TOO_LARGE;
          case 0xA:
            return AbortCode.TGW_PROVIDER_NOT_IMPLEMENTED_EXTENDED_SAR;
          case 0x40:
            return AbortCode.TGW_PROVIDER_TRANSACTION_TIMEOUT;
          default:
            logger.warn("Invalid provider abort reason: {}", abortReason);
            return AbortCode.TCE_PROVIDER_UNKNOWN;
        }
      case 0x1:
        switch ((int) abortReason) {
          case 0x0:
            return AbortCode.TGW_USER_UNKNOWN;
          case 0x80:
            return AbortCode.TGW_USER_UNSUPPORTED_SERVICE_VERSION;
          default:
            logger.warn("Invalid user abort reason: {}", abortReason);
            return AbortCode.TCE_USER_UNKNOWN;
        }
      default:
        logger.warn("Invalid abort type: {}", abortType);
        return AbortCode.TCE_PROVIDER_PROTOCOL_ERROR;
    }
  }
}
