package com.volvo.tisp.vwtp.converter.codectomodel;

import com.volvo.tisp.vwtp.codec.AbortPdu;
import com.volvo.tisp.vwtp.codec.AbortPdu2;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.model.AbortModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.TpiCommon;

/**
 * Converter utility for converting {@link AbortPdu} or {@link AbortPdu2} to instance of {@link
 * ModelCommon}
 */
public class AbortPduToModelCommonConverter {
  /** Private constructor to prevent instantiation of utility class */
  private AbortPduToModelCommonConverter() {}

  /**
   * Convert {@link AbortPdu} to {@link AbortModel}
   *
   * @param abortPdu {@link AbortPdu}
   * @return {@link AbortModel}
   */
  public static AbortModel convert(final AbortPdu abortPdu) {
    return AbortModel.builder()
        .withAbortCode(AbortCode.fromValue(abortPdu.getAbortType(), abortPdu.getAbortReason()))
        .withWtpTid(abortPdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_1)
        .withTpiArray(TpiCommon.EMPTY)
        .build();
  }

  /**
   * Convert {@link AbortPdu2} to {@link AbortModel}
   *
   * @param abortPdu {@link AbortPdu2}
   * @return {@link AbortModel}
   */
  public static AbortModel convert(final AbortPdu2 abortPdu) {
    return AbortModel.builder()
        .withAbortCode(AbortCode.fromValue(abortPdu.getAbortType(), abortPdu.getAbortReason()))
        .withWtpTid(abortPdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_2)
        .withVid(abortPdu.getVid())
        .withTpiArray(TpiCommon.EMPTY)
        .build();
  }
}
