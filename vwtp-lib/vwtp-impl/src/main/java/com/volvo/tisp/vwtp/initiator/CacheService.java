package com.volvo.tisp.vwtp.initiator;

import com.github.benmanes.caffeine.cache.Cache;
import com.volvo.tisp.vwtp.configuration.InitiatorConfiguration;
import com.volvo.tisp.vwtp.util.LogUtil;
import java.net.URI;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.lang.NonNull;
import org.springframework.stereotype.Component;

/** A component that exposes cache services with CRUD operations for for initiator caches */
@Component("initiatorCacheService")
public class CacheService implements DisposableBean {
  private static final Logger logger = LoggerFactory.getLogger(CacheService.class);

  private final Cache<InitiatorTransactionCacheKey, Transaction> transactionCache;
  private final Cache<URI, AtomicLong> lastWtpTidCache;

  public CacheService(
      final Cache<InitiatorTransactionCacheKey, Transaction> initiatorTransactionCache,
      final Cache<URI, AtomicLong> initiatorLastWtpTidCache) {
    transactionCache = initiatorTransactionCache;
    lastWtpTidCache = initiatorLastWtpTidCache;
  }

  /**
   * Create initiator WTP transaction
   *
   * @param transaction {@link Transaction}
   * @return com.volvo.tisp.vwtp.model.transaction.WtpInitiatorTransaction
   */
  public Transaction initiatorCreateWtpTransaction(final Transaction transaction) {
    final URI destination = transaction.getAddress();
    final Long wtpTid = transaction.getWtpTid();
    final InitiatorTransactionCacheKey key =
        InitiatorTransactionCacheKey.builder()
            .withDestination(destination)
            .withWtpTid(wtpTid)
            .build();

    transactionCache.put(key, transaction);
    logger.debug("#initiatorCreateWtpTransaction: {}", transaction);
    return transaction;
  }

  /**
   * Retrieve initiator WTP transaction
   *
   * @param destination {@link URI}
   * @param wtpTid a WTP TID
   * @return com.volvo.tisp.vwtp.model.transaction.WtpInitiatorTransaction
   */
  public Transaction initiatorReadWtpTransaction(final URI destination, final long wtpTid) {
    final InitiatorTransactionCacheKey key =
        InitiatorTransactionCacheKey.builder()
            .withDestination(destination)
            .withWtpTid(wtpTid)
            .build();

    return initiatorReadWtpTransaction(key);
  }

  /**
   * Retrieve initiator WTP transaction
   *
   * @param key {@link InitiatorTransactionCacheKey}
   * @return com.volvo.tisp.vwtp.model.transaction.WtpInitiatorTransaction
   */
  public Transaction initiatorReadWtpTransaction(final InitiatorTransactionCacheKey key) {
    final Transaction returnTransaction = transactionCache.getIfPresent(key);
    logger.debug("#initiatorReadWtpTransaction: {}", returnTransaction);
    return returnTransaction;
  }

  /**
   * Delete initiator WTP transaction
   *
   * @param transaction {@link Transaction}
   * @return com.volvo.tisp.vwtp.model.transaction.WtpInitiatorTransaction
   */
  public Transaction initiatorDeleteWtpTransaction(final Transaction transaction) {
    final URI destination = transaction.getAddress();
    final Long wtpTid = transaction.getWtpTid();
    final InitiatorTransactionCacheKey key =
        InitiatorTransactionCacheKey.builder()
            .withDestination(destination)
            .withWtpTid(wtpTid)
            .build();

    transactionCache.invalidate(key);
    logger.debug("#initiatorDeleteWtpTransaction: {}", transaction);
    return transaction;
  }

  /**
   * Reads the LastWtpTid if present or creates LastWtpTid if not already present
   *
   * @param destination - responder address
   * @return last WTP TID
   */
  @NonNull
  public AtomicLong initiatorReadOrCreateLastWtpTid(final URI destination) {
    final AtomicLong returnWtpTid =
        lastWtpTidCache.get(destination, CacheService::createInitialAtomicLong);
    logger.debug(
        "#initiatorReadOrCreateLastWtpTid: address={}, wtpTid={}", destination, returnWtpTid);
    return returnWtpTid;
  }

  /**
   * Creates initial {@link AtomicLong} for tracking last WTP TID
   *
   * @param uri instance of {@link URI}
   * @return instance of {@link AtomicLong}
   */
  private static AtomicLong createInitialAtomicLong(URI uri) {
    return new AtomicLong(-1L);
  }

  @Override
  public void destroy() {
    LogUtil.logCacheStats(InitiatorConfiguration.TRANSACTION_CACHE, transactionCache.stats());
    LogUtil.logCacheStats(InitiatorConfiguration.LAST_WTP_TID_CACHE, lastWtpTidCache.stats());
  }
}
