package com.volvo.tisp.vwtp.configuration;

import static com.volvo.tisp.vwtp.constants.PropertyKey.WTP_MAX_GROUP_SIZE;
import static com.volvo.tisp.vwtp.constants.PropertyKey.WTP_MTU;
import static com.volvo.tisp.vwtp.constants.PropertyKey.WTP_TIMER_GR;
import static com.volvo.tisp.vwtp.constants.PropertyKey.WTP_TIMER_R;
import static com.volvo.tisp.vwtp.util.LogUtil.ERROR_EMPTY;
import static com.volvo.tisp.vwtp.util.LogUtil.ERROR_NOT_POSITIVE;
import static com.volvo.tisp.vwtp.util.LogUtil.FORMAT_TABLE_OUTPUT;
import static java.util.Locale.US;

import com.volvo.tisp.vwtp.constants.WtpConstants;
import java.time.Duration;
import java.util.Arrays;
import java.util.Map;
import java.util.function.Predicate;
import java.util.regex.Pattern;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.lang.NonNull;
import org.springframework.util.Assert;

public class InitiatorProperties implements DisposableBean {
  private static final Logger logger = LoggerFactory.getLogger(InitiatorProperties.class);
  private static final Predicate<String> INTEGER_ARRAY_PREDICATE =
      Pattern.compile("\\s*\\d(?:\\s*,\\s\\d)*").asPredicate();

  private final int mtuSize;
  private final int version1MaxPduPayloadSize;
  private final int version2MaxPduPayloadSize;
  private final int version1MaxPayloadSize;
  private final int version2MaxPayloadSize;
  private final int groupSize;
  private final Duration[] timerR;
  private final Duration[] timerGR;

  @Autowired
  protected InitiatorProperties(
      @Value("${wtp.mtu:1024}") final int mtuSize,
      @Value("${wtp.group:8}") final int groupSize,
      @Value("${wtp.timer.r:5000,10000,15000,15000,15000}") final long[] timerR,
      @Value("${wtp.timer.gr:15000,15000,15000,15000}") final long[] timerGR) {
    this(mtuSize, groupSize, convertToDurationArray(timerR), convertToDurationArray(timerGR));
    Assert.isTrue(
        mtuSize > 8, String.format(US, FORMAT_TABLE_OUTPUT, WTP_MTU, "must be more than 8 bytes"));
    Assert.isTrue(
        groupSize > 0,
        String.format(US, FORMAT_TABLE_OUTPUT, WTP_MAX_GROUP_SIZE, ERROR_NOT_POSITIVE));
    Assert.isTrue(
        timerR != null && timerR.length > 0,
        String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_R, ERROR_EMPTY));
    Assert.isTrue(
        timerGR != null && timerGR.length > 0,
        String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_GR, ERROR_EMPTY));

    logProperties();
  }

  private InitiatorProperties(
      final int mtuSize, final int groupSize, final Duration[] timerR, final Duration[] timerGR) {
    this.mtuSize = mtuSize;
    this.groupSize = groupSize;
    this.timerR = timerR;
    this.timerGR = timerGR;
    version1MaxPduPayloadSize = mtuSize - 4;
    version2MaxPduPayloadSize = mtuSize - 8;
    version1MaxPayloadSize = version1MaxPduPayloadSize * WtpConstants.MAX_NUMBER_OF_SEGMENTS;
    version2MaxPayloadSize = version2MaxPduPayloadSize * WtpConstants.MAX_NUMBER_OF_SEGMENTS;
  }

  public int getVersion1MaxPduPayloadSize() {
    return version1MaxPduPayloadSize;
  }

  public int getVersion2MaxPduPayloadSize() {
    return version2MaxPduPayloadSize;
  }

  public int getVersion1MaxPayloadSize() {
    return version1MaxPayloadSize;
  }

  public int getVersion2MaxPayloadSize() {
    return version2MaxPayloadSize;
  }

  public int getGroupSize() {
    return groupSize;
  }

  public Duration[] getTimerR() {
    return timerR;
  }

  public Duration[] getTimerGR() {
    return timerGR;
  }

  private static Duration[] convertToDurationArray(@NonNull final String propertyValue) {
    return Stream.of(propertyValue.split(","))
        .map(String::trim)
        .mapToLong(Long::parseLong)
        .mapToObj(Duration::ofMillis)
        .toArray(Duration[]::new);
  }

  private static Duration[] convertToDurationArray(final long[] timerValues) {
    return Arrays.stream(timerValues).mapToObj(Duration::ofMillis).toArray(Duration[]::new);
  }

  public InitiatorProperties cloneWithMessageProperties(
      final Map<String, String> messageProperties) {
    final String timerRString = messageProperties.get(WTP_TIMER_R);
    final String timerGRString = messageProperties.get(WTP_TIMER_GR);
    if (timerRString != null || timerGRString != null) {
      Duration[] propertyTimerR = timerR;
      if (INTEGER_ARRAY_PREDICATE.test(timerRString)) {
        propertyTimerR = convertToDurationArray(timerRString);
      }
      Duration[] propertyTimerGR = timerGR;
      if (INTEGER_ARRAY_PREDICATE.test(timerGRString)) {
        propertyTimerGR = convertToDurationArray(timerGRString);
      }
      return new InitiatorProperties(mtuSize, groupSize, propertyTimerR, propertyTimerGR);
    }
    return this;
  }

  private void logProperties() {
    if (logger.isInfoEnabled()) {
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "VWTP INITATOR CONFIG", ""));
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, WTP_MAX_GROUP_SIZE, groupSize));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, WTP_MTU, mtuSize));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_R, Arrays.toString(timerR)));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_GR, Arrays.toString(timerGR)));
    }
  }

  @Override
  public void destroy() {
    logProperties();
  }
}
