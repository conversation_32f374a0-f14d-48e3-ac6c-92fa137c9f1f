package com.volvo.tisp.vwtp.responder;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.RemovalCause;
import com.volvo.tisp.vwtp.configuration.ResponderConfiguration;
import com.volvo.tisp.vwtp.configuration.ResponderProperties;
import com.volvo.tisp.vwtp.constants.TransactionStatus;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.util.LogUtil;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import java.net.URI;
import java.util.Iterator;
import java.util.function.Function;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

/** A component that exposes cache services with CRUD operations for responder caches */
@Component("responderCacheService")
public class CacheService implements DisposableBean {
  private static final Logger logger = LoggerFactory.getLogger(CacheService.class);
  private static final Counter transactionTimeoutCounter =
      Counter.builder(MetricNames.RESPONDER_INCOMING_ANOMALIES)
          .description("Transaction timed out while waiting for a segments")
          .tag(MetricNames.TYPE_TAG, "transaction-timeout")
          .register(Metrics.globalRegistry);

  private static final long TID_SPACE = 0x8000L;
  private static final long POSITIVE_WINDOW_SIZE = 0x4000L;
  private static final long NEGATIVE_WINDOW_SIZE = TID_SPACE - POSITIVE_WINDOW_SIZE;

  private final ResponderProperties responderProperties;
  private final Cache<URI, Cache<Long, Transaction>> initiatorsCache;
  private final Cache<String, Transaction> messageIdCache;

  public CacheService(
      final ResponderProperties responderProperties,
      final Cache<URI, Cache<Long, Transaction>> responderInitiatorsCache,
      final Cache<String, Transaction> responderMessageIdCache) {
    this.responderProperties = responderProperties;
    initiatorsCache = responderInitiatorsCache;
    messageIdCache = responderMessageIdCache;
  }

  /**
   * @param windowStart start position of the TID window
   * @param tid TID value to verify
   * @return true if TID is within window
   */
  private static boolean isWithinWindow(final long windowStart, final long tid) {
    if (tid >= windowStart) {
      return tid - windowStart < POSITIVE_WINDOW_SIZE;
    } else {
      return windowStart - tid > NEGATIVE_WINDOW_SIZE;
    }
  }

  /**
   * Invalidates a {@link Transaction} cache within window for specified address starting with
   * specified TID
   *
   * @param address initiator address
   * @param startingTid starting TID
   */
  public void responderInvalidateTransactions(@NonNull final URI address, final long startingTid) {
    final Cache<Long, Transaction> wtpTidCache = initiatorsCache.getIfPresent(address);
    if (wtpTidCache != null) {
      final Iterator<Transaction> cacheIterator = wtpTidCache.asMap().values().iterator();
      while (cacheIterator.hasNext()) {
        if (isWithinWindow(startingTid, cacheIterator.next().getWtpTid())) {
          cacheIterator.remove();
        }
      }
    }
  }

  /**
   * Creates a responder WTP TID cache
   *
   * @param address {@link URI}
   * @return {@link Cache}
   */
  @NonNull
  private Cache<Long, Transaction> responderCreateWtpTidCache(@NonNull final URI address) {
    logger.debug("#responderCreateWtpTidCache: address={}", address);
    return Caffeine.newBuilder()
        .recordStats()
        .executor(Runnable::run)
        .expireAfterAccess(responderProperties.getTransactionTimeoutInterval())
        .<Long, Transaction>removalListener(
            (key, transaction, cause) -> {
              logger.debug("Removing RESPONDER_TID_CACHE({}) entry: {}", cause, transaction);
              final TransactionStatus transactionStatus = transaction.markCompleted();
              if (transactionStatus != TransactionStatus.COMPLETED
                  && cause == RemovalCause.EXPIRED) {
                transactionTimeoutCounter.increment();
              }
              messageIdCache.invalidate(transaction.getMessageId());
            })
        .build();
  }

  /**
   * Retrieves a responder WTP TID cache
   *
   * @param address {@link URI}
   * @return {@link Cache}
   */
  @NonNull
  private Cache<Long, Transaction> responderReadWtpTidCache(@NonNull final URI address) {
    Cache<Long, Transaction> wtpTidCache =
        initiatorsCache.get(address, this::responderCreateWtpTidCache);
    if (wtpTidCache == null) {
      wtpTidCache = responderCreateWtpTidCache(address);
    }
    logger.debug("#responderReadWtpTidCache: address={}", address);
    return wtpTidCache;
  }

  /**
   * Creates a responder WTP transaction
   *
   * @param networkMessage instance of {@link NetworkMessageDto}
   * @param modelCommon instance of {@link ModelCommon}
   * @return {@link Transaction}
   */
  @NonNull
  public Transaction responderCreateWtpTransaction(
      @NonNull final NetworkMessageDto networkMessage, ModelCommon modelCommon) {
    Function<Long, Transaction> createWtpTransactionFunction =
        wtpTid -> {
          long vid = 0;
          if (modelCommon.getWtpVersion() == WtpVersion.VERSION_2) {
            vid = modelCommon.getVid();
          }
          Transaction transaction =
              new Transaction(
                  modelCommon.getWtpVersion(),
                  vid,
                  networkMessage.getAddress(),
                  ResponderUtils.createOrReuseMessageId(networkMessage.getMessageId()),
                  wtpTid,
                  networkMessage.getProperties());
          logger.debug("#responderCreateWtpTransaction: {}", "transaction");
          return transaction;
        };
    return responderReadWtpTidCache(networkMessage.getAddress())
        .get(modelCommon.getWtpTid(), createWtpTransactionFunction);
  }

  /**
   * Retrieves a responder WTP transaction
   *
   * @param address {@link URI}
   * @param wtpTid a WTP TID
   * @return {@link Transaction}
   */
  @Nullable
  public Transaction responderReadWtpTransaction(@NonNull final URI address, final long wtpTid) {
    final Transaction transaction = responderReadWtpTidCache(address).getIfPresent(wtpTid);
    logger.debug("#responderReadWtpTransaction: {}", transaction);
    return transaction;
  }

  /**
   * Create responder Message ID correlation
   *
   * @param transaction {@link Transaction}
   */
  public void responderCreateMessageIdCorrelation(@NonNull final Transaction transaction) {
    messageIdCache.put(transaction.getMessageId(), transaction);
    logger.debug("#responderCreateMessageIdCorrelation: {}", transaction);
  }

  /**
   * Retrieve responder Message ID correlation
   *
   * @param messageId {@link String}
   * @return {@link Transaction}
   */
  @Nullable
  public Transaction responderReadWtpTransaction(@NonNull final String messageId) {
    final Transaction transaction = messageIdCache.getIfPresent(messageId);
    logger.debug("#responderReadWtpTransaction: {}", transaction);
    return transaction;
  }

  @Override
  public void destroy() {
    LogUtil.logCacheStats(ResponderConfiguration.INITIATORS_CACHE, initiatorsCache.stats());
    LogUtil.logCacheStats(ResponderConfiguration.MESSAGE_ID_CACHE, messageIdCache.stats());
  }
}
