package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.InvokeStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.TrailerStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.InvokePdu;

public interface InvokePduBuilder<B>
    extends TrailerStep<B>, RidStep<B>, WtpTidStep<B>, InvokeStep<B> {
  InvokePdu buildInvokePdu();

  static <T extends InvokePduBuilder<T>> InvokePduBuilder<T> builder() {
    return new PduBuilder<>();
  }
}
