package com.volvo.tisp.vwtp.util;

import com.volvo.connectivity.metric.OperatorMetrics;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.model.AbortModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import java.net.URI;

/** Utility class that tracks initiators complex metrics */
public final class InitiatorMetrics {
  private static final OperatorMetrics operatorMetrics = OperatorMetrics.getInstance();

  /**
   * Increment a counter for a specific type of incoming PDU
   *
   * @param pdu instance of {@link ModelCommon}
   * @param address instance of {@link URI}
   */
  public static void incrementIncomingPdu(final ModelCommon pdu, URI address) {
    if (pdu.isRid()) {
      Counter.builder(MetricNames.INCOMING_PDU)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_INITIATOR)
          .tag(MetricNames.TYPE_TAG, pdu.getPduType().name())
          .tag(MetricNames.RETRANSMISSION_TAG, "true")
          .tags(operatorMetrics.getTags(address))
          .register(Metrics.globalRegistry)
          .increment();
    } else {
      Counter.builder(MetricNames.INCOMING_PDU)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_INITIATOR)
          .tag(MetricNames.TYPE_TAG, pdu.getPduType().name())
          .tags(operatorMetrics.getTags(address))
          .register(Metrics.globalRegistry)
          .increment();
    }
  }

  /**
   * Increment a counter for a specific type of outgoing PDU
   *
   * @param pduType instance of {@link PduType}
   * @param address instance of {@link URI}
   * @param isRid true if outgoing PDU is retransmitted
   */
  public static void incrementOutgoingPdu(final PduType pduType, final boolean isRid, URI address) {
    if (isRid) {
      Counter.builder(MetricNames.OUTGOING_PDU)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_INITIATOR)
          .tag(MetricNames.TYPE_TAG, pduType.name())
          .tag(MetricNames.RETRANSMISSION_TAG, "true")
          .tags(operatorMetrics.getTags(address))
          .register(Metrics.globalRegistry)
          .increment();
    } else {
      Counter.builder(MetricNames.OUTGOING_PDU)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_INITIATOR)
          .tag(MetricNames.TYPE_TAG, pduType.name())
          .tags(operatorMetrics.getTags(address))
          .register(Metrics.globalRegistry)
          .increment();
    }
  }

  /**
   * Increment a counter for a specific type of incoming abort PDU
   *
   * @param abort instance of {@link AbortModel}
   */
  public static void incrementIncomingAbort(final AbortModel abort) {
    Counter.builder(MetricNames.INITIATOR_INCOMING_ABORTS)
        .tag(MetricNames.TYPE_TAG, abort.getAbortCode().getMetricName())
        .register(Metrics.globalRegistry)
        .increment();
  }

  /**
   * Increment a counter for a specific type of outgoing abort PDU
   *
   * @param abortCode instance of {@link AbortCode}
   */
  public static void incrementOutgoingAbort(final AbortCode abortCode) {
    Counter.builder(MetricNames.INITIATOR_OUTGOING_ABORTS)
        .tag(MetricNames.TYPE_TAG, abortCode.getMetricName())
        .register(Metrics.globalRegistry)
        .increment();
  }

  /**
   * Increment a counter for returned user status message with abort status
   *
   * @param abortCode instance of {@link AbortCode}
   */
  public static void incrementOutgoingAbortStatus(final AbortCode abortCode) {
    Counter.builder(MetricNames.INITIATOR_OUTGOING_STATUSES)
        .tag(MetricNames.TYPE_TAG, abortCode.getMetricName())
        .register(Metrics.globalRegistry)
        .increment();
  }

  private InitiatorMetrics() {
    throw new IllegalStateException();
  }
}
