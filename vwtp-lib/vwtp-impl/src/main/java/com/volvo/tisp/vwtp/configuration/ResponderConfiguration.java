package com.volvo.tisp.vwtp.configuration;

import static com.volvo.tisp.vwtp.util.LogUtil.FORMAT_CACHE_DISPOSE_MESSAGE;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.volvo.tisp.vwtp.responder.CacheService;
import com.volvo.tisp.vwtp.responder.NetworkMessageFlow;
import com.volvo.tisp.vwtp.responder.Transaction;
import com.volvo.tisp.vwtp.responder.UserStatusFlow;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import java.net.URI;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/** Spring configuration */
@Configuration
@Import({
  ResponderProperties.class,
  CacheService.class,
  NetworkMessageFlow.class,
  UserStatusFlow.class
})
public class ResponderConfiguration {
  private static final Logger logger = LoggerFactory.getLogger(ResponderConfiguration.class);

  // Caches Names
  public static final String INITIATORS_CACHE = "RESPONDER_INITIATORS_CACHE";
  public static final String MESSAGE_ID_CACHE = "RESPONDER_MESSAGE_ID_CACHE";

  @Bean(destroyMethod = "invalidateAll")
  protected Cache<URI, Cache<Long, Transaction>> responderInitiatorsCache(
      final ResponderProperties responderProperties) {
    final Cache<URI, Cache<Long, Transaction>> cache =
        Caffeine.newBuilder()
            .recordStats()
            .executor(Runnable::run)
            .expireAfterAccess(
                responderProperties
                    .getTransactionTimeoutInterval()
                    .plus(responderProperties.getNackDelayInterval()))
            .<URI, Cache<Long, Transaction>>removalListener(
                (key, wtpTidCache, cause) -> {
                  logger.debug(
                      FORMAT_CACHE_DISPOSE_MESSAGE, INITIATORS_CACHE, key, "WTP_TID_CACHE", cause);
                  wtpTidCache.invalidateAll();
                  wtpTidCache.cleanUp();
                })
            .build();
    CaffeineCacheMetrics.monitor(
        Metrics.globalRegistry, cache, MetricNames.RESPONDER_INITIATORS_CACHE);
    return cache;
  }

  @Bean(destroyMethod = "invalidateAll")
  protected Cache<String, Transaction> responderMessageIdCache() {
    final Cache<String, Transaction> cache =
        Caffeine.newBuilder()
            .recordStats()
            .executor(Runnable::run)
            .removalListener(
                (key, value, cause) ->
                    logger.debug(FORMAT_CACHE_DISPOSE_MESSAGE, MESSAGE_ID_CACHE, key, value, cause))
            .build();
    CaffeineCacheMetrics.monitor(
        Metrics.globalRegistry, cache, MetricNames.RESPONDER_MESSAGE_ID_CACHE);
    return cache;
  }
}
