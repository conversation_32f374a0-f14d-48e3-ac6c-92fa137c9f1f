package com.volvo.tisp.vwtp.util;

/** Utility class that tracks names of metrics and metric tag names */
public final class MetricNames {

  public static final String INCOMING_PDU = "vwtp.incoming.pdu";
  public static final String INCOMING_PDU_SIZES = "vwtp.incoming.pdu.sizes";
  public static final String OUTGOING_PDU = "vwtp.outgoing.pdu";
  public static final String OUTGOING_PDU_SIZES = "vwtp.outgoing.pdu.sizes";

  public static final String INITIATOR_INCOMING_ABORTS = "vwtp.initiator.incoming.aborts";
  public static final String INITIATOR_INCOMING_ANOMALIES = "vwtp.initiator.incoming.anomalies";
  public static final String INITIATOR_INCOMING_TID_VERIFICATIONS =
      "vwtp.initiator.incoming.tid-verifications";
  public static final String INITIATOR_INCOMING_MESSAGES = "vwtp.initiator.incoming.user-messages";
  public static final String INITIATOR_INCOMING_MESSAGES_SEGMENTS =
      "vwtp.initiator.incoming.user-messages.segments";
  public static final String INITIATOR_INCOMING_MESSAGES_PAYLOADS =
      "vwtp.initiator.incoming.user-messages.payloads";
  public static final String INITIATOR_INCOMING_MESSAGES_SEGMENTED =
      "vwtp.initiator.incoming.user-messages.segmented";

  public static final String INITIATOR_OUTGOING_ABORTS = "vwtp.initiator.outgoing.aborts";
  public static final String INITIATOR_OUTGOING_ANOMALIES = "vwtp.initiator.outgoing.anomalies";
  public static final String INITIATOR_OUTGOING_TID_NEW = "vwtp.initiator.outgoing.tid-new";
  public static final String INITIATOR_OUTGOING_STATUSES = "vwtp.initiator.outgoing.user-statuses";

  public static final String INITIATOR_ROUNDTRIP_DURATIONS = "vwtp.initiator.roundtrip.durations";
  public static final String INITIATOR_TRANSACTION_DURATIONS =
      "vwtp.initiator.transaction.durations";
  public static final String INITIATOR_TRANSACTIONS_CACHE = "vwtp.initiator.transactions";
  public static final String INITIATOR_LAST_TID_CACHE = "vwtp.initiator.last-tid";

  public static final String RESPONDER_INCOMING_ABORTS = "vwtp.responder.incoming.aborts";
  public static final String RESPONDER_INCOMING_ANOMALIES = "vwtp.responder.incoming.anomalies";
  public static final String RESPONDER_INCOMING_TID_NEW = "vwtp.responder.incoming.tid-new";
  public static final String RESPONDER_INCOMING_STATUSES = "vwtp.responder.incoming.user-statuses";

  public static final String RESPONDER_OUTGOING_ABORTS = "vwtp.responder.outgoing.aborts";
  public static final String RESPONDER_OUTGOING_ANOMALIES = "vwtp.responder.outgoing.anomalies";
  public static final String RESPONDER_OUTGOING_MESSAGES = "vwtp.responder.outgoing.user-messages";
  public static final String RESPONDER_OUTGOING_MESSAGES_SEGMENTS =
      "vwtp.responder.outgoing.user-messages.segments";
  public static final String RESPONDER_OUTGOING_MESSAGES_PAYLOADS =
      "vwtp.responder.outgoing.user-messages.payloads";
  public static final String RESPONDER_OUTGOING_MESSAGES_SEGMENTED =
      "vwtp.responder.outgoing.user-messages.segmented";

  public static final String RESPONDER_ACTIVE_TRANSACTIONS = "vwtp.responder.active-transactions";
  public static final String RESPONDER_USERWAIT_DURATIONS = "vwtp.responder.userwait-durations";
  public static final String RESPONDER_INITIATORS_CACHE = "vwtp.responder.initiators";
  public static final String RESPONDER_MESSAGE_ID_CACHE = "vwtp.responder.message-id";

  public static final String TYPE_TAG = "type";
  public static final String PROVIDER_TAG = "provider";
  public static final String STATUS_TAG = "status";
  public static final String RETRANSMISSION_TAG = "retransmission";

  public static final String PROVIDER_TAG_RESPONDER = "responder";
  public static final String PROVIDER_TAG_INITIATOR = "initiator";

  private MetricNames() {
    throw new IllegalStateException();
  }
}
