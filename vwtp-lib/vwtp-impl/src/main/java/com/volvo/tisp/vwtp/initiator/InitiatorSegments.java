package com.volvo.tisp.vwtp.initiator;

import java.util.AbstractMap.SimpleEntry;
import java.util.Map;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class InitiatorSegments {
  private static final Logger logger = LoggerFactory.getLogger(InitiatorSegments.class);

  private final Map<Integer, Segment> segments;
  private final int maxSegmentsInGroup;
  private final int minPsnValue;
  private final int maxPsnValue;

  public InitiatorSegments(final Map<Integer, Segment> segments, final int maxSegmentsInGroup) {
    logger.debug(
        "Initiator - creating segments: {}, maxSegmentsInGroup: {}", segments, maxSegmentsInGroup);

    this.segments = segments;
    this.maxSegmentsInGroup = maxSegmentsInGroup;
    minPsnValue = segments.keySet().stream().min(Integer::compareTo).orElseThrow();
    maxPsnValue = segments.keySet().stream().max(Integer::compareTo).orElseThrow();
  }

  public Segment getFirstSegment() {
    final Segment segment = segments.get(minPsnValue);

    logger.debug("Initiator - getting first segment: {}", segment);

    return segment;
  }

  public Segment getLastSegment() {
    final Segment segment = segments.get(maxPsnValue);

    logger.debug("Initiator - getting last segment: {}", segment);

    return segment;
  }

  public Map<Integer, Segment> getAllSegments() {
    logger.debug("Initiator - getting all segments.");
    return segments;
  }

  public Map<Integer, Segment> getSegments(final int... psns) {
    final Map<Integer, Segment> segmentsMap = getPsnSegmentsMap(IntStream.of(psns).boxed());

    logger.debug("Initiator - getting segments: {}", segmentsMap);

    return segmentsMap;
  }

  public Segment getSegment(final int psn) {
    final Segment segment = segments.get(psn);
    logger.debug("Initiator - getting segments: {}", segment);

    return segment;
  }

  public Map<Integer, Segment> getGroup(final int fromPsnInclusive, final int toPsnInclusive) {
    final Map<Integer, Segment> segmentsMap =
        getPsnSegmentsMap(IntStream.rangeClosed(fromPsnInclusive, toPsnInclusive).boxed());

    logger.debug("Initiator - getting group: {}", segmentsMap);

    return segmentsMap;
  }

  public Map<Integer, Segment> getGroup(final int fromPsnExclusive) {
    final int fromPsnInclusive = getFromPsnInclusive(fromPsnExclusive);
    final int toPsnInclusive = getToPsnInclusive(fromPsnExclusive);
    final Map<Integer, Segment> segmentsMap = getGroup(fromPsnInclusive, toPsnInclusive);

    logger.debug("Initiator - getting group: {}", segmentsMap);

    return segmentsMap;
  }

  public int getMaxSegmentsInGroup() {
    logger.debug("Initiator - getting maxSegmentsInGroup: {}", maxSegmentsInGroup);

    return maxSegmentsInGroup;
  }

  public int getMinPsnValue() {
    return minPsnValue;
  }

  public int getMaxPsnValue() {
    return maxPsnValue;
  }

  public boolean isLastGroup(final int currentExpectedResponderPsn) {
    final boolean isLastGroup = currentExpectedResponderPsn == maxPsnValue;

    logger.debug("Initiator - getting isLastGroup: {}", isLastGroup);

    return isLastGroup;
  }

  private Map<Integer, Segment> getPsnSegmentsMap(final Stream<Integer> psnsStream) {
    return psnsStream
        .filter(psn -> segments.get(psn) != null)
        .map(
            psn -> {
              final Segment segment = segments.get(psn);
              return new SimpleEntry<>(segment.getPsn(), segment);
            })
        .collect(Collectors.toMap(Entry::getKey, Entry::getValue));
  }

  private static int getFromPsnInclusive(final int fromPsnExclusive) {
    return fromPsnExclusive + 1;
  }

  private int getToPsnInclusive(final int fromPsnExclusive) {
    final int max = fromPsnExclusive + maxSegmentsInGroup;

    return max <= maxPsnValue
        ? max
        : maxPsnValue; // Never use a to PSN greater than the greatest PSN value
  }
}
