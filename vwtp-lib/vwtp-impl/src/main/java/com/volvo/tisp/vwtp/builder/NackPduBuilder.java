package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.MissingPsnStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidReverseDirectionStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.NackPdu;

public interface NackPduBuilder<B>
    extends RidStep<B>, WtpTidStep<B>, WtpTidReverseDirectionStep<B>, MissingPsnStep<B> {
  NackPdu buildNackPdu();

  static <T extends NackPduBuilder<T>> NackPduBuilder<T> builder() {
    return new PduBuilder<>();
  }
}
