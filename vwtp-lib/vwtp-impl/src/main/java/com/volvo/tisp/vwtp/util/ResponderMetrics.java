package com.volvo.tisp.vwtp.util;

import com.volvo.connectivity.metric.OperatorMetrics;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.model.AbortModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import java.net.URI;

/** Utility class that tracks responders complex metrics */
public final class ResponderMetrics {
  private static final OperatorMetrics operatorMetrics = OperatorMetrics.getInstance();

  /**
   * Increment a counter for packet that could not be decoded to any known PDU types.
   *
   * @param address instance of {@link URI}
   */
  public static void incrementIncomingBadPdu(URI address) {
    Counter.builder(MetricNames.INCOMING_PDU)
        .tag(MetricNames.TYPE_TAG, "BAD-PDU")
        .tags(operatorMetrics.getTags(address))
        .register(Metrics.globalRegistry)
        .increment();
  }

  /**
   * Increment a counter for a specific type of incoming PDU
   *
   * @param pdu instance of {@link ModelCommon}
   * @param address instance of {@link URI}
   */
  public static void incrementIncomingPdu(final ModelCommon pdu, URI address) {
    if (pdu.isRid()) {
      Counter.builder(MetricNames.INCOMING_PDU)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_RESPONDER)
          .tag(MetricNames.TYPE_TAG, pdu.getPduType().name())
          .tag(MetricNames.RETRANSMISSION_TAG, "true")
          .tags(operatorMetrics.getTags(address))
          .register(Metrics.globalRegistry)
          .increment();
    } else {
      Counter.builder(MetricNames.INCOMING_PDU)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_RESPONDER)
          .tag(MetricNames.TYPE_TAG, pdu.getPduType().name())
          .tags(operatorMetrics.getTags(address))
          .register(Metrics.globalRegistry)
          .increment();
    }
  }

  /**
   * Increment a counter for a specific type of outgoing PDU
   *
   * @param pduType instance of {@link PduType}
   * @param address instance of {@link URI}
   */
  public static void incrementOutgoingPdu(final PduType pduType, URI address) {
    Counter.builder(MetricNames.OUTGOING_PDU)
        .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_RESPONDER)
        .tag(MetricNames.TYPE_TAG, pduType.name())
        .tags(operatorMetrics.getTags(address))
        .register(Metrics.globalRegistry)
        .increment();
  }

  /**
   * Increment a counter for a specific type of incoming abort PDU
   *
   * @param abort instance of {@link AbortModel}
   */
  public static void incrementIncomingAbort(final AbortModel abort) {
    Counter.builder(MetricNames.RESPONDER_INCOMING_ABORTS)
        .tag(MetricNames.TYPE_TAG, abort.getAbortCode().getMetricName())
        .register(Metrics.globalRegistry)
        .increment();
  }

  /**
   * Increment a counter for a specific type of outgoing abort PDU
   *
   * @param abortCode instance of {@link AbortCode}
   */
  public static void incrementOutgoingAbort(final AbortCode abortCode) {
    Counter.builder(MetricNames.RESPONDER_OUTGOING_ABORTS)
        .tag(MetricNames.TYPE_TAG, abortCode.getMetricName())
        .register(Metrics.globalRegistry)
        .increment();
  }

  /**
   * Increment a counter for incoming user status message
   *
   * @param userStatus instance of {@link UserStatusDto}
   */
  public static void incrementIncomingUserStatus(final UserStatusDto userStatus) {
    if (userStatus.isDelivered()) {
      Counter.builder(MetricNames.RESPONDER_INCOMING_STATUSES)
          .tag(MetricNames.TYPE_TAG, "delivered")
          .register(Metrics.globalRegistry)
          .increment();
    } else {
      Counter.builder(MetricNames.RESPONDER_INCOMING_STATUSES)
          .tag(MetricNames.TYPE_TAG, userStatus.getAbortCode().getMetricName())
          .register(Metrics.globalRegistry)
          .increment();
    }
  }

  private ResponderMetrics() {
    throw new IllegalStateException();
  }
}
