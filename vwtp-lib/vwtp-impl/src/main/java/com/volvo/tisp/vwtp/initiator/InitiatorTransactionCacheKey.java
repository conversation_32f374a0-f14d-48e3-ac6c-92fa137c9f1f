package com.volvo.tisp.vwtp.initiator;

import com.google.common.base.Preconditions;
import java.net.URI;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

public final class InitiatorTransactionCacheKey {
  private final URI destination;
  private final long wtpTid;

  public static InitiatorTransactionCacheKeyBuilder builder() {
    return new InitiatorTransactionCacheKeyBuilder();
  }

  private InitiatorTransactionCacheKey(final InitiatorTransactionCacheKeyBuilder builder) {
    Preconditions.checkNotNull(builder, "builder must not be null");
    Preconditions.checkNotNull(builder.destination, "builder destination must not be null");
    destination = builder.destination;
    wtpTid = builder.wtpTid;
  }

  public URI getDestination() {
    return destination;
  }

  public long getWtpTid() {
    return wtpTid;
  }

  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }

    if (o instanceof InitiatorTransactionCacheKey that) {
      return new EqualsBuilder()
          .append(wtpTid, that.wtpTid)
          .append(destination, that.destination)
          .isEquals();
    } else {
      return false;
    }
  }

  @Override
  public int hashCode() {
    return new HashCodeBuilder(17, 37).append(destination).append(wtpTid).toHashCode();
  }

  @Override
  public String toString() {
    return "InitiatorTransactionCacheKey{"
        + "destination="
        + destination
        + ", wtpTid="
        + wtpTid
        + '}';
  }

  public static class InitiatorTransactionCacheKeyBuilder {
    private URI destination;
    private long wtpTid;

    public InitiatorTransactionCacheKeyBuilder withDestination(final URI destination) {
      this.destination = destination;
      return this;
    }

    public InitiatorTransactionCacheKeyBuilder withWtpTid(final long wtpTid) {
      this.wtpTid = wtpTid;
      return this;
    }

    public InitiatorTransactionCacheKey build() {
      return new InitiatorTransactionCacheKey(this);
    }
  }
}
