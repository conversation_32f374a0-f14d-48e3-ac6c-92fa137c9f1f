package com.volvo.tisp.vwtp.model;

import com.volvo.tisp.vwtp.constants.PduType;
import java.util.Arrays;

/** Segmented invoke protocol data unit model */
public class SegInvokeModel extends InvokeCommon {

  private SegInvokeModel(final Builder builder) {
    super(builder);
  }

  /**
   * @return instance of an {@link Builder}
   */
  public static Builder builder() {
    return new Builder();
  }

  @Override
  public PduType getPduType() {
    return PduType.SEGINVOKE;
  }

  @Override
  public String toString() {
    return "SegInvokeModel{"
        + "gtr="
        + isGtr()
        + ", ttr="
        + isTtr()
        + ", rid="
        + isRid()
        + ", psn="
        + getPsn()
        + ", wtpTid="
        + getWtpTid()
        + ", wtpVersion="
        + getWtpVersion()
        + ", vid="
        + getVid()
        + ", tpiArray="
        + Arrays.toString(getTpiArray())
        + ", payload=["
        + HEX_ENCODER.encode(getPayload())
        + "], hashCode="
        + Integer.toHexString(hashCode())
        + '}';
  }

  /** Builder for segmented invoke PDU models */
  public static class Builder extends InvokeCommon.Builder<Builder, SegInvokeModel> {
    private Builder() {}

    @Override
    protected Builder getSelf() {
      return this;
    }

    @Override
    public SegInvokeModel build() {
      return new SegInvokeModel(this);
    }
  }
}
