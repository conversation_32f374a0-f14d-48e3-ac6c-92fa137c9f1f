package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.MissingPsnStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.VidStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidReverseDirectionStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.NackPdu2;

public interface NackPdu2Builder<B>
    extends RidStep<B>,
        WtpTidStep<B>,
        WtpTidReverseDirectionStep<B>,
        MissingPsnStep<B>,
        VidStep<B> {
  NackPdu2 buildNackPdu2();

  static <T extends NackPdu2Builder<T>> NackPdu2Builder<T> builder() {
    return new PduBuilder<>();
  }
}
