package com.volvo.tisp.vwtp.responder;

import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.configuration.ResponderProperties;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.TransactionStatus;
import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.model.AbortModel;
import com.volvo.tisp.vwtp.model.InvokeCommon;
import com.volvo.tisp.vwtp.model.InvokeModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.util.MetricNames;
import com.volvo.tisp.vwtp.util.ResponderMetrics;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Timer;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.GroupedFlux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitFailureHandler;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.core.scheduler.Scheduler;
import reactor.util.Logger;
import reactor.util.Loggers;
import reactor.util.context.Context;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

/** Implementation of WTP responder */
@Component("responderNetworkMessageFlow")
public class NetworkMessageFlow implements FlowComposer<NetworkMessageDto, Void> {
  private static final Logger logger = Loggers.getLogger(NetworkMessageFlow.class);
  private static final Counter tidNewCount =
      Counter.builder(MetricNames.RESPONDER_INCOMING_TID_NEW)
          .description("Count of incoming INVOKE's with TidNew flag")
          .register(Metrics.globalRegistry);
  private static final Counter incomingPduSizes =
      Counter.builder(MetricNames.INCOMING_PDU_SIZES)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_RESPONDER)
          .register(Metrics.globalRegistry);
  private static final Timer userwaitDurations =
      Timer.builder(MetricNames.RESPONDER_USERWAIT_DURATIONS)
          .description("Duration of transaction in USERWAIT status")
          .register(Metrics.globalRegistry);
  private static final String NETWORK_SINK_CONTEXT_KEY =
      "RESPONDER-NETWORK-SINK-" + NetworkMessageFlow.class.hashCode();

  private final FlowComposer<UserMessageDto, Void> outgoingUserMessageFlow;
  private final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow;
  private final CacheService cacheService;
  private final ResponderProperties configuration;

  /**
   * @param outgoingUserMessageFlow instance of {@link FlowComposer} &lt;{@link UserMessageDto},
   *     {@link Void}&gt;
   * @param outgoingNetworkMessageFlow instance of {@link FlowComposer} &lt;{@link
   *     NetworkMessageDto}, {@link Void}&gt;
   * @param cacheService instance of {@link CacheService}
   * @param configuration instance of {@link ResponderProperties}
   */
  protected NetworkMessageFlow(
      @Qualifier("outgoingResponderUserMessageFlow")
          final FlowComposer<UserMessageDto, Void> outgoingUserMessageFlow,
      @Qualifier("outgoingResponderNetworkMessageFlow")
          final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow,
      final CacheService cacheService,
      final ResponderProperties configuration) {
    this.outgoingUserMessageFlow = outgoingUserMessageFlow;
    this.outgoingNetworkMessageFlow = outgoingNetworkMessageFlow;
    this.cacheService = cacheService;
    this.configuration = configuration;
  }

  @Override
  public Publisher<Void> apply(final Flux<NetworkMessageDto> flux) {
    logger.info("Composing reactive flow");
    final Sinks.Many<NetworkMessageDto> networkSink =
        Sinks.unsafe().many().unicast().onBackpressureError();

    return Flux.merge(
        networkSink.asFlux().transform(outgoingNetworkMessageFlow),
        flux.handle(NetworkMessageToModelCommonConverter::convert)
            .doOnNext(this::logMetrics)
            .map(this::retrieveTransaction)
            .filter(this::ifValidPduTypeAndTransactionCombination)
            .map(this::invalidateTransactionsIfTidNewReceived)
            .flatMap(this::ifGlobalActiveTransactionCountIsNotExceeded)
            .map(this::createNewTransactionIfNull)
            .groupBy(this::groupByPduType)
            .flatMap(this::routeByPduType)
            .flatMap(
                this
                    ::ifPermissionForMessageAssemblyGrantedAndSheduleUserAcknowledgmentTimerOrSendAcknowledgment)
            .map(ResponderUtils::createUserMessage)
            .transform(this::completeFlowServiceUponTermination)
            .contextWrite(Context.of(NETWORK_SINK_CONTEXT_KEY, networkSink))
            .transform(outgoingUserMessageFlow));
  }

  /**
   * Completes the sub flows inside {@link Flux} {@link Context} when main flow completes
   *
   * @param flux {@link Flux}&lt;{@link UserMessageDto}&gt;
   * @return instance of {@link Publisher}&lt;{@link UserMessageDto}&gt;
   */
  private Publisher<UserMessageDto> completeFlowServiceUponTermination(
      final Flux<UserMessageDto> flux) {
    return Flux.deferContextual(
        context ->
            flux.doOnComplete(
                () -> {
                  final Sinks.Many<NetworkMessageDto> networkSink =
                      context.get(NETWORK_SINK_CONTEXT_KEY);
                  networkSink.emitComplete(EmitFailureHandler.FAIL_FAST);
                }));
  }

  /**
   * Logs received protocol data unit metrics
   *
   * @param networkMessageAndModel {@link Tuple2} of {@link NetworkMessageDto} and {@link
   *     ModelCommon}
   */
  private void logMetrics(final Tuple2<NetworkMessageDto, ModelCommon> networkMessageAndModel) {
    final NetworkMessageDto networkMessage = networkMessageAndModel.getT1();
    final ModelCommon modelCommon = networkMessageAndModel.getT2();
    logger.debug("Responder - Entering flow ({}): {}", networkMessage.getMessageId(), modelCommon);
    incomingPduSizes.increment(networkMessage.getPayload().length);
    ResponderMetrics.incrementIncomingPdu(modelCommon, networkMessage.getAddress());
  }

  /**
   * Retrieves transaction from the cache
   *
   * @param networkMessageAndModel {@link Tuple2} of {@link NetworkMessageDto} and {@link
   *     ModelCommon}
   * @return {@link Tuple3} of {@link NetworkMessageDto}, {@link ModelCommon} and {@link
   *     Optional}<{@link Transaction}>
   */
  private Tuple3<NetworkMessageDto, ModelCommon, Optional<Transaction>> retrieveTransaction(
      final Tuple2<NetworkMessageDto, ModelCommon> networkMessageAndModel) {
    final NetworkMessageDto networkMessage = networkMessageAndModel.getT1();
    final ModelCommon modelCommon = networkMessageAndModel.getT2();
    final Transaction transaction =
        cacheService.responderReadWtpTransaction(
            networkMessage.getAddress(), modelCommon.getWtpTid());
    return Tuples.of(networkMessage, modelCommon, Optional.ofNullable(transaction));
  }

  /**
   * Filters out invalid PDU and transaction combinations early in the flow
   *
   * @param networkMessageAndModelAndTransaction {@link Tuple3} of {@link NetworkMessageDto}, {@link
   *     ModelCommon} and {@link Optional}<{@link Transaction}>
   * @return true if combination is valid
   */
  private boolean ifValidPduTypeAndTransactionCombination(
      final Tuple3<NetworkMessageDto, ModelCommon, Optional<Transaction>>
          networkMessageAndModelAndTransaction) {
    final ModelCommon modelCommon = networkMessageAndModelAndTransaction.getT2();
    final Optional<Transaction> optionalTransaction = networkMessageAndModelAndTransaction.getT3();

    final boolean ifInvokesWithNoTransaction =
        !optionalTransaction.isPresent()
            && (modelCommon.getPduType() == PduType.INVOKE
                || modelCommon.getPduType() == PduType.SEGINVOKE);

    final boolean validPduTypeAndTransactionCombination =
        optionalTransaction.isPresent() || ifInvokesWithNoTransaction;
    if (logger.isDebugEnabled() && !validPduTypeAndTransactionCombination) {
      logger.debug("Filtered out on #ifValidPduTypeAndTransactionCombination");
    }
    return validPduTypeAndTransactionCombination;
  }

  /**
   * If Invoke PDU has TIDnew flag set, clean all the transaction cache within configured window
   * starting with WTP TID provided by Invoke PDU
   *
   * @param networkMessageAndModelAndTransaction {@link Tuple3} of {@link NetworkMessageDto}, {@link
   *     ModelCommon} and {@link Optional}<{@link Transaction}>
   * @return {@link Tuple3} of {@link NetworkMessageDto}, {@link ModelCommon} and {@link
   *     Optional}<{@link Transaction}>
   */
  private Tuple3<NetworkMessageDto, ModelCommon, Optional<Transaction>>
      invalidateTransactionsIfTidNewReceived(
          final Tuple3<NetworkMessageDto, ModelCommon, Optional<Transaction>>
              networkMessageAndModelAndTransaction) {
    final NetworkMessageDto networkMessage = networkMessageAndModelAndTransaction.getT1();
    final ModelCommon modelCommon = networkMessageAndModelAndTransaction.getT2();
    if (modelCommon.getPduType() == PduType.INVOKE && ((InvokeModel) modelCommon).isWtpTidNew()) {
      cacheService.responderInvalidateTransactions(
          networkMessage.getAddress(), modelCommon.getWtpTid());
      tidNewCount.increment();
      return Tuples.of(networkMessage, modelCommon, Optional.empty());
    }
    return networkMessageAndModelAndTransaction;
  }

  /**
   * Filters out and send Abort if current active transaction count is more than
   * "wtp.responder-active-transactions"
   *
   * @param networkMessageAndModelAndTransaction {@link Tuple3} of {@link NetworkMessageDto}, {@link
   *     ModelCommon} and {@link Optional}<{@link Transaction}>
   * @return true if current active transaction count is within limits
   */
  private Publisher<Tuple3<NetworkMessageDto, ModelCommon, Optional<Transaction>>>
      ifGlobalActiveTransactionCountIsNotExceeded(
          final Tuple3<NetworkMessageDto, ModelCommon, Optional<Transaction>>
              networkMessageAndModelAndTransaction) {
    return Mono.deferContextual(
        context -> {
          final NetworkMessageDto networkMessage = networkMessageAndModelAndTransaction.getT1();
          final ModelCommon modelCommon = networkMessageAndModelAndTransaction.getT2();
          final Optional<Transaction> optionalTransaction =
              networkMessageAndModelAndTransaction.getT3();

          if (optionalTransaction.isEmpty()
              && Transaction.getInprogressCounter() > configuration.getMaxActiveTransactions()) {
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            final NetworkMessageDto abortMessage =
                ResponderUtils.createAbort(
                    networkMessage, modelCommon, AbortCode.TCE_PROVIDER_CAPACITY_EXCEEDED);
            final EmitResult emitResult = networkSink.tryEmitNext(abortMessage);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::ifGlobalActiveTransactionCountIsNotExceeded",
                  emitResult,
                  abortMessage);
            }
            logger.debug("Filtered out on #ifGlobalActiveTransactionCountIsNotExceeded");
            return Mono.empty();
          }
          return Mono.just(networkMessageAndModelAndTransaction);
        });
  }

  /**
   * Creates new {@link Transaction} if {@link Optional}<{@link Transaction}> is empty
   *
   * @param networkMessageAndModelAndTransaction {@link Tuple3} of {@link NetworkMessageDto}, {@link
   *     ModelCommon} and {@link Optional}<{@link Transaction}>
   * @return {@link Tuple2} of {@link ModelCommon} and {@link Transaction}
   */
  private Tuple2<ModelCommon, Transaction> createNewTransactionIfNull(
      final Tuple3<NetworkMessageDto, ModelCommon, Optional<Transaction>>
          networkMessageAndModelAndTransaction) {
    final ModelCommon modelCommon = networkMessageAndModelAndTransaction.getT2();
    final Optional<Transaction> optionalTransaction = networkMessageAndModelAndTransaction.getT3();

    if (!optionalTransaction.isPresent()) {
      final NetworkMessageDto networkMessage = networkMessageAndModelAndTransaction.getT1();
      Transaction transaction =
          cacheService.responderCreateWtpTransaction(networkMessage, modelCommon);
      return Tuples.of(modelCommon, transaction);
    }
    return Tuples.of(modelCommon, optionalTransaction.get());
  }

  /**
   * Operator for splitting flow by {@link PduType}
   *
   * @param modelAndTransactionTuple {@link Tuple2} of {@link ModelCommon} and {@link Transaction}
   * @return {@link PduType}
   */
  private PduType groupByPduType(final Tuple2<ModelCommon, Transaction> modelAndTransactionTuple) {
    return modelAndTransactionTuple.getT1().getPduType();
  }

  /**
   * Predicate operator that returns true if permission for message assembly has been granted, also
   * ensures that there are no duplicate {@link UserMessageDto}'s sent to the WTP user by filtering
   * out attempts to do so
   *
   * <p>Also if user acknowledgment is required - schedules user acknowledgment, otherwise sends
   * back AckPdu if {@link TransactionClass#CLASS_1}
   *
   * @param transaction {@link Transaction}
   * @return {@link Transaction}
   */
  private Publisher<Transaction>
      ifPermissionForMessageAssemblyGrantedAndSheduleUserAcknowledgmentTimerOrSendAcknowledgment(
          final Transaction transaction) {
    return Mono.deferContextual(
        context -> {
          final TransactionStatus previousStatus;
          if (transaction.getTransactionClass() == TransactionClass.CLASS_1) {
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            final Scheduler scheduler = context.get(Scheduler.class);
            if (transaction.isUserAckRequired()) {
              cacheService.responderCreateMessageIdCorrelation(transaction);
              previousStatus =
                  scheduleUserAcknowledgementTimer(networkSink, scheduler, transaction);
            } else {
              previousStatus = transaction.markCompleted();
              final NetworkMessageDto networkMessage =
                  ResponderUtils.createAcknowledgement(transaction);
              final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::ifPermissionForMessageAssemblyGrantedAndSheduleUserAcknowledgmentTimerOrSendAcknowledgment",
                    emitResult,
                    networkMessage);
              }
            }
          } else {
            previousStatus = transaction.markCompleted();
          }
          if (previousStatus == TransactionStatus.INPROGRESS) {
            return Mono.just(transaction);
          }
          logger.debug(
              "Filtered out on #ifPermissionForMessageAssemblyGrantedAndSheduleUserAcknowledgmentTimerOrSendAcknowledgment {}",
              transaction);
          return Mono.empty();
        });
  }

  /**
   * Wires up flow based on {@link PduType}
   *
   * @param groupedFlux {@link GroupedFlux} grouped by {@link PduType}
   * @return {@link Flux} of {@link Transaction}
   */
  private Flux<Transaction> routeByPduType(
      final GroupedFlux<PduType, Tuple2<ModelCommon, Transaction>> groupedFlux) {
    switch (groupedFlux.key()) {
      case INVOKE, SEGINVOKE:
        return groupedFlux
            .map(pair -> Tuples.of((InvokeCommon) pair.getT1(), pair.getT2()))
            .flatMap(this::ifInvokeCommonRequiresProcessing)
            .map(this::processInvoke)
            .flatMap(this::ifValidTransactionClassOtherwiseSendAbort)
            .filter(this::ifNotDuplicateOrTrailerRetry)
            .flatMap(this::ifGroupCompleateOtherwiseSendNack)
            .filter(this::ifPermissionToAckTrailerGranted)
            .flatMap(this::ifLastGroupOterwiseSendGroupAck)
            .map(Tuple2::getT2);
      case ABORT:
        return groupedFlux.filter(this::abortTransaction).map(Tuple2::getT2);
      default:
        return groupedFlux
            /**
             * Responder will never request TID verification therefore should never receive any
             * {@link PduType#ACK} nor {@link PduType#NACK} filtering them out
             */
            .filter(modelAndTransactionTuple -> false)
            .map(Tuple2::getT2);
    }
  }

  /**
   * Predicate operator that returns true if {@link Transaction#getStatus} is {@link
   * TransactionStatus#INPROGRESS}.
   *
   * <p>Otherwise handles special cases where {@link Transaction#getStatus} is {@link
   * TransactionStatus#COMPLETED} or {@link TransactionStatus#USERWAIT} before returning false.
   *
   * <p>If {@link Transaction#getStatus} is {@link TransactionStatus#USERWAIT} then reschedules user
   * acknowledgement timer giving more time to user to send response.
   *
   * <p>If {@link Transaction#getStatus} is {@link TransactionStatus#COMPLETED} and it is a resent
   * trailer, assumes that either AbortPdu or AckPdu has been lost and resends one accordingly.
   *
   * @param invokeAndTransactionTuple {@link Tuple2} of {@link InvokeCommon} and {@link Transaction}
   * @return true if {@link Transaction#getStatus} is {@link TransactionStatus#INPROGRESS}
   */
  private Publisher<Tuple2<InvokeCommon, Transaction>> ifInvokeCommonRequiresProcessing(
      final Tuple2<InvokeCommon, Transaction> invokeAndTransactionTuple) {
    return Mono.deferContextual(
        context -> {
          final InvokeCommon invokeCommon = invokeAndTransactionTuple.getT1();
          final Transaction transaction = invokeAndTransactionTuple.getT2();
          final TransactionStatus transactionStatus = transaction.getStatus();
          if (transactionStatus == TransactionStatus.INPROGRESS) {
            return Mono.just(invokeAndTransactionTuple);
          } else if (invokeCommon.isRid() && invokeCommon.isTtr()) {
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            final Scheduler scheduler = context.get(Scheduler.class);
            if (transactionStatus == TransactionStatus.USERWAIT) {
              scheduleUserAcknowledgementTimer(networkSink, scheduler, transaction);
            } else if (transaction.getAbortCode() == null) {
              final NetworkMessageDto networkMessage =
                  ResponderUtils.createAcknowledgement(transaction);
              final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::ifInvokeCommonRequiresProcessing",
                    emitResult,
                    networkMessage);
              }
            } else {
              final NetworkMessageDto networkMessage =
                  ResponderUtils.createAbort(transaction, transaction.getAbortCode());
              final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::ifInvokeCommonRequiresProcessing",
                    emitResult,
                    networkMessage);
              }
            }
          }
          logger.debug("Filtered out on #ifInvokeCommonRequiresProcessing: {}", transaction);
          return Mono.empty();
        });
  }

  /**
   * Transfers metadata from {@link InvokeModel} to {@link Transaction}
   *
   * @param invokeAndTransactionTuple {@link Tuple2} of {@link InvokeCommon} and {@link Transaction}
   * @return {@link Tuple2} of {@link InvokeCommon} and {@link Transaction}
   */
  private final Tuple2<InvokeCommon, Transaction> processInvoke(
      final Tuple2<InvokeCommon, Transaction> invokeAndTransactionTuple) {
    final InvokeCommon invokeCommon = invokeAndTransactionTuple.getT1();
    final Transaction transaction = invokeAndTransactionTuple.getT2();
    if (invokeCommon.getPduType() == PduType.INVOKE) {
      final InvokeModel invokeModel = (InvokeModel) invokeCommon;
      transaction.setUserAckRequired(invokeModel.isUserAck());
      transaction.setTransactionClass(invokeModel.getTcl());
    }
    return invokeAndTransactionTuple;
  }

  /**
   * Predicate operator that performs validation of {@link TransactionClass} and sends abort if
   * invalid
   *
   * @param invokeAndTransactionTuple {@link Tuple2} of {@link InvokeCommon} and {@link Transaction}
   * @return true if {@link TransactionClass} is valid
   */
  private Publisher<Tuple2<InvokeCommon, Transaction>> ifValidTransactionClassOtherwiseSendAbort(
      final Tuple2<InvokeCommon, Transaction> invokeAndTransactionTuple) {
    return Mono.deferContextual(
        context -> {
          final Transaction transaction = invokeAndTransactionTuple.getT2();
          final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
          if (transaction.getTransactionClass() == TransactionClass.CLASS_2) {
            transaction.markCompleted();
            final NetworkMessageDto networkMessage =
                ResponderUtils.createAbort(
                    transaction, AbortCode.TCE_PROVIDER_NOT_IMPLEMENTED_CLASS);
            final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::ifValidTransactionClassOtherwiseSendAbort",
                  emitResult,
                  networkMessage);
            }
            logger.debug(
                "Filtered out on #ifValidTransactionClassOtherwiseSendAbort with {}: {}",
                AbortCode.TCE_PROVIDER_NOT_IMPLEMENTED_CLASS,
                transaction);
            return Mono.empty();
          } else if (transaction.getTransactionClass() == TransactionClass.UNKNOWN) {
            transaction.markCompleted();
            final NetworkMessageDto networkMessage =
                ResponderUtils.createAbort(transaction, AbortCode.TCE_PROVIDER_PROTOCOL_ERROR);
            final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::ifValidTransactionClassOtherwiseSendAbort",
                  emitResult,
                  networkMessage);
            }
            logger.debug(
                "Filtered out on #ifValidTransactionClassOtherwiseSendAbort with {}: {}",
                AbortCode.TCE_PROVIDER_PROTOCOL_ERROR,
                transaction);
            return Mono.empty();
          }
          return Mono.just(invokeAndTransactionTuple);
        });
  }

  /**
   * Predicate operator that filters out duplicate invokes unless invokes are resends of group or
   * transmission trailers
   *
   * <p>Saves the invoke's payload into transaction.
   *
   * <p>Also resets highest acknowledged segment position if invoke was resent trailer
   *
   * @param invokeAndTransactionTuple {@link Tuple2} with {@link InvokeCommon} and {@link
   *     Transaction}
   * @return true if not duplicate or group/transmission trailer
   */
  private boolean ifNotDuplicateOrTrailerRetry(
      final Tuple2<InvokeCommon, Transaction> invokeAndTransactionTuple) {
    final InvokeCommon invokeCommon = invokeAndTransactionTuple.getT1();
    final Transaction transaction = invokeAndTransactionTuple.getT2();
    final byte[] previousPayload = transaction.setSegmentAndGetPrevious(invokeCommon);

    final boolean notDuplicateOrTrailerRetry =
        previousPayload == null || invokeCommon.isTrailerRid();
    if (logger.isDebugEnabled() && !notDuplicateOrTrailerRetry) {
      logger.debug("Filtered out on #ifNotDuplicateOrTrailerRetry {}", transaction);
    }
    return notDuplicateOrTrailerRetry;
  }

  /**
   * Predicate operator that terminates processing if group is not complete and schedules negative
   * acknowledgement timer.
   *
   * @param invokeAndTransactionTuple {@link Tuple2} of {@link InvokeCommon} and {@link Transaction}
   * @return true if group currently being assembled is complete and current {@link InvokeCommon} is
   *     allowed to acknowledge it
   */
  private Publisher<Tuple2<InvokeCommon, Transaction>> ifGroupCompleateOtherwiseSendNack(
      final Tuple2<InvokeCommon, Transaction> invokeAndTransactionTuple) {
    return Mono.deferContextual(
        context -> {
          final InvokeCommon invokeCommon = invokeAndTransactionTuple.getT1();
          final Transaction transaction = invokeAndTransactionTuple.getT2();
          final int psn = invokeCommon.getPsn();

          if (psn > transaction.getPreviousTrailerPsn()
              && psn <= transaction.getHighestTrailer().getPsn()) {
            if (!transaction.isSegmentMissing()) {
              return Mono.just(invokeAndTransactionTuple);
            }
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            final Scheduler scheduler = context.get(Scheduler.class);
            scheduleNackTimer(networkSink, scheduler, transaction);
          }
          logger.debug("Filtered out on #ifGroupCompleateOtherwiseSendNack: {}", transaction);
          return Mono.empty();
        });
  }

  /**
   * Predicate operator that ensures only one trailer acknowledgment is sent
   *
   * @param invokeAndTransactionTuple {@link Tuple2} with {@link InvokeCommon} and {@link
   *     Transaction}
   * @return true if permission to acknowledge trailer was granted
   */
  private boolean ifPermissionToAckTrailerGranted(
      final Tuple2<InvokeCommon, Transaction> invokeAndTransactionTuple) {
    final InvokeCommon invokeCommon = invokeAndTransactionTuple.getT1();
    final Transaction transaction = invokeAndTransactionTuple.getT2();
    final boolean permissionToAckTrailerGranted =
        transaction.requestPermissionToAckTrailer(invokeCommon);
    if (logger.isDebugEnabled() && !permissionToAckTrailerGranted) {
      logger.debug("Filtered out on #ifPermissionToAckTrailerGranted: {}", transaction);
    }
    return permissionToAckTrailerGranted;
  }

  /**
   * Predicate operator that sends group acknowledgement and filters out, unless it is last group.
   *
   * @param invokeAndTransactionTuple {@link Tuple2} of {@link InvokeCommon} and {@link Transaction}
   * @return true if last group has been assembled
   */
  private Publisher<Tuple2<InvokeCommon, Transaction>> ifLastGroupOterwiseSendGroupAck(
      final Tuple2<InvokeCommon, Transaction> invokeAndTransactionTuple) {
    return Mono.deferContextual(
        context -> {
          final InvokeCommon invokeCommon = invokeAndTransactionTuple.getT1();
          final Transaction transaction = invokeAndTransactionTuple.getT2();
          final InvokeCommon highestTrailer = transaction.getHighestTrailer();

          if (invokeCommon.getPsn() > transaction.getPreviousTrailerPsn()) {
            if (highestTrailer.isTtr()) {
              return Mono.just(invokeAndTransactionTuple);
            }
            transaction.disposeActiveTimer();
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            final NetworkMessageDto networkMessage =
                ResponderUtils.createAcknowledgement(transaction);
            final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::ifLastGroupOterwiseSendGroupAck",
                  emitResult,
                  networkMessage);
            }
          }

          logger.debug("Filtered out on #ifLastGroupOterwiseSendGroupAck: {}", transaction);
          return Mono.empty();
        });
  }

  /**
   * Marks {@link Transaction} as {@link TransactionStatus#COMPLETED}, updates {@link AbortCode} and
   * filters out.
   *
   * @param modelAndTransactionTuple {@link Tuple2} of {@link ModelCommon} and {@link Transaction}
   * @return false
   */
  private boolean abortTransaction(
      final Tuple2<ModelCommon, Transaction> modelAndTransactionTuple) {
    final Transaction transaction = modelAndTransactionTuple.getT2();
    final AbortModel abortModel = (AbortModel) modelAndTransactionTuple.getT1();
    ResponderMetrics.incrementIncomingAbort(abortModel);
    transaction.markCompleted();
    transaction.setAbortCode(abortModel.getAbortCode());
    return false;
  }

  /**
   * Schedules User Acknowledgement timer and sets transaction status to {@link
   * TransactionStatus#USERWAIT}
   *
   * @param networkSink instance of {@link reactor.core.publisher.Sinks.Many}&lt;{@link
   *     NetworkMessageDto}&gt;
   * @param scheduler instance of {@link Scheduler} that {@link Flux} runs on
   * @param transaction instance of {@link Transaction}
   * @return previous {@link TransactionStatus}
   */
  private TransactionStatus scheduleUserAcknowledgementTimer(
      final Sinks.Many<NetworkMessageDto> networkSink,
      final Scheduler scheduler,
      final Transaction transaction) {
    final TransactionStatus previousStatus = transaction.markUserAcknowledgement();
    if (previousStatus != TransactionStatus.COMPLETED) {
      final Disposable timerHandle =
          Mono.just(Tuples.of(networkSink, transaction))
              .delayElement(configuration.getUserAcknowledgementInterval(), scheduler)
              .doOnCancel(
                  () -> logger.debug("Disposing UserAcknowledgementTimer for {}", transaction))
              .subscribe(this::sendAbortNoUserResponse, throwable -> logger.error("", throwable));
      transaction.updateActiveTimer(timerHandle);
      logger.debug("Scheduled UserAcknowledgementTimer for {}", transaction);
    }
    return previousStatus;
  }

  /**
   * Sends {@link NetworkMessageDto} with Abort PDU having {@link
   * AbortCode#TCE_PROVIDER_NO_RESPONSE}
   *
   * @param sinkAndTransactionTuple {@link Tuple2} of {@link
   *     reactor.core.publisher.Sinks.Many}&lt;{@link NetworkMessageDto}&gt; and {@link Transaction}
   */
  private void sendAbortNoUserResponse(
      final Tuple2<Sinks.Many<NetworkMessageDto>, Transaction> sinkAndTransactionTuple) {
    final Transaction transaction = sinkAndTransactionTuple.getT2();
    userwaitDurations.record(
        System.currentTimeMillis() - transaction.getUserWaitTimestamp(), TimeUnit.MILLISECONDS);
    final TransactionStatus previousStatus = transaction.markCompleted();
    if (previousStatus == TransactionStatus.USERWAIT) {
      final NetworkMessageDto networkMessage =
          ResponderUtils.createAbort(transaction, AbortCode.TCE_PROVIDER_NO_RESPONSE);
      final EmitResult emitResult = sinkAndTransactionTuple.getT1().tryEmitNext(networkMessage);
      if (emitResult.isFailure()) {
        logger.error(
            "Sink emission failed with {} for {} in ::sendAbortNoUserResponse",
            emitResult,
            networkMessage);
      }
    }
  }

  /**
   * Schedules Nack timer
   *
   * @param networkSink instance of {@link reactor.core.publisher.Sinks.Many}&lt;{@link
   *     NetworkMessageDto}&gt;
   * @param scheduler instance of {@link Scheduler} that {@link Flux} runs on
   * @param transaction instance of {@link Transaction}
   */
  private void scheduleNackTimer(
      final Sinks.Many<NetworkMessageDto> networkSink,
      final Scheduler scheduler,
      final Transaction transaction) {
    final Disposable timerHandle =
        Mono.just(Tuples.of(networkSink, transaction))
            .delayElement(configuration.getNackDelayInterval(), scheduler)
            .doOnCancel(() -> logger.debug("Disposing NackTimer for {}", transaction))
            .subscribe(this::sendNackMessage, throwable -> logger.error("", throwable));
    transaction.updateActiveTimer(timerHandle);
    logger.debug("Scheduled NackTimer for {}", transaction);
  }

  /**
   * Creates and sends {@link NetworkMessageDto} with Nack PDU if needed
   *
   * @param sinkAndTransactionTuple {@link Tuple2} of {@link
   *     reactor.core.publisher.Sinks.Many}&lt;{@link NetworkMessageDto}&gt; and {@link Transaction}
   */
  private void sendNackMessage(
      final Tuple2<Sinks.Many<NetworkMessageDto>, Transaction> sinkAndTransactionTuple) {
    final NetworkMessageDto networkMessage =
        ResponderUtils.createNegativeAcknowledgement(sinkAndTransactionTuple.getT2());
    if (networkMessage != null) {
      final EmitResult emitResult = sinkAndTransactionTuple.getT1().tryEmitNext(networkMessage);
      if (emitResult.isFailure()) {
        logger.error(
            "Sink emission failed with {} for {} in ::sendNackMessage", emitResult, networkMessage);
      }
    }
  }
}
