package com.volvo.tisp.vwtp.model;

import com.volvo.tisp.vwtp.constants.PduType;
import java.util.Arrays;

/** Acknowledgement protocol data unit model */
public final class AckModel extends ModelCommon {
  private final boolean otr;

  private AckModel(final Builder builder) {
    super(builder);
    otr = builder.otr;
  }

  /**
   * @return instance of an {@link Builder}
   */
  public static Builder builder() {
    return new Builder();
  }

  @Override
  public PduType getPduType() {
    return PduType.ACK;
  }

  /**
   * @return true if outstanding transaction flag is set
   */
  public boolean isOtr() {
    return otr;
  }

  @Override
  public String toString() {
    return "AckModel{"
        + "otr="
        + isOtr()
        + ", rid="
        + isRid()
        + ", wtpTid="
        + getWtpTid()
        + ", wtpVersion="
        + getWtpVersion()
        + ", vid="
        + getVid()
        + ", tpiArray="
        + Arrays.toString(getTpiArray())
        + ", hashCode="
        + Integer.toHexString(hashCode())
        + '}';
  }

  /** Builder for acknowledgement PDU models */
  public static class Builder extends ModelCommon.Builder<Builder, AckModel> {
    private boolean otr;

    private Builder() {}

    @Override
    protected Builder getSelf() {
      return this;
    }

    /**
     * @param otr outstanding transaction
     * @return builder
     */
    public Builder withOtr(final boolean otr) {
      this.otr = otr;
      return this;
    }

    @Override
    public AckModel build() {
      return new AckModel(this);
    }
  }
}
