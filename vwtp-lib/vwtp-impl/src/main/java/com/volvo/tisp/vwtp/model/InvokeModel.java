package com.volvo.tisp.vwtp.model;

import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import java.util.Arrays;

/** Invoke protocol data unit model */
public final class InvokeModel extends InvokeCommon {
  private final boolean wtpTidNew;
  private final boolean userAck;
  private final TransactionClass tcl;

  private InvokeModel(final Builder builder) {
    super(builder);
    wtpTidNew = builder.wtpTidNew;
    userAck = builder.userAck;
    tcl = builder.tcl;
  }

  /**
   * @return instance of an {@link Builder}
   */
  public static Builder builder() {
    return new Builder();
  }

  @Override
  public PduType getPduType() {
    return PduType.INVOKE;
  }

  /**
   * @return true if TID new flag is set
   */
  public boolean isWtpTidNew() {
    return wtpTidNew;
  }

  /**
   * @return true if user acknowledgement flag is set
   */
  public boolean isUserAck() {
    return userAck;
  }

  /**
   * @return {@link TransactionClass}
   */
  public TransactionClass getTcl() {
    return tcl;
  }

  @Override
  public int getPsn() {
    return 0;
  }

  @Override
  public String toString() {
    return "InvokeModel{"
        + "gtr="
        + isGtr()
        + ", ttr="
        + isTtr()
        + ", rid="
        + isRid()
        + ", psn="
        + getPsn()
        + ", wtpTid="
        + getWtpTid()
        + ", wtpVersion="
        + getWtpVersion()
        + ", wtpTidNew="
        + isWtpTidNew()
        + ", userAck="
        + isUserAck()
        + ", tcl="
        + getTcl()
        + ", vid="
        + getVid()
        + ", tpiArray="
        + Arrays.toString(getTpiArray())
        + ", payload=["
        + HEX_ENCODER.encode(getPayload())
        + "], hashCode="
        + Integer.toHexString(hashCode())
        + '}';
  }

  /** Builder for invoke PDU models */
  public static class Builder extends InvokeCommon.Builder<Builder, InvokeModel> {
    private boolean wtpTidNew;
    private boolean userAck;
    private TransactionClass tcl;

    private Builder() {}

    @Override
    protected Builder getSelf() {
      return this;
    }

    /**
     * @param wtpTidNew TID new flag
     * @return builder
     */
    public Builder withWtpTidNew(final boolean wtpTidNew) {
      this.wtpTidNew = wtpTidNew;
      return this;
    }

    /**
     * @param userAck user acknowledgement flag
     * @return builder
     */
    public Builder withUserAck(final boolean userAck) {
      this.userAck = userAck;
      return this;
    }

    /**
     * @param tcl {@link TransactionClass}
     * @return builder
     */
    public Builder withTcl(final TransactionClass tcl) {
      this.tcl = tcl;
      return this;
    }

    @Override
    public InvokeModel build() {
      return new InvokeModel(this);
    }
  }
}
