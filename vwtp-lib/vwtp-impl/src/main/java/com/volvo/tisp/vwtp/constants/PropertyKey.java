package com.volvo.tisp.vwtp.constants;

public class PropertyKey {
  private PropertyKey() {}

  /** Maximum Transmission Unit in bytes */
  public static final String WTP_MTU = "wtp.mtu";

  /** Maximum number of segments in a group */
  public static final String WTP_MAX_GROUP_SIZE = "wtp.group";

  /**
   * Retry timer: This sets a bound for the amount of time to wait before re-transmitting a PDU. The
   * value is an array of delays [ms], comma separated.
   */
  public static final String WTP_TIMER_R = "wtp.timer.r";

  /**
   * This is a counter for the WTP_TIMER_R, at the first retry this counter will be 1, the next 2,
   * and so forth
   */
  public static final String WTP_TIMER_R_RETRY = "wtp.timer.r.retry";

  /**
   * SAR Group Retry delay (GR): This sets a bound for the amount of time to until the last packet
   * in a group is resent.
   */
  public static final String WTP_TIMER_GR = "wtp.timer.gr";

  /**
   * Acknowledgement interval (A): This sets a bound for the amount of time to wait before sending
   * an acknowledgement [ms]. This is the time to wait for an user response acknowledgement
   */
  public static final String WTP_TIMER_A = "wtp.timer.a";

  /**
   * Wait timeout interval (W): This sets a bound for the amount of time to wait before state
   * information about a transaction is released [ms]. Only Class 2 Initiator and Class 1 Responder.
   */
  public static final String WTP_TIMER_W = "wtp.timer.w";

  /**
   * SAR Nack delay (SN]: This sets a bound for the amount of time to wait before a SAR Nack is sent
   * [ms].
   */
  public static final String WTP_TIMER_SN = "wtp.timer.sn";

  /** Interval in milliseconds for logging cache metrics */
  public static final String WTP_CACHE_METRICS_INTERVAL = "wtp.cache-metrics-interval";

  /** Interval in milliseconds for logging responder transaction metrics */
  public static final String WTP_RESPONDER_TRANSACTION_METRICS_INTERVAL =
      "wtp.responder-transaction-metrics-interval";

  /**
   * Maximum amount of {@link TransactionStatus#INPROGRESS} transactions in the cache, before
   * starting rejecting new transactions
   */
  public static final String WTP_RESPONDER_ACTIVE_TRANSACTIONS =
      "wtp.responder-active-transactions";
}
