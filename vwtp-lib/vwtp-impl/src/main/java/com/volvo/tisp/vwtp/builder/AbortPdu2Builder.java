package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.AbortCodeStep;
import com.volvo.tisp.vwtp.builder.step.VidStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidReverseDirectionStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.AbortPdu2;

public interface AbortPdu2Builder<B>
    extends AbortCodeStep<B>, WtpTidStep<B>, WtpTidReverseDirectionStep<B>, VidStep<B> {
  AbortPdu2 buildAbortPdu2();

  static <T extends AbortPdu2Builder<T>> AbortPdu2Builder<T> builder() {
    return new PduBuilder<>();
  }
}
