package com.volvo.tisp.vwtp.model;

import com.google.common.io.BaseEncoding;

/** Common ancestor of invoke and segmented invoke protocol data unit models */
public abstract class InvokeCommon extends ModelCommon {
  protected static final BaseEncoding HEX_ENCODER =
      BaseEncoding.base16().omitPadding().upperCase().withSeparator(" ", 2);
  private final boolean trailer;
  private final boolean trailerRid;
  private final boolean gtr;
  private final boolean ttr;
  private final int psn;
  private final byte[] payload;

  /**
   * @param builder {@link Builder}
   */
  protected InvokeCommon(final Builder<?, ?> builder) {
    super(builder);
    trailer = builder.gtr || builder.ttr;
    trailerRid = trailer && isRid();
    gtr = builder.gtr;
    ttr = builder.ttr;
    psn = builder.psn;
    payload = builder.payload;
  }

  /**
   * @return true if is trailer
   */
  public boolean isTrailer() {
    return trailer;
  }

  /**
   * @return true if is trailer retransmission
   */
  public boolean isTrailerRid() {
    return trailerRid;
  }

  /**
   * @return true if group trailer is set
   */
  public boolean isGtr() {
    return gtr;
  }

  /**
   * @return true if transaction trailer is set
   */
  public boolean isTtr() {
    return ttr;
  }

  /**
   * @return packet sequence number
   */
  public int getPsn() {
    return psn;
  }

  /**
   * @return payload
   */
  public byte[] getPayload() {
    return payload;
  }

  /**
   * Common ancestor of invoke and segmented invoke PDU model builders
   *
   * @param <B> super-type of {@link Builder} a.k.a. - Builder
   * @param <P> super-type of {@link InvokeCommon} a.k.a. - common invoke PDU model
   */
  public abstract static class Builder<B extends Builder<B, P>, P extends InvokeCommon>
      extends ModelCommon.Builder<B, P> {
    private boolean gtr;
    private boolean ttr;
    private int psn;
    private byte[] payload;

    /**
     * @param gtr group trailer
     * @return builder
     */
    public B withGtr(final boolean gtr) {
      this.gtr = gtr;
      return getSelf();
    }

    /**
     * @param ttr transaction trailer
     * @return builder
     */
    public B withTtr(final boolean ttr) {
      this.ttr = ttr;
      return getSelf();
    }

    /**
     * @param psn packet sequence number
     * @return builder
     */
    public B withPsn(final int psn) {
      this.psn = psn;
      return getSelf();
    }

    /**
     * @param payload payload
     * @return builder
     */
    public B withPayload(final byte[] payload) {
      this.payload = payload;
      return getSelf();
    }
  }
}
