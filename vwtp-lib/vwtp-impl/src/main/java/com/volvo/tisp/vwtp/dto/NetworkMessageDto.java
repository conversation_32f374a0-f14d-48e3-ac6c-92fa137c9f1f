package com.volvo.tisp.vwtp.dto;

import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.helpers.MessageFormatter;

public class NetworkMessageDto extends MessageDto {
  private final byte[] payload;

  public static NetworkMessageDtoBuilder builder() {
    return new NetworkMessageDtoBuilder();
  }

  private NetworkMessageDto(final NetworkMessageDtoBuilder builder) {
    super(builder);
    payload = builder.payload;
  }

  public byte[] getPayload() {
    return payload;
  }

  @Override
  public String toString() {
    return MessageFormatter.arrayFormat(
            "{} {\n\tmessageId={},\n\taddress={},\n\tproperties={},\n\tpayload=[{}]\n}",
            new Object[] {
              this.getClass().getSimpleName(),
              getMessageId(),
              getAddress(),
              getProperties(),
              HEX_ENCODER.encode(payload)
            })
        .getMessage();
  }

  public static class NetworkMessageDtoBuilder
      extends MessageDtoBuilder<NetworkMessageDtoBuilder, NetworkMessageDto> {
    private byte[] payload = ArrayUtils.EMPTY_BYTE_ARRAY;

    @Override
    protected NetworkMessageDtoBuilder getSelf() {
      return this;
    }

    public NetworkMessageDtoBuilder withPayload(final byte[] payload) {
      this.payload = payload;
      return this;
    }

    @Override
    public NetworkMessageDto build() {
      return new NetworkMessageDto(this);
    }
  }
}
