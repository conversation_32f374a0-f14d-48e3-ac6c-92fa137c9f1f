package com.volvo.tisp.vwtp.converter.codectomodel;

import com.volvo.tisp.vwtp.codec.InvokePdu;
import com.volvo.tisp.vwtp.codec.InvokePdu2;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.model.InvokeModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.SegInvokeModel;
import com.volvo.tisp.vwtp.model.TpiCommon;

/**
 * Converter utility for converting {@link InvokePdu} or {@link InvokePdu2} to instance of {@link
 * ModelCommon}
 */
public class InvokePduToModelCommonConverter {
  /** Private constructor to prevent instantiation of utility class */
  private InvokePduToModelCommonConverter() {}

  /**
   * Convert {@link InvokePdu} to {@link InvokeModel}
   *
   * @param invokePdu {@link InvokePdu}
   * @param payload byte array payload
   * @return {@link InvokeModel}
   */
  public static InvokeModel convert(final InvokePdu invokePdu, final byte[] payload) {
    return InvokeModel.builder()
        .withGtr(invokePdu.getGtr())
        .withTtr(invokePdu.getTtr())
        .withRid(invokePdu.getRid())
        .withWtpTid(invokePdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_1)
        .withWtpTidNew(invokePdu.getTidNew())
        .withUserAck(invokePdu.getUserAck())
        .withTcl(TransactionClass.fromValue(invokePdu.getTcl()))
        .withTpiArray(TpiCommon.EMPTY)
        .withPayload(payload)
        .build();
  }

  /**
   * Convert {@link InvokePdu2} to {@link InvokeModel}
   *
   * @param invokePdu {@link InvokePdu2}
   * @param payload byte array payload
   * @return {@link InvokeModel}
   */
  public static InvokeModel convert(final InvokePdu2 invokePdu, final byte[] payload) {
    return InvokeModel.builder()
        .withGtr(invokePdu.getGtr())
        .withTtr(invokePdu.getTtr())
        .withRid(invokePdu.getRid())
        .withWtpTid(invokePdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_2)
        .withWtpTidNew(invokePdu.getTidNew())
        .withUserAck(invokePdu.getUserAck())
        .withTcl(TransactionClass.fromValue(invokePdu.getTcl()))
        .withVid(invokePdu.getVid())
        .withTpiArray(TpiCommon.EMPTY)
        .withPayload(payload)
        .build();
  }

  /**
   * Convert {@link SegInvokePdu} to {@link SegInvokeModel}
   *
   * @param segInvokePdu {@link SegInvokePdu}
   * @param payload byte array payload
   * @return {@link SegInvokeModel}
   */
  public static SegInvokeModel convert(final SegInvokePdu segInvokePdu, final byte[] payload) {
    return SegInvokeModel.builder()
        .withGtr(segInvokePdu.getGtr())
        .withTtr(segInvokePdu.getTtr())
        .withRid(segInvokePdu.getRid())
        .withWtpTid(segInvokePdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_1)
        .withPsn((int) segInvokePdu.getPsn())
        .withTpiArray(TpiCommon.EMPTY)
        .withPayload(payload)
        .build();
  }

  /**
   * Convert {@link SegInvokePdu2} to {@link SegInvokeModel}
   *
   * @param segInvokePdu {@link SegInvokePdu2}
   * @param payload byte array payload
   * @return {@link SegInvokeModel}
   */
  public static SegInvokeModel convert(final SegInvokePdu2 segInvokePdu, final byte[] payload) {
    return SegInvokeModel.builder()
        .withGtr(segInvokePdu.getGtr())
        .withTtr(segInvokePdu.getTtr())
        .withRid(segInvokePdu.getRid())
        .withWtpTid(segInvokePdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_2)
        .withPsn((int) segInvokePdu.getPsn())
        .withVid(segInvokePdu.getVid())
        .withTpiArray(TpiCommon.EMPTY)
        .withPayload(payload)
        .build();
  }
}
