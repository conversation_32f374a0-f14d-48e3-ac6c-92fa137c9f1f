package com.volvo.tisp.vwtp.configuration;

import static com.volvo.tisp.vwtp.util.LogUtil.FORMAT_CACHE_DISPOSE_MESSAGE;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.volvo.tisp.vwtp.initiator.CacheService;
import com.volvo.tisp.vwtp.initiator.InitiatorTransactionCacheKey;
import com.volvo.tisp.vwtp.initiator.NetworkMessageFlow;
import com.volvo.tisp.vwtp.initiator.Transaction;
import com.volvo.tisp.vwtp.initiator.UserMessageFlow;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.binder.cache.CaffeineCacheMetrics;
import java.net.URI;
import java.util.concurrent.atomic.AtomicLong;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/** VWTP Initiator spring configuration */
@Configuration
@Import({
  InitiatorProperties.class,
  CacheService.class,
  UserMessageFlow.class,
  NetworkMessageFlow.class
})
public class InitiatorConfiguration {
  private static final Logger logger = LoggerFactory.getLogger(InitiatorConfiguration.class);

  // Caches Names
  public static final String TRANSACTION_CACHE = "INITIATOR_TRANSACTION_CACHE";
  public static final String LAST_WTP_TID_CACHE = "INITIATOR_LAST_WTP_TID_CACHE";

  @Bean(destroyMethod = "invalidateAll")
  protected Cache<InitiatorTransactionCacheKey, Transaction> initiatorTransactionCache() {
    final Cache<InitiatorTransactionCacheKey, Transaction> cache =
        Caffeine.newBuilder()
            .recordStats()
            .executor(Runnable::run)
            .<InitiatorTransactionCacheKey, Transaction>removalListener(
                (key, transaction, cause) -> {
                  logger.debug(
                      FORMAT_CACHE_DISPOSE_MESSAGE, TRANSACTION_CACHE, key, transaction, cause);
                  transaction.disposeActiveTimer();
                })
            .build();
    CaffeineCacheMetrics.monitor(
        Metrics.globalRegistry, cache, MetricNames.INITIATOR_TRANSACTIONS_CACHE);
    return cache;
  }

  @Bean(destroyMethod = "invalidateAll")
  protected Cache<URI, AtomicLong> initiatorLastWtpTidCache() {
    final Cache<URI, AtomicLong> cache =
        Caffeine.newBuilder()
            .recordStats()
            .executor(Runnable::run)
            .removalListener(
                (key, value, cause) ->
                    logger.debug(
                        FORMAT_CACHE_DISPOSE_MESSAGE, LAST_WTP_TID_CACHE, key, value, cause))
            .build();
    CaffeineCacheMetrics.monitor(
        Metrics.globalRegistry, cache, MetricNames.INITIATOR_LAST_TID_CACHE);
    return cache;
  }
}
