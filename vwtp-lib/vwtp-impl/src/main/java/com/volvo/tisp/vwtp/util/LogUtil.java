package com.volvo.tisp.vwtp.util;

import static java.util.Locale.US;

import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LogUtil {
  private static final Logger logger = LoggerFactory.getLogger(LogUtil.class);

  public static final String FORMAT_TABLE_OUTPUT = "%-45s: %s";
  public static final String FORMAT_CACHE_DISPOSE_MESSAGE = "Removing {} entry: {}, {}, {}";

  public static final String ERROR_NOT_POSITIVE = "must be more than 0";
  public static final String ERROR_EMPTY = "can not be empty";

  /** Private constructor to prevent instantiation */
  private LogUtil() {}

  /**
   * Log {@link CacheStats} with specified cache name
   *
   * @param cacheName name of the chache
   * @param stats instance of {@link CacheStats}
   */
  public static void logCacheStats(final String cacheName, final CacheStats stats) {
    if (logger.isInfoEnabled()) {
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, cacheName + " STATS", ""));
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "Hit Count", stats.hitCount()));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "Miss Count", stats.missCount()));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Load Success Count", stats.loadSuccessCount()));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Load Failure Count", stats.loadFailureCount()));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Total Load Time (ns)", stats.totalLoadTime()));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "Eviction Count", stats.evictionCount()));
      logger.info(
          String.format(US, FORMAT_TABLE_OUTPUT, "Eviction Weight", stats.evictionWeight()));
    }
  }
}
