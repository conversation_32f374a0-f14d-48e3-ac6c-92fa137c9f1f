package com.volvo.tisp.vwtp.dto;

import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import org.apache.commons.lang3.ArrayUtils;
import org.slf4j.helpers.MessageFormatter;

public class UserMessageDto extends MessageDto {
  private final byte[] payload;
  private final long vehicleId;
  private final TransactionClass transactionClass;
  private final WtpVersion wtpVersion;

  public static UserMessageDtoBuilder builder() {
    return new UserMessageDtoBuilder();
  }

  private UserMessageDto(final UserMessageDtoBuilder builder) {
    super(builder);
    payload = builder.payload;
    vehicleId = builder.vehicleId;
    transactionClass = builder.transactionClass;
    wtpVersion = builder.wtpVersion;
  }

  public WtpVersion getWtpVersion() {
    return wtpVersion;
  }

  public TransactionClass getTransactionClass() {
    return transactionClass;
  }

  public byte[] getPayload() {
    return payload;
  }

  public long getVehicleId() {
    return vehicleId;
  }

  @Override
  public String toString() {
    return MessageFormatter.arrayFormat(
            "{} {\n\tmessageId={},\n\taddress={},\n\tvehicleId={},\n\ttransactionClass={},\n\tproperties={},\n\tpayload=[{}]\n}",
            new Object[] {
              this.getClass().getSimpleName(),
              getMessageId(),
              getAddress(),
              vehicleId,
              transactionClass,
              getProperties(),
              HEX_ENCODER.encode(payload)
            })
        .getMessage();
  }

  public static class UserMessageDtoBuilder
      extends MessageDtoBuilder<UserMessageDtoBuilder, UserMessageDto> {
    private byte[] payload = ArrayUtils.EMPTY_BYTE_ARRAY;
    private long vehicleId = 0;
    private TransactionClass transactionClass = TransactionClass.UNKNOWN;
    private WtpVersion wtpVersion = WtpVersion.VERSION_2;

    @Override
    protected UserMessageDtoBuilder getSelf() {
      return this;
    }

    public UserMessageDtoBuilder withPayload(final byte[] payload) {
      this.payload = payload;
      return this;
    }

    public UserMessageDtoBuilder withVehicleId(final long vehicleId) {
      this.vehicleId = vehicleId;
      return this;
    }

    public UserMessageDtoBuilder withTransactionClass(final TransactionClass transactionClass) {
      this.transactionClass = transactionClass;
      return this;
    }

    public UserMessageDtoBuilder withWtpVersion(final WtpVersion wtpVersion) {
      this.wtpVersion = wtpVersion;
      return this;
    }

    @Override
    public UserMessageDto build() {
      return new UserMessageDto(this);
    }
  }
}
