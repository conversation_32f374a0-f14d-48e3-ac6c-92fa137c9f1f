package com.volvo.tisp.vwtp.builder.step;

/**
 * Step that transforms WTP TID for response and sets to the builder
 *
 * @param <B> - builder
 */
public interface WtpTidReverseDirectionStep<B> {
  /**
   * Inverts the high order bit on provided wtpTid and sets the value to the builder
   *
   * @param wtpTid WTP transaction identifier
   * @return builder
   */
  B withWtpTidReverseDirection(final long wtpTid);
}
