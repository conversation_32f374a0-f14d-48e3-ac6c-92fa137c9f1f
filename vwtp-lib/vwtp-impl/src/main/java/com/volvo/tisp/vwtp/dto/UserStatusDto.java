package com.volvo.tisp.vwtp.dto;

import com.volvo.tisp.vwtp.constants.AbortCode;
import org.slf4j.helpers.MessageFormatter;

public class UserStatusDto extends MessageDto {
  private static final String DELIVERED_MESSAGE_FORMAT =
      "{} {\n\tmessageId={},\n\taddress={},\n\tdelivered=true,\n\tproperties={}\n}";
  private static final String ABORTED_MESSAGE_FORMAT =
      "{} {\n\tmessageId={},\n\taddress={},\n\tdelivered=false,\n\tabortCode={},\n\tproperties={}\n}";

  private final boolean delivered;
  private final AbortCode abortCode;

  public static UserStatusDtoBuilder builder() {
    return new UserStatusDtoBuilder();
  }

  private UserStatusDto(final UserStatusDtoBuilder builder) {
    super(builder);
    delivered = builder.delivered;
    abortCode = builder.abortCode;
  }

  public boolean isDelivered() {
    return delivered;
  }

  public AbortCode getAbortCode() {
    return abortCode;
  }

  @Override
  public String toString() {
    if (delivered) {
      return MessageFormatter.arrayFormat(
              DELIVERED_MESSAGE_FORMAT,
              new Object[] {
                this.getClass().getSimpleName(), getMessageId(), getAddress(), getProperties()
              })
          .getMessage();
    } else {
      return MessageFormatter.arrayFormat(
              ABORTED_MESSAGE_FORMAT,
              new Object[] {
                this.getClass().getSimpleName(),
                getMessageId(),
                getAddress(),
                abortCode,
                getProperties()
              })
          .getMessage();
    }
  }

  public static class UserStatusDtoBuilder
      extends MessageDtoBuilder<UserStatusDtoBuilder, UserStatusDto> {
    private boolean delivered = true;
    private AbortCode abortCode = AbortCode.TCE_PROVIDER_UNKNOWN;

    @Override
    protected UserStatusDtoBuilder getSelf() {
      return this;
    }

    public UserStatusDtoBuilder withDelivered(final boolean delivered) {
      this.delivered = delivered;
      return this;
    }

    public UserStatusDtoBuilder withAbortCode(final AbortCode abortCode) {
      this.abortCode = abortCode;
      return this;
    }

    @Override
    public UserStatusDto build() {
      return new UserStatusDto(this);
    }
  }
}
