package com.volvo.tisp.vwtp.initiator;

import com.volvo.tisp.vwtp.builder.AbortPdu2Builder;
import com.volvo.tisp.vwtp.builder.AbortPduBuilder;
import com.volvo.tisp.vwtp.builder.AckPdu2Builder;
import com.volvo.tisp.vwtp.builder.AckPduBuilder;
import com.volvo.tisp.vwtp.builder.InvokePdu2Builder;
import com.volvo.tisp.vwtp.builder.InvokePduBuilder;
import com.volvo.tisp.vwtp.builder.SegInvokePdu2Builder;
import com.volvo.tisp.vwtp.builder.SegInvokePduBuilder;
import com.volvo.tisp.vwtp.codec.AbortPdu;
import com.volvo.tisp.vwtp.codec.AbortPdu2;
import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import com.volvo.tisp.vwtp.codec.InvokePdu;
import com.volvo.tisp.vwtp.codec.InvokePdu2;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.converter.codectobytes.AbortPduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.AckPduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.InvokePduToByteArrayConverter;
import com.volvo.tisp.vwtp.converter.codectobytes.SegInvokePduToByteArrayConverter;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto.NetworkMessageDtoBuilder;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.util.InitiatorMetrics;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Timer;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/** Comprises a set of convenience methods for the {@link UserMessageFlow} */
public final class InitiatorUtils {
  private static final DistributionSummary messageSegmentCountDistribution =
      DistributionSummary.builder(MetricNames.INITIATOR_INCOMING_MESSAGES_SEGMENTS)
          .description("Tracks number of sements in a message")
          .maximumExpectedValue(256d)
          .baseUnit("segments")
          .register(Metrics.globalRegistry);
  private static final Counter tidNewCount =
      Counter.builder(MetricNames.INITIATOR_OUTGOING_TID_NEW)
          .description("Count of outgoing INVOKE's with TidNew flag")
          .register(Metrics.globalRegistry);
  private static final Counter deliveredCount =
      Counter.builder(MetricNames.INITIATOR_OUTGOING_STATUSES)
          .description("Count of outgoing user statuses with status delivered")
          .tag(MetricNames.TYPE_TAG, "delivered")
          .register(Metrics.globalRegistry);
  private static final Counter outgoingPduSizes =
      Counter.builder(MetricNames.OUTGOING_PDU_SIZES)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_INITIATOR)
          .register(Metrics.globalRegistry);
  private static final Timer transactionDurations =
      Timer.builder(MetricNames.INITIATOR_TRANSACTION_DURATIONS)
          .description("Duration of initators transaction")
          .register(Metrics.globalRegistry);

  /**
   * Creates segments from the {@link UserMessageDto#getPayload()}
   *
   * <p>N.B. the first segment is for the Invoke PDU that always has the GTR flag set.
   *
   * <p>The following segments, i.e. from index 1 -&gt;, will be SegmInvokePDU(s)
   *
   * <p>The Key / Value: PSN / Segment
   *
   * @param userMessage {@link UserMessageDto}
   * @param maxMtuSize MTU size
   * @param groupMaxSize size of WTP group
   * @return {@link Map}
   */
  public static Map<Integer, Segment> createSegments(
      final UserMessageDto userMessage, final int maxMtuSize, final int groupMaxSize) {
    final int payloadSize = userMessage.getPayload().length;
    final int payloadRemainderSize = payloadSize % maxMtuSize;
    final int numberOfSegments = payloadSize / maxMtuSize;
    final Map<Integer, Segment> segments = new HashMap<>(numberOfSegments + 1);

    if (payloadSize == 0) {
      segments.put(0, new Segment(0, new byte[] {}, true, true));
      return segments;
    }

    int index = 0;
    for (; index < numberOfSegments; index++) {
      final byte[] segment = new byte[maxMtuSize];

      System.arraycopy(userMessage.getPayload(), index * maxMtuSize, segment, 0, maxMtuSize);

      final boolean lastSegment = numberOfSegments - 1 == index && payloadRemainderSize == 0;
      final boolean lastInGroup = index % groupMaxSize == 0 && !lastSegment && numberOfSegments > 1;

      segments.put(index, new Segment(index, segment, lastInGroup, lastSegment));
    }

    if (payloadRemainderSize != 0) {
      final byte[] segment = new byte[payloadRemainderSize];
      System.arraycopy(
          userMessage.getPayload(), index * maxMtuSize, segment, 0, payloadRemainderSize);
      segments.put(index, new Segment(index, segment, numberOfSegments == 0, true));
      index++;
    }

    messageSegmentCountDistribution.record(index);

    return segments;
  }

  /**
   * Constructs Invoke PDU wrapped in {@link NetworkMessageDto}
   *
   * @param transaction the WTP transaction
   * @param isGtr is group trailer
   * @param isTtr is transmission trailer
   * @param isRid indicates re-transmission
   * @param isTidNew is tid new
   * @return instance of {@link NetworkMessageDto}
   */
  static NetworkMessageDto createInvokePdu(
      final Transaction transaction,
      final boolean isGtr,
      final boolean isTtr,
      final boolean isRid,
      final boolean isTidNew) {
    final NetworkMessageDto networkMessage;
    final NetworkMessageDtoBuilder networkMessageBuilder =
        NetworkMessageDto.builder()
            .withAddress(transaction.getAddress())
            .withMessageId(transaction.getMessageId())
            .withProperties(transaction.getProperties());

    if (transaction.getWtpVersion() == WtpVersion.VERSION_2) {
      final InvokePdu2 invokePdu =
          InvokePdu2Builder.builder()
              .withGtr(isGtr)
              .withTtr(isTtr)
              .withRid(isRid)
              .withWtpTid(transaction.getWtpTid())
              .withTidNew(isTidNew)
              .withUserAck(true)
              .withTransactionClass(transaction.getTransactionClass())
              .withVid(transaction.getVehicleId())
              .buildInvokePdu2();
      final byte[] payload =
          InvokePduToByteArrayConverter.convert(
              invokePdu, transaction.getSegments().getFirstSegment().getPayload());
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    } else {
      final InvokePdu invokePdu =
          InvokePduBuilder.builder()
              .withGtr(isGtr)
              .withTtr(isTtr)
              .withRid(isRid)
              .withWtpTid(transaction.getWtpTid())
              .withTidNew(isTidNew)
              .withUserAck(true)
              .withTransactionClass(transaction.getTransactionClass())
              .buildInvokePdu();
      final byte[] payload =
          InvokePduToByteArrayConverter.convert(
              invokePdu, transaction.getSegments().getFirstSegment().getPayload());
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    }
    if (isTidNew) {
      tidNewCount.increment();
    }
    outgoingPduSizes.increment(networkMessage.getPayload().length);
    InitiatorMetrics.incrementOutgoingPdu(PduType.INVOKE, isRid, networkMessage.getAddress());
    return networkMessage;
  }

  /**
   * Constructs SegInvoke PDU wrapped in {@link NetworkMessageDto}
   *
   * @param transaction the WTP transaction
   * @param isGtr is group trailer
   * @param isTtr is transmission trailer
   * @param isRid indicates re-transmission
   * @param segment segment
   * @return instance of {@link NetworkMessageDto}
   */
  static NetworkMessageDto createSegInvokePdu(
      final Transaction transaction,
      final boolean isGtr,
      final boolean isTtr,
      final boolean isRid,
      final Segment segment) {
    final NetworkMessageDto networkMessage;
    final NetworkMessageDtoBuilder networkMessageBuilder =
        NetworkMessageDto.builder()
            .withAddress(transaction.getAddress())
            .withMessageId(transaction.getMessageId())
            .withProperties(transaction.getProperties());

    if (transaction.getWtpVersion() == WtpVersion.VERSION_2) {
      final SegInvokePdu2 segInvokePdu =
          SegInvokePdu2Builder.builder()
              .withGtr(isGtr)
              .withTtr(isTtr)
              .withRid(isRid)
              .withWtpTid(transaction.getWtpTid())
              .withPsn(segment.getPsn())
              .withVid(transaction.getVehicleId())
              .buildSegInvokePdu2();
      final byte[] payload =
          SegInvokePduToByteArrayConverter.convert(segInvokePdu, segment.getPayload());
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    } else {
      final SegInvokePdu segInvokePdu =
          SegInvokePduBuilder.builder()
              .withGtr(isGtr)
              .withTtr(isTtr)
              .withRid(isRid)
              .withWtpTid(transaction.getWtpTid())
              .withPsn(segment.getPsn())
              .buildSegInvokePdu();
      final byte[] payload =
          SegInvokePduToByteArrayConverter.convert(segInvokePdu, segment.getPayload());
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    }
    outgoingPduSizes.increment(networkMessage.getPayload().length);
    InitiatorMetrics.incrementOutgoingPdu(PduType.SEGINVOKE, isRid, networkMessage.getAddress());
    return networkMessage;
  }

  /**
   * Creates {@link UserStatusDto}
   *
   * @param transaction instance of {@link Transaction}
   * @return {@link UserStatusDto}
   */
  static UserStatusDto createUserStatusDelivered(final Transaction transaction) {
    final UserStatusDto userStatus =
        UserStatusDto.builder()
            .withMessageId(transaction.getMessageId())
            .withAddress(transaction.getAddress())
            .withProperties(transaction.getProperties())
            .withDelivered(true)
            .withAbortCode(AbortCode.TCE_PROVIDER_UNKNOWN)
            .build();
    final long roundTripTime = System.currentTimeMillis() - transaction.getCreationTime();
    deliveredCount.increment();
    transactionDurations.record(roundTripTime, TimeUnit.MILLISECONDS);
    return userStatus;
  }

  /**
   * Creates {@link UserStatusDto}
   *
   * @param transaction instance of {@link Transaction}
   * @param abortCode instance of {@link AbortCode} indicating the reason of unsuccessful delivery.
   *     Or {@link AbortCode#TCE_PROVIDER_UNKNOWN} if delivered successfully
   * @return {@link UserStatusDto}
   */
  static UserStatusDto createUserStatusAborted(
      final Transaction transaction, final AbortCode abortCode) {
    final UserStatusDto userStatus =
        UserStatusDto.builder()
            .withMessageId(transaction.getMessageId())
            .withAddress(transaction.getAddress())
            .withProperties(transaction.getProperties())
            .withDelivered(false)
            .withAbortCode(abortCode)
            .build();
    final long roundTripTime = System.currentTimeMillis() - transaction.getCreationTime();
    transactionDurations.record(roundTripTime, TimeUnit.MILLISECONDS);
    InitiatorMetrics.incrementOutgoingAbortStatus(abortCode);
    return userStatus;
  }

  /**
   * Creates {@link UserStatusDto}
   *
   * @param userMessage instance of {@link UserMessageDto}
   * @param abortCode instance of {@link AbortCode} indicating the reason of unsuccessful delivery.
   *     Or {@link AbortCode#TCE_PROVIDER_UNKNOWN} if delivered successfully
   * @return {@link UserStatusDto}
   */
  static UserStatusDto createUserStatusAborted(
      final UserMessageDto userMessage, final AbortCode abortCode) {
    final UserStatusDto userStatus =
        UserStatusDto.builder()
            .withMessageId(userMessage.getMessageId())
            .withAddress(userMessage.getAddress())
            .withProperties(userMessage.getProperties())
            .withDelivered(false)
            .withAbortCode(abortCode)
            .build();
    InitiatorMetrics.incrementOutgoingAbortStatus(abortCode);
    return userStatus;
  }

  /**
   * Constructs Ack PDU wrapped in {@link NetworkMessageDto}
   *
   * @param transaction the WTP transaction
   * @param isOtr is transaction outstanding
   * @return the network message
   */
  static NetworkMessageDto createAckPdu(final Transaction transaction, final boolean isOtr) {
    final NetworkMessageDto networkMessage;
    final NetworkMessageDtoBuilder networkMessageBuilder =
        NetworkMessageDto.builder()
            .withAddress(transaction.getAddress())
            .withMessageId(transaction.getMessageId())
            .withProperties(transaction.getProperties());

    if (transaction.getWtpVersion() == WtpVersion.VERSION_2) {
      final AckPdu2 ackPdu =
          AckPdu2Builder.builder()
              .withWtpTid(transaction.getWtpTid())
              .withOtr(isOtr)
              .withRid(false)
              .withVid(transaction.getVehicleId())
              .buildAckPdu2();
      final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu);
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    } else {
      final AckPdu ackPdu =
          AckPduBuilder.builder()
              .withWtpTid(transaction.getWtpTid())
              .withOtr(isOtr)
              .withRid(false)
              .buildAckPdu();
      final byte[] payload = AckPduToByteArrayConverter.convert(ackPdu);
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    }
    outgoingPduSizes.increment(networkMessage.getPayload().length);
    InitiatorMetrics.incrementOutgoingPdu(PduType.ACK, false, networkMessage.getAddress());
    return networkMessage;
  }

  /**
   * Constructs Abort PDU wrapped in {@link NetworkMessageDto}
   *
   * @param referenceNetworkMessage instance of {@link NetworkMessageDto}
   * @param modelCommon instance of {@link ModelCommon}
   * @param abortCode instance of {@link AbortCode}
   * @return {@link NetworkMessageDto}
   */
  static NetworkMessageDto createAbortPdu(
      final NetworkMessageDto referenceNetworkMessage,
      final ModelCommon modelCommon,
      final AbortCode abortCode) {
    final NetworkMessageDto networkMessage;
    final NetworkMessageDtoBuilder networkMessageBuilder =
        NetworkMessageDto.builder()
            .withAddress(referenceNetworkMessage.getAddress())
            .withProperties(referenceNetworkMessage.getProperties())
            .withMessageId(referenceNetworkMessage.getMessageId());

    if (modelCommon.getWtpVersion() == WtpVersion.VERSION_2) {
      final AbortPdu2 abortPdu =
          AbortPdu2Builder.builder()
              .withAbortCode(abortCode)
              .withWtpTid(modelCommon.getReverseWtpTid())
              .withVid(modelCommon.getVid())
              .buildAbortPdu2();
      final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    } else {
      final AbortPdu abortPdu =
          AbortPduBuilder.builder()
              .withAbortCode(abortCode)
              .withWtpTid(modelCommon.getReverseWtpTid())
              .buildAbortPdu();
      final byte[] payload = AbortPduToByteArrayConverter.convert(abortPdu);
      networkMessage = networkMessageBuilder.withPayload(payload).build();
    }
    outgoingPduSizes.increment(networkMessage.getPayload().length);
    InitiatorMetrics.incrementOutgoingPdu(PduType.ABORT, false, networkMessage.getAddress());
    InitiatorMetrics.incrementOutgoingAbort(abortCode);
    return networkMessage;
  }

  /** Private constructor to prevent initialization */
  private InitiatorUtils() {
    throw new IllegalStateException();
  }
}
