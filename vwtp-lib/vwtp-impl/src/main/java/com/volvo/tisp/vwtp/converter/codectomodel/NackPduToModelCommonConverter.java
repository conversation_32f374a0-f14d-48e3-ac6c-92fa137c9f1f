package com.volvo.tisp.vwtp.converter.codectomodel;

import com.volvo.tisp.vwtp.codec.NackPdu;
import com.volvo.tisp.vwtp.codec.NackPdu2;
import com.volvo.tisp.vwtp.codec.NackPdu2_mp;
import com.volvo.tisp.vwtp.codec.NackPdu_mp;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.NackModel;
import com.volvo.tisp.vwtp.model.TpiCommon;

/**
 * Converter utility for converting {@link NackPdu} or {@link NackPdu2} to instance of {@link
 * ModelCommon}
 */
public class NackPduToModelCommonConverter {
  /** Private constructor to prevent instantiation of utility class */
  private NackPduToModelCommonConverter() {}

  /**
   * Convert {@link NackPdu} to {@link NackModel}
   *
   * @param nackPdu {@link NackPdu}
   * @return {@link NackModel}
   */
  public static NackModel convert(final NackPdu nackPdu) {
    final NackPdu_mp missingPsn = nackPdu.getMp();
    final int missingPsnArraySize = (int) missingPsn.getSize();
    final int[] missingPsnArray = new int[missingPsnArraySize];
    for (int index = 0; index < missingPsnArraySize; index++) {
      missingPsnArray[index] = (int) missingPsn.get(index);
    }
    return NackModel.builder()
        .withRid(nackPdu.getRid())
        .withWtpTid(nackPdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_1)
        .withMissingPsn(missingPsnArray)
        .withTpiArray(TpiCommon.EMPTY)
        .build();
  }

  /**
   * Convert {@link NackPdu2} to {@link NackModel}
   *
   * @param nackPdu {@link NackPdu2}
   * @return {@link NackModel}
   */
  public static NackModel convert(final NackPdu2 nackPdu) {
    final NackPdu2_mp missingPsn = nackPdu.getMp();
    final int missingPsnArraySize = (int) missingPsn.getSize();
    final int[] missingPsnArray = new int[missingPsnArraySize];
    for (int index = 0; index < missingPsnArraySize; index++) {
      missingPsnArray[index] = (int) missingPsn.get(index);
    }
    return NackModel.builder()
        .withRid(nackPdu.getRid())
        .withWtpTid(nackPdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_2)
        .withMissingPsn(missingPsnArray)
        .withVid(nackPdu.getVid())
        .withTpiArray(TpiCommon.EMPTY)
        .build();
  }
}
