package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.AbortCodeStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidReverseDirectionStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.AbortPdu;

public interface AbortPduBuilder<B>
    extends AbortCodeStep<B>, WtpTidStep<B>, WtpTidReverseDirectionStep<B> {
  AbortPdu buildAbortPdu();

  static <T extends AbortPduBuilder<T>> AbortPduBuilder<T> builder() {
    return new PduBuilder<>();
  }
}
