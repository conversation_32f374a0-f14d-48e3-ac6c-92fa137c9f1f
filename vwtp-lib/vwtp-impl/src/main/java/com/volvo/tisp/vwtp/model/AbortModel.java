package com.volvo.tisp.vwtp.model;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import java.util.Arrays;

/** Abort protocol data unit model */
public final class AbortModel extends ModelCommon {
  private final AbortCode abortCode;

  private AbortModel(final Builder builder) {
    super(builder);
    abortCode = builder.abortCode;
  }

  /**
   * @return instance of an {@link Builder}
   */
  public static Builder builder() {
    return new Builder();
  }

  @Override
  public PduType getPduType() {
    return PduType.ABORT;
  }

  @Override
  public boolean isRid() {
    return false;
  }

  /**
   * @return enumerator of abort code {@link AbortCode}
   */
  public AbortCode getAbortCode() {
    return abortCode;
  }

  @Override
  public String toString() {
    return "AbortModel{"
        + "rid="
        + isRid()
        + ", abortCode="
        + getAbortCode()
        + ", wtpTid="
        + getWtpTid()
        + ", wtpVersion="
        + getWtpVersion()
        + ", vid="
        + getVid()
        + ", tpiArray="
        + Arrays.toString(getTpiArray())
        + ", hashCode="
        + Integer.toHexString(hashCode())
        + '}';
  }

  /** Builder for abort PDU models */
  public static class Builder extends ModelCommon.Builder<Builder, AbortModel> {
    private AbortCode abortCode;

    private Builder() {}

    @Override
    protected Builder getSelf() {
      return this;
    }

    /**
     * @param abortCode enumerator of abort code {@link AbortCode}
     * @return builder
     */
    public Builder withAbortCode(final AbortCode abortCode) {
      Preconditions.checkNotNull(abortCode, "Abort code must not be null");
      this.abortCode = abortCode;
      return this;
    }

    @Override
    public AbortModel build() {
      return new AbortModel(this);
    }
  }
}
