package com.volvo.tisp.vwtp.converter.codectomodel;

import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.model.AckModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.TpiCommon;

/**
 * Converter utility for converting {@link AckPdu} or {@link AckPdu2} to instance of {@link
 * ModelCommon}
 */
public class AckPduToModelCommonConverter {
  /** Private constructor to prevent instantiation of utility class */
  private AckPduToModelCommonConverter() {}

  /**
   * Convert {@link AckPdu} to {@link AckModel}
   *
   * @param ackPdu {@link AckPdu}
   * @param tpiArray PSN TPI data representing packet sequence number to be acknowledged in SAR
   * @return {@link AckModel}
   */
  public static AckModel convert(final AckPdu ackPdu, final TpiCommon[] tpiArray) {
    return AckModel.builder()
        .withOtr(ackPdu.getOtr())
        .withRid(ackPdu.getRid())
        .withWtpTid(ackPdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_1)
        .withTpiArray(tpiArray)
        .build();
  }

  /**
   * Convert {@link AckPdu2} to {@link AckModel}
   *
   * @param ackPdu {@link AckPdu2}
   * @param tpiArray PSN TPI data representing packet sequence number to be acknowledged in SAR
   * @return {@link AckModel}
   */
  public static AckModel convert(final AckPdu2 ackPdu, final TpiCommon[] tpiArray) {
    return AckModel.builder()
        .withOtr(ackPdu.getOtr())
        .withRid(ackPdu.getRid())
        .withWtpTid(ackPdu.getTid())
        .withWtpVersion(WtpVersion.VERSION_2)
        .withVid(ackPdu.getVid())
        .withTpiArray(tpiArray)
        .build();
  }
}
