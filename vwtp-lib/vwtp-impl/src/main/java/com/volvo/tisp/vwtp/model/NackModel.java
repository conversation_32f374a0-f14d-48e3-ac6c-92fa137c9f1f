package com.volvo.tisp.vwtp.model;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.constants.PduType;
import java.util.Arrays;
import org.apache.commons.lang3.ArrayUtils;

/** Negative acknowledgement protocol data unit model */
public final class NackModel extends ModelCommon {
  private final int[] missingPsn;

  private NackModel(final Builder builder) {
    super(builder);
    missingPsn = builder.missingPsn;
  }

  /**
   * @return instance of an {@link Builder}
   */
  public static Builder builder() {
    return new Builder();
  }

  @Override
  public PduType getPduType() {
    return PduType.NACK;
  }

  /**
   * @return array of missing packet sequence numbers
   */
  public int[] getMissingPsn() {
    return missingPsn;
  }

  @Override
  public String toString() {
    return "NackModel{"
        + "rid="
        + isRid()
        + ", wtpTid="
        + getWtpTid()
        + ", wtpVersion="
        + getWtpVersion()
        + ", missingPsn="
        + Arrays.toString(getMissingPsn())
        + ", vid="
        + getVid()
        + ", tpiArray="
        + Arrays.toString(getTpiArray())
        + ", hashCode="
        + Integer.toHexString(hashCode())
        + '}';
  }

  /** Builder for negative acknowledgement PDU models */
  public static class Builder extends ModelCommon.Builder<Builder, NackModel> {
    private int[] missingPsn = ArrayUtils.EMPTY_INT_ARRAY;

    private Builder() {}

    @Override
    protected Builder getSelf() {
      return this;
    }

    /**
     * @param missingPsn array of missing packet sequence numbers
     * @return builder
     */
    public Builder withMissingPsn(final int... missingPsn) {
      Preconditions.checkArgument(
          missingPsn != null && missingPsn.length <= 256,
          "Number of missing packet sequence numbers must be within [0 .. 256]");
      this.missingPsn = missingPsn;
      return this;
    }

    @Override
    public NackModel build() {
      return new NackModel(this);
    }
  }
}
