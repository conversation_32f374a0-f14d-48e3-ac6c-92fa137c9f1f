package com.volvo.tisp.vwtp.converter.codectomodel;

import com.volvo.tisp.vwtp.codec.ASNException;
import com.volvo.tisp.vwtp.codec.PERStream;
import com.volvo.tisp.vwtp.codec.TpiIdentity;
import com.volvo.tisp.vwtp.codec.TpiPdu;
import com.volvo.tisp.vwtp.model.PsnTpiModel;
import com.volvo.tisp.vwtp.model.TpiCommon;

/** Converter utility for converting {@link PERStream} to {@link TpiCommon} array */
public class PERStreamToTpiCommonArray {
  private PERStreamToTpiCommonArray() {}

  /**
   * Convert {@link PERStream} to {@link TpiCommon} array
   *
   * @param perStream {@link PERStream}
   * @return {@link TpiCommon} array
   * @throws ASNException whenever decoding of {@link PERStream} fails
   */
  public static TpiCommon[] convert(final PERStream perStream) throws ASNException {
    boolean continueFlag = true;
    TpiCommon[] tpiArray = TpiCommon.EMPTY;
    final TpiPdu tpi = new TpiPdu();
    while (continueFlag) {
      tpi.decode(perStream);
      if (tpi.getTpiId() == TpiIdentity.E_PSN) {
        byte[] tpiData;
        tpiData = tpi.getPdu().getShort().getData();
        // TODO: if tpiData.length > 1 should report an Abort pdu with error
        // AbortCode.TCE_PROVIDER_NOT_IMPLEMENTED_EXTENDED_SAR
        final PsnTpiModel psnTpiModel =
            PsnTpiModel.builder().withPacketSequenceNumber(tpiData[0] & 0xFF).build();
        tpiArray = new TpiCommon[] {psnTpiModel};
      }
      continueFlag = tpi.getCon();
    }
    return tpiArray;
  }
}
