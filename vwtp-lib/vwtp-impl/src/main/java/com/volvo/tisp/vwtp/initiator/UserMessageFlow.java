package com.volvo.tisp.vwtp.initiator;

import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.codec.InvokePdu;
import com.volvo.tisp.vwtp.configuration.InitiatorProperties;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.TransactionStatus;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.MessageDto;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.DistributionSummary;
import io.micrometer.core.instrument.Metrics;
import java.time.Duration;
import java.util.concurrent.atomic.AtomicLong;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitFailureHandler;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.core.scheduler.Scheduler;
import reactor.util.Logger;
import reactor.util.Loggers;
import reactor.util.context.Context;

/** Implementation of the WTP Initiator Flow */
@Component("initiatorUserMessageFlow")
public class UserMessageFlow implements FlowComposer<UserMessageDto, Void> {
  private static final Logger logger = Loggers.getLogger(UserMessageFlow.class);
  private static final DistributionSummary messagePayloadDistribution =
      DistributionSummary.builder(MetricNames.INITIATOR_INCOMING_MESSAGES_PAYLOADS)
          .description("Distribution of incoming user message payload sizes")
          .baseUnit("segments")
          .register(Metrics.globalRegistry);
  private static final Counter incommingUserMessageCounter =
      Counter.builder(MetricNames.INITIATOR_INCOMING_MESSAGES)
          .description("Counter for an incoming user message")
          .register(Metrics.globalRegistry);

  private static final String NETWORK_SINK_CONTEXT_KEY =
      "INITIATOR-NETWORK-SINK-" + UserMessageFlow.class.hashCode();
  private static final String STATUS_SINK_CONTEXT_KEY =
      "INITIATOR-STATUS-SINK-" + UserMessageFlow.class.hashCode();
  private static final long MAX_TID = 32768L; // 2^15

  private final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow;
  private final FlowComposer<UserStatusDto, Void> outgoingUserStatusFlow;
  private final InitiatorProperties initiatorProperties;
  private final CacheService cacheService;

  /**
   * Build and launch the Initiator
   *
   * @param outgoingNetworkMessageFlow instance of {@link FlowComposer} &lt;{@link
   *     NetworkMessageDto}, {@link Void}&gt;
   * @param outgoingUserStatusFlow instance of {@link FlowComposer} &lt;{@link UserStatusDto},
   *     {@link Void}&gt;
   * @param initiatorProperties instance of {@link InitiatorProperties}
   * @param cacheService instance of {@link CacheService}
   */
  protected UserMessageFlow(
      @Qualifier("outgoingInitiatorNetworkMessageFlow")
          final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow,
      @Qualifier("outgoingInitiatorUserStatusFlow")
          final FlowComposer<UserStatusDto, Void> outgoingUserStatusFlow,
      final InitiatorProperties initiatorProperties,
      final CacheService cacheService) {
    this.outgoingNetworkMessageFlow = outgoingNetworkMessageFlow;
    this.outgoingUserStatusFlow = outgoingUserStatusFlow;
    this.initiatorProperties = initiatorProperties;
    this.cacheService = cacheService;
  }

  @Override
  public Publisher<Void> apply(final Flux<UserMessageDto> flux) {
    logger.info("Composing reactive flow");
    final Sinks.Many<UserStatusDto> statusSink =
        Sinks.unsafe().many().unicast().onBackpressureError();
    final Sinks.Many<NetworkMessageDto> networkSink =
        Sinks.unsafe().many().unicast().onBackpressureError();

    return Flux.merge(
        statusSink.asFlux().transform(outgoingUserStatusFlow),
        Flux.merge(
                networkSink.asFlux(),
                flux.doOnNext(this::logMetrics)
                    .flatMap(this::validateUserMessage)
                    .map(this::createTransaction)
                    .map(cacheService::initiatorCreateWtpTransaction)
                    .flatMap(this::scheduleResendTimer)
                    .map(this::createNetworkMessageWithInvokePdu)
                    .transform(this::completeProcessorsUponTermination)
                    .contextWrite(Context.of(NETWORK_SINK_CONTEXT_KEY, networkSink))
                    .contextWrite(Context.of(STATUS_SINK_CONTEXT_KEY, statusSink)))
            .transform(outgoingNetworkMessageFlow));
  }

  /**
   * Completes the sub flows inside {@link Flux} {@link Context} when main flow completes
   *
   * @param flux {@link Flux}&lt;{@link NetworkMessageDto}&gt;
   * @return instance of {@link Publisher}&lt;{@link NetworkMessageDto}&gt;
   */
  public Publisher<NetworkMessageDto> completeProcessorsUponTermination(
      final Flux<NetworkMessageDto> flux) {
    return Flux.deferContextual(
        context ->
            flux.doOnComplete(
                () -> {
                  final Sinks.Many<NetworkMessageDto> networkSink =
                      context.get(NETWORK_SINK_CONTEXT_KEY);
                  networkSink.emitComplete(EmitFailureHandler.FAIL_FAST);
                  final Sinks.Many<UserStatusDto> statusSink = context.get(STATUS_SINK_CONTEXT_KEY);
                  statusSink.emitComplete(EmitFailureHandler.FAIL_FAST);
                }));
  }

  /**
   * Logs received {@link UserMessageDto} metrics
   *
   * @param userMessage instance of {@link MessageDto}
   */
  private void logMetrics(final UserMessageDto userMessage) {
    incommingUserMessageCounter.increment();
    messagePayloadDistribution.record(userMessage.getPayload().length);
  }

  /**
   * Validates if {@link UserMessageDto} is valid
   *
   * @param userMessage instance of {@link UserMessageDto}
   * @return true if valid
   */
  private Publisher<UserMessageDto> validateUserMessage(final UserMessageDto userMessage) {
    return Mono.deferContextual(
        context -> {
          logger.debug("Initiator - Entering validateUserMessage");

          final int payloadSize = userMessage.getPayload().length;
          int maxPayloadSize;

          if (userMessage.getWtpVersion() == WtpVersion.VERSION_2) {
            maxPayloadSize = initiatorProperties.getVersion2MaxPayloadSize();
          } else {
            maxPayloadSize = initiatorProperties.getVersion1MaxPayloadSize();
          }

          final Sinks.Many<UserStatusDto> statusSink = context.get(STATUS_SINK_CONTEXT_KEY);
          if (userMessage.getTransactionClass() != TransactionClass.CLASS_1) {
            logger.warn(
                "Initiator - only TransactionClass.CLASS_1 is supported, received transaction class {} for {}",
                userMessage.getTransactionClass().getValue(),
                userMessage.getAddress());
            final UserStatusDto userStatus =
                InitiatorUtils.createUserStatusAborted(
                    userMessage, AbortCode.TCE_PROVIDER_NOT_IMPLEMENTED_CLASS);
            final EmitResult emitResult = statusSink.tryEmitNext(userStatus);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::validateUserMessage",
                  emitResult,
                  userStatus);
            }
            return Mono.empty();
          } else if (payloadSize > maxPayloadSize) {
            logger.warn("Payload with length '{}' is too large", payloadSize);
            final UserStatusDto userStatus =
                InitiatorUtils.createUserStatusAborted(
                    userMessage, AbortCode.TCE_PROVIDER_MESSAGE_TOO_LARGE);
            final EmitResult emitResult = statusSink.tryEmitNext(userStatus);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::validateUserMessage",
                  emitResult,
                  userStatus);
            }
            return Mono.empty();
          }

          return Mono.just(userMessage);
        });
  }

  /**
   * Creates {@link Transaction} from {@link UserMessageDto}
   *
   * @param userMessage instance of {@link UserMessageDto}
   * @return instance of {@link Transaction}
   */
  private Transaction createTransaction(final UserMessageDto userMessage) {
    logger.debug("Initiator - Entering createTransaction");

    final AtomicLong lastWtpTid =
        cacheService.initiatorReadOrCreateLastWtpTid(userMessage.getAddress());
    final long wtpTid = lastWtpTid.updateAndGet(prev -> (prev + 1) % MAX_TID);
    return new Transaction(wtpTid, userMessage, initiatorProperties);
  }

  /**
   * Creates {@link NetworkMessageDto} with {@link InvokePdu} inside
   *
   * @param transaction instance of {@link Transaction}
   * @return transaction instance of {@link NetworkMessageDto}
   */
  private NetworkMessageDto createNetworkMessageWithInvokePdu(final Transaction transaction) {
    return InitiatorUtils.createInvokePdu(
        transaction, true, !transaction.isSegmented(), false, transaction.isTidNew());
  }

  /**
   * Schedules ResendTimer with configuration from transaction
   *
   * @param transaction instance of {@link Transaction}
   * @return instance of {@link Transaction}
   */
  public Publisher<Transaction> scheduleResendTimer(final Transaction transaction) {
    return Mono.deferContextual(
        context -> {
          final Scheduler scheduler = context.get(Scheduler.class);
          final Duration[] durations = transaction.getConfiguration().getTimerR();
          final Disposable timerHandle =
              Flux.range(1, durations.length)
                  .delayUntil(
                      counter -> Mono.just(counter).delayElement(durations[counter - 1], scheduler))
                  .publishOn(scheduler)
                  .doOnCancel(() -> logger.debug("Disposing ResendTimer for {}", transaction))
                  .concatMap(this::fireResendTimer)
                  .subscribe(
                      null,
                      throwable -> logger.error("", throwable),
                      null,
                      Context.of(context).put(Transaction.class, transaction));
          transaction.updateActiveTimer(timerHandle);
          logger.debug("Scheduled ResendTimer (R) for {}", transaction);
          return Mono.just(transaction);
        });
  }

  /**
   * Executes set of actions when ResendTimer fires
   *
   * @param counter resent counter
   * @return instance of {@link Context} {@link Publisher}
   */
  private Publisher<Void> fireResendTimer(final Integer counter) {
    return Mono.deferContextual(
        context -> {
          final Transaction transaction = context.get(Transaction.class);
          final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
          final Sinks.Many<UserStatusDto> statusSink = context.get(STATUS_SINK_CONTEXT_KEY);
          final Duration[] durations = transaction.getConfiguration().getTimerR();
          if (counter < durations.length) {
            if (transaction.getStatus() == TransactionStatus.INPROGRESS) {
              logger.debug(
                  "Initiator - ResendTimer ({}) userHookContinue Message ID {}",
                  counter,
                  transaction.getMessageId());
              final NetworkMessageDto networkMessage =
                  InitiatorUtils.createInvokePdu(
                      transaction, true, !transaction.isSegmented(), true, transaction.isTidNew());
              final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::fireResendTimer",
                    emitResult,
                    networkMessage);
              }
              transaction.resetRoundtripMetric();
            }
          } else {
            if (TransactionStatus.INPROGRESS
                == transaction.getAndSetTransactionStatus(TransactionStatus.COMPLETED)) {
              logger.debug(
                  "Initiator - ResendTimer ({}) userHookComplete Message ID {}",
                  counter,
                  transaction.getMessageId());
              cacheService.initiatorDeleteWtpTransaction(transaction);
              final UserStatusDto userStatus =
                  InitiatorUtils.createUserStatusAborted(
                      transaction, AbortCode.TCE_PROVIDER_NO_RESPONSE);
              final EmitResult emitResult = statusSink.tryEmitNext(userStatus);
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::fireResendTimer",
                    emitResult,
                    userStatus);
              }
            }
          }
          return Mono.empty();
        });
  }
}
