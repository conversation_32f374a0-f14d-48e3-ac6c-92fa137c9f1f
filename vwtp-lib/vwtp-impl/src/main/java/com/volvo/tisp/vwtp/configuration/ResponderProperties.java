package com.volvo.tisp.vwtp.configuration;

import static com.volvo.tisp.vwtp.constants.PropertyKey.*;
import static com.volvo.tisp.vwtp.util.LogUtil.*;
import static java.util.Locale.US;

import java.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;

public class ResponderProperties implements DisposableBean {
  private static final Logger logger = LoggerFactory.getLogger(ResponderProperties.class);

  private final Duration userAcknowledgementInterval;
  private final Duration transactionTimeoutInterval;
  private final Duration nackDelayInterval;
  private final long maxActiveTransactions;

  protected ResponderProperties(
      @Value("${wtp.timer.a:5000}") final long timerA,
      @Value("${wtp.timer.w:65000}") final long timerW,
      @Value("${wtp.timer.sn:5000}") final long timerSN,
      @Value("${wtp.responder-active-transactions:100000}") final long maxActiveTransactions) {
    Assert.isTrue(
        timerA > 0, String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_A, ERROR_NOT_POSITIVE));
    Assert.isTrue(
        timerW > 0, String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_W, ERROR_NOT_POSITIVE));
    Assert.isTrue(
        timerSN > 0, String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_SN, ERROR_NOT_POSITIVE));
    Assert.isTrue(
        maxActiveTransactions > 0,
        String.format(
            US, FORMAT_TABLE_OUTPUT, WTP_RESPONDER_ACTIVE_TRANSACTIONS, ERROR_NOT_POSITIVE));
    userAcknowledgementInterval = Duration.ofMillis(timerA);
    transactionTimeoutInterval = Duration.ofMillis(timerW);
    nackDelayInterval = Duration.ofMillis(timerSN);
    this.maxActiveTransactions = maxActiveTransactions;

    logProperties();
  }

  /**
   *
   *
   * <table style="border: 0px">
   * <caption>&nbsp;</caption>
   * <tr style="text-align: left">
   * <th>Also known as:</th>
   * <td>Acknowledgement interval (A)</td>
   * </tr>
   * <tr style="text-align: left">
   * <th>Reference:</th>
   * <td><a href="https://confluence1.srv.volvo.com:9443/download/attachments/54177452/volvo-wap-224-wtp-20170615-a.pdf">volvo-wap-224-wtp-20170615-a.pdf</a>
   * </td>
   * </tr>
   * <tr style="text-align: left">
   * <th>Page:</th>
   * <td>78</td>
   * </tr>
   * </table>
   *
   * @return {@link Duration} of user response wait interval
   */
  public Duration getUserAcknowledgementInterval() {
    return userAcknowledgementInterval;
  }

  /**
   *
   *
   * <table style="border: 0px">
   * <caption>&nbsp;</caption>
   * <tr style="text-align: left">
   * <th>Also known as:</th>
   * <td>Wait timeout interval (W)</td>
   * </tr>
   * <tr style="text-align: left">
   * <th>Reference:</th>
   * <td><a href="https://confluence1.srv.volvo.com:9443/download/attachments/54177452/volvo-wap-224-wtp-20170615-a.pdf">volvo-wap-224-wtp-20170615-a.pdf</a>
   * </td>
   * </tr>
   * <tr style="text-align: left">
   * <th>Page:</th>
   * <td>78</td>
   * </tr>
   * </table>
   *
   * @return {@link Duration} of transaction timeout interval
   */
  public Duration getTransactionTimeoutInterval() {
    return transactionTimeoutInterval;
  }

  /**
   *
   *
   * <table style="border: 0px">
   * <caption>&nbsp;</caption>
   * <tr style="text-align: left">
   * <th>Also known as:</th>
   * <td>SAR Nack delay (SN)</td>
   * </tr>
   * <tr style="text-align: left">
   * <th>Reference:</th>
   * <td><a href="https://confluence1.srv.volvo.com:9443/download/attachments/54177452/volvo-wap-224-wtp-20170615-a.pdf">volvo-wap-224-wtp-20170615-a.pdf</a>
   * </td>
   * </tr>
   * <tr style="text-align: left">
   * <th>Page:</th>
   * <td>78</td>
   * </tr>
   * </table>
   *
   * @return {@link Duration} of nackDelayInterval
   */
  public Duration getNackDelayInterval() {
    return nackDelayInterval;
  }

  /**
   * @return Maximum allowed number of active responder transactions
   */
  public long getMaxActiveTransactions() {
    return maxActiveTransactions;
  }

  private void logProperties() {
    if (logger.isInfoEnabled()) {
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, "VWTP RESPONDER CONFIG", ""));
      logger.info("");
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_A, userAcknowledgementInterval));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_W, transactionTimeoutInterval));
      logger.info(String.format(US, FORMAT_TABLE_OUTPUT, WTP_TIMER_SN, nackDelayInterval));
      logger.info(
          String.format(
              US, FORMAT_TABLE_OUTPUT, WTP_RESPONDER_ACTIVE_TRANSACTIONS, maxActiveTransactions));
    }
  }

  @Override
  public void destroy() throws Exception {
    logProperties();
  }
}
