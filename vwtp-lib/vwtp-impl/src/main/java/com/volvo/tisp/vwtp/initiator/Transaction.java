package com.volvo.tisp.vwtp.initiator;

import com.volvo.tisp.vwtp.configuration.InitiatorProperties;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.TransactionStatus;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.dto.UserMessageDto;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Timer;
import java.net.URI;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.Disposable;
import reactor.core.Disposable.Swap;
import reactor.core.Disposables;

/** Represents a state of a single VWTP initiator transaction */
public final class Transaction {
  private static final Disposable DISPOSED = Disposables.disposed();
  private static final Logger logger = LoggerFactory.getLogger(Transaction.class);
  private static final Counter messageSegmentedCount =
      Counter.builder(MetricNames.INITIATOR_INCOMING_MESSAGES_SEGMENTED)
          .description("Count of received segmented user messages")
          .register(Metrics.globalRegistry);
  private static final Timer roundtripDurations =
      Timer.builder(MetricNames.INITIATOR_ROUNDTRIP_DURATIONS)
          .description("Duration between transmission and response")
          .register(Metrics.globalRegistry);

  private final long wtpTid;
  private final long vehicleId;
  private final URI address;
  private final String messageId;
  private final WtpVersion wtpVersion;
  private final TransactionClass transactionClass;
  private final boolean tidNew;
  private final boolean segmented;
  private final Map<String, String> properties;
  private final InitiatorProperties initiatorProperties;
  private final InitiatorSegments segments;
  private final long creationTime;

  private volatile long transmissionTime;

  private final Swap timerHandleContainer = Disposables.swap();
  private final AtomicReference<TransactionStatus> status =
      new AtomicReference<>(TransactionStatus.INPROGRESS);
  private final AtomicInteger expectedResponderPsn = new AtomicInteger(0);
  private final AtomicInteger currentPsnFromResponse = new AtomicInteger(-1);

  public Transaction(
      final long wtpTid,
      final UserMessageDto userMessage,
      final InitiatorProperties initiatorProperties) {
    Map<Integer, Segment> segmentMap;
    if (userMessage.getWtpVersion() == WtpVersion.VERSION_2) {
      segmentMap =
          InitiatorUtils.createSegments(
              userMessage,
              initiatorProperties.getVersion2MaxPduPayloadSize(),
              initiatorProperties.getGroupSize());
    } else {
      segmentMap =
          InitiatorUtils.createSegments(
              userMessage,
              initiatorProperties.getVersion1MaxPduPayloadSize(),
              initiatorProperties.getGroupSize());
    }
    if (segmentMap.size() > 1) {
      logger.debug("Initiator - Segmentation Required");
      messageSegmentedCount.increment();
      segmented = true;
    } else {
      logger.debug("Initiator - Segmentation Not Required");
      segmented = false;
    }
    segments = new InitiatorSegments(segmentMap, initiatorProperties.getGroupSize());

    this.wtpTid = wtpTid;
    vehicleId = userMessage.getVehicleId();
    address = userMessage.getAddress();
    messageId = userMessage.getMessageId();
    wtpVersion = userMessage.getWtpVersion();
    tidNew = wtpTid == 0;
    properties = userMessage.getProperties();
    this.initiatorProperties = initiatorProperties.cloneWithMessageProperties(properties);
    transactionClass = userMessage.getTransactionClass();

    timerHandleContainer.update(DISPOSED);
    creationTime = transmissionTime = System.currentTimeMillis();
  }

  public int getAndUpdateCurrentExpectedResponderPsn(final int acknowledged) {
    return expectedResponderPsn.getAndAccumulate(acknowledged, this::expectedPsnAccumulator);
  }

  private int expectedPsnAccumulator(final int current, final int acknowledged) {
    if (current == acknowledged) {
      final int maxPsn = segments.getMaxPsnValue();
      int expected = current + segments.getMaxSegmentsInGroup();
      expected = expected > maxPsn ? maxPsn : expected;
      logger.debug("Initiator - expectedPsnAccumulator: expected {}", expected);
      return expected;
    }
    return current;
  }

  public int getCurrentPsnFromResponse() {
    final int psn = currentPsnFromResponse.get();
    logger.debug("Initiator - getCurrentPsnFromResponse psn {}", psn);
    return psn;
  }

  public void setCurrentPsnFromResponse(final int currentPsnFromResponse) {
    logger.debug(
        "Initiator - setCurrentPsnFromResponse currentPsnFromResponse {}", currentPsnFromResponse);
    this.currentPsnFromResponse.set(currentPsnFromResponse);
  }

  public int getExpectedResponderPsn() {
    final int psn = expectedResponderPsn.get();
    logger.debug("Initiator - getExpectedResponderPsn expectedResponderPsn {}", psn);
    return psn;
  }

  /**
   * Records {@link MetricNames#INITIATOR_ROUNDTRIP_DURATIONS} metric and resets transmission time
   */
  public void recordRoundtripMetric() {
    long currentTime = System.currentTimeMillis();
    final long roundTripTime = currentTime - transmissionTime;
    transmissionTime = currentTime;
    roundtripDurations.record(roundTripTime, TimeUnit.MILLISECONDS);
  }

  /**
   * Resets transmission time used in recording {@link MetricNames#INITIATOR_ROUNDTRIP_DURATIONS}
   * metric
   */
  public void resetRoundtripMetric() {
    transmissionTime = System.currentTimeMillis();
  }

  public boolean isSegmented() {
    return segmented;
  }

  public String getMessageId() {
    return messageId;
  }

  public long getCreationTime() {
    return creationTime;
  }

  public boolean isTidNew() {
    return tidNew;
  }

  public Map<String, String> getProperties() {
    return properties;
  }

  public InitiatorProperties getConfiguration() {
    return initiatorProperties;
  }

  public TransactionStatus getStatus() {
    return status.get();
  }

  public TransactionStatus getAndSetTransactionStatus(final TransactionStatus transactionStatus) {
    return this.status.getAndSet(transactionStatus);
  }

  public void updateActiveTimer(final Disposable disposable) {
    timerHandleContainer.update(disposable);
  }

  /** Disposes current transaction timer (if any) */
  public void disposeActiveTimer() {
    timerHandleContainer.get().dispose();
  }

  public URI getAddress() {
    return address;
  }

  public long getWtpTid() {
    return wtpTid;
  }

  public long getVehicleId() {
    return vehicleId;
  }

  public WtpVersion getWtpVersion() {
    return wtpVersion;
  }

  public TransactionClass getTransactionClass() {
    return transactionClass;
  }

  public InitiatorSegments getSegments() {
    return segments;
  }

  @Override
  public boolean equals(final Object o) {
    if (this == o) {
      return true;
    }

    if (o instanceof Transaction that) {
      return new EqualsBuilder()
          .append(address, that.address)
          .append(wtpTid, that.wtpTid)
          .append(messageId, that.messageId)
          .isEquals();
    }
    return false;
  }

  @Override
  public int hashCode() {
    return new HashCodeBuilder(17, 37)
        .append(address)
        .append(wtpTid)
        .append(messageId)
        .toHashCode();
  }

  @Override
  public String toString() {
    return "Transaction{"
        + "messageId="
        + messageId
        + ", status="
        + status
        + ", wtpTid="
        + wtpTid
        + ", address="
        + address
        + ", vehicleId="
        + vehicleId
        + ", wtpVersion="
        + wtpVersion
        + ", transactionClass="
        + transactionClass
        + ", segmented="
        + segmented
        + ", tidNew="
        + tidNew
        + ", properties="
        + properties
        + '}';
  }
}
