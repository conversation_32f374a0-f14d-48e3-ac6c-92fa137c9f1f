package com.volvo.tisp.vwtp.constants;

public enum TransactionClass {
  CLASS_0(0L),
  CLASS_1(1L),
  CLASS_2(2L),
  UNKNOWN(-1);

  private final long value;

  TransactionClass(final long newValue) {
    value = newValue;
  }

  public static TransactionClass fromValue(final long value) {
    if (value == 0L) {
      return TransactionClass.CLASS_0;
    } else if (value == 1L) {
      return TransactionClass.CLASS_1;
    } else if (value == 2L) {
      return TransactionClass.CLASS_2;
    } else {
      return TransactionClass.UNKNOWN;
    }
  }

  public long getValue() {
    return value;
  }
}
