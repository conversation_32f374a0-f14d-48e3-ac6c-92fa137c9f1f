package com.volvo.tisp.vwtp.dto;

import com.google.common.io.BaseEncoding;
import java.net.URI;
import java.util.Collections;
import java.util.Map;
import org.apache.commons.lang3.builder.Builder;

/** Parent for all data transfer objects interfacing VWTP */
public abstract class MessageDto {
  protected static final BaseEncoding HEX_ENCODER =
      BaseEncoding.base16().omitPadding().upperCase().withSeparator(" ", 2);
  private final String messageId;
  private final URI address;
  private final Map<String, String> properties;

  /**
   * @param builder {@link MessageDtoBuilder}
   */
  protected MessageDto(final MessageDtoBuilder<?, ?> builder) {
    messageId = builder.messageId;
    address = builder.address;
    properties = builder.properties;
  }

  /**
   * Returns Message ID
   *
   * @return Message ID
   */
  public String getMessageId() {
    return messageId;
  }

  /**
   * Returns instance of {@link URI} as Address
   *
   * @return instance of {@link URI} as Address
   */
  public URI getAddress() {
    return address;
  }

  /**
   * Returns property {@link Map}
   *
   * @deprecated since 2023-05-15
   * @return property {@link Map}
   */
  @Deprecated(since = "2023-05-15", forRemoval = true)
  public Map<String, String> getProperties() {
    return properties;
  }

  /**
   * Builder for {@link MessageDto}
   *
   * @param <B> super-type of {@link MessageDtoBuilder} a.k.a. - Builder
   * @param <M> super-type of {@link MessageDto} a.k.a. - Message
   */
  protected abstract static class MessageDtoBuilder<
          B extends MessageDtoBuilder<B, M>, M extends MessageDto>
      implements Builder<M> {
    private static final URI EMPTY_URI = URI.create("none:empty");

    private URI address = EMPTY_URI;
    private String messageId = "";
    private Map<String, String> properties = Collections.emptyMap();

    /**
     * Returns instance of self (<strong>this</strong>)
     *
     * @return instance of self (<strong>this</strong>)
     */
    protected abstract B getSelf();

    /**
     * Adds Message ID to builder
     *
     * @param messageId Message ID
     * @return builder
     */
    public B withMessageId(final String messageId) {
      this.messageId = messageId;
      return getSelf();
    }

    /**
     * Adds instance of {@link URI} as Address
     *
     * @param address instance of {@link URI} as Address
     * @return builder
     */
    public B withAddress(final URI address) {
      this.address = address;
      return getSelf();
    }

    /**
     * Adds property {@link Map} to builder
     *
     * @deprecated since 2023-05-15
     * @param properties property {@link Map}
     * @return builder
     */
    @Deprecated(since = "2023-05-15", forRemoval = true)
    public B withProperties(final Map<String, String> properties) {
      this.properties = properties;
      return getSelf();
    }
  }
}
