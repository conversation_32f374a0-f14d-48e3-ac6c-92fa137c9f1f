package com.volvo.tisp.vwtp.model;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.WtpVersion;

/** Common ancestor of all incoming protocol data unit models */
public abstract class ModelCommon {
  private static final long HIGH_ORDER_BIT = 0x8000L;

  private final boolean rid;
  private final long wtpTid;
  private final WtpVersion wtpVersion;
  private final long vid;
  private final TpiCommon[] tpiArray;

  /**
   * @param builder {@link Builder}
   */
  protected ModelCommon(final Builder<?, ?> builder) {
    rid = builder.rid;
    wtpTid = builder.wtpTid;
    wtpVersion = builder.wtpVersion;
    vid = builder.vid;
    tpiArray = builder.tpiArray;
  }

  /**
   * @return enumerator of PDU type {@link PduType}
   */
  public abstract PduType getPduType();

  /**
   * @return true if retransmission indicator is set
   */
  public boolean isRid() {
    return rid;
  }

  /**
   * @return transaction ID
   */
  public long getWtpTid() {
    return wtpTid;
  }

  /**
   * Inverts the high order bit of WTP TID, returning a valid TID with opposite direction.
   *
   * @return transaction ID
   */
  public long getReverseWtpTid() {
    return wtpTid ^ HIGH_ORDER_BIT;
  }

  /**
   * @return WTP version
   */
  public WtpVersion getWtpVersion() {
    return wtpVersion;
  }

  /**
   * @return vehicle ID
   */
  public long getVid() {
    return vid;
  }

  /**
   * @return transport information item array
   */
  public TpiCommon[] getTpiArray() {
    return tpiArray;
  }

  /**
   * Common ancestor of all PDU model builders
   *
   * @param <B> super-type of {@link Builder} a.k.a. - Builder
   * @param <P> super-type of {@link ModelCommon} a.k.a. - common PDU model
   */
  protected abstract static class Builder<B extends Builder<B, P>, P extends ModelCommon>
      implements org.apache.commons.lang3.builder.Builder<P> {
    private boolean rid;
    private long wtpTid;
    private WtpVersion wtpVersion = WtpVersion.VERSION_1;
    private long vid;
    private TpiCommon[] tpiArray = TpiCommon.EMPTY;

    /**
     * @return instance of self (<strong>this</strong>)
     */
    protected abstract B getSelf();

    /**
     * @param rid retransmission indicator
     * @return builder
     */
    public B withRid(final boolean rid) {
      this.rid = rid;
      return getSelf();
    }

    /**
     * @param wtpTid transaction ID
     * @return builder
     */
    public B withWtpTid(final long wtpTid) {
      Preconditions.checkArgument(
          wtpTid >= 0L && wtpTid < 65_536L,
          "WTP Transaction ID (TID) must be within [0 .. 65 535L]");
      this.wtpTid = wtpTid;
      return getSelf();
    }

    /**
     * @param wtpVersion WTP version
     * @return builder
     */
    public B withWtpVersion(final WtpVersion wtpVersion) {
      Preconditions.checkNotNull(wtpVersion, "WTP version must not be null");
      this.wtpVersion = wtpVersion;
      return getSelf();
    }

    /**
     * @param vid vehicle ID
     * @return builder
     */
    public B withVid(final long vid) {
      Preconditions.checkArgument(
          vid >= 0L && vid < 4_294_967_296L,
          "WTP Vehicle ID (VID) must be within [0 .. 4 294 967 295]");
      this.vid = vid;
      return getSelf();
    }

    /**
     * @param tpiArray transport information item array
     * @return builder
     */
    public B withTpiArray(final TpiCommon[] tpiArray) {
      Preconditions.checkNotNull(tpiArray, "TPI array must not be null");
      this.tpiArray = tpiArray;
      return getSelf();
    }
  }
}
