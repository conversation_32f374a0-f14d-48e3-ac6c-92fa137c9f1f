package com.volvo.tisp.vwtp.exception;

/** {@code RuntimeException} indicating failure in WTP module */
public class WtpRuntimeException extends RuntimeException {
  private static final long serialVersionUID = 2018_11_09L;

  /**
   * @see RuntimeException#RuntimeException(String message)
   * @param message the detail message. The detail message is saved for later retrieval by the
   *     {@link #getMessage()} method.
   */
  public WtpRuntimeException(final String message) {
    super(message);
  }

  /**
   * @see RuntimeException#RuntimeException(Throwable cause)
   * @param cause the cause (which is saved for later retrieval by the {@link #getCause()} method).
   *     (A <code>null</code> value is permitted, and indicates that the cause is nonexistent or
   *     unknown.)
   */
  public WtpRuntimeException(final Throwable cause) {
    super(cause);
  }

  /**
   * @see RuntimeException#RuntimeException(String message, Throwable cause)
   * @param message the detail message (which is saved for later retrieval by the {@link
   *     #getMessage()} method).
   * @param cause the cause (which is saved for later retrieval by the {@link #getCause()} method).
   *     (A <code>null</code> value is permitted, and indicates that the cause is nonexistent or
   *     unknown.)
   */
  public WtpRuntimeException(final String message, final Throwable cause) {
    super(message, cause);
  }

  /**
   * @see RuntimeException#RuntimeException(String message, Throwable cause, boolean
   *     enableSuppression, boolean writableStackTrace)
   * @param message the detail message.
   * @param cause the cause. (A {@code null} value is permitted, and indicates that the cause is
   *     nonexistent or unknown.)
   * @param enableSuppression whether or not suppression is enabled or disabled
   * @param writableStackTrace whether or not the stack trace should be writable
   */
  public WtpRuntimeException(
      final String message,
      final Throwable cause,
      final boolean enableSuppression,
      final boolean writableStackTrace) {
    super(message, cause, enableSuppression, writableStackTrace);
  }
}
