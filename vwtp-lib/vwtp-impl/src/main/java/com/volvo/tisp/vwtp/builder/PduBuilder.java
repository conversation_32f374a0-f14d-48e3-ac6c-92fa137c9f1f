package com.volvo.tisp.vwtp.builder;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.codec.ASNException;
import com.volvo.tisp.vwtp.codec.AbortPdu;
import com.volvo.tisp.vwtp.codec.AbortPdu2;
import com.volvo.tisp.vwtp.codec.AckPdu;
import com.volvo.tisp.vwtp.codec.AckPdu2;
import com.volvo.tisp.vwtp.codec.InvokePdu;
import com.volvo.tisp.vwtp.codec.InvokePdu2;
import com.volvo.tisp.vwtp.codec.NackPdu;
import com.volvo.tisp.vwtp.codec.NackPdu2;
import com.volvo.tisp.vwtp.codec.SegInvokePdu;
import com.volvo.tisp.vwtp.codec.SegInvokePdu2;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.TransactionClass;
import com.volvo.tisp.vwtp.constants.WtpConstants;
import com.volvo.tisp.vwtp.constants.WtpVersion;
import com.volvo.tisp.vwtp.exception.WtpRuntimeException;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.lang.NonNull;

final class PduBuilder<B>
    implements AbortPduBuilder<B>,
        AbortPdu2Builder<B>,
        AckPduBuilder<B>,
        AckPdu2Builder<B>,
        InvokePduBuilder<B>,
        InvokePdu2Builder<B>,
        NackPduBuilder<B>,
        NackPdu2Builder<B>,
        SegInvokePduBuilder<B>,
        SegInvokePdu2Builder<B> {
  private static final long HIGH_ORDER_BIT = 0x8000L;

  private TransactionClass transactionClass = TransactionClass.CLASS_0;
  private AbortCode abortCode = AbortCode.TCE_PROVIDER_UNKNOWN;
  private boolean rid;
  private boolean otr;
  private boolean gtr;
  private boolean ttr;
  private boolean tidNew;
  private boolean userAck;
  private long wtpTid;
  private long vid;
  private long psn = 1L;
  private long[] missingPsn = ArrayUtils.EMPTY_LONG_ARRAY;

  @SuppressWarnings("unchecked")
  private B getSelf() {
    return (B) this;
  }

  @Override
  public B withWtpTid(final long wtpTid) {
    validateTid(wtpTid);
    this.wtpTid = wtpTid;
    return getSelf();
  }

  @Override
  public B withWtpTidReverseDirection(final long wtpTid) {
    validateTid(wtpTid);
    this.wtpTid = wtpTid ^ HIGH_ORDER_BIT;
    return getSelf();
  }

  @Override
  public B withVid(final long vid) {
    Preconditions.checkArgument(
        vid >= 0L && vid < 4_294_967_296L,
        "WTP Vehicle ID (VID) must be within [0 .. 4 294 967 295]");
    this.vid = vid;
    return getSelf();
  }

  @Override
  public B withOtr(final boolean otr) {
    this.otr = otr;
    return getSelf();
  }

  @Override
  public B withRid(final boolean rid) {
    this.rid = rid;
    return getSelf();
  }

  @Override
  public B withAbortCode(@NonNull final AbortCode abortCode) {
    this.abortCode = abortCode;
    return getSelf();
  }

  @Override
  public B withGtr(final boolean gtr) {
    this.gtr = gtr;
    return getSelf();
  }

  @Override
  public B withTtr(final boolean ttr) {
    this.ttr = ttr;
    return getSelf();
  }

  @Override
  public B withTidNew(final boolean tidNew) {
    this.tidNew = tidNew;
    return getSelf();
  }

  @Override
  public B withUserAck(final boolean userAck) {
    this.userAck = userAck;
    return getSelf();
  }

  @Override
  public B withTransactionClass(@NonNull final TransactionClass transactionClass) {
    this.transactionClass = transactionClass;
    return getSelf();
  }

  @Override
  public B withPsn(final long psn) {
    Preconditions.checkArgument(
        psn >= 0L && psn < WtpConstants.MAX_NUMBER_OF_SEGMENTS,
        "Packet sequence number must be positive and less than "
            + WtpConstants.MAX_NUMBER_OF_SEGMENTS);
    this.psn = psn;
    return getSelf();
  }

  @Override
  public B withMissingPsn(@NonNull final long... missingPsn) {
    Preconditions.checkArgument(
        missingPsn.length <= WtpConstants.MAX_NUMBER_OF_SEGMENTS,
        "Number of missing PSN's can not be bigger than " + WtpConstants.MAX_NUMBER_OF_SEGMENTS);
    this.missingPsn = missingPsn;
    return getSelf();
  }

  @Override
  public AckPdu buildAckPdu() {
    final AckPdu ackPdu = new AckPdu();
    ackPdu.setOtr(this.otr);
    ackPdu.setRid(this.rid);
    ackPdu.setTid(this.wtpTid);
    return ackPdu;
  }

  @Override
  public AckPdu2 buildAckPdu2() {
    final AckPdu2 ackPdu = new AckPdu2();
    ackPdu.setOtr(this.otr);
    ackPdu.setRid(this.rid);
    ackPdu.setTid(this.wtpTid);
    ackPdu.setVid(this.vid);
    return ackPdu;
  }

  @Override
  public AbortPdu buildAbortPdu() {
    final AbortPdu abortPdu = new AbortPdu();
    abortPdu.setAbortType(this.abortCode.getType());
    abortPdu.setTid(this.wtpTid);
    abortPdu.setAbortReason(this.abortCode.getReason());
    return abortPdu;
  }

  @Override
  public AbortPdu2 buildAbortPdu2() {
    final AbortPdu2 abortPdu = new AbortPdu2();
    abortPdu.setAbortType(this.abortCode.getType());
    abortPdu.setTid(this.wtpTid);
    abortPdu.setAbortReason(this.abortCode.getReason());
    abortPdu.setVid(this.vid);
    return abortPdu;
  }

  @Override
  public InvokePdu buildInvokePdu() {
    final InvokePdu invokePdu = new InvokePdu();
    invokePdu.setGtr(this.gtr);
    invokePdu.setTtr(this.ttr);
    invokePdu.setRid(this.rid);
    invokePdu.setTid(this.wtpTid);
    invokePdu.setVersion(WtpVersion.VERSION_1.getValue());
    invokePdu.setTidNew(this.tidNew);
    invokePdu.setUserAck(this.userAck);
    invokePdu.setTcl(this.transactionClass.getValue());
    return invokePdu;
  }

  @Override
  public InvokePdu2 buildInvokePdu2() {
    final InvokePdu2 invokePdu = new InvokePdu2();
    invokePdu.setGtr(this.gtr);
    invokePdu.setTtr(this.ttr);
    invokePdu.setRid(this.rid);
    invokePdu.setTid(this.wtpTid);
    invokePdu.setVersion(WtpVersion.VERSION_2.getValue());
    invokePdu.setTidNew(this.tidNew);
    invokePdu.setUserAck(this.userAck);
    invokePdu.setTcl(this.transactionClass.getValue());
    invokePdu.setVid(this.vid);
    return invokePdu;
  }

  @Override
  public NackPdu buildNackPdu() {
    final NackPdu nackPdu = new NackPdu();
    nackPdu.setRid(this.rid);
    nackPdu.setTid(this.wtpTid);
    try {
      nackPdu.getMp().setSize(this.missingPsn.length);
      for (int index = 0; index < this.missingPsn.length; index++) {
        final long nackPsn = this.missingPsn[index];
        validateMissingPsn(nackPsn);
        nackPdu.getMp().set(index, nackPsn);
      }
    } catch (final ASNException e) {
      throw new WtpRuntimeException(e);
    }
    return nackPdu;
  }

  @Override
  public NackPdu2 buildNackPdu2() {
    final NackPdu2 nackPdu = new NackPdu2();
    nackPdu.setRid(this.rid);
    nackPdu.setTid(this.wtpTid);
    try {
      nackPdu.getMp().setSize(this.missingPsn.length);
      for (int index = 0; index < this.missingPsn.length; index++) {
        final long nackPsn = this.missingPsn[index];
        validateMissingPsn(nackPsn);
        nackPdu.getMp().set(index, nackPsn);
      }
    } catch (final ASNException e) {
      throw new WtpRuntimeException(e);
    }
    nackPdu.setVid(this.vid);
    return nackPdu;
  }

  @Override
  public SegInvokePdu buildSegInvokePdu() {
    final SegInvokePdu segInvokePdu = new SegInvokePdu();
    segInvokePdu.setGtr(this.gtr);
    segInvokePdu.setTtr(this.ttr);
    segInvokePdu.setRid(this.rid);
    segInvokePdu.setTid(this.wtpTid);
    segInvokePdu.setPsn(this.psn);
    return segInvokePdu;
  }

  @Override
  public SegInvokePdu2 buildSegInvokePdu2() {
    final SegInvokePdu2 segInvokePdu = new SegInvokePdu2();
    segInvokePdu.setGtr(this.gtr);
    segInvokePdu.setTtr(this.ttr);
    segInvokePdu.setRid(this.rid);
    segInvokePdu.setTid(this.wtpTid);
    segInvokePdu.setPsn(this.psn);
    segInvokePdu.setVid(this.vid);
    return segInvokePdu;
  }

  private static void validateMissingPsn(final long psn) {
    Preconditions.checkArgument(
        psn >= 0L && psn < 256L,
        "Missing packet sequence number must be positive and less than "
            + WtpConstants.MAX_NUMBER_OF_SEGMENTS);
  }

  private static void validateTid(final long wtpTid) {
    Preconditions.checkArgument(
        wtpTid >= 0L && wtpTid < 32_768L, "WTP Transaction ID (TID) must be within [0 .. 32 767]");
  }
}
