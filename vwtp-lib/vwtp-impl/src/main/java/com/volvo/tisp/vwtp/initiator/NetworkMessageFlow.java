package com.volvo.tisp.vwtp.initiator;

import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.constants.AbortCode;
import com.volvo.tisp.vwtp.constants.PduType;
import com.volvo.tisp.vwtp.constants.TpiType;
import com.volvo.tisp.vwtp.constants.TransactionStatus;
import com.volvo.tisp.vwtp.converter.NetworkMessageToModelCommonConverter;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.model.AbortModel;
import com.volvo.tisp.vwtp.model.AckModel;
import com.volvo.tisp.vwtp.model.ModelCommon;
import com.volvo.tisp.vwtp.model.NackModel;
import com.volvo.tisp.vwtp.model.PsnTpiModel;
import com.volvo.tisp.vwtp.model.TpiCommon;
import com.volvo.tisp.vwtp.util.InitiatorMetrics;
import com.volvo.tisp.vwtp.util.MetricNames;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import java.net.URI;
import java.time.Duration;
import java.util.Map;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import reactor.core.Disposable;
import reactor.core.publisher.Flux;
import reactor.core.publisher.GroupedFlux;
import reactor.core.publisher.Mono;
import reactor.core.publisher.Sinks;
import reactor.core.publisher.Sinks.EmitFailureHandler;
import reactor.core.publisher.Sinks.EmitResult;
import reactor.core.scheduler.Scheduler;
import reactor.util.Logger;
import reactor.util.Loggers;
import reactor.util.context.Context;
import reactor.util.context.ContextView;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/** A component managing Initiator responses */
@Component("initiatorNetworkMessageFlow")
public class NetworkMessageFlow implements FlowComposer<NetworkMessageDto, Void> {
  private static final Logger logger = Loggers.getLogger(NetworkMessageFlow.class);
  private static final Counter tidVerificationCounter =
      Counter.builder(MetricNames.INITIATOR_INCOMING_TID_VERIFICATIONS)
          .description("Received tid verification requests")
          .register(Metrics.globalRegistry);
  private static final Counter nackForAllSegmentsCounter =
      Counter.builder(MetricNames.INITIATOR_INCOMING_ANOMALIES)
          .description("Received NACK that is requesting all segments")
          .tag(MetricNames.TYPE_TAG, "nack-for-all-segments")
          .register(Metrics.globalRegistry);
  private static final Counter nackForNonexistentSegmentsCounter =
      Counter.builder(MetricNames.INITIATOR_INCOMING_ANOMALIES)
          .description("Received NACK that is requesting nonexistent segments")
          .tag(MetricNames.TYPE_TAG, "nack-for-nonexistent-segments")
          .register(Metrics.globalRegistry);
  private static final Counter nonexistenttransactionCounter =
      Counter.builder(MetricNames.INITIATOR_INCOMING_ANOMALIES)
          .description("Received response for nonexistent transaction")
          .tag(MetricNames.TYPE_TAG, "nonexistent-transaction")
          .register(Metrics.globalRegistry);
  private static final Counter incomingPduSizes =
      Counter.builder(MetricNames.INCOMING_PDU_SIZES)
          .tag(MetricNames.PROVIDER_TAG, MetricNames.PROVIDER_TAG_INITIATOR)
          .register(Metrics.globalRegistry);
  private static final String NETWORK_SINK_CONTEXT_KEY =
      "INITIATOR-NETWORK-SINK-" + NetworkMessageFlow.class.hashCode();
  private static final String STATUS_SINK_CONTEXT_KEY =
      "INITIATOR-STATUS-SINK-" + NetworkMessageFlow.class.hashCode();

  private final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow;
  private final FlowComposer<UserStatusDto, Void> outgoingUserStatusFlow;
  private final CacheService cacheService;

  /**
   * Build and launch the Initiator
   *
   * @param outgoingNetworkMessageFlow instance of {@link FlowComposer} &lt;{@link
   *     NetworkMessageDto}, {@link Void}&gt;
   * @param outgoingUserStatusFlow instance of {@link FlowComposer} &lt;{@link UserStatusDto},
   *     {@link Void}&gt;
   * @param cacheService instance of {@link CacheService}
   */
  protected NetworkMessageFlow(
      @Qualifier("outgoingInitiatorNetworkMessageFlow")
          final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow,
      @Qualifier("outgoingInitiatorUserStatusFlow")
          final FlowComposer<UserStatusDto, Void> outgoingUserStatusFlow,
      final CacheService cacheService) {
    this.outgoingNetworkMessageFlow = outgoingNetworkMessageFlow;
    this.outgoingUserStatusFlow = outgoingUserStatusFlow;
    this.cacheService = cacheService;
  }

  @Override
  public Publisher<Void> apply(final Flux<NetworkMessageDto> flux) {
    logger.info("Composing reactive flow");
    final Sinks.Many<UserStatusDto> statusSink =
        Sinks.unsafe().many().unicast().onBackpressureError();
    final Sinks.Many<NetworkMessageDto> networkSink =
        Sinks.unsafe().many().unicast().onBackpressureError();

    return Flux.merge(
        networkSink.asFlux().transform(outgoingNetworkMessageFlow),
        Flux.merge(
                statusSink.asFlux(),
                flux.handle(NetworkMessageToModelCommonConverter::convert)
                    .doOnNext(this::logMetrics)
                    .flatMap(this::retrieveTransaction)
                    .groupBy(this::groupByPduType)
                    .flatMap(this::routeByPduType)
                    .transform(this::completeProcessorsUponTermination)
                    .contextWrite(Context.of(NETWORK_SINK_CONTEXT_KEY, networkSink))
                    .contextWrite(Context.of(STATUS_SINK_CONTEXT_KEY, statusSink)))
            .transform(outgoingUserStatusFlow));
  }

  /**
   * Completes the sub flows inside {@link Flux} {@link Context} when main flow completes
   *
   * @param flux {@link Flux}&lt;{@link UserStatusDto}&gt;
   * @return instance of {@link Publisher}&lt;{@link UserStatusDto}&gt;
   */
  public Publisher<UserStatusDto> completeProcessorsUponTermination(
      final Flux<UserStatusDto> flux) {
    return Flux.deferContextual(
        context ->
            flux.doOnComplete(
                () -> {
                  final Sinks.Many<NetworkMessageDto> networkProcessor =
                      context.get(NETWORK_SINK_CONTEXT_KEY);
                  networkProcessor.emitComplete(EmitFailureHandler.FAIL_FAST);
                  final Sinks.Many<UserStatusDto> statusProcessor =
                      context.get(STATUS_SINK_CONTEXT_KEY);
                  statusProcessor.emitComplete(EmitFailureHandler.FAIL_FAST);
                }));
  }

  /**
   * Logs received protocol data unit metrics
   *
   * @param networkMessageAndModel {@link Tuple2} of {@link NetworkMessageDto} and {@link
   *     ModelCommon}
   */
  private void logMetrics(final Tuple2<NetworkMessageDto, ModelCommon> networkMessageAndModel) {
    final NetworkMessageDto networkMessage = networkMessageAndModel.getT1();
    final ModelCommon modelCommon = networkMessageAndModel.getT2();
    logger.debug("Initiator - Entering flow ({}): {}", networkMessage.getMessageId(), modelCommon);
    incomingPduSizes.increment(networkMessage.getPayload().length);
    InitiatorMetrics.incrementIncomingPdu(modelCommon, networkMessage.getAddress());
  }

  /**
   * Retrieves a transaction from a transaction cache based on address value and TID value
   *
   * @param networkMessageAndModel instance of {@link Tuple2} of {@link NetworkMessageDto} and
   *     {@link ModelCommon}
   * @return Publisher
   */
  private Publisher<Tuple2<ModelCommon, Transaction>> retrieveTransaction(
      final Tuple2<NetworkMessageDto, ModelCommon> networkMessageAndModel) {
    return Mono.deferContextual(
        context -> {
          logger.debug("Initiator - Entering retrieveTransaction");

          final NetworkMessageDto networkMessage = networkMessageAndModel.getT1();
          final ModelCommon modelCommon = networkMessageAndModel.getT2();
          final long tidWithInvertedDirectionBit = modelCommon.getReverseWtpTid();
          final URI address = networkMessage.getAddress();
          final Transaction transaction =
              cacheService.initiatorReadWtpTransaction(address, tidWithInvertedDirectionBit);
          if (transaction != null && transaction.getStatus() == TransactionStatus.INPROGRESS) {
            /* Updating Message properties with the Latest Values from the NetworkMessageDto */
            networkMessage.getProperties().forEach(transaction.getProperties()::putIfAbsent);
            return Mono.just(Tuples.of(modelCommon, transaction));
          }

          if (modelCommon.getPduType() == PduType.ACK && ((AckModel) modelCommon).isOtr()) {
            logger.debug("Initiator - Tid verification failed. No active transaction.");
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            final NetworkMessageDto abortMessage =
                InitiatorUtils.createAbortPdu(
                    networkMessage, modelCommon, AbortCode.TCE_PROVIDER_INVALID_WTP_TID);
            final EmitResult emitResult = networkSink.tryEmitNext(abortMessage);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::retrieveTransaction",
                  emitResult,
                  abortMessage);
            }
          }
          nonexistenttransactionCounter.increment();
          return Mono.empty();
        });
  }

  /**
   * Extract the PDU type from the {@link ModelCommon}
   *
   * @param networkMessageDtoAndModelCommonAndTransaction a network message, a model common, and a
   *     WTP transaction
   * @return a PDU type
   */
  private PduType groupByPduType(
      final Tuple2<ModelCommon, Transaction> networkMessageDtoAndModelCommonAndTransaction) {
    final ModelCommon modelCommon = networkMessageDtoAndModelCommonAndTransaction.getT1();

    logger.debug("Initiator - Entering groupByPduType");

    return modelCommon.getPduType();
  }

  /**
   * Routes the network message to different flows depending on the PDU type, i.e. ACK, ABORT and
   * NACK
   *
   * @param groupedFlux the key PDU type, and a tuple of network message, model common, and a WTP
   *     transaction
   * @return {@link Flux}
   */
  private Flux<UserStatusDto> routeByPduType(
      final GroupedFlux<PduType, Tuple2<ModelCommon, Transaction>> groupedFlux) {
    final PduType pduType = groupedFlux.key();
    logger.debug("Initiator - Entering routeByPduType");

    if (pduType == PduType.ACK) {
      return groupedFlux
          .map(this::convertModelCommonToAckModel)
          .flatMap(this::performTidVerificationIfNeeded)
          .filter(this::ifExpectedPsn)
          .flatMap(this::sendNextGroup)
          .filter(this::markCompleated)
          .map(this::createAndSendUserConfirmation);
    } else if (pduType == PduType.NACK) {
      return groupedFlux.flatMap(this::resendMissingSegments);
    } else if (pduType == PduType.ABORT) {
      return groupedFlux.map(this::handleAbort);
    } else {
      return groupedFlux
          /**
           * Initiator should never receive {@link PduType#INVOKE} and {@link PduType#SEGINVOKE}
           * filtering them out
           */
          .filter(modelAndTransaction -> false)
          .cast(UserStatusDto.class);
    }
  }

  /**
   * Convert {@link ModelCommon} to {@link AckModel}
   *
   * @param modelAndTransaction instance of {@link Tuple2} of {@link ModelCommon} and {@link
   *     Transaction}
   * @return instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   */
  private Tuple2<AckModel, Transaction> convertModelCommonToAckModel(
      final Tuple2<ModelCommon, Transaction> modelAndTransaction) {
    logger.debug("Initiator - Entering convertModelCommonToAckModel");
    return Tuples.of((AckModel) modelAndTransaction.getT1(), modelAndTransaction.getT2());
  }

  /**
   * Send back Ack with Otr if TID verification is requested
   *
   * @param ackAndTransaction instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   * @return instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   */
  private Publisher<Tuple2<AckModel, Transaction>> performTidVerificationIfNeeded(
      final Tuple2<AckModel, Transaction> ackAndTransaction) {
    return Mono.deferContextual(
        context -> {
          final AckModel ackModel = ackAndTransaction.getT1();
          final Transaction transaction = ackAndTransaction.getT2();

          if (ackModel.isOtr()) {
            logger.debug("Initiator - TidVerification Requested, Sending TID OK.");
            tidVerificationCounter.increment();
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            final NetworkMessageDto networkMessage = InitiatorUtils.createAckPdu(transaction, true);
            final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::performTidVerificationIfNeeded",
                  emitResult,
                  networkMessage);
            }
          }
          return Mono.just(ackAndTransaction);
        });
  }

  /**
   * If ACK PSN number was expected otherwise stop further processing
   *
   * @param ackAndTransaction instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   * @return true if ACK PSN was expected
   */
  private boolean ifExpectedPsn(final Tuple2<AckModel, Transaction> ackAndTransaction) {
    final AckModel ackModel = ackAndTransaction.getT1();
    final Transaction transaction = ackAndTransaction.getT2();

    PsnTpiModel psnModel = null;

    for (final TpiCommon tpi : ackModel.getTpiArray()) {
      if (tpi.getType() == TpiType.PSN) {
        psnModel = (PsnTpiModel) tpi;
      }
    }

    int acknowledgedPsn = 0;
    if (psnModel != null) {
      acknowledgedPsn = psnModel.getPacketSequenceNumber();
    }

    if (acknowledgedPsn == transaction.getAndUpdateCurrentExpectedResponderPsn(acknowledgedPsn)) {
      transaction.setCurrentPsnFromResponse(acknowledgedPsn);
      return true;
    }

    logger.debug("Initiator - Filtered out ifExpectedPsn.");
    return false;
  }

  /**
   * Retrieves the next group from the transaction container and send each network message
   *
   * @param ackAndTransaction instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   * @return instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   */
  private Publisher<Tuple2<AckModel, Transaction>> sendNextGroup(
      final Tuple2<AckModel, Transaction> ackAndTransaction) {
    return Mono.deferContextual(
        context -> {
          final Transaction transaction = ackAndTransaction.getT2();

          if (transaction.getExpectedResponderPsn() != transaction.getCurrentPsnFromResponse()) {
            logger.debug("Initiator - Starting Group Resend Timer");
            scheduleGroupResendTimer(context, transaction);

            logger.debug("Initiator - Sending Next Group of SegInvokes");
            final Map<Integer, Segment> segments =
                transaction.getSegments().getGroup(transaction.getCurrentPsnFromResponse());
            final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
            for (final Segment segment : segments.values()) {
              final NetworkMessageDto networkMessage =
                  InitiatorUtils.createSegInvokePdu(
                      transaction,
                      segment.isLastInGroup(),
                      segment.isLastSegment(),
                      false,
                      segment);
              final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::sendNextGroup",
                    emitResult,
                    networkMessage);
              }
            }
            transaction.recordRoundtripMetric();
            logger.debug("Initiator - Filtered out sendNextGroup.");
            return Mono.empty();
          }
          return Mono.just(ackAndTransaction);
        });
  }

  /**
   * Marks transaction as completed unless TID verification is in progress and we need to wait for
   * another ACK
   *
   * @param ackAndTransaction instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   * @return instance of {@link Tuple2} of {@link AckModel} and {@link Transaction}
   */
  private boolean markCompleated(final Tuple2<AckModel, Transaction> ackAndTransaction) {
    final AckModel ackModel = ackAndTransaction.getT1();
    if (ackModel.isOtr()) {
      return false;
    } else {
      logger.debug("Initiator - markCompleated");
      final Transaction transaction = ackAndTransaction.getT2();
      transaction.disposeActiveTimer();
      return TransactionStatus.INPROGRESS
          == transaction.getAndSetTransactionStatus(TransactionStatus.COMPLETED);
    }
  }

  /**
   * Disposes of transaction and sends DELIVERED {@link UserStatusDto} through the user status
   * consumer
   *
   * @param ackModelAndWtpInitiatorTransaction an acknowledge model and a WTP transaction
   * @return a WTP transaction
   */
  private UserStatusDto createAndSendUserConfirmation(
      final Tuple2<AckModel, Transaction> ackModelAndWtpInitiatorTransaction) {
    final Transaction transaction = ackModelAndWtpInitiatorTransaction.getT2();
    transaction.recordRoundtripMetric();
    cacheService.initiatorDeleteWtpTransaction(transaction);
    return InitiatorUtils.createUserStatusDelivered(transaction);
  }

  /**
   * Re-sends missing segments requested in {@link NackModel}
   *
   * @param nackAndTransaction {@link Tuple2} of {@link NackModel} and {@link Transaction}
   * @return false to filter out everything
   */
  private Publisher<UserStatusDto> resendMissingSegments(
      final Tuple2<ModelCommon, Transaction> nackAndTransaction) {
    return Mono.deferContextual(
        context -> {
          logger.debug("Initiator - Received Nack");
          final NackModel nackModel = (NackModel) nackAndTransaction.getT1();
          final Transaction transaction = nackAndTransaction.getT2();
          final int[] missingPsnArray = nackModel.getMissingPsn();
          Map<Integer, Segment> segmentsToBeResent;

          if (missingPsnArray.length == 0) {
            segmentsToBeResent = transaction.getSegments().getAllSegments();
            nackForAllSegmentsCounter.increment();
          } else {
            segmentsToBeResent = transaction.getSegments().getSegments(missingPsnArray);
            if (missingPsnArray.length != segmentsToBeResent.size()) {
              logger.warn(
                  "Initiator - Not all requested NACK segment numbers {} exist in transaction.",
                  missingPsnArray);
              nackForNonexistentSegmentsCounter.increment();
              return Mono.empty();
            }
          }

          logger.debug("Initiator - Starting Timer GR for Nack");
          scheduleGroupResendTimer(context, transaction);

          logger.debug("Initiator - Sending Missing Segment numbers {}", segmentsToBeResent.size());
          final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
          for (final Segment segment : segmentsToBeResent.values()) {
            final NetworkMessageDto networkMessage;
            if (segment.getPsn() == 0) {
              networkMessage =
                  InitiatorUtils.createInvokePdu(
                      transaction, false, segment.isLastSegment(), true, false);
            } else {
              networkMessage =
                  InitiatorUtils.createSegInvokePdu(
                      transaction, false, segment.isLastSegment(), true, segment);
            }
            final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
            if (emitResult.isFailure()) {
              logger.error(
                  "Sink emission failed with {} for {} in ::resendMissingSegments",
                  emitResult,
                  networkMessage);
            }
          }
          transaction.resetRoundtripMetric();

          return Mono.empty();
        });
  }

  /**
   * Schedules ResendTimer with configuration from transaction
   *
   * @param context instance of {@link Context}
   * @param transaction instance of {@link Transaction}
   */
  public void scheduleGroupResendTimer(final ContextView context, final Transaction transaction) {
    final Scheduler scheduler = context.get(Scheduler.class);
    final Duration[] durations = transaction.getConfiguration().getTimerGR();
    final Disposable timerHandle =
        Flux.range(1, durations.length)
            .delayUntil(
                counter -> Mono.just(counter).delayElement(durations[counter - 1], scheduler))
            .doOnCancel(() -> logger.debug("Disposing GroupResendTimer for {}", transaction))
            .concatMap(this::fireGroupResendTimer)
            .subscribe(
                null,
                throwable -> logger.error("", throwable),
                null,
                Context.of(context).put(Transaction.class, transaction));
    transaction.updateActiveTimer(timerHandle);
    logger.debug("Scheduled GroupResendTimer for {}", transaction);
  }

  /**
   * Executes set of actions when GroupResendTimer fires
   *
   * @param counter resent counter
   * @return instance of {@link Context} {@link Publisher}
   */
  private Publisher<Void> fireGroupResendTimer(final Integer counter) {
    return Mono.deferContextual(
        context -> {
          final Transaction transaction = context.get(Transaction.class);
          final Sinks.Many<NetworkMessageDto> networkSink = context.get(NETWORK_SINK_CONTEXT_KEY);
          final Sinks.Many<UserStatusDto> statusSink = context.get(STATUS_SINK_CONTEXT_KEY);
          final Duration[] durations = transaction.getConfiguration().getTimerGR();
          if (counter < durations.length) {
            if (transaction.getStatus() == TransactionStatus.INPROGRESS) {
              logger.debug(
                  "Initiator - GroupResendTimer ({}) userHookContinue Message ID {}",
                  counter,
                  transaction.getMessageId());
              final Segment segmentToResend =
                  transaction.getSegments().getSegment(transaction.getExpectedResponderPsn());
              final NetworkMessageDto networkMessage =
                  InitiatorUtils.createSegInvokePdu(
                      transaction,
                      !segmentToResend.isLastSegment(),
                      segmentToResend.isLastSegment(),
                      true,
                      segmentToResend);
              final EmitResult emitResult = networkSink.tryEmitNext(networkMessage);
              transaction.resetRoundtripMetric();
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::fireGroupResendTimer",
                    emitResult,
                    networkMessage);
              }
            }
          } else {
            if (TransactionStatus.INPROGRESS
                == transaction.getAndSetTransactionStatus(TransactionStatus.COMPLETED)) {
              logger.debug(
                  "Initiator - GroupResendTimer ({}) userHookComplete Message ID {}",
                  counter,
                  transaction.getMessageId());
              cacheService.initiatorDeleteWtpTransaction(transaction);
              final UserStatusDto userStatus =
                  InitiatorUtils.createUserStatusAborted(
                      transaction, AbortCode.TCE_PROVIDER_NO_RESPONSE);
              final EmitResult emitResult = statusSink.tryEmitNext(userStatus);
              if (emitResult.isFailure()) {
                logger.error(
                    "Sink emission failed with {} for {} in ::fireGroupResendTimer",
                    emitResult,
                    userStatus);
              }
            }
          }
          return Mono.empty();
        });
  }

  /**
   * Tries to take ownership for handling an abort
   *
   * @param modelAndTransaction {@link Tuple2} of {@link ModelCommon} and {@link Transaction}
   * @return {@link UserStatusDto}
   */
  private UserStatusDto handleAbort(final Tuple2<ModelCommon, Transaction> modelAndTransaction) {
    final AbortModel abortModel = (AbortModel) modelAndTransaction.getT1();
    final Transaction transaction = modelAndTransaction.getT2();
    InitiatorMetrics.incrementIncomingAbort(abortModel);
    transaction.getAndSetTransactionStatus(TransactionStatus.COMPLETED);
    cacheService.initiatorDeleteWtpTransaction(transaction);
    return InitiatorUtils.createUserStatusAborted(transaction, abortModel.getAbortCode());
  }
}
