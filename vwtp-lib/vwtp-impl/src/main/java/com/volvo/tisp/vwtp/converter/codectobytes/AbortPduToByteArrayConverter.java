package com.volvo.tisp.vwtp.converter.codectobytes;

import com.google.common.base.Preconditions;
import com.volvo.tisp.vwtp.codec.ASNSequence;
import com.volvo.tisp.vwtp.codec.AbortPdu;
import com.volvo.tisp.vwtp.codec.AbortPdu2;
import com.volvo.tisp.vwtp.codec.WirelessTransactionPdu_pdu;

/** Converter utility for converting {@link AbortPdu} or {@link AbortPdu2} into byte[] */
public class AbortPduToByteArrayConverter {
  /** Private constructor to prevent instantiation of utility class */
  private AbortPduToByteArrayConverter() {}

  /**
   * Convert {@link AbortPdu} to byte array
   *
   * @param abortPdu {@link AbortPdu}
   * @return byte[]
   */
  public static byte[] convert(final AbortPdu abortPdu) {
    validateAbortPdu(abortPdu);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setAbortPdu(abortPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 4);
  }

  /**
   * Convert {@link AbortPdu2} to byte array
   *
   * @param abortPdu {@link AbortPdu2}
   * @return byte[]
   */
  public static byte[] convert(final AbortPdu2 abortPdu) {
    validateAbortPdu(abortPdu);

    final WirelessTransactionPdu_pdu pdu = new WirelessTransactionPdu_pdu();
    pdu.setAbortPdu2(abortPdu);

    return WtpPduToByteArrayConverter.convert(pdu, 8);
  }

  /**
   * @param nackPdu {@link AbortPdu} or {@link AbortPdu2}
   */
  private static void validateAbortPdu(final ASNSequence nackPdu) {
    Preconditions.checkNotNull(nackPdu, "AbortPdu must not be null");
  }
}
