package com.volvo.tisp.vwtp.builder;

import com.volvo.tisp.vwtp.builder.step.OtrStep;
import com.volvo.tisp.vwtp.builder.step.RidStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidReverseDirectionStep;
import com.volvo.tisp.vwtp.builder.step.WtpTidStep;
import com.volvo.tisp.vwtp.codec.AckPdu;

public interface AckPduBuilder<B>
    extends OtrStep<B>, RidStep<B>, WtpTidStep<B>, WtpTidReverseDirectionStep<B> {
  AckPdu buildAckPdu();

  static <T extends AckPduBuilder<T>> AckPduBuilder<T> builder() {
    return new PduBuilder<>();
  }
}
