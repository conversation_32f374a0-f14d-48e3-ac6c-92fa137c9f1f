package com.volvo.tisp.vwtp.responder;

import com.volvo.tisp.flow.FlowComposer;
import com.volvo.tisp.vwtp.constants.TransactionStatus;
import com.volvo.tisp.vwtp.dto.NetworkMessageDto;
import com.volvo.tisp.vwtp.dto.UserStatusDto;
import com.volvo.tisp.vwtp.util.MetricNames;
import com.volvo.tisp.vwtp.util.ResponderMetrics;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Timer;
import java.util.concurrent.TimeUnit;
import org.reactivestreams.Publisher;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.SynchronousSink;
import reactor.util.Logger;
import reactor.util.Loggers;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

/** Implementation of WTP responder flow for processing delivery statuses from user */
@Component("responderUserStatusFlow")
public class UserStatusFlow implements FlowComposer<UserStatusDto, Void> {
  private static final Logger logger = Loggers.getLogger(UserStatusFlow.class);
  private static final Counter irrelevantCounter =
      Counter.builder(MetricNames.RESPONDER_INCOMING_ANOMALIES)
          .description(
              "Count of received user delivery statuses for transacgtion that does not exist, or allready compleated")
          .tag(MetricNames.TYPE_TAG, "irrelevant-user-status")
          .register(Metrics.globalRegistry);
  private static final Timer userwaitDurations =
      Timer.builder(MetricNames.RESPONDER_USERWAIT_DURATIONS)
          .description("Duration of transaction in USERWAIT status")
          .register(Metrics.globalRegistry);

  private final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow;
  private final CacheService cacheService;

  /**
   * @param outgoingNetworkMessageFlow instance of {@link FlowComposer} &lt;{@link
   *     NetworkMessageDto}, {@link Void}&gt;
   * @param cacheService instance of {@link CacheService}
   */
  protected UserStatusFlow(
      @Qualifier("outgoingResponderNetworkMessageFlow")
          final FlowComposer<NetworkMessageDto, Void> outgoingNetworkMessageFlow,
      final CacheService cacheService) {
    this.outgoingNetworkMessageFlow = outgoingNetworkMessageFlow;
    this.cacheService = cacheService;
  }

  @Override
  public Publisher<Void> apply(final Flux<UserStatusDto> flux) {
    logger.info("Composing reactive flow");
    return flux.doOnNext(this::logMetrics)
        .handle(this::retrieveTransaction)
        .filter(this::ifTransactionIsWaitingForUserResponse)
        .map(this::generateNetworkResponse)
        .transform(outgoingNetworkMessageFlow);
  }

  /**
   * Logs received {@link UserStatusDto} metrics
   *
   * @param messageDto instance of {@link UserStatusDto}
   */
  private void logMetrics(final UserStatusDto messageDto) {
    ResponderMetrics.incrementIncomingUserStatus(messageDto);
  }

  /**
   * Lookup {@link Transaction} in the cache from data in {@link UserStatusDto} and advance further
   * down the flow
   *
   * @param userStatus {@link UserStatusDto}
   * @param sink {@link SynchronousSink} that is used to advance message further into a flow if
   *     {@link Transaction} exist in cache
   */
  private void retrieveTransaction(
      final UserStatusDto userStatus,
      final SynchronousSink<Tuple2<UserStatusDto, Transaction>> sink) {
    logger.debug(
        "Entering user status flow ({}): delivered={}",
        userStatus.getMessageId(),
        userStatus.isDelivered());
    final Transaction transaction =
        cacheService.responderReadWtpTransaction(userStatus.getMessageId());

    if (transaction == null) {
      logger.debug("No transaction found for Message ID: {}", userStatus.getMessageId());
    } else {
      userwaitDurations.record(
          System.currentTimeMillis() - transaction.getUserWaitTimestamp(), TimeUnit.MILLISECONDS);
      sink.next(Tuples.of(userStatus, transaction));
    }
  }

  /**
   * Filter function to allow only where {@link Transaction} status is {@link
   * TransactionStatus#USERWAIT}
   *
   * @param tuple {@link Tuple2}
   * @return if {@link Transaction} is not null
   */
  private boolean ifTransactionIsWaitingForUserResponse(
      final Tuple2<UserStatusDto, Transaction> tuple) {
    final boolean isUserwait = tuple.getT2().markCompleted() == TransactionStatus.USERWAIT;
    if (!isUserwait) {
      irrelevantCounter.increment();
      logger.debug("Filtered out on #ifTransactionIsWaitingForUserResponse");
    }
    return isUserwait;
  }

  /**
   * Mapping function that generates {@link NetworkMessageDto} with Acknowledgement or Abort PDU
   * depending on response in {@link UserStatusDto}
   *
   * @param tuple {@link Tuple2}
   * @return {@link NetworkMessageDto}
   */
  private NetworkMessageDto generateNetworkResponse(
      final Tuple2<UserStatusDto, Transaction> tuple) {
    final UserStatusDto userStatus = tuple.getT1();
    final Transaction transaction = tuple.getT2();

    if (userStatus.isDelivered()) {
      return ResponderUtils.createAcknowledgement(transaction);
    }

    return ResponderUtils.createAbort(transaction, userStatus.getAbortCode());
  }
}
