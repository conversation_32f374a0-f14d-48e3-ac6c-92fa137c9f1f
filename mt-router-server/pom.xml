<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->

  <parent>
    <groupId>com.volvo.tisp</groupId>
    <artifactId>tisp-parent</artifactId>
    <version>147</version>
    <relativePath/>
  </parent>

  <groupId>com.volvo.connectivity.mtrouter</groupId>
  <artifactId>mt-router-server</artifactId>
  <version>0-SNAPSHOT</version>
  <packaging>pom</packaging>
  <name>mt-router :: Server</name>
  <description>Description of component</description>

  <properties>
    <component.long.name>${project.artifactId}</component.long.name>
    <component.short.name>mtrouter</component.short.name>
    <component.debug.port>8787</component.debug.port>
    <component.debug.suspend>n</component.debug.suspend>
    <maven.compiler.failOnWarning>true</maven.compiler.failOnWarning>
    <nullaway.level>ERROR</nullaway.level>
    <spotbugs.failOnError>false</spotbugs.failOnError>
    <tisp-dependencies.version>147</tisp-dependencies.version>
    <component.zookeeper-url>127.0.0.1</component.zookeeper-url>
    <vc-influxdb-event-reporter-lib.version>46</vc-influxdb-event-reporter-lib.version>

    <tce-common-discovery.version>333</tce-common-discovery.version>
    <component-commons.version>45</component-commons.version>
    <deploy.engine.jar.version>243</deploy.engine.jar.version>
    <deploy.engine.maven.plugin.version>46</deploy.engine.maven.plugin.version>
    <deploy.engine.version>56</deploy.engine.version>
    <supervisor3x.version>51</supervisor3x.version>
    <jdk17.version>15</jdk17.version>
    <jaxb2-core-bindings.version>27</jaxb2-core-bindings.version>
    <lombok.version>1.18.36</lombok.version>
    <blockhound.version>1.0.10.RELEASE</blockhound.version>
    <vc-crypto-message.version>114</vc-crypto-message.version>
    <connectivity-repository-client2-api.version>226</connectivity-repository-client2-api.version>
    <subscriptionrepository-client.version>658</subscriptionrepository-client.version>
    <unit-test-lib.version>61</unit-test-lib.version>
    <connectivity-api.version>36</connectivity-api.version>
    <common-dto-lib.version>162</common-dto-lib.version>
    <asset-connectivity-repository-models.version>0.2.0</asset-connectivity-repository-models.version>
    <connectivity-management-service-models.version>0.5.0</connectivity-management-service-models.version>
  </properties>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency Management -->
  <!-- +=============================================== -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tisp-dependencies</artifactId>
        <version>${tisp-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-influxdb-event-reporter-lib</artifactId>
        <version>${vc-influxdb-event-reporter-lib.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.connectivity</groupId>
        <artifactId>connectivity-api</artifactId>
        <version>${connectivity-api.version}</version>
      </dependency>
      <dependency>
        <groupId>io.projectreactor.tools</groupId>
        <artifactId>blockhound</artifactId>
        <version>${blockhound.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>tce-common-discovery</artifactId>
        <version>${tce-common-discovery.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.volvo.tisp</groupId>
            <artifactId>tce-common-proto-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.volvo.connectivity.common</groupId>
        <artifactId>component-commons</artifactId>
        <version>${component-commons.version}</version>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.framework.deploy-engine</groupId>
        <artifactId>deploy-engine-jar-deployer</artifactId>
        <version>${deploy.engine.jar.version}</version>
        <classifier>bundle</classifier>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>com.wirelesscar.framework.deploy-engine</groupId>
        <artifactId>deploy-engine</artifactId>
        <version>${deploy.engine.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.supervisord</groupId>
        <artifactId>supervisor3x</artifactId>
        <version>${supervisor3x.version}</version>
        <type>npm</type>
      </dependency>
      <dependency>
        <groupId>net.java.jdk</groupId>
        <artifactId>jdk17</artifactId>
        <version>${jdk17.version}</version>
        <type>npm</type>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>vc-crypto-message</artifactId>
        <version>${vc-crypto-message.version}</version>
      </dependency>
      <dependency>
        <groupId>com.volvo.vc.conrepo2</groupId>
        <artifactId>connectivity-repository-client2-api</artifactId>
        <version>${connectivity-repository-client2-api.version}</version>
      </dependency>

      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-impl</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>

      <dependency>
        <groupId>com.wirelesscar.subscriptionrepository</groupId>
        <artifactId>subscriptionrepository-client-test-util</artifactId>
        <version>${subscriptionrepository-client.version}</version>
      </dependency>

      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
        <scope>provided</scope>
      </dependency>
      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>unit-test-lib</artifactId>
        <version>${unit-test-lib.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.tisp</groupId>
        <artifactId>common-dto-lib</artifactId>
        <version>${common-dto-lib.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.connectivity</groupId>
        <artifactId>asset-connectivity-repository-models</artifactId>
        <version>${asset-connectivity-repository-models.version}</version>
      </dependency>

      <dependency>
        <groupId>com.volvo.connectivity</groupId>
        <artifactId>connectivity-management-service-models</artifactId>
        <version>${connectivity-management-service-models.version}</version>
      </dependency>

    </dependencies>
  </dependencyManagement>

  <!-- +=============================================== -->
  <!-- | Section 3: Module definitions -->
  <!-- +=============================================== -->

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <configuration>
            <annotationProcessorPaths combine.children="append">
              <path>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
              </path>
            </annotationProcessorPaths>
          </configuration>
        </plugin>
        <plugin>
          <groupId>com.wirelesscar.framework.deploy-engine</groupId>
          <artifactId>deploy-engine-maven-plugin</artifactId>
          <version>${deploy.engine.maven.plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <profile>
      <id>default</id>
      <activation>
        <activeByDefault>true</activeByDefault>
      </activation>
      <modules>
        <module>mt-router-server-app</module>
        <module>mt-router-server-impl</module>
      </modules>
    </profile>
    <profile>
      <id>deployable-assembly</id>
      <activation>
        <property>
          <name>deployable-assembly</name>
        </property>
      </activation>
      <modules>
        <module>deployable-assembly</module>
      </modules>
    </profile>
    <profile>
      <id>component-tests</id>
    </profile>
  </profiles>
</project>
