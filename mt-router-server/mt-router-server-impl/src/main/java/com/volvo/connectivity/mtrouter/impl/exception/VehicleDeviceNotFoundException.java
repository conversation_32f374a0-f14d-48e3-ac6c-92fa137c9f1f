package com.volvo.connectivity.mtrouter.impl.exception;

import java.io.Serial;

public class VehicleDeviceNotFoundException extends RuntimeException {
  @Serial private static final long serialVersionUID = 7991797329405831293L;

  public VehicleDeviceNotFoundException(final String message, final Throwable cause) {
    super(message, cause);
  }

  public VehicleDeviceNotFoundException(final Throwable e) {
    super(e.getMessage(), e);
  }

  public VehicleDeviceNotFoundException(final String message) {
    super(message);
  }
}
