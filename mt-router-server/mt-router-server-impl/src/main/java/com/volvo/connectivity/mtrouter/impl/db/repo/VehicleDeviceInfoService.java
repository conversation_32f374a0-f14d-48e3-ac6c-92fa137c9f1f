package com.volvo.connectivity.mtrouter.impl.db.repo;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.util.VehicleDeviceInfoMapper;
import java.util.Collection;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Service
@SuppressWarnings("CE_CLASS_ENVY")
public class VehicleDeviceInfoService {

  private final ReactiveMongoTemplate reactiveMongoTemplate;

  public VehicleDeviceInfoService(ReactiveMongoTemplate reactiveMongoTemplate) {
    this.reactiveMongoTemplate = reactiveMongoTemplate;
  }

  public Mono<Void> insertOrUpdateActivatedVehicles(
      Collection<VehicleDeviceInfo> activatedVehicleDeviceInfoList) {
    return Flux.fromIterable(activatedVehicleDeviceInfoList)
        .flatMap(
            vehicleDeviceInfo -> {
              Query query = new Query(Criteria.where("vpi").is(vehicleDeviceInfo.getVpi()));
              Update update = VehicleDeviceInfoMapper.toUpdate(vehicleDeviceInfo);

              return reactiveMongoTemplate
                  .upsert(query, update, VehicleDeviceInfo.class)
                  .doOnError(
                      throwable ->
                          log.error(
                              "Error during upsert vehicleDeviceInfo with vpi: {}. {}",
                              vehicleDeviceInfo.getVpi(),
                              throwable.getMessage()))
                  .doOnSuccess(
                      updateResult ->
                          log.debug(
                              "Successfully upsert vehicleDeviceInfo with vpi: {}",
                              vehicleDeviceInfo.getVpi()));
            })
        .doOnComplete(() -> log.debug("All activated vehicles were inserted/updated successfully"))
        .then();
  }

  public Mono<Void> removeDeActivatedVehicles(
      Collection<VehicleDeviceInfo> deactivatedVehicleDeviceInfoList) {
    return Flux.fromIterable(deactivatedVehicleDeviceInfoList)
        .flatMap(
            deviceInfo ->
                reactiveMongoTemplate
                    .remove(deviceInfo, "vehicleDeviceInfo")
                    .doOnError(
                        throwable ->
                            log.error(
                                "Error during removing deactivated vehicleDeviceInfo with vpi: {}. {}",
                                deviceInfo.getVpi(),
                                throwable.getMessage()))
                    .doOnSuccess(
                        deleteResult ->
                            log.debug(
                                "Successfully removed deactivated vehicleDeviceInfo with vpi: {}",
                                deviceInfo.getVpi())))
        .then();
  }
}
