package com.volvo.connectivity.mtrouter.impl.security;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.vc.crypto.common.encoding.MessageDecoder;
import com.volvo.vc.crypto.common.encoding.MessageDecodingException;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.Unmarshaller;
import java.io.StringReader;

public final class MessageDecoderXmlImpl<T> implements MessageDecoder {
  private final JAXBContext jaxbContext;

  public MessageDecoderXmlImpl(Class<T> type) {
    try {
      jaxbContext = JAXBContext.newInstance(type);
    } catch (Exception e) {
      throw new RuntimeException("Error initializing JAXBContext", e);
    }
  }

  @Override
  public <R> Either<RuntimeException, R> decode(PlainTextPayload plainTextPayload, Class<R> type) {
    Validate.notNull(plainTextPayload, "plainTextPayload");
    Validate.notNull(type, "type");

    try {
      Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();

      try (StringReader stringReader = new StringReader(plainTextPayload.toString())) {
        return Either.right(type.cast(unmarshaller.unmarshal(stringReader)));
      }
    } catch (Exception e) {
      return Either.left(new MessageDecodingException(e));
    }
  }
}
