package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import org.springframework.data.mongodb.core.query.Update;

public class VehicleDeviceInfoMapper {

  private VehicleDeviceInfoMapper() {}

  public static Update toUpdate(VehicleDeviceInfo deviceInfo) {
    return new Update()
        .set("obsAlias", deviceInfo.getObsAlias())
        .set("wtpVersion", deviceInfo.getWtpVersion())
        .set("ipv4Address", deviceInfo.getIpv4Address())
        .set("ipv4Port", deviceInfo.getIpv4Port())
        .set("mobileNetworkOperator", deviceInfo.getMobileNetworkOperator())
        .set("msisdn", deviceInfo.getMsisdn())
        .set("satelliteId", deviceInfo.getSatelliteId())
        .set("vpnIpv4Address", deviceInfo.getVpnIpv4Address())
        .set("telematicUnit", deviceInfo.getTelematicUnit());
  }
}
