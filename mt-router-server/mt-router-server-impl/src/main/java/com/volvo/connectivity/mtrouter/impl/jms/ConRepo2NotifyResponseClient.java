package com.volvo.connectivity.mtrouter.impl.jms;

import com.volvo.connectivity.mtrouter.impl.util.MetricsReporter;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.vc.conrepo.api.v2.NotifyResponseMessage;
import java.util.concurrent.CompletableFuture;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import reactor.core.publisher.Mono;

@Slf4j
@Service
public class ConRepo2NotifyResponseClient {
  MessagePublisher<NotifyResponseMessage> notifyResponseMessageMessagePublisher;

  public ConRepo2NotifyResponseClient(
      MessagePublisher<NotifyResponseMessage> notifyResponseMessageMessagePublisher) {
    this.notifyResponseMessageMessagePublisher = notifyResponseMessageMessagePublisher;
  }

  public void publishResponseMessage(NotifyResponseMessage notifyResponseMessage) {
    log.debug(
        "Publishing NotifyResponseMessage. correlationId: {}, responseStatus: {}",
        notifyResponseMessage.getReferenceId(),
        notifyResponseMessage.getResponseStatus());
    CompletableFuture<Integer> future =
        notifyResponseMessageMessagePublisher
            .newMessage()
            .correlationId(notifyResponseMessage.getReferenceId())
            .publish(notifyResponseMessage);

    Mono.fromFuture(() -> future)
        .doOnNext(
            numberOfPublishedMessages -> {
              log.info(
                  "Successfully published NotifyResponseMessage. noOfPublishedMessages: {}, correlationId: {}, responseStatus: {}",
                  numberOfPublishedMessages,
                  notifyResponseMessage.getReferenceId(),
                  notifyResponseMessage.getResponseStatus());
              MetricsReporter.onNotifyResponsePublishSuccess();
            })
        .onErrorResume(
            throwable -> {
              log.error(
                  "Failed to publish NotifyResponseMessage. correlationId: {}, responseStatus: {}",
                  notifyResponseMessage.getReferenceId(),
                  notifyResponseMessage.getResponseStatus(),
                  throwable);
              MetricsReporter.onNotifyResponsePublishFailure();
              return Mono.error(
                  new RuntimeException("Failed to publish NotifyResponseMessage.", throwable));
            })
        .subscribe();
  }
}
