package com.volvo.connectivity.mtrouter.impl.util;

/** Utility class that tracks names of metrics and metric tag names */
public class MetricNames {
  public static final String MT_ROUTER_OUTGOING_USER_MESSAGES = "mt.router.outgoing.usermesages";
  public static final String ANOMALIES = "mt.router.anomalies";
  public static final String MT_INCOMING_ANOMALIES = "mt.router.incoming.anomalies";
  public static final String ACTIVATION_NOTIFY = "mt.router.secured-activation-notify";
  public static final String WECU_CHANGE_NOTIFY = "mt.router.wecu-change-notify";
  public static final String TIS_MESSAGE_SENDING = "mt.router.tis-message.sending";
  public static final String DEACTIVATION_NOTIFY = "mt.router.deactivation-notify";
  public static final String VPN_IP_UPDATE_EVENT = "mt.router.vpn-ip.update";
  public static final String STATUS_TAG = "status";
  public static final String TYPE_TAG = "type";
  public static final String SUCCESS = "success";
  public static final String FAIL = "fail";

  private MetricNames() {}
}
