package com.volvo.connectivity.mtrouter.impl.conf;

import static java.util.Locale.ENGLISH;

import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.DefaultValue;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@ConfigurationProperties("bulksync")
public record BulkSyncProperties(
    @NotBlank String publicCertificate,
    @NotBlank String privateKey,
    @NotBlank String clientKeyAlias,
    @NotBlank String conrepoKeyAlias,
    @DefaultValue("3000") int batchSize)
    implements DisposableBean, InitializingBean {

  private static final String FORMAT = "%-45s: %s";

  private void logProperties() {
    if (log.isInfoEnabled()) {
      log.info("");
      log.info(String.format(ENGLISH, FORMAT, "BULKSYNC CONFIGURATION PROPERTIES", ""));
      log.info("");
      log.info(String.format(ENGLISH, FORMAT, "bulksync.public-certificate", "***"));
      log.info(String.format(ENGLISH, FORMAT, "bulksync.private-key", "***"));
      log.info(String.format(ENGLISH, FORMAT, "bulksync.client-key-alias", clientKeyAlias));
      log.info(String.format(ENGLISH, FORMAT, "bulksync.conrepo-key-alias", conrepoKeyAlias));
      log.info(String.format(ENGLISH, FORMAT, "bulksync.batch-size", batchSize));
    }
  }

  @Override
  public void afterPropertiesSet() {
    logProperties();
  }

  @Override
  public void destroy() {
    logProperties();
  }
}
