package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.tisp.framework.http.TispHttpHeaders;
import org.springframework.http.HttpHeaders;
import org.springframework.web.server.ServerWebExchange;
import org.springframework.web.server.WebFilter;
import org.springframework.web.server.WebFilterChain;
import reactor.core.publisher.Mono;

public class TispHeadersFilter implements WebFilter {

  @Override
  public Mono<Void> filter(ServerWebExchange exchange, WebFilterChain chain) {
    HttpHeaders headers = exchange.getRequest().getHeaders();
    return chain
        .filter(exchange)
        .contextWrite(
            context ->
                context
                    .putNonNull(
                        TispHttpHeaders.TRACKING_ID,
                        headers.getFirst(TispHttpHeaders.TRACKING_ID.key()))
                    .putNonNull(
                        TispHttpHeaders.WORKFLOW_ID,
                        headers.getFirst(TispHttpHeaders.WORKFLOW_ID.key())));
  }
}
