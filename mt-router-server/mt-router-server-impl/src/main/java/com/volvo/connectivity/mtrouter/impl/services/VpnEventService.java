package com.volvo.connectivity.mtrouter.impl.services;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.util.MetricsReporter;
import com.volvo.connectivity.proto.VpnEvent;
import com.volvo.connectivity.proto.VpnStatus;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class VpnEventService {

  private final ReactiveMongoTemplate reactiveMongoTemplate;

  public VpnEventService(ReactiveMongoTemplate reactiveMongoTemplate) {
    this.reactiveMongoTemplate = reactiveMongoTemplate;
  }

  public void updateVpnIpAddress(VpnEvent vpnEvent) {
    Validate.notNull(vpnEvent, "vpnEvent");
    String vpnIpAddress = vpnEvent.getStatus() == VpnStatus.ACTIVATED ? vpnEvent.getIp() : null;
    VehicleDeviceInfo vehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(vpnEvent.getVpi())
            .setVpnIpv4Address(vpnIpAddress)
            .build();
    updateVehicleDeviceInfo(vehicleDeviceInfo, vpnEvent.getStatus());
  }

  private void updateVehicleDeviceInfo(VehicleDeviceInfo deviceInfo, VpnStatus vpnStatus) {
    Query query = new Query(Criteria.where("vpi").is(deviceInfo.getVpi()));
    Update update = new Update().set("vpnIpv4Address", deviceInfo.getVpnIpv4Address());

    reactiveMongoTemplate
        .updateFirst(query, update, VehicleDeviceInfo.class)
        .doOnError(
            throwable -> {
              MetricsReporter.onVpnIpUpdateRequestFailure(vpnStatus);
              log.error(
                  "Error during updating the vpnIpv4Address: {} of vpi: {} from vpnEvent: {}. {}",
                  deviceInfo.getVpnIpv4Address(),
                  deviceInfo.getVpi(),
                  vpnStatus,
                  throwable.getMessage());
            })
        .doOnSuccess(
            updateResult -> {
              MetricsReporter.onVpnIpUpdateRequestSuccess(vpnStatus);
              log.debug(
                  "Successfully updated the vpnIpv4Address: {} of vpi: {} from vpnEvent: {}",
                  deviceInfo.getVpnIpv4Address(),
                  deviceInfo.getVpi(),
                  vpnStatus);
            })
        .block();
  }
}
