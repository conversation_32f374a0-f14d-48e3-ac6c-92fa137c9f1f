package com.volvo.connectivity.mtrouter.impl.controller;

import com.volvo.connectivity.mtrouter.impl.services.DataEnrichmentService;
import com.volvo.connectivity.mtrouter.impl.services.RoutingService;
import com.volvo.connectivity.mtrouter.impl.util.MessageValidator;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import java.nio.charset.StandardCharsets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@Authentication(required = false)
public class MtRouterController {
  private static final String PATH = "/api/v1/mt/message";

  private final DataEnrichmentService dataEnrichment;
  private final RoutingService routingService;

  @Autowired
  public MtRouterController(
      final DataEnrichmentService dataEnrichment, final RoutingService routingService) {
    Validate.notNull(dataEnrichment, "dataEnrichment");
    Validate.notNull(routingService, "routingService");
    this.dataEnrichment = dataEnrichment;
    this.routingService = routingService;
  }

  @PostMapping(path = PATH)
  public Mono<Void> receiveMt(@RequestBody final Mono<MtMessage> mtMessageMono) {
    return mtMessageMono
        .doOnNext(MessageValidator::validateMtMessage)
        .flatMap(dataEnrichment::convertToProto)
        .flatMap(routingService::route);
  }

  /**
   * Error handler for all service handlers
   *
   * @param throwable instance of {@link Throwable}
   * @return instance of {@link ResponseEntity}&lt;{@link String}&gt;
   */
  @ExceptionHandler
  protected ResponseEntity<String> handleException(final Throwable throwable) {
    log.error("", throwable);
    if (throwable instanceof WebClientResponseException wcre) {
      return ResponseEntity.status(wcre.getStatusCode())
          .<String>body(wcre.getResponseBodyAsString(StandardCharsets.UTF_8));
    } else if (throwable instanceof ResponseStatusException rse) {
      return ResponseEntity.status(rse.getStatusCode()).<String>body(rse.getMessage());
    } else {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .<String>body(throwable.toString());
    }
  }
}
