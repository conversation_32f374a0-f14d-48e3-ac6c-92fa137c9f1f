package com.volvo.connectivity.mtrouter.impl.services;

import static org.springframework.http.converter.protobuf.ProtobufHttpMessageConverter.PROTOBUF;

import com.google.protobuf.MessageOrBuilder;
import com.volvo.connectivity.mtrouter.impl.util.MetricsReporter;
import com.volvo.connectivity.proto.Address;
import com.volvo.connectivity.proto.TisMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.http.TispHttpHeaders;
import com.volvo.tisp.identifier.TrackingIdentifier;
import com.volvo.tisp.identifier.WorkflowIdentifier;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import java.net.URI;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.x.discovery.ServiceInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.util.UriComponentsBuilder;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class RoutingService {
  private final WebClient webClient;
  private final SharderServiceProvider sharderServiceProvider;
  private final MessagePublisher<UserMessage> satMessagePublisher;
  private final MessagePublisher<UserMessage> smsMessagePublisher;
  private final String tisRouterUrl;

  @Autowired
  public RoutingService(
      final WebClient.Builder webClientBuilder,
      final SharderServiceProvider sharderServiceProvider,
      final MessagePublisher<UserMessage> satMessagePublisher,
      final MessagePublisher<UserMessage> smsMessagePublisher,
      @Value("${tis-router.url}") final String tisRouterUrl) {

    Validate.notNull(webClientBuilder, "webClientBuilder");
    Validate.notNull(sharderServiceProvider, "sharderServiceProvider");
    Validate.notNull(satMessagePublisher, "satMessagePublisher");
    Validate.notNull(smsMessagePublisher, "smsMessagePublisher");

    this.webClient = webClientBuilder.build();
    this.sharderServiceProvider = sharderServiceProvider;
    this.satMessagePublisher = satMessagePublisher;
    this.smsMessagePublisher = smsMessagePublisher;
    this.tisRouterUrl = tisRouterUrl;
  }

  public Mono<Void> route(MessageOrBuilder message) {

    if (message instanceof UserMessage userMessage) {
      return routeUserMessage(userMessage);
    } else {
      return routeTisMessage((TisMessage) message);
    }
  }

  public Mono<Void> routeUserMessage(UserMessage userMessage) {

    Transport transport = userMessage.getAddress().getTransport();
    Mono<Void> routingMono =
        switch (transport) {
          case UDP, VPN -> this.vwtpInitiatorFlow(userMessage);
          case SMS -> this.messagePublisherFlow(userMessage, smsMessagePublisher);
          case SAT -> this.messagePublisherFlow(userMessage, satMessagePublisher);
          default ->
              Mono.error(
                  new ResponseStatusException(
                      HttpStatus.BAD_REQUEST, "Unsupported transport value: " + transport));
        };
    return routingMono
        .doOnSuccess(nothing -> MetricsReporter.onMtUserMessageStatusSuccess(transport))
        .doOnError(nothing -> MetricsReporter.onMtUserMessageStatusFailed(transport));
  }

  public Mono<Void> routeTisMessage(TisMessage tisMessage) {
    return webClient
        .post()
        .uri(UriComponentsBuilder.fromHttpUrl(tisRouterUrl).path("/in").build().toUri())
        .accept(PROTOBUF)
        .contentType(PROTOBUF)
        .body(Mono.just(tisMessage), TisMessage.class)
        .retrieve()
        .bodyToMono(Void.class)
        .doOnSuccess(nothing -> MetricsReporter.onTisMessageSendingSuccess())
        .doOnError(nothing -> MetricsReporter.onTisMessageSendingFailure());
  }

  public Mono<Void> vwtpInitiatorFlow(UserMessage userMessage) {
    /* UDP is used even for VPN when sends down, rebuild the UserMessage with UDP transport in
    address */
    UserMessage udpUserMessage =
        userMessage.toBuilder()
            .setAddress(userMessage.getAddress().toBuilder().setTransport(Transport.UDP).build())
            .build();
    Address address = udpUserMessage.getAddress();
    Transport transport = address.getTransport();
    URI uri =
        URI.create(
            transport.name() + "://" + address.getDestination() + ":" + address.getQualifier());
    ServiceInstance<InstanceDetails> serviceInstance =
        sharderServiceProvider.getServiceInstanceFromObject(uri);

    if (serviceInstance != null) {
      log.debug("Sending message to instance: {} of UDP flow.", serviceInstance);
      return this.postToService(udpUserMessage, serviceInstance);
    } else {
      return Mono.error(new RuntimeException("UDP service instance not found"));
    }
  }

  public Mono<Void> postToService(
      final UserMessage userMessage, final ServiceInstance<InstanceDetails> serviceInstance) {
    return Mono.deferContextual(
        context ->
            webClient
                .post()
                .uri(serviceInstance.buildUriSpec())
                .headers(
                    headers -> {
                      context
                          .<String>getOrEmpty(TispHttpHeaders.TRACKING_ID)
                          .ifPresent(tid -> headers.set(TispHttpHeaders.TRACKING_ID.key(), tid));
                      context
                          .<String>getOrEmpty(TispHttpHeaders.WORKFLOW_ID)
                          .ifPresent(wid -> headers.set(TispHttpHeaders.WORKFLOW_ID.key(), wid));
                    })
                .bodyValue(userMessage)
                .retrieve()
                .toBodilessEntity()
                .then());
  }

  public Mono<Void> messagePublisherFlow(
      UserMessage message, MessagePublisher<UserMessage> messagePublisher) {
    return Mono.deferContextual(
        context ->
            Mono.fromFuture(
                TispContext.supplyInContext(
                    () ->
                        messagePublisher
                            .newMessage()
                            .publish(message)
                            .thenAccept(
                                numberOfMessages ->
                                    log.debug(
                                        "{} was published to {}",
                                        numberOfMessages,
                                        message.getAddress().getTransport())),
                    builder -> {
                      context
                          .<String>getOrEmpty(TispHttpHeaders.TRACKING_ID)
                          .map(TrackingIdentifier::fromString)
                          .ifPresent(builder::tid);
                      context
                          .<String>getOrEmpty(TispHttpHeaders.WORKFLOW_ID)
                          .map(WorkflowIdentifier::fromString)
                          .ifPresent(builder::wid);
                      return builder;
                    })));
  }
}
