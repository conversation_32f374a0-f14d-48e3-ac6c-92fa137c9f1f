package com.volvo.connectivity.mtrouter.impl.db.entity;

import jakarta.validation.constraints.*;
import lombok.Builder;
import lombok.Value;
import org.bson.codecs.pojo.annotations.BsonId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/***
 *  This entity represents vehicle device information. */

@Value
@Builder(setterPrefix = "set")
@Document(collection = "vehicleDeviceInfo")
public class VehicleDeviceInfo {

  @NotEmpty
  @Pattern(regexp = "[0-9A-F]{32}", message = "value '${validatedValue}' is invalid")
  @BsonId
  @Id
  String vpi;

  @Positive
  @Max(
      value = 4294967295L,
      message = "value '${validatedValue}' must be positive and not exceed 4294967295L")
  Long obsAlias;

  WtpVersion wtpVersion;

  @Pattern(
      regexp = "^(((1?[1-9]?|10|2[0-4])\\d|25[0-5])($|\\.(?!$))){4}$",
      message = "value '${validatedValue}' is invalid")
  String ipv4Address;

  @PositiveOrZero
  @Max(value = 65535, message = "value '${validatedValue}' must be between 0 and 65535")
  int ipv4Port;

  @Size(
      min = 1,
      max = 200,
      message = "length of '${validatedValue}' must be between {min} and {max}")
  String mobileNetworkOperator;

  @Pattern(regexp = "\\+\\d{5,15}$", message = "value '${validatedValue}' is invalid")
  String msisdn;

  @Pattern(
      regexp = "^\\d{8}SKY[a-zA-Z0-9]{4}|SOFTCAR\\d{7}$",
      message = "value '${validatedValue}' is invalid")
  String satelliteId;

  @Pattern(
      regexp = "^(((1?[1-9]?|10|2[0-4])\\d|25[0-5])($|\\.(?!$))){4}$",
      message = "value '${validatedValue}' is invalid")
  String vpnIpv4Address;

  TelematicUnit telematicUnit;
}
