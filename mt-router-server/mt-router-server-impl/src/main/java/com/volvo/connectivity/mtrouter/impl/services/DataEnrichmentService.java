package com.volvo.connectivity.mtrouter.impl.services;

import com.google.protobuf.MessageOrBuilder;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.impl.exception.VehicleDeviceNotFoundException;
import com.volvo.connectivity.proto.*;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ResponseStatusException;
import reactor.core.publisher.Mono;

@SuppressWarnings("EnumOrdinal")
@Component
public class DataEnrichmentService {
  private final VehicleDeviceInfoRepository vehicleDeviceInfoRepository;

  public DataEnrichmentService(final VehicleDeviceInfoRepository vehicleDeviceInfoRepository) {
    Validate.notNull(vehicleDeviceInfoRepository, "vehicleDeviceInfoRepository");
    this.vehicleDeviceInfoRepository = vehicleDeviceInfoRepository;
  }

  public Mono<MessageOrBuilder> convertToProto(final MtMessage mtMessage) {
    String vpi = mtMessage.getVpi();
    return vehicleDeviceInfoRepository
        .findByVpi(vpi)
        .switchIfEmpty(
            Mono.error(
                () ->
                    new VehicleDeviceNotFoundException("No vehicle device found for vpi: " + vpi)))
        .map(
            vehicleDeviceInfo ->
                enrichUserMessageWithVehicleDeviceInformation(vehicleDeviceInfo, mtMessage));
  }

  private MessageOrBuilder enrichUserMessageWithVehicleDeviceInformation(
      final VehicleDeviceInfo vehicleDeviceInfo, final MtMessageOrBuilder message) {
    if (TelematicUnit.WECU == vehicleDeviceInfo.getTelematicUnit()) {
      return TisMessage.newBuilder()
          .setMessageId(message.getMessageId())
          .setPayload(message.getPayload())
          .setAddress(generateAssetAddress(vehicleDeviceInfo, message.getTransport()))
          .build();

    } else {
      Address address = createAddress(vehicleDeviceInfo, message.getTransport());
      return UserMessage.newBuilder()
          .setVehicleIdentifier(vehicleDeviceInfo.getObsAlias())
          .setPayload(message.getPayload())
          .setWtpVersionValue(vehicleDeviceInfo.getWtpVersion().ordinal())
          .setAddress(address)
          .setTransactionClass(TransactionClass.CLASS_1)
          .setMessageId(message.getMessageId())
          .build();
    }
  }

  private AssetAddress generateAssetAddress(
      final VehicleDeviceInfo vehicleDeviceInfo, final Transport transport) {
    AssetAddress.Builder assetAddressBuilder = AssetAddress.newBuilder();
    return switch (transport) {
      case UDP ->
          assetAddressBuilder
              .setUdp(
                  UdpAddress.newBuilder()
                      .setIp(vehicleDeviceInfo.getIpv4Address())
                      .setPort(vehicleDeviceInfo.getIpv4Port())
                      .build())
              .build();
      case SMS ->
          assetAddressBuilder
              .setSms(
                  SmsAddress.newBuilder()
                      .setMsisdn(vehicleDeviceInfo.getMsisdn())
                      .setOperator(vehicleDeviceInfo.getMobileNetworkOperator()))
              .build();
      case SAT ->
          assetAddressBuilder
              .setSat(SatAddress.newBuilder().setSatId(vehicleDeviceInfo.getSatelliteId()))
              .build();
      default -> throw new IllegalStateException("Unexpected transport type : " + transport);
    };
  }

  private Address createAddress(
      final VehicleDeviceInfo vehicleDeviceInfo, final Transport transport) {
    return switch (transport) {
      case UDP ->
          Address.newBuilder()
              .setDestination(vehicleDeviceInfo.getIpv4Address())
              .setQualifier(Integer.toString(vehicleDeviceInfo.getIpv4Port()))
              .setTransport(transport)
              .build();
      case VPN ->
          Address.newBuilder()
              .setDestination(vehicleDeviceInfo.getVpnIpv4Address())
              .setQualifier(Integer.toString(vehicleDeviceInfo.getIpv4Port()))
              .setTransport(transport)
              .build();
      case SMS ->
          Address.newBuilder()
              .setDestination(vehicleDeviceInfo.getMsisdn())
              .setQualifier(vehicleDeviceInfo.getMobileNetworkOperator())
              .setTransport(transport)
              .build();
      case SAT ->
          Address.newBuilder()
              .setDestination(vehicleDeviceInfo.getSatelliteId())
              .setTransport(transport)
              .build();
      default ->
          throw new ResponseStatusException(
              HttpStatus.BAD_REQUEST, "Unsupported transport value: " + transport);
    };
  }
}
