package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.connectivity.asset.repository.MessageTypes;
import com.volvo.tisp.framework.jms.TispJmsHeader;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import org.springframework.jms.core.MessagePostProcessor;

@SuppressFBWarnings("CFS_CONFUSING_FUNCTION_SEMANTICS")
public class JmsUtils {

  public static MessagePostProcessor getMessagePostProcessorAndSetCorrelationId(
      String correlationId) {
    return message -> {
      message.setStringProperty(
          TispJmsHeader.MESSAGE_TYPE.value(), MessageTypes.CHANGE_NOTIFICATION_RESPONSE_MESSAGE);
      message.setStringProperty(
          TispJmsHeader.MESSAGE_TYPE_VERSION.value(), MessageTypes.VERSION_1_0);
      message.setJMSCorrelationID(correlationId);
      return message;
    };
  }
}
