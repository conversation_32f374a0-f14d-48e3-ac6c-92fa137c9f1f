package com.volvo.connectivity.mtrouter.impl.conf;

import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.proto.UserMessage;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.vc.conrepo.MessageTypes;
import com.volvo.vc.conrepo.api.v2.NotifyResponseMessage;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class JmsConfiguration {

  @Bean
  MessagePublisher<NotifyResponseMessage> createResponsePublisher(
      MessagePublisher.Builder builder) {
    return builder
        .messageType(MessageTypes.NOTIFY_RESPONSE_MESSAGE_TYPE, NotifyResponseMessage.class)
        .version(MessageTypes.VERSION_2_0)
        .build();
  }

  @Bean
  MessagePublisher<UserMessage> satMessagePublisher(MessagePublisher.Builder builder) {
    return builder
        .messageType(ServiceConstant.MT_SATELLITE_MESSAGE_TYPE, UserMessage.class)
        .version("1.0")
        .build();
  }

  @Bean
  MessagePublisher<UserMessage> smsMessagePublisher(MessagePublisher.Builder builder) {
    return builder
        .messageType(ServiceConstant.MT_TGW_SMS_MESSAGE_TYPE, UserMessage.class)
        .version("1.0")
        .build();
  }
}
