package com.volvo.connectivity.mtrouter.impl.conf;

import static java.util.Locale.ENGLISH;

import jakarta.validation.constraints.NotBlank;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.bind.Name;
import org.springframework.validation.annotation.Validated;

@Slf4j
@Validated
@ConfigurationProperties("security.transport")
public record SecureTransportProperties(
    @NotBlank @Name("keystore.password") String keystorePasswordAlias,
    @NotBlank @Name("keystore.path") String keystorePath,
    @NotBlank @Name("notify.key.public") String notifyPublicKey,
    @NotBlank @Name("key.private") String privateKey)
    implements InitializingBean, DisposableBean {
  private static final String FORMAT = "%-45s: %s";

  private void logProperties() {
    if (log.isDebugEnabled()) {
      log.debug(String.format(ENGLISH, FORMAT, "security.transport.keystore.path", keystorePath));
      log.debug(
          String.format(ENGLISH, FORMAT, "security.transport.notify.key.public", notifyPublicKey));
    }
  }

  @Override
  public void afterPropertiesSet() {
    logProperties();
  }

  @Override
  public void destroy() {
    logProperties();
  }
}
