package com.volvo.connectivity.mtrouter.impl.conf;

import com.volvo.vc.crypto.asymmetric.encryption.AsymmetricEncryptionService;
import com.volvo.vc.crypto.asymmetric.encryption.rsa.RsaEncryptionServiceImpl;
import com.volvo.vc.crypto.asymmetric.key.AsymmetricKeyRepository;
import com.volvo.vc.crypto.asymmetric.key.rsa.RsaKeyRepositoryImpl;
import com.volvo.vc.crypto.asymmetric.signature.AsymmetricSigningService;
import com.volvo.vc.crypto.asymmetric.signature.rsa.RsaSigningService;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepository;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepositoryConfig;
import com.volvo.vc.crypto.common.keystore.impl.KeyStoreCacheRepositoryImpl;
import com.volvo.vc.crypto.common.keystore.impl.KeyStoreRepositoryImpl;
import com.volvo.vc.crypto.common.keystore.password.Password;
import com.volvo.vc.crypto.message.decryption.MessageDecryptionService;
import com.volvo.vc.crypto.message.decryption.MessageDecryptionServiceImpl;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionService;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionServiceImpl;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;
import java.nio.file.FileSystems;
import java.nio.file.Path;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class TransportSecurityConfiguration {
  private final SecureTransportProperties propertiesConfig;

  public TransportSecurityConfiguration(SecureTransportProperties propertiesConfig) {
    this.propertiesConfig = propertiesConfig;
  }

  @Bean
  AsymmetricEncryptionService createAsymmetricEncryptionService() {
    return RsaEncryptionServiceImpl.INSTANCE;
  }

  @Bean
  AsymmetricKeyRepository createAsymmetricKeyRepository(KeyStoreRepository keyStoreRepository) {
    return RsaKeyRepositoryImpl.create(keyStoreRepository);
  }

  @Bean
  KeyStoreRepository createAsymmetricKeyStoreRepository() {
    Password passwordAlias = Password.ofString(propertiesConfig.keystorePasswordAlias());
    Path path = FileSystems.getDefault().getPath(propertiesConfig.keystorePath());
    KeyStoreRepositoryConfig keyStoreRepositoryConfig =
        new KeyStoreRepositoryConfig(passwordAlias, path);
    KeyStoreRepository keyStoreRepository =
        KeyStoreRepositoryImpl.createKeyStoreRepository(keyStoreRepositoryConfig);
    return KeyStoreCacheRepositoryImpl.create(keyStoreRepository);
  }

  @Bean
  AsymmetricSigningService createAsymmetricSigningService() {
    return RsaSigningService.INSTANCE;
  }

  @Bean
  MessageDecryptionService createMessageDecryptionService(
      AsymmetricEncryptionService asymmetricEncryptionService,
      AsymmetricSigningService asymmetricSigningService,
      AsymmetricKeyRepository asymmetricKeyRepository) {
    return MessageDecryptionServiceImpl.create(
        asymmetricEncryptionService,
        asymmetricSigningService,
        asymmetricKeyRepository,
        SymmetricAesGcmEncryptionServiceImpl.INSTANCE);
  }

  @Bean
  MessageEncryptionService createMessageEncryptionService(
      AsymmetricEncryptionService asymmetricEncryptionService,
      AsymmetricSigningService asymmetricSigningService,
      AsymmetricKeyRepository asymmetricKeyRepository) {
    return MessageEncryptionServiceImpl.create(
        asymmetricEncryptionService,
        asymmetricSigningService,
        asymmetricKeyRepository,
        SymmetricAesGcmEncryptionServiceImpl.INSTANCE);
  }
}
