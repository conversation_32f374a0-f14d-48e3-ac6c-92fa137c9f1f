package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.tisp.tce.discovery.InstanceDetails;
import java.io.Serial;
import java.io.Serializable;
import java.util.Arrays;
import java.util.Comparator;
import org.apache.curator.x.discovery.ServiceInstance;

public class InstanceComparator
    implements Comparator<ServiceInstance<InstanceDetails>>, Serializable {

  @Serial private static final long serialVersionUID = 675969666324742617L;

  @Override
  public int compare(ServiceInstance<InstanceDetails> o1, ServiceInstance<InstanceDetails> o2) {
    return compareAddresses(o1.getAddress(), o2.getAddress());
  }

  private int compareAddresses(String address1, String address2) {
    int[] aOct = Arrays.stream(address1.split("\\.")).mapToInt(Integer::parseInt).toArray();
    int[] bOct = Arrays.stream(address2.split("\\.")).mapToInt(Integer::parseInt).toArray();
    int r = 0;
    for (int i = 0; i < aOct.length && i < bOct.length; i++) {
      r = Integer.compare(aOct[i], bOct[i]);
      if (r != 0) {
        return r;
      }
    }
    return r;
  }
}
