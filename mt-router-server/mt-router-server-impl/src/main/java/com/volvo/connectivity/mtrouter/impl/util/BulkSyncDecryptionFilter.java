package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.connectivity.mtrouter.impl.conf.BulkSyncProperties;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import jakarta.annotation.Nullable;
import java.io.ByteArrayInputStream;
import java.nio.ByteBuffer;
import java.security.GeneralSecurityException;
import java.security.Key;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.Base64.Decoder;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.core.io.buffer.DataBuffer.ByteBufferIterator;
import org.springframework.core.io.buffer.DataBufferFactory;
import org.springframework.core.io.buffer.DataBufferUtils;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeFunction;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public final class BulkSyncDecryptionFilter implements ExchangeFilterFunction {

  public static final String RSA_ECB_PKCS1PADDING = "RSA/ECB/PKCS1Padding";
  public static final String AES_CBC_PKCS5PADDING = "AES/CBC/PKCS5Padding";
  public static final String SHA256_WITH_RSA = "SHA256withRSA";
  public static final String HEADER_ENCRYPTED_AES_KEY = "ENCRYPTED_AES_KEY";
  public static final String HEADER_IV = "IV";
  public static final String HEADER_SIGNATURE = "SIGNATURE";

  private DataBufferFactory bufferFactory;
  private Decoder base64;
  private Certificate certificate;
  private PrivateKey privateKey;

  @SuppressWarnings("java:S5542")
  public BulkSyncDecryptionFilter(
      BulkSyncProperties bulkSyncProperties, DataBufferFactory bufferFactory)
      throws GeneralSecurityException {
    Validate.notNull(bulkSyncProperties, "bulkSyncProperties");
    Validate.notNull(bufferFactory, "bufferFactory");
    this.bufferFactory = bufferFactory;
    this.base64 = Base64.getDecoder();
    CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
    this.certificate =
        certificateFactory.generateCertificate(
            new ByteArrayInputStream(base64.decode(bulkSyncProperties.publicCertificate())));
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    PKCS8EncodedKeySpec privateKeySpec =
        new PKCS8EncodedKeySpec(base64.decode(bulkSyncProperties.privateKey()));
    this.privateKey = keyFactory.generatePrivate(privateKeySpec);
  }

  @Nullable
  @SuppressWarnings("java:S5542")
  @SuppressFBWarnings({"CIPHER_INTEGRITY", "PADDING_ORACLE"})
  private final ClientResponse decryptResponse(ClientResponse response) {
    HttpHeaders httpHeaders = response.headers().asHttpHeaders();
    String encryptedAesKeyString = httpHeaders.getFirst(HEADER_ENCRYPTED_AES_KEY);
    String ivString = httpHeaders.getFirst(HEADER_IV);
    String signatureString = httpHeaders.getFirst(HEADER_SIGNATURE);
    if (encryptedAesKeyString == null || ivString == null || signatureString == null) {
      log.warn(
          "Bulksync: missing security headers in conrepo response. {}:{}, {}:{}, {}:{}",
          HEADER_ENCRYPTED_AES_KEY,
          encryptedAesKeyString,
          HEADER_IV,
          ivString,
          HEADER_SIGNATURE,
          signatureString);
      return response;
    }
    try {
      byte[] encryptedAesKeyBytes = base64.decode(encryptedAesKeyString);
      byte[] ivBytes = base64.decode(ivString);
      byte[] signatureBytes = base64.decode(signatureString);

      Signature publicSignature = Signature.getInstance(SHA256_WITH_RSA);
      publicSignature.initVerify(certificate);
      publicSignature.update(encryptedAesKeyBytes);
      if (!publicSignature.verify(signatureBytes)) {
        log.error("Bulksync: signature verification failed");
        return null;
      }

      Cipher decryptCipher = Cipher.getInstance(RSA_ECB_PKCS1PADDING);
      decryptCipher.init(Cipher.DECRYPT_MODE, privateKey);
      byte[] decryptedAesKeyBytes = decryptCipher.doFinal(encryptedAesKeyBytes);
      Key aesSymetricKey = new SecretKeySpec(decryptedAesKeyBytes, "AES");
      Cipher aesDecryptCipher = Cipher.getInstance(AES_CBC_PKCS5PADDING);
      aesDecryptCipher.init(Cipher.DECRYPT_MODE, aesSymetricKey, new IvParameterSpec(ivBytes));

      return response
          .mutate()
          .body(
              dataBufferFlux ->
                  dataBufferFlux
                      .<DataBuffer>handle(
                          (dataBuffer, sink) -> {
                            try (ByteBufferIterator byteBufferIterator =
                                dataBuffer.readableByteBuffers()) {
                              while (byteBufferIterator.hasNext()) {
                                ByteBuffer inputBuffer = byteBufferIterator.next();
                                ByteBuffer outputBuffer =
                                    ByteBuffer.allocate(inputBuffer.capacity() + 32);
                                aesDecryptCipher.update(inputBuffer, outputBuffer);
                                outputBuffer.flip();
                                sink.next(bufferFactory.wrap(outputBuffer));
                              }
                            } catch (GeneralSecurityException e) {
                              log.error("Bulksync: symetric decryption failed Cipher::update", e);
                            } finally {
                              DataBufferUtils.release(dataBuffer);
                            }
                          })
                      .concatWith(
                          Mono.fromSupplier(
                              () -> {
                                try {
                                  return bufferFactory.wrap(aesDecryptCipher.doFinal());
                                } catch (GeneralSecurityException e) {
                                  log.error(
                                      "Bulksync: symetric decryption failed on Cipher::doFinal", e);
                                  return null;
                                }
                              })))
          .build();
    } catch (GeneralSecurityException e) {
      log.error("Bulksync:", e);
      return null;
    }
  }

  @Override
  public Mono<ClientResponse> filter(ClientRequest request, ExchangeFunction next) {
    return next.exchange(request).mapNotNull(this::decryptResponse);
  }
}
