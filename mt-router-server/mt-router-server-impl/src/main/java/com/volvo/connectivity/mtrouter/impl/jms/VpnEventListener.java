package com.volvo.connectivity.mtrouter.impl.jms;

import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.mtrouter.impl.services.VpnEventService;
import com.volvo.connectivity.proto.VpnEvent;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@JmsController(destination = VpnEventListener.VPN_EVENT_MESSAGE_QUEUE)
public class VpnEventListener {

  public static final String VPN_EVENT_MESSAGE_QUEUE = "VPN.EVENT.MESSAGE.IN";
  public static final String VERSION_1_0 = "1.0";
  private final VpnEventService vpnEventService;

  public VpnEventListener(VpnEventService vpnEventService) {
    Validate.notNull(vpnEventService, "vpnEventService");
    this.vpnEventService = vpnEventService;
  }

  @JmsMessageMapping(
      consumesType = ServiceConstant.VPN_EVENT_MESSAGE_TYPE,
      consumesVersion = VERSION_1_0)
  public void receiveMessage(JmsMessage<VpnEvent> jmsMessage) {
    log.debug("Received VpnEvent jms message: {}", jmsMessage);
    vpnEventService.updateVpnIpAddress(jmsMessage.payload());
  }
}
