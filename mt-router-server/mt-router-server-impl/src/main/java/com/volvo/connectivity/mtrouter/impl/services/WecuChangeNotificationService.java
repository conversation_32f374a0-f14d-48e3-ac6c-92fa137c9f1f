package com.volvo.connectivity.mtrouter.impl.services;

import com.volvo.connectivity.asset.repository.events.model.Status;
import com.volvo.connectivity.asset.repository.events.model.WecuChangeNotifyEvent;
import com.volvo.connectivity.asset.repository.events.model.WecuChangeNotifyEventReply;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoService;
import com.volvo.connectivity.mtrouter.impl.util.DeviceConvertUtil;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import jakarta.jms.Destination;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class WecuChangeNotificationService {

  private final VehicleDeviceInfoService vehicleDeviceInfoService;
  private final JmsService jmsService;

  public WecuChangeNotificationService(
      VehicleDeviceInfoService vehicleDeviceInfoService, JmsService jmsService) {
    this.vehicleDeviceInfoService = vehicleDeviceInfoService;
    this.jmsService = jmsService;
  }

  public Mono<WecuChangeNotifyEvent> persistWecuChangeNotification(
      WecuChangeNotifyEvent wecuChangeNotifyEvent) {
    Validate.notNull(wecuChangeNotifyEvent, "wecuChangeNotifyEvent");
    VehicleDeviceInfo wecuVehicleDeviceInfo =
        DeviceConvertUtil.toVehicleDeviceInfoOfWecuUpdate(wecuChangeNotifyEvent.getAsset());

    Mono<Void> updateMono =
        wecuVehicleDeviceInfo != null
            ? vehicleDeviceInfoService.insertOrUpdateActivatedVehicles(
                List.of(wecuVehicleDeviceInfo))
            : Mono.empty();
    return updateMono.thenReturn(wecuChangeNotifyEvent);
  }

  public void sendWecuChangeNotifyEventReply(
      String coRelationId, Destination replyQueue, Status status, String errorMessage) {
    WecuChangeNotifyEventReply wecuChangeNotifyEventReply = new WecuChangeNotifyEventReply();
    wecuChangeNotifyEventReply.setStatus(status);
    wecuChangeNotifyEventReply.setErrorMessage(errorMessage);
    jmsService.sendWecuChangeNotifyEventReply(coRelationId, replyQueue, wecuChangeNotifyEventReply);
  }
}
