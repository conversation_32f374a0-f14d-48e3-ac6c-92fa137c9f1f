package com.volvo.connectivity.mtrouter.impl.services;

import com.volvo.connectivity.cms.events.model.CapabilityState;
import com.volvo.connectivity.cms.events.model.TgwNotifyEvent;
import com.volvo.connectivity.proto.VpnEvent;
import com.volvo.connectivity.proto.VpnStatus;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/** Skeleton TGW notification service. To be implemented. */
@Component
@RequiredArgsConstructor
public class TgwNotificationService {

  private final VpnEventService vpnEventService;

  public Mono<TgwNotifyEvent> persistTgwNotification(TgwNotifyEvent event) {

    // WIFI
    event
        .getConnectivityCapability()
        .getWifi()
        .ifPresent(
            wifi -> {
              if (wifi.getState() == CapabilityState.AVAILABLE) {
                wifi.getVpnIpAddress()
                    .ifPresent(
                        vpnIpAddress -> {
                          VpnEvent vpnEvent =
                              VpnEvent.newBuilder()
                                  .setVpi(event.getAsset().getVpi())
                                  .setIp(vpnIpAddress)
                                  .setStatus(VpnStatus.ACTIVATED)
                                  .build();
                          vpnEventService.updateVpnIpAddress(vpnEvent);
                        });
              } else if (wifi.getState() == CapabilityState.UNAVAILABLE) {
                VpnEvent vpnEvent =
                    VpnEvent.newBuilder()
                        .setVpi(event.getAsset().getVpi())
                        .setStatus(VpnStatus.DEACTIVATED)
                        .build();
                vpnEventService.updateVpnIpAddress(vpnEvent);
              }
            });

    return Mono.just(event);
  }
}
