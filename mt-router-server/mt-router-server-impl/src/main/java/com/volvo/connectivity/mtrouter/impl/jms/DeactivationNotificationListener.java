package com.volvo.connectivity.mtrouter.impl.jms;

import com.volvo.connectivity.mtrouter.impl.services.ConRepo2NotifyService;
import com.volvo.connectivity.mtrouter.impl.util.MetricsReporter;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.vc.conrepo.MessageTypes;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@JmsController(destination = DeactivationNotificationListener.CONREPO2_DEACTIVATION_NOTIFY_IN_QUEUE)
public class DeactivationNotificationListener {
  public static final String CONREPO2_DEACTIVATION_NOTIFY_IN_QUEUE = "DEACTIVATION.NOTIFICATION.IN";
  private final ConRepo2NotifyService conRepo2NotifyService;

  public DeactivationNotificationListener(ConRepo2NotifyService conRepo2NotifyService) {
    Validate.notNull(conRepo2NotifyService, "conRepo2NotifyService");
    this.conRepo2NotifyService = conRepo2NotifyService;
  }

  @JmsMessageMapping(
      consumesType = MessageTypes.DEACTIVATION_NOTIFY_MESSAGE_TYPE,
      consumesVersion = MessageTypes.VERSION_2_0)
  public void receivingDeactivationNotifyMessage(
      JmsMessage<ActivationNotifyEventMessage> jmsMessage) {
    log.debug(
        "Received deactivation notify jms message: {} on queue: {} ",
        jmsMessage,
        CONREPO2_DEACTIVATION_NOTIFY_IN_QUEUE);
    ActivationNotifyEventMessage deactivationNotifyEventMessage = jmsMessage.payload();
    Mono.just(deactivationNotifyEventMessage)
        .publishOn(Schedulers.single())
        .flatMap(this.conRepo2NotifyService::persistActivationNotifyMessage)
        .subscribe(
            activationNotifyEventMessage -> {
              log.info(
                  "Successfully processed deactivation message request. referenceId: {}",
                  activationNotifyEventMessage.getReferenceId());
              MetricsReporter.onDeactivationNotifyRequestSuccess();
              conRepo2NotifyService.sendNotifyResponseMessage(
                  activationNotifyEventMessage.getReferenceId(),
                  "Success",
                  ResponseStatus.SUCCESS,
                  ActivationMessageType.DEACTIVATION_NOTIFY);
            },
            throwable -> {
              log.error(
                  "Failed to process deactivation message request. referenceId: {}",
                  jmsMessage.correlationId().get(),
                  throwable);
              MetricsReporter.onDeactivationNotifyRequestFailure();
              conRepo2NotifyService.sendNotifyResponseMessage(
                  jmsMessage.correlationId().get(),
                  throwable.getMessage() != null ? throwable.getMessage() : "Failed",
                  ResponseStatus.FAILED,
                  ActivationMessageType.DEACTIVATION_NOTIFY);
            });
  }
}
