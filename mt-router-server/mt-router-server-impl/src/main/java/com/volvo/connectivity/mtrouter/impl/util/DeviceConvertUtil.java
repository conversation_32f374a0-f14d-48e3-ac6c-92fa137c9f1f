package com.volvo.connectivity.mtrouter.impl.util;

import com.google.common.net.InetAddresses;
import com.volvo.connectivity.asset.repository.events.model.CellularCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularIPCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularIPPrivateCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularSMSCapability;
import com.volvo.connectivity.asset.repository.events.model.ConnectivityCapabilities;
import com.volvo.connectivity.asset.repository.events.model.SatelliteCapability;
import com.volvo.connectivity.asset.repository.events.model.WecuAsset;
import com.volvo.connectivity.asset.repository.events.model.WifiCapability;
import com.volvo.connectivity.mtrouter.impl.db.entity.*;
import com.volvo.cos.conrepo.bulk_sync.v1.VehicleInfoOrBuilder;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.vc.conrepo.api.v2.DeviceDetailedEntry;
import com.volvo.vc.conrepo.api.v2.DeviceSim;
import com.volvo.vc.conrepo.api.v2.WtpProtocolVersion;
import java.util.HexFormat;
import java.util.Optional;
import javax.annotation.Nullable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class DeviceConvertUtil {
  private static final HexFormat HEX_FORMAT = HexFormat.of().withUpperCase();

  private DeviceConvertUtil() {
    throw new IllegalStateException();
  }

  /**
   * Converts an instance of {@link VehicleInfoOrBuilder} to {@link VehicleDeviceInfo}
   *
   * @param vehicleInfo instance of {@link VehicleInfoOrBuilder}
   * @return instance of {@link VehicleDeviceInfo}
   */
  public static VehicleDeviceInfo vehicleDeviceToVehicleDeviceInfo(
      VehicleInfoOrBuilder vehicleInfo) {

    return VehicleDeviceInfo.builder()
        .setObsAlias(vehicleInfo.getObsAlias())
        .setVpi(HEX_FORMAT.formatHex(vehicleInfo.getVpi().toByteArray()))
        .setSatelliteId(
            vehicleInfo.getSatelliteId().isBlank() ? null : vehicleInfo.getSatelliteId())
        .setIpv4Address(
            vehicleInfo.getIpV4Address() == 0
                ? null
                : InetAddresses.fromInteger(vehicleInfo.getIpV4Address()).getHostAddress())
        .setWtpVersion(convertWtpVersion(vehicleInfo.getWtpVersion()))
        .setIpv4Port(vehicleInfo.getPort())
        .setMobileNetworkOperator(
            vehicleInfo.getMobileNetworkOperator().isBlank()
                ? null
                : vehicleInfo.getMobileNetworkOperator())
        .setMsisdn(vehicleInfo.getMsisdn().isBlank() ? null : vehicleInfo.getMsisdn())
        .build();
  }

  @Nullable
  public static VehicleDeviceInfo toVehicleDeviceInfoOfWecuUpdate(WecuAsset asset) {
    Optional<ConnectivityCapabilities> connectivityCapabilitiesOptional =
        asset.getConnectivityCapabilities();
    if (connectivityCapabilitiesOptional.isEmpty()) {
      return null;
    }
    ConnectivityCapabilities connectivityCapabilities = connectivityCapabilitiesOptional.get();

    VehicleDeviceInfo.VehicleDeviceInfoBuilder vehicleDeviceInfoBuilder =
        VehicleDeviceInfo.builder();
    vehicleDeviceInfoBuilder.setVpi(asset.getVpi());

    String ipv4Address =
        connectivityCapabilities
            .getCellular()
            .flatMap(CellularCapability::getIp)
            .flatMap(CellularIPCapability::getPrivate)
            .flatMap(CellularIPPrivateCapability::getIpAddress)
            .orElse(null);
    vehicleDeviceInfoBuilder.setIpv4Address(ipv4Address);

    String mobileNetworkOperator =
        connectivityCapabilities
            .getCellular()
            .flatMap(CellularCapability::getSms)
            .flatMap(CellularSMSCapability::getMobileNetworkOperator)
            .orElse(null);
    vehicleDeviceInfoBuilder.setMobileNetworkOperator(mobileNetworkOperator);

    String msisdn =
        connectivityCapabilities
            .getCellular()
            .flatMap(CellularCapability::getSms)
            .flatMap(CellularSMSCapability::getMsisdn)
            .orElse(null);
    vehicleDeviceInfoBuilder.setMsisdn(msisdn);

    String satelliteId =
        connectivityCapabilities
            .getSatellite()
            .flatMap(SatelliteCapability::getSatelliteId)
            .orElse(null);
    vehicleDeviceInfoBuilder.setSatelliteId(satelliteId);

    String vpnIpv4Address =
        connectivityCapabilities.getWifi().flatMap(WifiCapability::getVpnIpAddress).orElse(null);
    vehicleDeviceInfoBuilder.setVpnIpv4Address(vpnIpv4Address);

    vehicleDeviceInfoBuilder.setTelematicUnit(TelematicUnit.WECU);
    return vehicleDeviceInfoBuilder.build();
  }

  /**
   * Converts an instance of {@link DeviceDetailedEntry} to {@link VehicleDeviceInfo} where {@link
   * VehicleDeviceInfo} to be inserted or updated.
   *
   * @param deviceDetailedEntry an instance of {@link DeviceDetailedEntry}
   * @return an instance of {@link VehicleDeviceInfo}
   */
  public static VehicleDeviceInfo toVehicleDeviceInfoToUpdate(
      DeviceDetailedEntry deviceDetailedEntry) {
    Validate.notNull(deviceDetailedEntry, "deviceDetailedEntry");

    logDeviceDetailedEntryDetails(deviceDetailedEntry);

    VehicleDeviceInfo.VehicleDeviceInfoBuilder vehicleDeviceInfoBuilder =
        VehicleDeviceInfo.builder();
    Optional<WtpVersion> wtpVersion =
        DeviceConvertUtil.convertWtpProtocolVersion(deviceDetailedEntry.getWtpProtocolVersion());
    DeviceSim deviceSim = deviceDetailedEntry.getSimEntry();
    vehicleDeviceInfoBuilder
        .setVpi(deviceDetailedEntry.getVehiclePlatformId())
        .setObsAlias(deviceDetailedEntry.getObsAlias())
        .setSatelliteId(deviceDetailedEntry.getSatelliteId())
        .setWtpVersion(wtpVersion.orElse(null))
        .setTelematicUnit(TelematicUnit.TGW);
    if (deviceSim != null) {
      vehicleDeviceInfoBuilder
          .setIpv4Address(deviceSim.getIp())
          .setIpv4Port(deviceSim.getPort())
          .setMobileNetworkOperator(deviceSim.getOperator())
          .setMsisdn(deviceSim.getMsisdn());
    } else {
      vehicleDeviceInfoBuilder.setMobileNetworkOperator(null).setMsisdn(null);
    }
    return vehicleDeviceInfoBuilder.build();
  }

  /**
   * Converts an instance of {@link DeviceDetailedEntry} to {@link VehicleDeviceInfo} where {@link
   * VehicleDeviceInfo} to be deleted.
   *
   * @param deviceDetailedEntry an instance of {@link DeviceDetailedEntry}
   * @return an instance of {@link VehicleDeviceInfo}
   */
  public static VehicleDeviceInfo toVehicleDeviceInfoToDelete(
      DeviceDetailedEntry deviceDetailedEntry) {
    Validate.notNull(deviceDetailedEntry, "deviceDetailedEntry");

    logDeviceDetailedEntryDetails(deviceDetailedEntry);

    return VehicleDeviceInfo.builder()
        .setVpi(deviceDetailedEntry.getVehiclePlatformId())
        .setObsAlias(deviceDetailedEntry.getObsAlias())
        .build();
  }

  private static WtpVersion convertWtpVersion(
      com.volvo.cos.conrepo.bulk_sync.v1.WtpVersion wtpVersion) {
    return switch (wtpVersion) {
      case VERSION_1 -> WtpVersion.VERSION_1;
      case VERSION_2 -> WtpVersion.VERSION_2;
      case UNRECOGNIZED -> throw new IllegalStateException("unknown wtp version: " + wtpVersion);
    };
  }

  private static Optional<WtpVersion> convertWtpProtocolVersion(
      WtpProtocolVersion wtpProtocolVersion) {
    if (wtpProtocolVersion == null) {
      return Optional.empty();
    }

    switch (wtpProtocolVersion) {
      case VERSION_1:
        return Optional.of(WtpVersion.VERSION_1);
      case VERSION_2:
        return Optional.of(WtpVersion.VERSION_2);
      default:
        throw new IllegalStateException("unknown wtp protocol version: " + wtpProtocolVersion);
    }
  }

  private static void logDeviceDetailedEntryDetails(DeviceDetailedEntry deviceDetailedEntry) {
    if (log.isDebugEnabled()) {
      DeviceSim deviceSim = deviceDetailedEntry.getSimEntry();
      log.debug("-----------------------------device details---------------------------------");
      log.debug("deviceId: {}", deviceDetailedEntry.getId());
      log.debug("vehiclePlatformId: {}", deviceDetailedEntry.getVehiclePlatformId());
      log.debug("obsAlias: {}", deviceDetailedEntry.getObsAlias());
      log.debug("wtpProtocolVersion: {}", deviceDetailedEntry.getWtpProtocolVersion());
      log.debug("satelliteId: {}", deviceDetailedEntry.getSatelliteId());
      log.debug("imsi: {}", deviceSim != null ? deviceSim.getImsi() : null);
      log.debug("msisdn: {}", deviceSim != null ? deviceSim.getMsisdn() : null);
      log.debug("operator: {}", deviceSim != null ? deviceSim.getOperator() : null);
      log.debug("ip: {}", deviceSim != null ? deviceSim.getIp() : null);
      log.debug("port: {}", deviceSim != null ? deviceSim.getPort() : null);
      log.debug("state: {}", deviceDetailedEntry.getState());
      log.debug("-----------------------------------------------------------------------------");
    }
  }
}
