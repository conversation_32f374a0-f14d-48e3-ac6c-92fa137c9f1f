package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.VpnStatus;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.Metrics;
import java.util.Arrays;
import java.util.EnumMap;

/** Utility class that tracks mt-router communication of different protocols custom metrics. */
@SuppressFBWarnings
public class MetricsReporter {

  private static final Counter invalidMtMessageCounter =
      Counter.builder(MetricNames.MT_INCOMING_ANOMALIES)
          .description("Count of received invalid mt  message")
          .tag(MetricNames.TYPE_TAG, "invalid-mt-message")
          .register(Metrics.globalRegistry);
  private static final Counter successActivationNotifyRequestCounter =
      Counter.builder(MetricNames.ACTIVATION_NOTIFY)
          .description("Count of success activation notify requests")
          .tag(MetricNames.TYPE_TAG, "request")
          .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
          .register(Metrics.globalRegistry);
  private static final Counter failedActivationNotifyRequestCounter =
      Counter.builder(MetricNames.ACTIVATION_NOTIFY)
          .description("Count of failed activation notify requests")
          .tag(MetricNames.TYPE_TAG, "request")
          .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
          .register(Metrics.globalRegistry);

  private static final Counter successWecuActivationNotifyRequestCounter =
      Counter.builder(MetricNames.WECU_CHANGE_NOTIFY)
          .description("Count of success wecu change notify requests")
          .tag(MetricNames.TYPE_TAG, "request")
          .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
          .register(Metrics.globalRegistry);
  private static final Counter failedWecuActivationNotifyRequestCounter =
      Counter.builder(MetricNames.WECU_CHANGE_NOTIFY)
          .description("Count of failed wecu change notify requests")
          .tag(MetricNames.TYPE_TAG, "request")
          .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
          .register(Metrics.globalRegistry);

  private static final Counter successfulOutgoingTisMessageCounter =
      Counter.builder(MetricNames.TIS_MESSAGE_SENDING)
          .description("Count of success tis message routing")
          .tag(MetricNames.TYPE_TAG, "response")
          .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
          .register(Metrics.globalRegistry);
  private static final Counter failedOutgoingTisMessageCounter =
      Counter.builder(MetricNames.TIS_MESSAGE_SENDING)
          .description("Count of success tis message routing")
          .tag(MetricNames.TYPE_TAG, "response")
          .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
          .register(Metrics.globalRegistry);

  private static final Counter successActivationNotifyResponseCounter =
      Counter.builder(MetricNames.ACTIVATION_NOTIFY)
          .description("Count of success activation notify responses")
          .tag(MetricNames.TYPE_TAG, "response")
          .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
          .register(Metrics.globalRegistry);
  private static final Counter failedActivationNotifyResponseCounter =
      Counter.builder(MetricNames.ACTIVATION_NOTIFY)
          .description("Count of failed activation notify responses")
          .tag(MetricNames.TYPE_TAG, "response")
          .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
          .register(Metrics.globalRegistry);
  private static final Counter successWecuChangeNotifyResponseCounter =
      Counter.builder(MetricNames.TIS_MESSAGE_SENDING)
          .description("Count of success wecu change notify responses")
          .tag(MetricNames.TYPE_TAG, "response")
          .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
          .register(Metrics.globalRegistry);
  private static final Counter failedWecuChangeNotifyResponseCounter =
      Counter.builder(MetricNames.TIS_MESSAGE_SENDING)
          .description("Count of failed wecu change notify responses")
          .tag(MetricNames.TYPE_TAG, "response")
          .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
          .register(Metrics.globalRegistry);
  private static final Counter successDeactivationNotifyRequestCounter =
      Counter.builder(MetricNames.DEACTIVATION_NOTIFY)
          .description("Count of success deactivation notify requests")
          .tag(MetricNames.TYPE_TAG, "request")
          .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
          .register(Metrics.globalRegistry);

  private static final Counter failedDeactivationNotifyRequestCounter =
      Counter.builder(MetricNames.DEACTIVATION_NOTIFY)
          .description("Count of failed deactivation notify requests")
          .tag(MetricNames.TYPE_TAG, "request")
          .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
          .register(Metrics.globalRegistry);
  private static final EnumMap<Transport, Counter> successfulOutgoingMtStatusCounter =
      new EnumMap<>(Transport.class);
  private static final EnumMap<Transport, Counter> failedOutgoingMtStatusCounter =
      new EnumMap<>(Transport.class);

  private static final EnumMap<VpnStatus, Counter> successVpnIpUpdateRequestCounter =
      new EnumMap<>(VpnStatus.class);

  private static final EnumMap<VpnStatus, Counter> failedVpnIpUpdateRequestCounter =
      new EnumMap<>(VpnStatus.class);

  static {
    Arrays.stream(Transport.values())
        .forEach(
            transport ->
                successfulOutgoingMtStatusCounter.put(
                    transport,
                    Counter.builder(MetricNames.MT_ROUTER_OUTGOING_USER_MESSAGES)
                        .description(
                            "Count of successful outgoing user messages with status "
                                + transport.name())
                        .tag(MetricNames.TYPE_TAG, transport.name())
                        .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
                        .register(Metrics.globalRegistry)));

    Arrays.stream(Transport.values())
        .forEach(
            transport ->
                failedOutgoingMtStatusCounter.put(
                    transport,
                    Counter.builder(MetricNames.MT_ROUTER_OUTGOING_USER_MESSAGES)
                        .description(
                            "Count of failed outgoing user messages with status "
                                + transport.name())
                        .tag(MetricNames.TYPE_TAG, transport.name())
                        .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
                        .register(Metrics.globalRegistry)));

    Arrays.stream(VpnStatus.values())
        .forEach(
            vpnStatus -> {
              successVpnIpUpdateRequestCounter.put(
                  vpnStatus,
                  Counter.builder(MetricNames.VPN_IP_UPDATE_EVENT)
                      .description(
                          "Count of success " + vpnStatus.name() + " vpn ip update requests")
                      .tag(MetricNames.TYPE_TAG, vpnStatus.name())
                      .tag(MetricNames.STATUS_TAG, MetricNames.SUCCESS)
                      .register(Metrics.globalRegistry));
              failedVpnIpUpdateRequestCounter.put(
                  vpnStatus,
                  Counter.builder(MetricNames.VPN_IP_UPDATE_EVENT)
                      .description(
                          "Count of failed " + vpnStatus.name() + " vpn ip update requests")
                      .tag(MetricNames.TYPE_TAG, vpnStatus.name())
                      .tag(MetricNames.STATUS_TAG, MetricNames.FAIL)
                      .register(Metrics.globalRegistry));
            });
  }

  /**
   * Increment a counter for mt status.
   *
   * @param transport an instance of {@link Transport}
   */
  @SuppressWarnings("NullAway")
  public static void onMtUserMessageStatusSuccess(Transport transport) {
    successfulOutgoingMtStatusCounter.get(transport).increment();
  }

  /**
   * Increment a counter for mt status.
   *
   * @param transport an instance of {@link Transport}
   */
  @SuppressWarnings("NullAway")
  public static void onMtUserMessageStatusFailed(Transport transport) {
    failedOutgoingMtStatusCounter.get(transport).increment();
  }

  /**
   * Increment a counter for an invalid MT {@link com.volvo.connectivity.proto.MtMessage} received.
   */
  public static void onInvalidMtMessage() {
    invalidMtMessageCounter.increment();
  }

  /** Increment a counter for successfully processed activation notify message. */
  public static void onActivationNotifyRequestSuccess() {
    successActivationNotifyRequestCounter.increment();
  }

  /** Increment a counter for activation notify message processing failure. */
  public static void onActivationNotifyRequestFailure() {
    failedActivationNotifyRequestCounter.increment();
  }

  /** Increment a counter for successfully processed wecu change notify message. */
  public static void onWecuChangeNotifyRequestSuccess() {
    successWecuActivationNotifyRequestCounter.increment();
  }

  /** Increment a counter for wecu change notify message processing failure. */
  public static void onWecuChangeNotifyRequestFailure() {
    failedWecuActivationNotifyRequestCounter.increment();
  }

  /** Increment a counter for successfully sent tis message. */
  public static void onTisMessageSendingSuccess() {
    successfulOutgoingTisMessageCounter.increment();
  }

  /** Increment a counter for tis message sending failure. */
  public static void onTisMessageSendingFailure() {
    failedOutgoingTisMessageCounter.increment();
  }

  /** Increment a counter for successfully published wecu change notify response. */
  public static void onWecuChangeNotifyResponsePublishSuccess() {
    successWecuChangeNotifyResponseCounter.increment();
  }

  /** Increment a counter for wecu change notify response publish failure. */
  public static void onWecuChangeNotifyResponsePublishFailure() {
    failedWecuChangeNotifyResponseCounter.increment();
  }

  /** Increment a counter for successfully published activation/deactivation notify response. */
  public static void onNotifyResponsePublishSuccess() {
    successActivationNotifyResponseCounter.increment();
  }

  /** Increment a counter for activation/deactivation notify response publish failure. */
  public static void onNotifyResponsePublishFailure() {
    failedActivationNotifyResponseCounter.increment();
  }

  /** Increment a counter for successfully processed deactivation notify message. */
  public static void onDeactivationNotifyRequestSuccess() {
    successDeactivationNotifyRequestCounter.increment();
  }

  /** Increment a counter for deactivation notify message processing failure. */
  public static void onDeactivationNotifyRequestFailure() {
    failedDeactivationNotifyRequestCounter.increment();
  }

  /**
   * Increment a counter on successfully update the vpn ip address.
   *
   * @param vpnStatus the vpn status
   */
  @SuppressWarnings("NullAway")
  public static void onVpnIpUpdateRequestSuccess(VpnStatus vpnStatus) {
    successVpnIpUpdateRequestCounter.get(vpnStatus).increment();
  }

  /**
   * Increment a counter on vpn ip address update failure.
   *
   * @param vpnStatus the vpn status
   */
  @SuppressWarnings("NullAway")
  public static void onVpnIpUpdateRequestFailure(VpnStatus vpnStatus) {
    failedVpnIpUpdateRequestCounter.get(vpnStatus).increment();
  }
}
