package com.volvo.connectivity.mtrouter.impl.jms;

import com.volvo.connectivity.cms.MessageTypes;
import com.volvo.connectivity.cms.events.model.TgwNotifyEvent;
import com.volvo.connectivity.mtrouter.impl.services.TgwNotificationService;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/** JMS Listener for TGW notify events. */
@Slf4j
@JmsController(destination = TgwNotifyEventListener.TGW_NOTIFY_IN_QUEUE)
public class TgwNotifyEventListener {

  public static final String TGW_NOTIFY_IN_QUEUE = "TGW.NOTIFY.EVENT.MESSAGE.IN";

  private final TgwNotificationService tgwNotificationService;

  public TgwNotifyEventListener(TgwNotificationService tgwNotificationService) {
    Validate.notNull(tgwNotificationService, "tgwNotificationService");
    this.tgwNotificationService = tgwNotificationService;
  }

  @JmsMessageMapping(
      consumesType = MessageTypes.TGW_NOTIFY_EVENT_MESSAGE,
      consumesVersion = MessageTypes.VERSION_1_0)
  public void handleTgwNotifyEvent(JmsMessage<TgwNotifyEvent> jmsMessage) {
    log.info(
        "Received TGW notify event jms message: {} on queue: {} ", jmsMessage, TGW_NOTIFY_IN_QUEUE);

    String correlationId =
        jmsMessage
            .correlationId()
            .orElseThrow(() -> new IllegalArgumentException("Missing JMS correlationId"));

    Mono.just(jmsMessage.payload())
        .publishOn(Schedulers.single())
        .flatMap(tgwNotificationService::persistTgwNotification)
        .subscribe(
            event -> {
              log.info(
                  "Successfully processed tgw notify event. correlationId: {} assetVpi: {}",
                  correlationId,
                  event.getAsset() != null ? event.getAsset().getVpi() : "<null>");
            },
            throwable -> {
              log.error(
                  "Failed to process tgw notify event. correlationId: {}",
                  correlationId,
                  throwable);
            });
  }
}
