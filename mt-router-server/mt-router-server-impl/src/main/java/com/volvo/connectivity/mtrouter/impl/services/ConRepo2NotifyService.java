package com.volvo.connectivity.mtrouter.impl.services;

import com.volvo.connectivity.mtrouter.impl.conf.SecureTransportProperties;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoService;
import com.volvo.connectivity.mtrouter.impl.jms.ConRepo2NotifyResponseClient;
import com.volvo.connectivity.mtrouter.impl.security.MessageDecoderXmlImpl;
import com.volvo.connectivity.mtrouter.impl.util.DeviceConvertUtil;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEvent;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.ChangeStatus;
import com.volvo.vc.conrepo.api.v2.NotifyResponseMessage;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import com.volvo.vc.crypto.common.entity.KeyId;
import com.volvo.vc.crypto.common.keystore.KeyAlias;
import com.volvo.vc.crypto.message.EncryptedMessage;
import com.volvo.vc.crypto.message.decryption.MessageDecryptionConfig;
import com.volvo.vc.crypto.message.decryption.MessageDecryptionService;
import java.util.*;
import java.util.function.Function;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@Component
public class ConRepo2NotifyService {
  private final MessageDecryptionService messageDecryptionService;
  private SecureTransportProperties secureTransportProperties;
  private MessageDecoderXmlImpl<ActivationNotifyEventMessage> messageDecoderXml;
  private ActivationNotifyEventComparator activationNotifyEventComparator;
  private final ConRepo2NotifyResponseClient conrepo2NotifyResponseClient;
  private final VehicleDeviceInfoService vehicleDeviceInfoService;

  public ConRepo2NotifyService(
      MessageDecryptionService messageDecryptionService,
      SecureTransportProperties secureTransportProperties,
      MessageDecoderXmlImpl<ActivationNotifyEventMessage> messageDecoderXml,
      ActivationNotifyEventComparator activationNotifyEventComparator,
      ConRepo2NotifyResponseClient conrepo2NotifyResponseClient,
      VehicleDeviceInfoService vehicleDeviceInfoService) {
    this.messageDecryptionService = messageDecryptionService;
    this.secureTransportProperties = secureTransportProperties;
    this.messageDecoderXml = messageDecoderXml;
    this.activationNotifyEventComparator = activationNotifyEventComparator;
    this.conrepo2NotifyResponseClient = conrepo2NotifyResponseClient;
    this.vehicleDeviceInfoService = vehicleDeviceInfoService;
  }

  public ActivationNotifyEventMessage extractActivationNotifyEventMessage(
      EncryptedMessage encryptedMessage) {
    try {
      return messageDecryptionService
          .decrypt(createMessageDecryptionConfig(encryptedMessage), encryptedMessage)
          .orElseThrowLeft(Function.identity());
    } catch (RuntimeException e) {
      log.error("Failed to decrypt activation message request", e);
      throw e;
    }
  }

  public Mono<ActivationNotifyEventMessage> persistActivationNotifyMessage(
      ActivationNotifyEventMessage activationNotifyEventMessage) {

    Validate.notNull(activationNotifyEventMessage, "activationNotifyEventMessage");

    List<ActivationNotifyEvent> activationNotifyEvents =
        activationNotifyEventMessage.getActivationNotifyEvents();
    if (Objects.isNull(activationNotifyEvents) || activationNotifyEvents.isEmpty()) {
      throw new RuntimeException("Activation notification events empty.");
    }
    log.debug(
        "persistActivationNotifyMessage:  Number of activationNotifyEvents = {}",
        activationNotifyEvents.size());
    Collections.sort(activationNotifyEvents, activationNotifyEventComparator);
    List<VehicleDeviceInfo> activatedVehicleDeviceInfoList =
        activationNotifyEvents.stream()
            .filter(
                activationNotifyEvent ->
                    activationNotifyEvent.getChangeStatus() == ChangeStatus.UPDATED)
            .map(
                activationNotifyEvent ->
                    DeviceConvertUtil.toVehicleDeviceInfoToUpdate(
                        activationNotifyEvent.getDeviceDetail()))
            .toList();

    List<VehicleDeviceInfo> deactivatedVehicleDeviceInfoList =
        activationNotifyEvents.stream()
            .filter(
                activationNotifyEvent ->
                    activationNotifyEvent.getChangeStatus() == ChangeStatus.DELETED)
            .map(
                activationNotifyEvent ->
                    DeviceConvertUtil.toVehicleDeviceInfoToDelete(
                        activationNotifyEvent.getDeviceDetail()))
            .toList();

    List<Mono<Void>> operations = new ArrayList<>();

    operations.add(
        vehicleDeviceInfoService.insertOrUpdateActivatedVehicles(activatedVehicleDeviceInfoList));
    operations.add(
        vehicleDeviceInfoService.removeDeActivatedVehicles(deactivatedVehicleDeviceInfoList));

    return Flux.concat(operations).then(Mono.just(activationNotifyEventMessage));
  }

  private MessageDecryptionConfig<ActivationNotifyEventMessage> createMessageDecryptionConfig(
      EncryptedMessage encryptedMessage) {
    return MessageDecryptionConfig.create(
        createKeyAlias(secureTransportProperties.privateKey(), encryptedMessage.getPrivateKeyId()),
        createKeyAlias(
            secureTransportProperties.notifyPublicKey(), encryptedMessage.getPublicKeyId()),
        messageDecoderXml,
        ActivationNotifyEventMessage.class);
  }

  private static KeyAlias createKeyAlias(String keyAliasPrefix, KeyId keyId) {
    return KeyAlias.ofString(keyAliasPrefix + '-' + keyId.toLong());
  }

  public void sendNotifyResponseMessage(
      String referenceId,
      String description,
      ResponseStatus responseStatus,
      ActivationMessageType messageType) {
    NotifyResponseMessage notifyResponseMessage = new NotifyResponseMessage();
    notifyResponseMessage.setReferenceId(referenceId);
    notifyResponseMessage.setResponseStatus(responseStatus);
    notifyResponseMessage.setMessageType(messageType);
    notifyResponseMessage.setDescription(description);

    conrepo2NotifyResponseClient.publishResponseMessage(notifyResponseMessage);
  }
}
