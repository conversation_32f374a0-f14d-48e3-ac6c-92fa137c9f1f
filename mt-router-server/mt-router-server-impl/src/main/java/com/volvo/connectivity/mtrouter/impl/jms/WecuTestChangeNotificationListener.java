package com.volvo.connectivity.mtrouter.impl.jms;

import com.volvo.connectivity.asset.repository.events.model.Status;
import com.volvo.connectivity.asset.repository.events.model.WecuChangeNotifyEvent;
import com.volvo.connectivity.mtrouter.impl.services.WecuChangeNotificationService;
import com.volvo.connectivity.mtrouter.impl.util.MetricsReporter;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import jakarta.jms.Destination;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@JmsController(destination = WecuTestChangeNotificationListener.WECU_TEST_NOTIFY_IN_QUEUE)
public class WecuTestChangeNotificationListener {

  public static final String WECU_TEST_NOTIFY_IN_QUEUE = "WECU.TEST.CHANGE.NOTIFY.IN";
  public static final String VERSION_1_0 = "1.0";

  private final WecuChangeNotificationService wecuChangeNotificationService;

  public WecuTestChangeNotificationListener(
      WecuChangeNotificationService wecuChangeNotificationService) {
    Validate.notNull(wecuChangeNotificationService, "wecuChangeNotificationService");
    this.wecuChangeNotificationService = wecuChangeNotificationService;
  }

  @JmsMessageMapping(
      consumesType = "WECU_TEST_CHANGE_NOTIFICATION_MESSAGE",
      consumesVersion = VERSION_1_0)
  public void handleWecuChangeNotificationEvent(JmsMessage<WecuChangeNotifyEvent> jmsMessage) {
    log.info(
        "Received wecu notification event jms message: {} on queue: {} ",
        jmsMessage,
        WECU_TEST_NOTIFY_IN_QUEUE);
    String correlationId =
        jmsMessage
            .correlationId()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Correlation ID header is missing on JMS message"));
    Destination replyQueue =
        jmsMessage
            .replyTo()
            .orElseThrow(
                () ->
                    new IllegalArgumentException(
                        "Reply-to queue header is missing on JMS message"));
    Mono.just(jmsMessage.payload())
        .publishOn(Schedulers.single())
        .flatMap(this.wecuChangeNotificationService::persistWecuChangeNotification)
        .subscribe(
            wecuChangeNotifyEventMessage -> {
              log.info(
                  "Successfully processed wecu change notification message request. correlationId: {} vpi: {} ",
                  correlationId,
                  wecuChangeNotifyEventMessage.getAsset().getVpi());
              MetricsReporter.onWecuChangeNotifyRequestSuccess();

              wecuChangeNotificationService.sendWecuChangeNotifyEventReply(
                  correlationId, replyQueue, Status.OK, "");
            },
            throwable -> {
              log.error(
                  "Failed to process wecu change notification request. referenceId: {}",
                  jmsMessage.correlationId().get(),
                  throwable);
              MetricsReporter.onWecuChangeNotifyRequestFailure();
              if (replyQueue != null) {
                wecuChangeNotificationService.sendWecuChangeNotifyEventReply(
                    jmsMessage.correlationId().get(),
                    replyQueue,
                    Status.ERROR,
                    throwable.getMessage() != null ? throwable.getMessage() : "Failed");
              }
            });
    ;
  }
}
