package com.volvo.connectivity.mtrouter.impl.services;

import com.volvo.vc.conrepo.api.v2.ActivationNotifyEvent;
import java.io.Serial;
import java.io.Serializable;
import java.util.Comparator;
import org.springframework.stereotype.Component;

@Component
public class ActivationNotifyEventComparator
    implements Comparator<ActivationNotifyEvent>, Serializable {
  @Serial private static final long serialVersionUID = 1L;

  @Override
  public int compare(
      ActivationNotifyEvent activationNotifyEvent1, ActivationNotifyEvent activationNotifyEvent2) {
    return activationNotifyEvent1
        .getChangeStatus()
        .toString()
        .compareTo(activationNotifyEvent2.getChangeStatus().toString());
  }
}
