package com.volvo.connectivity.mtrouter.impl.security;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.crypto.common.entity.InitializationVector;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.EncryptedPayloadWithoutMac;
import com.volvo.vc.crypto.symmetric.encryption.gcm.MessageAuthenticationCode;

public final class SecureUtils {
  private SecureUtils() {
    throw new IllegalStateException();
  }

  public static AesGcmEncryptionResult createAesGcmEncryptionResult(
      ImmutableByteArray immutableByteArray) {
    Validate.notNull(immutableByteArray, "immutableByteArray");

    InitializationVector initializationVector =
        InitializationVector.create(
            immutableByteArray.copyOfRange(0, InitializationVector.IV_LENGTH_IN_BYTES));
    ImmutableByteArray encryptedPayload =
        immutableByteArray.copyOfRange(
            InitializationVector.IV_LENGTH_IN_BYTES, immutableByteArray.getLength());

    return createAesGcmEncryptionResult(encryptedPayload, initializationVector);
  }

  public static ImmutableByteArray getPayload(AesGcmEncryptionResult aesGcmEncryptionResult) {
    Validate.notNull(aesGcmEncryptionResult, "aesGcmEncryptionResult");

    return aesGcmEncryptionResult
        .getInitializationVector()
        .getImmutableByteArray()
        .concat(aesGcmEncryptionResult.getEncryptedPayloadWithoutMac().getImmutableByteArray())
        .concat(aesGcmEncryptionResult.getMessageAuthenticationCode().getImmutableByteArray());
  }

  private static AesGcmEncryptionResult createAesGcmEncryptionResult(
      ImmutableByteArray immutableByteArray, InitializationVector initializationVector) {
    int length = immutableByteArray.getLength();
    EncryptedPayloadWithoutMac encryptedPayloadWithoutMac =
        EncryptedPayloadWithoutMac.create(
            immutableByteArray.copyOfRange(
                0, length - MessageAuthenticationCode.MAC_LENGTH_IN_BYTES));

    MessageAuthenticationCode messageAuthenticationCode =
        MessageAuthenticationCode.create(
            immutableByteArray.copyOfRange(
                length - MessageAuthenticationCode.MAC_LENGTH_IN_BYTES, length));

    return AesGcmEncryptionResult.create(
        encryptedPayloadWithoutMac, initializationVector, messageAuthenticationCode);
  }
}
