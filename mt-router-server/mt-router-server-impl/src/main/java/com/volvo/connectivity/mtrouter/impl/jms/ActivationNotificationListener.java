package com.volvo.connectivity.mtrouter.impl.jms;

import com.volvo.connectivity.mtrouter.impl.db.entity.SecureMessage;
import com.volvo.connectivity.mtrouter.impl.security.SecureUtils;
import com.volvo.connectivity.mtrouter.impl.services.ConRepo2NotifyService;
import com.volvo.connectivity.mtrouter.impl.util.MetricsReporter;
import com.volvo.tisp.framework.jms.JmsMessage;
import com.volvo.tisp.framework.jms.annotation.JmsController;
import com.volvo.tisp.framework.jms.annotation.JmsMessageMapping;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.conrepo.MessageTypes;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import com.volvo.vc.conrepo.api.v2.SecureActivationNotifyEventMessage;
import com.volvo.vc.crypto.asymmetric.signature.Signature;
import com.volvo.vc.crypto.common.entity.EncryptedAesKey;
import com.volvo.vc.crypto.common.entity.KeyId;
import com.volvo.vc.crypto.message.EncryptedMessage;
import com.volvo.vc.crypto.message.RsaEncryptedMessage;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

@Slf4j
@JmsController(destination = ActivationNotificationListener.CONREPO2_NOTIFY_IN_QUEUE)
public class ActivationNotificationListener {
  public static final String CONREPO2_NOTIFY_IN_QUEUE = "ACTIVATION.NOTIFICATION.IN";
  private final ConRepo2NotifyService conRepo2NotifyService;

  public ActivationNotificationListener(ConRepo2NotifyService conRepo2NotifyService) {
    Validate.notNull(conRepo2NotifyService, "conRepo2NotifyService");
    this.conRepo2NotifyService = conRepo2NotifyService;
  }

  @JmsMessageMapping(
      consumesType = MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE,
      consumesVersion = MessageTypes.VERSION_2_0)
  public void receivingActivationNotifyMessage(
      JmsMessage<SecureActivationNotifyEventMessage> jmsMessage) {
    log.debug(
        "Received activation notify jms message: {} on queue: {} ",
        jmsMessage,
        CONREPO2_NOTIFY_IN_QUEUE);
    SecureActivationNotifyEventMessage secureActivationNotifyEventMessage = jmsMessage.payload();
    Mono.just(secureActivationNotifyEventMessage)
        .publishOn(Schedulers.single())
        .map(this::convertToSecureActivationNotifyEventMessageEntity)
        .map(this::createRsaEncryptedMessage)
        .map(this.conRepo2NotifyService::extractActivationNotifyEventMessage)
        .flatMap(this.conRepo2NotifyService::persistActivationNotifyMessage)
        .subscribe(
            activationNotifyEventMessage -> {
              log.info(
                  "Successfully processed activation message request. referenceId: {}",
                  activationNotifyEventMessage.getReferenceId());
              MetricsReporter.onActivationNotifyRequestSuccess();
              conRepo2NotifyService.sendNotifyResponseMessage(
                  activationNotifyEventMessage.getReferenceId(),
                  "Success",
                  ResponseStatus.SUCCESS,
                  ActivationMessageType.NOTIFY);
            },
            throwable -> {
              log.error(
                  "Failed to process activation message request. referenceId: {}",
                  jmsMessage.correlationId().get(),
                  throwable);
              MetricsReporter.onActivationNotifyRequestFailure();
              conRepo2NotifyService.sendNotifyResponseMessage(
                  jmsMessage.correlationId().get(),
                  throwable.getMessage() != null ? throwable.getMessage() : "Failed",
                  ResponseStatus.FAILED,
                  ActivationMessageType.NOTIFY);
            });
  }

  private EncryptedMessage createRsaEncryptedMessage(
      SecureMessage secureActivationNotifyEventMessageEntity) {
    return RsaEncryptedMessage.create(
        EncryptedAesKey.create(
            ImmutableByteArray.of(secureActivationNotifyEventMessageEntity.getEncryptedAesKey())),
        SecureUtils.createAesGcmEncryptionResult(
            ImmutableByteArray.of(secureActivationNotifyEventMessageEntity.getEncryptedPayload())),
        Signature.create(
            ImmutableByteArray.of(secureActivationNotifyEventMessageEntity.getSignature())),
        KeyId.ofLong(secureActivationNotifyEventMessageEntity.getPrivateKeyId().longValue()),
        KeyId.ofLong(secureActivationNotifyEventMessageEntity.getPublicKeyId().longValue()));
  }

  private SecureMessage convertToSecureActivationNotifyEventMessageEntity(
      SecureActivationNotifyEventMessage secureActivationNotifyEventMessageExternal) {
    Validate.notNull(
        secureActivationNotifyEventMessageExternal, "secureActivationNotifyEventMessageExternal");
    return SecureMessage.builder()
        .setEncryptedAesKey(secureActivationNotifyEventMessageExternal.getEncryptedAesKey())
        .setSignature(secureActivationNotifyEventMessageExternal.getSignature())
        .setEncryptedPayload(secureActivationNotifyEventMessageExternal.getEncryptedPayload())
        .setPrivateKeyId(secureActivationNotifyEventMessageExternal.getPrivateKeyId())
        .setPublicKeyId(secureActivationNotifyEventMessageExternal.getPublicKeyId())
        .setAnies(secureActivationNotifyEventMessageExternal.getAnies())
        .build();
  }
}
