package com.volvo.connectivity.mtrouter.impl.db.entity;

import java.math.BigInteger;
import java.util.List;
import lombok.Builder;
import lombok.Value;
import org.w3c.dom.Element;

@Value
@Builder(setterPrefix = "set")
public class SecureMessage {
  private final byte[] encryptedAesKey;
  private final byte[] signature;
  private final byte[] encryptedPayload;

  private final BigInteger privateKeyId;
  private final BigInteger publicKeyId;
  private final List<Element> anies;
}
