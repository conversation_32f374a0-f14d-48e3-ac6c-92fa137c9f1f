package com.volvo.connectivity.mtrouter.impl.services;

import static com.volvo.connectivity.mtrouter.impl.util.JmsUtils.getMessagePostProcessorAndSetCorrelationId;

import com.volvo.connectivity.asset.repository.events.model.WecuChangeNotifyEventReply;
import com.volvo.connectivity.mtrouter.impl.util.MetricsReporter;
import jakarta.jms.Destination;
import lombok.extern.slf4j.Slf4j;
import org.springframework.jms.JmsException;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class JmsService {

  private final JmsTemplate jmsTemplate;

  public JmsService(JmsTemplate jmsTemplate) {
    this.jmsTemplate = jmsTemplate;
  }

  public void sendWecuChangeNotifyEventReply(
      String coRelationId,
      Destination replyQueue,
      WecuChangeNotifyEventReply wecuChangeNotifyEventReply) {
    try {
      jmsTemplate.convertAndSend(
          replyQueue,
          wecuChangeNotifyEventReply,
          getMessagePostProcessorAndSetCorrelationId(coRelationId));
      log.debug(
          "Sent WecuChangeNotifyEventReply {} to the queue {}",
          wecuChangeNotifyEventReply,
          replyQueue);
      MetricsReporter.onWecuChangeNotifyResponsePublishSuccess();
    } catch (JmsException jmsException) {
      log.error(
          "Failed to publish WecuChangeNotifyEventReply: {}",
          wecuChangeNotifyEventReply,
          jmsException);
      MetricsReporter.onWecuChangeNotifyResponsePublishFailure();
    }
  }
}
