package com.volvo.connectivity.mtrouter.impl.services;

import com.volvo.connectivity.sharding.SharderService;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.ServiceName;
import java.io.Closeable;
import java.io.Serializable;
import java.net.URI;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.state.ConnectionState;
import org.apache.curator.x.discovery.ServiceCache;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.apache.curator.x.discovery.ServiceInstance;
import org.apache.curator.x.discovery.details.ServiceCacheListener;
import org.springframework.lang.Nullable;

/**
 * Service that keeps a list of up to date and active {@link ServiceInstance} and performs
 * calculations which message {@link URI} belongs to witch {@link ServiceInstance}
 */
@Slf4j
public final class SharderServiceProvider implements Closeable {

  private final SharderService<ServiceInstance<InstanceDetails>> sharderService;
  private final ServiceCache<InstanceDetails> serviceCache;
  private final ServiceName serviceName;

  public SharderServiceProvider(
      ServiceDiscovery<InstanceDetails> serviceDiscovery, ServiceName serviceName) {
    this.sharderService = new SharderService<>(new InstanceComparator());
    this.serviceName = serviceName;
    this.serviceCache =
        serviceDiscovery.serviceCacheBuilder().name(serviceName.getZNodeForService()).build();

    serviceCache.addListener(new InstanceCacheListener());

    try {
      serviceCache.start();
      log.info("Created SharderServiceProvider for service {}", serviceName);
    } catch (final Exception e) {
      throw new IllegalStateException(
          "Failed starting SharderServiceProvider for service " + serviceName, e);
    }

    updateSharding();
  }

  @Override
  public void close() {
    try {
      serviceCache.close();
      log.info("Closed SharderServiceProvider for service {}", serviceName);
    } catch (final Exception e) {
      log.warn("Failed to close SharderServiceProvider for service {}", serviceName, e);
    }

    sharderService.updateSharding(Collections.emptyList());
  }

  /**
   * Returns {@link ServiceInstance} that {@link Object} hash belongs to. Must have a {@link
   * java.lang.Object#hashCode() #hashCode()} method with implementation that satisfies the general
   * contract of the {@link java.lang.Object#hashCode() Object.hashCode()} method
   *
   * @param object instance of {@link URI}
   * @return {@link ServiceInstance} that {@link Object} belongs to
   */
  @Nullable
  public ServiceInstance<InstanceDetails> getServiceInstanceFromObject(@Nullable Object object) {
    return sharderService.getShardFromObject(object);
  }

  /** Updates sharding to latest state */
  public final void updateSharding() {
    List<ServiceInstance<InstanceDetails>> instanceList = serviceCache.getInstances();
    sharderService.updateSharding(instanceList);
  }

  /**
   * Implementation of {@link Comparator} used to sort {@link ServiceInstance} inside {@link
   * SharderService}
   */
  private static class InstanceComparator
      implements Comparator<ServiceInstance<InstanceDetails>>, Serializable {
    private static final long serialVersionUID = 1L;

    @Override
    public int compare(
        ServiceInstance<InstanceDetails> instance1, ServiceInstance<InstanceDetails> instance2) {
      String uriString1 = instance1.buildUriSpec();
      String uriString2 = instance2.buildUriSpec();
      return uriString1.compareTo(uriString2);
    }
  }

  /**
   * Implementation of {@link ServiceCacheListener} that updates sharding on {@link ServiceCache}
   * events
   */
  private class InstanceCacheListener implements ServiceCacheListener {
    private ConnectionState lastConnectionState = ConnectionState.CONNECTED;

    @Override
    public void cacheChanged() {
      log.info(
          "Instance cache has changed and now has {} instances",
          serviceCache.getInstances().size());

      if (lastConnectionState != null && lastConnectionState.isConnected()) {
        updateSharding();
      } else {
        log.info(
            "Ignoring instance change event! Connection state is {}. Waiting for reconnect!",
            lastConnectionState);
      }
    }

    @Override
    public void stateChanged(
        final CuratorFramework curatorFramework, final ConnectionState newConnectionState) {
      log.debug(
          "Zookeeper connection state changed from {} to {}",
          lastConnectionState,
          newConnectionState);
      lastConnectionState = newConnectionState;
    }
  }
}
