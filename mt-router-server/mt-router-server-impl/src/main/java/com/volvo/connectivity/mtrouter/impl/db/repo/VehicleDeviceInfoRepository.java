package com.volvo.connectivity.mtrouter.impl.db.repo;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import org.springframework.data.mongodb.repository.ReactiveMongoRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Mono;

@Repository
public interface VehicleDeviceInfoRepository
    extends ReactiveMongoRepository<VehicleDeviceInfo, String> {

  Mono<VehicleDeviceInfo> findByVpi(String vpiString);
}
