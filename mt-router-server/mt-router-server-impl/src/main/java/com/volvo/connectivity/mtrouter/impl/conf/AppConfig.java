package com.volvo.connectivity.mtrouter.impl.conf;

import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.mtrouter.impl.security.MessageDecoderXmlImpl;
import com.volvo.connectivity.mtrouter.impl.services.SharderServiceProvider;
import com.volvo.connectivity.mtrouter.impl.util.InstanceComparator;
import com.volvo.connectivity.mtrouter.impl.util.TispHeadersFilter;
import com.volvo.connectivity.sharding.SharderService;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.ServiceName;
import com.volvo.tisp.tce.discovery.ServiceNameBuilder;
import com.volvo.tisp.tce.discovery.conf.DiscoveryConfig;
import com.volvo.tisp.tce.discovery.monitor.ServiceMonitoringClient;
import com.volvo.tisp.tce.discovery.monitor.ServiceMonitoringClientBuilder;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEventMessage;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import io.micrometer.observation.ObservationRegistry;
import jakarta.validation.Validator;
import java.util.Comparator;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.apache.curator.x.discovery.ServiceInstance;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.mongo.MongoClientSettingsBuilderCustomizer;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.context.event.ApplicationEventMulticaster;
import org.springframework.data.mongodb.ReactiveMongoDatabaseFactory;
import org.springframework.data.mongodb.ReactiveMongoTransactionManager;
import org.springframework.data.mongodb.core.ReactiveMongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.mapping.event.ValidatingMongoEventListener;
import org.springframework.data.mongodb.observability.ContextProviderFactory;
import org.springframework.data.mongodb.observability.MongoObservationCommandListener;
import org.springframework.data.mongodb.repository.config.EnableReactiveMongoRepositories;
import org.springframework.http.converter.protobuf.ProtobufHttpMessageConverter;
import org.springframework.web.reactive.config.EnableWebFlux;

@Slf4j
@SpringBootApplication(scanBasePackages = {"com.volvo.connectivity.mtrouter.impl"})
@EnableWebFlux
@Import({DiscoveryConfig.class, InfluxEventReporter.class, AppEvent.class, TispHeadersFilter.class})
@EnableReactiveMongoRepositories(basePackages = "com.volvo.connectivity.mtrouter.impl.db.repo")
@ConfigurationPropertiesScan
public class AppConfig {

  public static void main(String[] args) {
    SpringApplication.run(AppConfig.class, args);
  }

  ServiceName createServiceName(final String serviceName) {
    final Config config = ConfigFactory.getConfig();
    return new ServiceNameBuilder()
        .setEnvironment(config.getEnvironmentId())
        .setSite(config.getSite())
        .setSolution(config.getSolution())
        .setName(serviceName)
        .build();
  }

  @Bean
  MongoClientSettingsBuilderCustomizer mongoClientSettingsBuilderCustomizer(
      ObservationRegistry registry) {
    return builder ->
        builder
            .contextProvider(ContextProviderFactory.create(registry))
            .addCommandListener(new MongoObservationCommandListener(registry));
  }

  @Bean
  @ConditionalOnProperty(name = "mongo.validating-event-listener-enabled", matchIfMissing = true)
  public ValidatingMongoEventListener validatingMongoEventListener(final Validator validator) {
    return new ValidatingMongoEventListener(validator);
  }

  @Bean
  @ConditionalOnProperty(name = "mongo.transaction-manager-enabled", matchIfMissing = true)
  ReactiveMongoTransactionManager mongoTransactionManager(ReactiveMongoDatabaseFactory dbFactory) {
    return new ReactiveMongoTransactionManager(dbFactory);
  }

  @Bean
  public ReactiveMongoTemplate reactiveMongoTemplate(
      ReactiveMongoDatabaseFactory databaseFactory, MappingMongoConverter converter) {
    converter.setTypeMapper(new DefaultMongoTypeMapper(null)); // Removes _class column
    return new ReactiveMongoTemplate(databaseFactory, converter);
  }

  @Bean
  ProtobufHttpMessageConverter protobufHttpMessageConverter() {
    return new ProtobufHttpMessageConverter();
  }

  @Bean
  SharderService<ServiceInstance<InstanceDetails>> instanceSharderService() {
    Comparator<ServiceInstance<InstanceDetails>> serviceInstanceComparator =
        new InstanceComparator();
    return new SharderService<>(serviceInstanceComparator);
  }

  @Bean(destroyMethod = "close")
  ServiceMonitoringClient mtServiceMonitoringClientVwtpInitiatorService(
      ServiceDiscovery<InstanceDetails> serviceDiscovery,
      ApplicationEventMulticaster applicationEventMulticaster) {
    Validate.notNull(serviceDiscovery, "serviceDiscovery");
    Validate.notNull(applicationEventMulticaster, "applicationEventMulticaster");
    log.info("Creating ServiceMonitoringClient for \"{}\"", ServiceConstant.VWTP_INITIATOR_SERVICE);
    return new ServiceMonitoringClientBuilder()
        .setApplicationEventMulticaster(applicationEventMulticaster)
        .setServiceDiscovery(serviceDiscovery)
        .setServiceName(createServiceName(ServiceConstant.VWTP_INITIATOR_SERVICE))
        .build();
  }

  @Bean(destroyMethod = "close")
  SharderServiceProvider providerVwtpInitiatorService(
      final ServiceDiscovery<InstanceDetails> serviceDiscovery) {
    var serviceName = createServiceName(ServiceConstant.VWTP_INITIATOR_SERVICE);
    log.info("Creating SharderServiceProvider for \"{}\"", serviceName);
    return new SharderServiceProvider(serviceDiscovery, serviceName);
  }

  @Bean
  public MessageDecoderXmlImpl<ActivationNotifyEventMessage> createMessageDecoder() {
    return new MessageDecoderXmlImpl<>(ActivationNotifyEventMessage.class);
  }
}
