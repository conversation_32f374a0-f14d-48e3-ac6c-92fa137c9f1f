package com.volvo.connectivity.mtrouter.impl.controller;

import com.google.protobuf.TextFormat;
import com.volvo.connectivity.mtrouter.impl.conf.BulkSyncProperties;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.impl.util.BulkSyncDecryptionFilter;
import com.volvo.connectivity.mtrouter.impl.util.DeviceConvertUtil;
import com.volvo.cos.conrepo.bulk_sync.v1.VehicleInfo;
import com.volvo.tisp.framework.security.annotation.Authentication;
import com.volvo.tisp.vc.main.utils.lib.Validate;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.nio.charset.StandardCharsets;
import java.time.Clock;
import java.time.Duration;
import java.time.Instant;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Slf4j
@RestController
@RequestMapping("/api/v1")
@Authentication(required = false)
public class BulkSyncRestController {

  private final Clock clock;
  private final WebClient webClient;
  private final BulkSyncProperties bulkSyncProperties;
  private final VehicleDeviceInfoRepository vehicleDeviceInfoRepository;
  private final VehicleDeviceInfoRepository vehicleDeviceInfoRepositoryProxy;

  @SuppressFBWarnings("CT_CONSTRUCTOR_THROW")
  public BulkSyncRestController(
      final BulkSyncDecryptionFilter decryptionFilter,
      final WebClient.Builder webClientBuilder,
      final BulkSyncProperties bulkSyncProperties,
      final VehicleDeviceInfoRepository vehicleDeviceInfoRepository) {
    Validate.notNull(decryptionFilter, "decryptionFilter");
    Validate.notNull(webClientBuilder, "webClientBuilder");
    Validate.notNull(bulkSyncProperties, "bulkSyncProperties");
    Validate.notNull(vehicleDeviceInfoRepository, "vehicleDeviceInfoRepository");

    this.clock = Clock.systemUTC();
    this.webClient =
        webClientBuilder
            .filter(decryptionFilter)
            .baseUrl("http://conrepo2/api/v1/bulksync")
            .build();
    this.bulkSyncProperties = bulkSyncProperties;
    this.vehicleDeviceInfoRepository = vehicleDeviceInfoRepository;
    this.vehicleDeviceInfoRepositoryProxy =
        (VehicleDeviceInfoRepository)
            Proxy.newProxyInstance(
                this.getClass().getClassLoader(),
                new Class<?>[] {VehicleDeviceInfoRepository.class},
                this::mongoRepositoryInvocationHandler);
  }

  @Transactional
  @GetMapping("/bulk-sync")
  @SuppressFBWarnings("CRLF_INJECTION_LOGS")
  public Mono<Void> startBulkSync(
      @RequestParam(name = "region", required = false) String region,
      @RequestParam(name = "softcarOnly", defaultValue = "false") boolean softcarOnly,
      @RequestParam(name = "dryRun", defaultValue = "true") boolean dryRun) {
    log.info(
        "Bulksync: starting with region: {}, softcarOnly: {}, dryRun: {}",
        region,
        softcarOnly,
        dryRun);
    Instant startTime = clock.instant();

    VehicleDeviceInfoRepository repository = vehicleDeviceInfoRepository;
    if (dryRun) {
      log.info("Bulksync: running in dryRun mode. No database modifications will be executed");
      repository = vehicleDeviceInfoRepositoryProxy;
    }

    return repository
        .count()
        .doOnNext(count -> log.info("Bulksync: {} records about to be deleted", count))
        .then(
            repository
                .deleteAll()
                .then(
                    webClient
                        .get()
                        .uri(
                            builder ->
                                builder
                                    .queryParamIfPresent("region", Optional.ofNullable(region))
                                    .queryParam(
                                        "clientKeyAlias", bulkSyncProperties.clientKeyAlias())
                                    .queryParam(
                                        "conrepoKeyAlias", bulkSyncProperties.conrepoKeyAlias())
                                    .queryParam("softcarOnly", softcarOnly)
                                    .build())
                        .retrieve()
                        .bodyToFlux(VehicleInfo.class)
                        .doOnNext(
                            vehicleInfo ->
                                log.atDebug()
                                    .setMessage("Bulksync: received  -> VehicleInfo({})")
                                    .addArgument(() -> TextFormat.shortDebugString(vehicleInfo))
                                    .log())
                        .map(DeviceConvertUtil::vehicleDeviceToVehicleDeviceInfo)
                        .doOnNext(
                            vehicleDeviceInfo ->
                                log.debug("Bulksync: inserting -> {}", vehicleDeviceInfo))
                        .buffer(bulkSyncProperties.batchSize())
                        .concatMap(repository::insert)
                        .count()
                        .doOnNext(count -> log.info("Bulksync: {} records inserted", count))
                        .then()))
        .doOnTerminate(
            () ->
                log.info("Bulksync: duration - {}", Duration.between(startTime, clock.instant())));
  }

  /**
   * Error handler for all service handlers
   *
   * @param throwable instance of {@link Throwable}
   * @return instance of {@link ResponseEntity}&lt;{@link String}&gt;
   */
  @ExceptionHandler
  protected ResponseEntity<String> handleException(final Throwable throwable) {
    log.error("Bulksync:", throwable);
    if (throwable instanceof WebClientResponseException wcre) {
      return ResponseEntity.status(wcre.getStatusCode())
          .<String>body(wcre.getResponseBodyAsString(StandardCharsets.UTF_8));
    } else {
      return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
          .<String>body(throwable.toString());
    }
  }

  /**
   * Implementation of {@link InvocationHandler#invoke(Object, Method, Object[])} to handle proxy
   * invocations for {@link VehicleDeviceInfoRepository}
   *
   * @param proxy instance of {@link Object}
   * @param method instance of {@link Method}
   * @param args instance of {@link Object}[]
   * @return instance of {@link Object}
   * @see InvocationHandler#invoke(Object, Method, Object[])
   */
  @SuppressFBWarnings({"UP_UNUSED_PARAMETER", "URV_CHANGE_RETURN_TYPE"})
  private Object mongoRepositoryInvocationHandler(Object proxy, Method method, Object[] args) {
    if ("count".equals(method.getName()) && method.getParameterCount() == 0) {
      return vehicleDeviceInfoRepository.count();
    } else if ("deleteAll".equals(method.getName()) && method.getParameterCount() == 0) {
      return Mono.empty();
    } else if ("insert".equals(method.getName())
        && method.getParameterCount() == 1
        && args[0] instanceof Iterable<?> iterableArg) {
      return Flux.fromIterable(iterableArg);
    } else {
      throw new IllegalArgumentException(
          "Bulksync: dryRun - proxy support for method '"
              + method.toString()
              + "' - not implemented");
    }
  }
}
