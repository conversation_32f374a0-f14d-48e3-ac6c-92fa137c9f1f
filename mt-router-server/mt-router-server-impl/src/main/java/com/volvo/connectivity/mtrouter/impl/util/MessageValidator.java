package com.volvo.connectivity.mtrouter.impl.util;

import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.TextFormat;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.Transport;
import org.springframework.http.HttpStatus;
import org.springframework.web.server.ResponseStatusException;

public class MessageValidator {

  public static void validateMtMessage(MtMessage mtMessage) {
    if (mtMessage == null) {
      throw new ResponseStatusException(HttpStatus.BAD_REQUEST, "MtMessage is null");
    }

    final String vpi = mtMessage.getVpi();
    if (vpi.isBlank()) {
      throw new ResponseStatusException(
          HttpStatus.BAD_REQUEST,
          "vpi in MtMessage is null or blank : " + getDebugString(mtMessage));
    }

    if (mtMessage.getTransport() == Transport.UNRECOGNIZED) {
      throw new ResponseStatusException(
          HttpStatus.BAD_REQUEST,
          "transport in MtMessage is UNRECOGNIZED : " + getDebugString(mtMessage));
    }

    final byte[] payload = mtMessage.getPayload().toByteArray();
    if (payload.length == 0) {
      throw new ResponseStatusException(
          HttpStatus.BAD_REQUEST,
          "payload in mtMessage is null or empty : " + getDebugString(mtMessage));
    }

    final String messageId = mtMessage.getMessageId();
    if (messageId.isBlank()) {
      throw new ResponseStatusException(
          HttpStatus.BAD_REQUEST,
          "messageId in MtMessage is null or blank : " + getDebugString(mtMessage));
    }
  }

  private static String getDebugString(MessageOrBuilder mtMessage) {
    return TextFormat.shortDebugString(mtMessage);
  }

  private MessageValidator() {}
}
