package com.volvo.connectivity.mtrouter.impl.conf;

import com.volvo.connectivity.mtrouter.impl.services.SharderServiceProvider;
import com.volvo.connectivity.mtrouter.impl.util.MetricNames;
import com.volvo.tisp.framework.servicediscovery.ServiceActivatedEvent;
import com.volvo.tisp.framework.servicediscovery.ServiceDeactivatedEvent;
import com.volvo.tisp.framework.servicediscovery.ServicePausedEvent;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tags;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.actuate.health.HealthContributorRegistry;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.EventListener;
import reactor.core.publisher.Hooks;

@Slf4j
public class AppEvent {
  private final List<ServiceDiscoveryRegistration> serviceDiscoveryRegistrations;
  private final HealthContributorRegistry healthContributorRegistry;
  private final SharderServiceProvider providerVwtpInitiatorService;

  public AppEvent(
      final List<ServiceDiscoveryRegistration> serviceDiscoveryRegistrations,
      final HealthContributorRegistry healthContributorRegistry,
      final SharderServiceProvider providerVwtpInitiatorService) {
    this.serviceDiscoveryRegistrations = serviceDiscoveryRegistrations;
    this.healthContributorRegistry = healthContributorRegistry;
    this.providerVwtpInitiatorService = providerVwtpInitiatorService;
  }

  @EventListener(ApplicationStartedEvent.class)
  public void registerReactorMetrics() {
    log.info("Registering \"onNextDropped\" metrics hook");
    Hooks.onNextDropped(
        object ->
            Metrics.counter(
                    MetricNames.ANOMALIES,
                    Tags.of(
                        MetricNames.TYPE_TAG,
                        "reactor-dropped-" + object.getClass().getSimpleName()))
                .increment());
  }

  @EventListener(ServiceActivatedEvent.class)
  public void registerServiceDiscovery() {
    serviceDiscoveryRegistrations.forEach(ServiceDiscoveryRegistration::open);
  }

  @EventListener({
    ServiceDeactivatedEvent.class,
    ServicePausedEvent.class,
    ContextClosedEvent.class
  })
  void unregisterServiceDiscovery() {
    serviceDiscoveryRegistrations.forEach(ServiceDiscoveryRegistration::close);
  }

  @EventListener(ApplicationReadyEvent.class)
  public void onApplicationReadyEvent() {
    healthContributorRegistry.unregisterContributor("serviceDiscovery");
    providerVwtpInitiatorService.updateSharding();
  }
}
