openapi: 3.0.3

info:
  title: API between mt-router-server and tgw-mt-server
  description: Open API specification for API between mt-router-server and tgw-mt-server
  version: '1.2'

servers:
  - url: irs.$environment.$solution.$site.test.aws.vgthosting.net:47265
    description: mt-router-server

security:
  - basicAuth: []
paths:
  /test:
    get:
      summary: >-
        test endpoint
      responses:
        '200':
          description: OK
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '429':
          $ref: '#/components/responses/TooManyRequests'
        '503':
          $ref: '#/components/responses/ServiceUnavailable'
components:
  responses:
    ServiceUnavailable:
      description: >-
        Service Unavailable
      content:
        text/plain: {}
    BadRequest:
      description: >-
        Bad Request
      content:
        text/plain: {}
    Forbidden:
      description: >-
        Forbidden
      content:
        text/plain: {}
    NotFound:
      description: >-
        Not Found
      content:
        text/plain: {}
    TooManyRequests:
      description: >-
        Too Many Requests
      content:
        text/plain: {}
  securitySchemes:
    basicAuth:
      type: http
      scheme: basic