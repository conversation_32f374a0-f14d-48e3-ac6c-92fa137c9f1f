version: '3.3'
services:
  mongo:
    image: mongo:6.0
    entrypoint: [ "/usr/bin/mongod", "--bind_ip_all", "--replSet", "rs0" ]
    ports:
      - 27017:27017/tcp
  influxdb:
    image: influxdb:1.8-alpine
    ports:
      - 9086:8086/tcp
  zoo:
    image: zookeeper:3.8
    ports:
      - 2181:2181/tcp
  wiremock:
    image: wiremock/wiremock:latest-alpine
    command:
      - --async-response-enabled=true
      - --global-response-templating
    ports:
      - 3080:8080/tcp
  artemis:
    image: apache/activemq-artemis:latest-alpine
    environment:
      - ARTEMIS_USER=admin
      - ARTEMIS_PASSWORD=admin
    ports:
      - 3161:8161/tcp
      - 31616:61616/tcp
