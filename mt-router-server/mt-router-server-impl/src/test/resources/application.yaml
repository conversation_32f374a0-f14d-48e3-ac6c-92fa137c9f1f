---
spring:
  data.mongodb:
    authentication-database: admin
    host: ${container.host}
    replica-set-name: rs0
    port: 27017
    database: MTROUTER
  artemis:
    mode: native
    broker-url: tcp://${container.host}:31616

management.influx.metrics.export:
  db: connectivity_services
  uri: http://${container.host}:9086
  retention-policy: 30days
  auto-create-db: false

security:
  transport:
    keystore:
      path: ./src/test/resources/mtrouter-transport-keystore.pkcs12
      password: mtrouter@test!
    notify.key.public: conrepo2-test
    key.private: mtrouter-test

bulksync:
  public-certificate: MIIDdzCCAl+gAwIBAgIEfp8slDANBgkqhkiG9w0BAQsFADBsMRAwDgYDVQQGEwdVbmtub3duMRAwDgYDVQQIEwdVbmtub3duMRAwDgYDVQQHEwdVbmtub3duMRAwDgYDVQQKEwdVbmtub3duMRAwDgYDVQQLEwdVbmtub3duMRAwDgYDVQQDEwdVbmtub3duMB4XDTE5MDgwNzA3MzUyMVoXDTE5MTEwNTA3MzUyMVowbDEQMA4GA1UEBhMHVW5rbm93bjEQMA4GA1UECBMHVW5rbm93bjEQMA4GA1UEBxMHVW5rbm93bjEQMA4GA1UEChMHVW5rbm93bjEQMA4GA1UECxMHVW5rbm93bjEQMA4GA1UEAxMHVW5rbm93bjCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALUP8dt8yDiQs9+xfJp06xIcawn+Yql2nU7EApo+zDPN5+fn8NekabqmbWWKIXGC+84mVPfTONIENMy56eyubfWFtGJRh4HZBz0QL4mtMObGb6SSUHux3ZmOcK/E8IJ5qVcsWO4PkMQVPI87IIwzDKb6mK1feJP7TG/wF4gd5/yY9MqFQWn4rU0pWMKGCYb/NVYnileU5CMkWyXMxvDojmRwF9AKbe/X+EqSeSYfsdo/DTkJAu/3KIIhKyZ+jOQ5+BRpkAIYlcYWrW9H/YogsHtmfurcID8HWeUJC+cgtNN4FsZDMadHZPtnfEvjf5FJoWjnFDmPBVYY9riQt1Dlpz8CAwEAAaMhMB8wHQYDVR0OBBYEFIh8MXVjrr8e9a8RbWRI8XxC+WBqMA0GCSqGSIb3DQEBCwUAA4IBAQBTj1ASSCZwc/ka3foyNlGU4Hb+d9tnqOPpGJSDmKeOkbs1DVcZq8cmQwqBf8C7nhuWVLKvdzPiGXQDff7KlvnUtA5F4wjYOapxuUX+BolPBUe/ySK7iokkY/4YTSUirBx1++CQst4Q3YduP7nkt0yw8SEK8Ju5jFhCqlKHHwdZpkq2zDRgqHdHF3Mfuu3/7ss/qGn92t2HQvUM8JRQKy2boDZ1KHJSLVaDacQQwm6cNjI+5NsfSxjRUH+0Q6iKKIA50tPqyL2VGMmQPp/2xkqotUZD6y67jSGOFqDE45VOd6t6HoJnDEdg7rGca1/+LHV4npfSbTGjsM+Bk7T4T0mn
  private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC1D/HbfMg4kLPfsXyadOsSHGsJ/mKpdp1OxAKaPswzzefn5/DXpGm6pm1liiFxgvvOJlT30zjSBDTMuensrm31hbRiUYeB2Qc9EC+JrTDmxm+kklB7sd2ZjnCvxPCCealXLFjuD5DEFTyPOyCMMwym+pitX3iT+0xv8BeIHef8mPTKhUFp+K1NKVjChgmG/zVWJ4pXlOQjJFslzMbw6I5kcBfQCm3v1/hKknkmH7HaPw05CQLv9yiCISsmfozkOfgUaZACGJXGFq1vR/2KILB7Zn7q3CA/B1nlCQvnILTTeBbGQzGnR2T7Z3xL43+RSaFo5xQ5jwVWGPa4kLdQ5ac/AgMBAAECggEAArrGzqRG8HNeAtVOJ5XzpUZAZtoq6psbLADQeTrRIUbNZDWe7+FOpr2zkIBQmMF8wlLZnRLWHHRrx1pWKHdIuRbW7VSFUnPPJCCGVvEBd7kuHbEG/ZZk3NfE4yF47r6eyu2kOZKKgCpBy3b8uqBaZHoKqzJxQuna8SURdb/dq3UsVmHLKlxPlchT2+mRsJTDih5VLQpubRsrdIul8truGkqIRjiIUCjrbsYAWhQnuVXo26ocbGbZFvzrlnbONBfOmh4C/QsOB9q0G1jt0JEcYV8Lv+i4uoPNnjhdWJYhvQTwy7iQ8rTyy8xwa0ozMWIKa6o4Jji30DSl6pk83Km+oQKBgQDsjzI1sz73+GUZwNfhoq6cD0X/c5PkCbfVEx3j6+9nNwISjYwxNoOM2Db3R9qzH9SxMZ/31yAJPYtJSzH9hU5YNRto1A2woNUxFU3090u1mXQ6ApPF8g/LsvVje5tZNfZbgRPbX3xbO6cU77mZ9qLpKD0UL8qvBx9+BBVsdqsV3QKBgQDD8S9n6q7W/bp+KsSK1StBeCUYAimPP1vLlosAbWz8XfpPsCV7xGlBCETurGOe/VxRQbfKL9SprL6sHxV0tXXPDL/f6Ru30y/gWr6nzB1JAjl+LqNk3OTdeh0ZJS2x3AkI1OWTnlzJS95nbiQSR2qoQ5uJxSMpgJqbGkWJlSYFywKBgQDdS2fxDGmPl3MckGZj0cf+fdtBiWGEJgMu9N81ZHVk3PS3XmsTbqfJjkp9MV7JyrExwfaf50M04bn3KrztKVOM+MFXY2gbH5nQfNmcm+rbS16LFfYfar6UFCmykuNUpUJ9f1aQFddDkZ5P26KJAUyNSPM6eyrkaYlwXWmdObX6kQKBgDKwYC0n3xcrCGW/Np0bzFbhd+cgGBRAtcSTK15u8HTQ8wJOdUPxabEzLHH6EsN8k7dJovwhJ4jFcYYhfHF2yAVKLREFwu/pKEeH8i6CdUj/1FO6SPa81lAdWQ26Onn6jDPGr6VioGpKwDxNl/NKmmLxKnmXkCMaa+SYBUk0VmPFAoGACJDd0esk3iQiwEuM17yk2USzVtotTa/eV8qVVqrVojGGBiTgNn6U5m/RumKXGKzaD/KwFWjjZQcpUO5POwmbFNXOxLCc/g1CcGzRqxk6srYlUep0dxjPVOFhWvw5cC6E+yDERotaEs78IhpmeiUsMO4OyJHQuIk/UMXelBrocrQ=
  client-key-alias: tcetisp-test-1
  conrepo-key-alias: tcetisp-test-1

logging.level:
  web.test.client: TRACE
  com.volvo.connectivity.mtrouter: TRACE

tis-router.url: http://${container.host}:3080

servicediscovery:
  subr: http://${container.host}:3080
  auth: http://${container.host}:3080
  conrepo2: http://${container.host}:3080
  active-by-default: true
subscription.cache.enabled: false
