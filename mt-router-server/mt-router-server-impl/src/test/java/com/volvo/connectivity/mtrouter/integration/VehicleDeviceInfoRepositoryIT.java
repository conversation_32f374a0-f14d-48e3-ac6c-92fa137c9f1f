package com.volvo.connectivity.mtrouter.integration;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.entity.WtpVersion;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import java.time.Duration;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class VehicleDeviceInfoRepositoryIT extends TestcontainersBase {
  private static final String VPI = "41935A6C40BDCE235353D3F2F28CD336";
  @Autowired private VehicleDeviceInfoRepository vehicleDeviceInfoRepository;

  @Test
  void givenAVehicleDeviceInfo_whenFindByVehiclePlatformId_thenShouldReturnCorrectData() {
    // given
    VehicleDeviceInfo deviceInfo =
        VehicleDeviceInfo.builder()
            .setObsAlias(123l)
            .setVpi(VPI)
            .setWtpVersion(WtpVersion.VERSION_2)
            .setIpv4Address("*********")
            .setIpv4Port(8080)
            .setMobileNetworkOperator("telenor")
            .setMsisdn("+8947184345")
            .build();
    // when
    VehicleDeviceInfo insertedDeviceInfo = vehicleDeviceInfoRepository.insert(deviceInfo).block();
    VehicleDeviceInfo retrievedDeviceInfo =
        vehicleDeviceInfoRepository.findByVpi(VPI).block(Duration.ofSeconds(10));
    // then
    Assertions.assertNotNull(retrievedDeviceInfo);
    Assertions.assertEquals(deviceInfo.getVpi(), retrievedDeviceInfo.getVpi());
    Assertions.assertEquals(deviceInfo.getIpv4Address(), retrievedDeviceInfo.getIpv4Address());
    Assertions.assertEquals(deviceInfo.getIpv4Port(), retrievedDeviceInfo.getIpv4Port());
    Assertions.assertEquals(
        deviceInfo.getMobileNetworkOperator(), retrievedDeviceInfo.getMobileNetworkOperator());
    Assertions.assertEquals(deviceInfo.getMsisdn(), retrievedDeviceInfo.getMsisdn());
    Assertions.assertEquals(deviceInfo.getWtpVersion(), retrievedDeviceInfo.getWtpVersion());
    vehicleDeviceInfoRepository.deleteById(insertedDeviceInfo.getVpi()).block();
  }
}
