package com.volvo.connectivity.mtrouter.integration.activationNotificationtests;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.entity.WtpVersion;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.JmsUtil;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.NotificationTestClient;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.SecurityTestUtil;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import com.volvo.vc.conrepo.MessageTypes;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEvent;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.ChangeStatus;
import com.volvo.vc.conrepo.api.v2.DeviceDetailedEntry;
import com.volvo.vc.conrepo.api.v2.DeviceSim;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import com.volvo.vc.conrepo.api.v2.SecureActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.State;
import jakarta.jms.Message;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;

@Slf4j
@ExtendWith(TispContextExtension.class)
class ActivationNotificationAndMtFlowIT extends TestcontainersBase {

  private static final String SERVICE_PATH = "/api/v1/mt/message";
  @Autowired ServiceDiscovery<InstanceDetails> serviceDiscovery;
  @Autowired private VehicleDeviceInfoRepository vehicleDeviceInfoRepository;
  @Autowired private JmsTemplate jmsTemplate;
  @Autowired private MessagePublisher.Builder messagePublisherBuilder;
  private MessagePublisher<SecureActivationNotifyEventMessage> activationNotificationPublisher;

  @BeforeEach
  public void setup() {
    activationNotificationPublisher =
        messagePublisherBuilder
            .messageType(
                MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE,
                SecureActivationNotifyEventMessage.class)
            .version(MessageTypes.VERSION_2_0)
            .build();
  }

  @AfterEach
  public void clear() {
    vehicleDeviceInfoRepository.deleteAll().block();
  }

  @Test
  void integrationTestSATFlow() throws Exception {
    activationNotificationMessaging();

    MtMessage mtMessage = createMtMessage(Transport.SAT);

    WEB_TEST_CLIENT.post().uri(SERVICE_PATH).bodyValue(mtMessage).exchange().expectStatus().isOk();

    Message message = jmsTemplate.receive("MT.USERMESSAGE.IN");
    log.info("JMS Message: {}", message);
    byte[] jmsPayload = message.getBody(byte[].class);
    MtStatus mtStatusMessage = MtStatus.parseFrom(jmsPayload);
    Assertions.assertEquals(Status.DELIVERED, mtStatusMessage.getStatus());
    Assertions.assertEquals(
        "A4635A6C40B45E235353D3F2F28CD339", mtStatusMessage.getMessageId().trim());
  }

  @Test
  void integrationTestSMSFlow() throws Exception {
    activationNotificationMessaging();

    MtMessage mtMessage = createMtMessage(Transport.SMS);

    WEB_TEST_CLIENT.post().uri(SERVICE_PATH).bodyValue(mtMessage).exchange().expectStatus().isOk();

    Message message = jmsTemplate.receive("MT.USERMESSAGE.IN");
    log.info("JMS Message: {}", message);
    byte[] jmsPayload = message.getBody(byte[].class);
    MtStatus mtStatusMessage = MtStatus.parseFrom(jmsPayload);
    Assertions.assertEquals(Status.DELIVERED, mtStatusMessage.getStatus());
    Assertions.assertEquals(
        "A4635A6C40B45E235353D3F2F28CD339", mtStatusMessage.getMessageId().trim());
  }

  @Test
  void integrationTestUDPFlow() throws Exception {
    try (ServiceDiscoveryRegistration serviceDiscoveryRegistration =
        IntegrationTestHelper.createServiceRegistration(
            serviceDiscovery, ServiceConstant.VWTP_INITIATOR_SERVICE)) {
      serviceDiscoveryRegistration.open();

      activationNotificationMessaging();
      IntegrationTestHelper.stubWireMockForService();

      MtMessage mtMessage = createMtMessage(Transport.UDP);

      WEB_TEST_CLIENT
          .post()
          .uri(SERVICE_PATH)
          .bodyValue(mtMessage)
          .exchange()
          .expectStatus()
          .isOk();
    }
  }

  private void activationNotificationMessaging() throws Exception {
    DeviceSim deviceSim =
        SecurityTestUtil.createDeviceSim(
            DataUtil.IMSI_1.toString(),
            DataUtil.IPV4_ADDRESS_1.toString(),
            DataUtil.MSISDN_1.toString(),
            DataUtil.MOBILE_NETWORK_OPERATOR_1,
            DataUtil.IPV4_PORT_1);
    DeviceDetailedEntry deviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_1.toString(),
            DataUtil.OBS_ALIAS_1.toLong(),
            deviceSim,
            DataUtil.SATELLITE_ID_1,
            State.ACTIVATED);
    ActivationNotifyEvent activationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(ChangeStatus.UPDATED, deviceDetailedEntry);
    ActivationNotifyEventMessage activationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.NOTIFY, List.of(activationNotifyEvent));

    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.NOTIFY);
    VehicleDeviceInfo expectdVehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_1.toLong())
            .setWtpVersion(WtpVersion.VERSION_2)
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_1)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setSatelliteId(DataUtil.SATELLITE_ID_1)
            .setTelematicUnit(TelematicUnit.TGW)
            .build();
    DataUtil.verifyIfVehiclePersistedInDB(vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);
  }

  private MtMessage createMtMessage(final Transport transport) {
    return MtMessage.newBuilder()
        .setVpi(DataUtil.VPI_1.toString())
        .setMessageId("A4635A6C40B45E235353D3F2F28CD339")
        .setPayload(ByteString.copyFromUtf8("Test Message"))
        .setTransport(transport)
        .build();
  }
}
