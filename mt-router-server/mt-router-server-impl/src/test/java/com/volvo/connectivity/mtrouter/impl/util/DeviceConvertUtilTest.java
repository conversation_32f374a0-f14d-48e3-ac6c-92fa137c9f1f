package com.volvo.connectivity.mtrouter.impl.util;

import static org.assertj.core.api.Assertions.assertThat;

import com.google.protobuf.ByteString;
import com.volvo.connectivity.asset.repository.events.model.CellularCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularIPCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularIPPrivateCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularSMSCapability;
import com.volvo.connectivity.asset.repository.events.model.ConnectivityCapabilities;
import com.volvo.connectivity.asset.repository.events.model.SatelliteCapability;
import com.volvo.connectivity.asset.repository.events.model.WecuAsset;
import com.volvo.connectivity.asset.repository.events.model.WifiCapability;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.entity.WtpVersion;
import com.volvo.cos.conrepo.bulk_sync.v1.VehicleInfo;
import com.volvo.cos.conrepo.bulk_sync.v1.VehicleInfoOrBuilder;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import com.volvo.vc.conrepo.api.v2.DeviceDetailedEntry;
import com.volvo.vc.conrepo.api.v2.DeviceSim;
import com.volvo.vc.conrepo.api.v2.WtpProtocolVersion;
import org.junit.jupiter.api.Test;

/** Test utility methods in {@link DeviceConvertUtil} */
public class DeviceConvertUtilTest {

  public static final Imsi IMSI = Imsi.ofLong(123456789);
  public static final Ipv4Address IPV4_ADDRESS = Ipv4Address.ofString("***********");
  public static final Ipv4Address VPN_IP_ADDRESS = Ipv4Address.ofString("***********");
  public static final int IPV4_PORT = 8090;
  public static final String MOBILE_NETWORK_OPERATOR = "telenor";
  public static final Msisdn MSISDN = Msisdn.ofString("+461234567891");
  private static final ObsAlias OBS_ALIAS = ObsAlias.ofLong(2000000000L);
  private static final Vpi VPI = Vpi.ofString("********************************");
  private static final String SATELLITE_ID = "01813088SKYC21D";

  /**
   * Test method for {@link
   * DeviceConvertUtil#vehicleDeviceToVehicleDeviceInfo(VehicleInfoOrBuilder)}
   */
  @Test
  void testVehicleDeviceToVehicleDeviceInfo() {
    VehicleInfoOrBuilder vehicleInfo =
        VehicleInfo.newBuilder()
            .setIpV4Address(IPV4_ADDRESS.toInt())
            .setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR)
            .setMsisdn(MSISDN.toString())
            .setObsAlias(OBS_ALIAS.toLong())
            .setPort(IPV4_PORT)
            .setSatelliteId(SATELLITE_ID)
            .setVpi(ByteString.copyFrom(VPI.toBytes()))
            .setWtpVersionValue(1)
            .build();

    VehicleDeviceInfo vehicleDeviceInfo =
        DeviceConvertUtil.vehicleDeviceToVehicleDeviceInfo(vehicleInfo);
    assertThat(vehicleDeviceInfo)
        .isNotNull()
        .describedAs("VehicleDeviceInfo")
        .hasFieldOrPropertyWithValue("vpi", VPI.toString())
        .hasFieldOrPropertyWithValue("obsAlias", OBS_ALIAS.toLong())
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("ipv4Address", IPV4_ADDRESS.toString())
        .hasFieldOrPropertyWithValue("ipv4Port", IPV4_PORT)
        .hasFieldOrPropertyWithValue("mobileNetworkOperator", MOBILE_NETWORK_OPERATOR)
        .hasFieldOrPropertyWithValue("msisdn", MSISDN.toString())
        .hasFieldOrPropertyWithValue("satelliteId", SATELLITE_ID);
  }

  /** Test method for {@link DeviceConvertUtil#toVehicleDeviceInfoOfWecuUpdate(WecuAsset)} */
  @Test
  void testtoVehicleDeviceInfoOfWecuUpdate() {

    ConnectivityCapabilities connectivityCapabilities = new ConnectivityCapabilities();
    CellularCapability cellularCapability = new CellularCapability();
    CellularIPCapability cellularIPCapability = new CellularIPCapability();
    CellularIPPrivateCapability cellularIPPrivateCapability = new CellularIPPrivateCapability();
    cellularIPPrivateCapability.setIpAddress(IPV4_ADDRESS.toString());
    cellularIPCapability.setPrivate(cellularIPPrivateCapability);

    CellularSMSCapability cellularSMSCapability = new CellularSMSCapability();
    cellularSMSCapability.setMobileNetworkOperator(MOBILE_NETWORK_OPERATOR);
    cellularSMSCapability.setMsisdn(MSISDN.toString());

    cellularCapability.setIp(cellularIPCapability);
    cellularCapability.setSms(cellularSMSCapability);

    SatelliteCapability satelliteCapability = new SatelliteCapability();
    satelliteCapability.setSatelliteId(SATELLITE_ID);

    WifiCapability wifiCapability = new WifiCapability();
    wifiCapability.setVpnIpAddress(VPN_IP_ADDRESS.toString());

    connectivityCapabilities.setCellular(cellularCapability);
    connectivityCapabilities.setSatellite(satelliteCapability);
    connectivityCapabilities.setWifi(wifiCapability);

    WecuAsset wecuAsset = new WecuAsset();
    wecuAsset.setVpi(VPI.toString());
    wecuAsset.setConnectivityCapabilities(connectivityCapabilities);

    VehicleDeviceInfo vehicleDeviceInfo =
        DeviceConvertUtil.toVehicleDeviceInfoOfWecuUpdate(wecuAsset);

    assertThat(vehicleDeviceInfo)
        .isNotNull()
        .describedAs("VehicleDeviceInfo")
        .hasFieldOrPropertyWithValue("vpi", VPI.toString())
        .hasFieldOrPropertyWithValue("ipv4Address", IPV4_ADDRESS.toString())
        .hasFieldOrPropertyWithValue("mobileNetworkOperator", MOBILE_NETWORK_OPERATOR)
        .hasFieldOrPropertyWithValue("msisdn", MSISDN.toString())
        .hasFieldOrPropertyWithValue("satelliteId", SATELLITE_ID)
        .hasFieldOrPropertyWithValue("vpnIpv4Address", VPN_IP_ADDRESS.toString())
        .hasFieldOrPropertyWithValue("telematicUnit", TelematicUnit.WECU);
  }

  /**
   * Test method for {@link DeviceConvertUtil#toVehicleDeviceInfoToUpdate(DeviceDetailedEntry)}
   * returns an instance of {@link VehicleDeviceInfo} with satellite id and sim details for
   * satellite and sms activated device.
   */
  @Test
  void testToVehicleDeviceInfoToUpdate_smsAndSatActivation() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setObsAlias(OBS_ALIAS.toLong());
    deviceDetailedEntry.setSimEntry(getDeviceSim());
    deviceDetailedEntry.setVehiclePlatformId(VPI.toString());
    deviceDetailedEntry.setSatelliteId(SATELLITE_ID);
    deviceDetailedEntry.setWtpProtocolVersion(WtpProtocolVersion.VERSION_2);

    VehicleDeviceInfo vehicleDeviceInfo =
        DeviceConvertUtil.toVehicleDeviceInfoToUpdate(deviceDetailedEntry);
    assertThat(vehicleDeviceInfo)
        .isNotNull()
        .describedAs("VehicleDeviceInfo")
        .hasFieldOrPropertyWithValue("vpi", VPI.toString())
        .hasFieldOrPropertyWithValue("obsAlias", OBS_ALIAS.toLong())
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("ipv4Address", IPV4_ADDRESS.toString())
        .hasFieldOrPropertyWithValue("ipv4Port", IPV4_PORT)
        .hasFieldOrPropertyWithValue("mobileNetworkOperator", MOBILE_NETWORK_OPERATOR)
        .hasFieldOrPropertyWithValue("msisdn", MSISDN.toString())
        .hasFieldOrPropertyWithValue("satelliteId", SATELLITE_ID);
  }

  /**
   * Test method for {@link DeviceConvertUtil#toVehicleDeviceInfoToUpdate(DeviceDetailedEntry)}
   * returns an instance of {@link VehicleDeviceInfo} with only sim details for sms activated
   * device.
   */
  @Test
  void testToVehicleDeviceInfoToUpdate_smsActivation() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setObsAlias(OBS_ALIAS.toLong());
    deviceDetailedEntry.setSimEntry(getDeviceSim());
    deviceDetailedEntry.setVehiclePlatformId(VPI.toString());
    deviceDetailedEntry.setWtpProtocolVersion(WtpProtocolVersion.VERSION_2);

    VehicleDeviceInfo vehicleDeviceInfo =
        DeviceConvertUtil.toVehicleDeviceInfoToUpdate(deviceDetailedEntry);
    assertThat(vehicleDeviceInfo)
        .isNotNull()
        .describedAs("VehicleDeviceInfo")
        .hasFieldOrPropertyWithValue("vpi", VPI.toString())
        .hasFieldOrPropertyWithValue("obsAlias", OBS_ALIAS.toLong())
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("ipv4Address", IPV4_ADDRESS.toString())
        .hasFieldOrPropertyWithValue("ipv4Port", IPV4_PORT)
        .hasFieldOrPropertyWithValue("mobileNetworkOperator", MOBILE_NETWORK_OPERATOR)
        .hasFieldOrPropertyWithValue("msisdn", MSISDN.toString())
        .hasFieldOrPropertyWithValue("satelliteId", null);
  }

  /**
   * Test method for {@link DeviceConvertUtil#toVehicleDeviceInfoToUpdate(DeviceDetailedEntry)}
   * returns an instance of {@link VehicleDeviceInfo} with only satellite id for satellite activated
   * device.
   */
  @Test
  void testToVehicleDeviceInfoToUpdate_satActivation() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setObsAlias(OBS_ALIAS.toLong());
    deviceDetailedEntry.setVehiclePlatformId(VPI.toString());
    deviceDetailedEntry.setWtpProtocolVersion(WtpProtocolVersion.VERSION_2);
    deviceDetailedEntry.setSatelliteId(SATELLITE_ID);

    VehicleDeviceInfo vehicleDeviceInfo =
        DeviceConvertUtil.toVehicleDeviceInfoToUpdate(deviceDetailedEntry);
    assertThat(vehicleDeviceInfo)
        .isNotNull()
        .describedAs("VehicleDeviceInfo")
        .hasFieldOrPropertyWithValue("vpi", VPI.toString())
        .hasFieldOrPropertyWithValue("obsAlias", OBS_ALIAS.toLong())
        .hasFieldOrPropertyWithValue("wtpVersion", WtpVersion.VERSION_2)
        .hasFieldOrPropertyWithValue("ipv4Address", null)
        .hasFieldOrPropertyWithValue("ipv4Port", 0)
        .hasFieldOrPropertyWithValue("mobileNetworkOperator", null)
        .hasFieldOrPropertyWithValue("msisdn", null)
        .hasFieldOrPropertyWithValue("satelliteId", SATELLITE_ID);
  }

  /**
   * Test method for {@link DeviceConvertUtil#toVehicleDeviceInfoToDelete(DeviceDetailedEntry)}
   * returns an instance of {@link VehicleDeviceInfo} with only vpi and obsAlias attributes.
   */
  @Test
  void testToVehicleDeviceInfoToDelete_deactivation() {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();
    deviceDetailedEntry.setObsAlias(OBS_ALIAS.toLong());
    deviceDetailedEntry.setVehiclePlatformId(VPI.toString());

    VehicleDeviceInfo vehicleDeviceInfo =
        DeviceConvertUtil.toVehicleDeviceInfoToDelete(deviceDetailedEntry);
    assertThat(vehicleDeviceInfo)
        .isNotNull()
        .describedAs("VehicleDeviceInfo")
        .hasFieldOrPropertyWithValue("vpi", VPI.toString())
        .hasFieldOrPropertyWithValue("obsAlias", OBS_ALIAS.toLong())
        .hasFieldOrPropertyWithValue("wtpVersion", null)
        .hasFieldOrPropertyWithValue("ipv4Address", null)
        .hasFieldOrPropertyWithValue("ipv4Port", 0)
        .hasFieldOrPropertyWithValue("mobileNetworkOperator", null)
        .hasFieldOrPropertyWithValue("msisdn", null)
        .hasFieldOrPropertyWithValue("satelliteId", null);
  }

  private static DeviceSim getDeviceSim() {
    DeviceSim deviceSim = new DeviceSim();
    deviceSim.setImsi(IMSI.toString());
    deviceSim.setIp(IPV4_ADDRESS.toString());
    deviceSim.setMsisdn(MSISDN.toString());
    deviceSim.setOperator(MOBILE_NETWORK_OPERATOR.toString());
    deviceSim.setPort(IPV4_PORT);
    return deviceSim;
  }
}
