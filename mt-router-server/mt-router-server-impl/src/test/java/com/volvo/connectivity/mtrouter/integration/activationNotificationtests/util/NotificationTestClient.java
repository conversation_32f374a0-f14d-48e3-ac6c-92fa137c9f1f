package com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util;

import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.vc.conrepo.api.v2.*;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public final class NotificationTestClient {

  public NotificationTestClient() {
    throw new IllegalStateException();
  }

  /***
   * Method to publish the secureActivationMessage using the subscription repository api
   * @param secureActivationNotifyEventMessage an instance of {@link SecureActivationNotifyEventMessage}
   * @param activationNotificationPublisher an instance of {@link MessagePublisher}&lt;{@link SecureActivationNotifyEventMessage}&gt;
   */
  private static void publishNotifyEventMessage(
      SecureActivationNotifyEventMessage secureActivationNotifyEventMessage,
      MessagePublisher<SecureActivationNotifyEventMessage> activationNotificationPublisher) {
    activationNotificationPublisher
        .newMessage()
        .correlationId(SecurityTestUtil.REFERENCE_ID)
        .publish(secureActivationNotifyEventMessage)
        .thenAccept(
            numberOfPublishedMessages -> {
              log.info("{} was published to sub repo..", numberOfPublishedMessages);
            })
        .exceptionally(
            throwable -> {
              throw new RuntimeException(
                  "Integration Testing-Failed to publish vehicle activation notification to mt-router-server. ",
                  throwable);
            });
  }

  /***
   * Manage the creation and sending of a secured activation notification message to the mtrouter
   * as being published by conrepo2 in the real scenario
   * @param activationNotifyEventMessage an instance of {@link ActivationNotifyEventMessage}
   * @param activationNotificationPublisher an instance of {@link MessagePublisher}&lt;{@link SecureActivationNotifyEventMessage}&gt;
   */
  public static void sendSecureActivationNotificationToMtRouter(
      ActivationNotifyEventMessage activationNotifyEventMessage,
      MessagePublisher<SecureActivationNotifyEventMessage> activationNotificationPublisher) {
    SecureActivationNotifyEventMessage secureActivationNotifyEventMessage =
        SecurityTestUtil.encryptActivationNotifyEventMessage(activationNotifyEventMessage);

    Either<IllegalArgumentException, SecureActivationNotifyEventMessage> either =
        convertSecureActivationNotifyEventMessage(secureActivationNotifyEventMessage);
    if (either.isLeft()) {
      throw new IllegalArgumentException();
    }
    publishNotifyEventMessage(either.getRight(), activationNotificationPublisher);
  }

  /***
   * This method converts the model/entity SecureActivationNotifyEventMessageEntity defined in mtrouter
   * to SecureActivationNotifyEventMessage object of Conrepo2 library
   * @param secureActivationNotifyEventMessage
   * @return
   */
  private static Either<IllegalArgumentException, SecureActivationNotifyEventMessage>
      convertSecureActivationNotifyEventMessage(
          SecureActivationNotifyEventMessage secureActivationNotifyEventMessage) {
    try {
      SecureActivationNotifyEventMessage secureActivationNotifyEventMessageConverted =
          new SecureActivationNotifyEventMessage();

      secureActivationNotifyEventMessageConverted.setEncryptedAesKey(
          secureActivationNotifyEventMessage.getEncryptedAesKey());
      secureActivationNotifyEventMessageConverted.setSignature(
          secureActivationNotifyEventMessage.getSignature());
      secureActivationNotifyEventMessageConverted.setEncryptedPayload(
          secureActivationNotifyEventMessage.getEncryptedPayload());
      secureActivationNotifyEventMessageConverted.setPrivateKeyId(
          secureActivationNotifyEventMessage.getPrivateKeyId());
      secureActivationNotifyEventMessageConverted.setPublicKeyId(
          secureActivationNotifyEventMessage.getPublicKeyId());
      return Either.right(secureActivationNotifyEventMessageConverted);
    } catch (IllegalArgumentException e) {
      return Either.left(e);
    }
  }

  /***
   * Method to publish {@link ActivationNotifyEventMessage} for vehicle deactivation.
   * @param deactivationNotifyEventMessage an instance of {@link ActivationNotifyEventMessage}
   * @param deactivationNotificationPublisher an instance of {@link MessagePublisher}&lt;{@link ActivationNotifyEventMessage}&gt;
   */
  public static void publishDeactivationNotifyEventMessage(
      ActivationNotifyEventMessage deactivationNotifyEventMessage,
      MessagePublisher<ActivationNotifyEventMessage> deactivationNotificationPublisher) {
    deactivationNotificationPublisher
        .newMessage()
        .correlationId(SecurityTestUtil.REFERENCE_ID)
        .publish(deactivationNotifyEventMessage)
        .thenAccept(
            numberOfPublishedMessages -> {
              log.debug(
                  "{} deactivation notify message was published to sub repo",
                  numberOfPublishedMessages);
            })
        .exceptionally(
            throwable -> {
              throw new RuntimeException(
                  "Integration Testing-Failed to publish vehicle deactivation notification to mt-router-server",
                  throwable);
            });
  }
}
