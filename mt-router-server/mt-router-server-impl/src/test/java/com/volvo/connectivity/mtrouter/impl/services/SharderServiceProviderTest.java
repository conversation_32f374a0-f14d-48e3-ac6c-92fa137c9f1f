package com.volvo.connectivity.mtrouter.impl.services;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.volvo.connectivity.mtrouter.impl.util.TestUtils;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistrationBuilder;
import java.net.URI;
import org.apache.curator.test.TestingServer;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

class SharderServiceProviderTest {

  @Test
  void shardingWithServiceDiscoveryRegistrationsTest() throws Exception {
    try (TestingServer server = new TestingServer();
        ServiceDiscovery<InstanceDetails> serviceDiscovery =
            TestUtils.createRunnningServiceDiscovery(server.getConnectString());
        ServiceDiscoveryRegistration serviceDiscoveryRegistration1 =
            new ServiceDiscoveryRegistrationBuilder()
                .setServiceDiscovery(serviceDiscovery)
                .setServiceName(TestUtils.SERVICE_NAME)
                .setServiceUri(URI.create("ws://192.168.0.10:8081"))
                .build();
        ServiceDiscoveryRegistration serviceDiscoveryRegistration2 =
            new ServiceDiscoveryRegistrationBuilder()
                .setServiceDiscovery(serviceDiscovery)
                .setServiceName(TestUtils.SERVICE_NAME)
                .setServiceUri(URI.create("ws://192.168.0.20:8081"))
                .build()) {
      serviceDiscoveryRegistration1.open();
      serviceDiscoveryRegistration2.open();

      try (SharderServiceProvider sharderServiceProvider =
          new SharderServiceProvider(serviceDiscovery, TestUtils.SERVICE_NAME)) {

        assertEquals(
            "ws://192.168.0.10:8081",
            sharderServiceProvider.getServiceInstanceFromObject("object1").buildUriSpec());
        assertEquals(
            "ws://192.168.0.20:8081",
            sharderServiceProvider.getServiceInstanceFromObject("object2").buildUriSpec());

        serviceDiscoveryRegistration1.close();

        Thread.sleep(100);

        assertEquals(
            "ws://192.168.0.20:8081",
            sharderServiceProvider.getServiceInstanceFromObject("object1").buildUriSpec());
        assertEquals(
            "ws://192.168.0.20:8081",
            sharderServiceProvider.getServiceInstanceFromObject("object2").buildUriSpec());

        serviceDiscoveryRegistration2.close();
        Thread.sleep(100);

        Assertions.assertNull(sharderServiceProvider.getServiceInstanceFromObject("object1"));
        Assertions.assertNull(sharderServiceProvider.getServiceInstanceFromObject("object2"));
      }
    }
  }

  @Test
  void shardingWithoutServiceDiscoveryRegistrationsTest() throws Exception {
    try (TestingServer server = new TestingServer();
        ServiceDiscovery<InstanceDetails> serviceDiscovery =
            TestUtils.createRunnningServiceDiscovery(server.getConnectString())) {

      try (SharderServiceProvider sharderServiceProvider =
          new SharderServiceProvider(serviceDiscovery, TestUtils.SERVICE_NAME)) {

        Assertions.assertNull(sharderServiceProvider.getServiceInstanceFromObject("object"));
      }
    }
  }
}
