package com.volvo.connectivity.mtrouter.integration.util;

import static com.github.tomakehurst.wiremock.client.WireMock.*;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.google.protobuf.ByteString;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.asset.repository.events.model.CellularCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularIPCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularIPPrivateCapability;
import com.volvo.connectivity.asset.repository.events.model.CellularSMSCapability;
import com.volvo.connectivity.asset.repository.events.model.ConnectivityCapabilities;
import com.volvo.connectivity.asset.repository.events.model.SatelliteCapability;
import com.volvo.connectivity.asset.repository.events.model.WecuAsset;
import com.volvo.connectivity.asset.repository.events.model.WecuChangeNotifyEvent;
import com.volvo.connectivity.asset.repository.events.model.WifiCapability;
import com.volvo.connectivity.cms.events.model.TgwNotifyEvent;
import com.volvo.connectivity.mtrouter.impl.db.entity.*;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.TisMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.connectivity.proto.VpnEvent;
import com.volvo.tisp.framework.context.TispContext;
import com.volvo.tisp.framework.http.TispHttpHeaders;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.ServiceName;
import com.volvo.tisp.tce.discovery.ServiceNameBuilder;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistrationBuilder;
import com.wirelesscar.config.Config;
import com.wirelesscar.config.ConfigFactory;
import java.net.URI;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.curator.x.discovery.ServiceDiscovery;

@Slf4j
public final class IntegrationTestHelper {
  private static final String MT_REST_PATH = "/mtMessage";
  private static final String TIS_ROUTER_REST_PATH = "/in";

  public static AutoCloseable createServiceDiscoveryRegistrationWrapper(
      final int port, final ServiceDiscovery<InstanceDetails> serviceDiscovery) throws Exception {
    List<ServiceDiscoveryRegistration> registrationList =
        List.of(
            createServiceDiscoveryRegistrationMt(
                port,
                serviceDiscovery,
                "http://*********:",
                ServiceConstant.VWTP_INITIATOR_SERVICE),
            createServiceDiscoveryRegistrationMt(
                port,
                serviceDiscovery,
                "http://192.0.0.2:",
                ServiceConstant.VWTP_INITIATOR_SERVICE),
            createServiceDiscoveryRegistrationMt(
                port,
                serviceDiscovery,
                "http://192.0.0.4:",
                ServiceConstant.VWTP_INITIATOR_SERVICE),
            createServiceDiscoveryRegistrationMt(
                port,
                serviceDiscovery,
                "http://192.0.0.6:",
                ServiceConstant.VWTP_INITIATOR_SERVICE),
            createServiceDiscoveryRegistrationMt(
                port,
                serviceDiscovery,
                "http://**********:",
                ServiceConstant.VWTP_INITIATOR_SERVICE));
    registrationList.forEach(ServiceDiscoveryRegistration::open);
    return () -> registrationList.forEach(ServiceDiscoveryRegistration::close);
  }

  private static ServiceDiscoveryRegistration createServiceDiscoveryRegistrationMt(
      final int port,
      final ServiceDiscovery<InstanceDetails> serviceDiscovery,
      final String host,
      final String name) {
    return new ServiceDiscoveryRegistrationBuilder()
        .setServiceDiscovery(serviceDiscovery)
        .setServiceName(createServiceName(name))
        .setServiceUri(URI.create(host + port + IntegrationTestHelper.MT_REST_PATH))
        .build();
  }

  public static ServiceName createServiceName(String name) {
    Config config = ConfigFactory.getConfig();
    return new ServiceNameBuilder()
        .setEnvironment(config.getEnvironmentId())
        .setName(name)
        .setSite(config.getSite())
        .setSolution(config.getSolution())
        .build();
  }

  public static ServiceDiscoveryRegistration createServiceDiscoveryRegistor(
      final ServiceDiscovery<InstanceDetails> serviceDiscovery, final int port) {
    Config config = ConfigFactory.getConfig();
    final ServiceName serviceName =
        new ServiceNameBuilder()
            .setName(ServiceConstant.VWTP_INITIATOR_SERVICE)
            .setEnvironment(config.getEnvironmentId())
            .setSite(config.getSite())
            .setSolution(config.getSolution())
            .build();
    return new ServiceDiscoveryRegistrationBuilder()
        .setServiceDiscovery(serviceDiscovery)
        .setServiceName(serviceName)
        .setServiceUri(URI.create("http://127.0.0.1:" + port + IntegrationTestHelper.MT_REST_PATH))
        .build();
  }

  private IntegrationTestHelper() {
    throw new IllegalArgumentException();
  }

  public static ServiceDiscoveryRegistration createServiceRegistration(
      ServiceDiscovery<InstanceDetails> serviceDiscovery, String service) {
    String host = "http://" + System.getProperty("container.host") + ":";
    return new ServiceDiscoveryRegistrationBuilder()
        .setServiceDiscovery(serviceDiscovery)
        .setServiceName(IntegrationTestHelper.createServiceName(service))
        .setServiceUri(URI.create(host + 3080 + IntegrationTestHelper.MT_REST_PATH))
        .build();
  }

  public static void stubWireMockForService() {
    WireMock.stubFor(
        post(urlPathEqualTo(IntegrationTestHelper.MT_REST_PATH))
            .withHeader(
                TispHttpHeaders.TRACKING_ID.key(), equalTo(TispContext.current().tid().toString()))
            .withHeader("Content-Type", equalTo("application/x-protobuf"))
            .willReturn(aResponse().withStatus(200)));
  }

  public static void stubWireMockForTisRouterApi() {
    WireMock.stubFor(
        post(urlPathEqualTo(TIS_ROUTER_REST_PATH))
            .withHeader("Content-Type", matching("application/x-protobuf.*"))
            .withHeader("Accept", matching("application/x-protobuf.*"))
            .willReturn(aResponse().withStatus(200)));
  }

  public static VehicleDeviceInfo createVehicleDeviceInfo(String vpi, TelematicUnit telematicUnit) {
    return VehicleDeviceInfo.builder()
        .setObsAlias(123l)
        .setVpi(vpi)
        .setWtpVersion(WtpVersion.VERSION_2)
        .setIpv4Address("*********")
        .setIpv4Port(8080)
        .setMobileNetworkOperator("telenor")
        .setMsisdn("+8947184345")
        .setSatelliteId("SOFTCAR0000001")
        .setVpnIpv4Address("*********")
        .setTelematicUnit(telematicUnit)
        .build();
  }

  public static VehicleDeviceInfo createVehicleDeviceInfoForWecu(String vpi) {
    return VehicleDeviceInfo.builder()
        .setVpi(vpi)
        .setIpv4Address("*********")
        .setMobileNetworkOperator("telenor")
        .setMsisdn("+8947184345")
        .setSatelliteId("SOFTCAR0000001")
        .setVpnIpv4Address("*********")
        .setTelematicUnit(TelematicUnit.WECU)
        .build();
  }

  public static MtMessage createMtMessage(final Transport transport, String vpi) {
    return MtMessage.newBuilder()
        .setVpi(vpi)
        .setMessageId("A4635A6C40B45E235353D3F2F28CD339")
        .setPayload(ByteString.copyFromUtf8("Test Message"))
        .setTransport(transport)
        .build();
  }

  public static void publishVpnEventMessage(
      VpnEvent vpnEvent, MessagePublisher<VpnEvent> vpnEventPublisher) {
    vpnEventPublisher
        .newMessage()
        .publish(vpnEvent)
        .thenAccept(
            numberOfPublishedMessages -> {
              log.info("{} VpnEvent message was published", numberOfPublishedMessages);
            })
        .exceptionally(
            throwable -> {
              throw new RuntimeException(
                  "Integration test, failed to publish VpnEvet message", throwable);
            });
  }

  public static void publishTgwNotifyEventMessage(
      TgwNotifyEvent tgwNotifyEvent, MessagePublisher<TgwNotifyEvent> tgwNotifyEventPublisher) {
    tgwNotifyEventPublisher
        .newMessage()
        .header("jms_correlationId", "correlationId")
        .publish(tgwNotifyEvent)
        .thenAccept(
            numberOfPublishedMessages -> {
              log.info("{} TgwNotifyEvent message was published", numberOfPublishedMessages);
            })
        .exceptionally(
            throwable -> {
              throw new RuntimeException(
                  "Integration test, failed to publish TgwNotifyEvent message", throwable);
            });
  }

  public static void verifyTisRouterApiInvocation(TisMessage tisMessage) {
    verify(
        postRequestedFor(urlEqualTo(TIS_ROUTER_REST_PATH))
            .withHeader("Content-Type", matching("application/x-protobuf.*"))
            .withHeader("Accept", matching("application/x-protobuf.*"))
            .withRequestBody(binaryEqualTo(tisMessage.toByteArray())));
  }

  public static WecuChangeNotifyEvent buildWecuChangeNotifyEvent(String vpi, String vpnIpAddress) {

    ConnectivityCapabilities connectivityCapabilities = new ConnectivityCapabilities();
    CellularCapability cellularCapability = new CellularCapability();
    CellularIPCapability cellularIPCapability = new CellularIPCapability();
    CellularIPPrivateCapability cellularIPPrivateCapability = new CellularIPPrivateCapability();
    cellularIPPrivateCapability.setIpAddress(DataUtil.IPV4_ADDRESS_1.toString());
    cellularIPCapability.setPrivate(cellularIPPrivateCapability);

    CellularSMSCapability cellularSMSCapability = new CellularSMSCapability();
    cellularSMSCapability.setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1);
    cellularSMSCapability.setMsisdn(DataUtil.MSISDN_1.toString());

    cellularCapability.setIp(cellularIPCapability);
    cellularCapability.setSms(cellularSMSCapability);

    SatelliteCapability satelliteCapability = new SatelliteCapability();
    satelliteCapability.setSatelliteId(DataUtil.SATELLITE_ID_1);

    WifiCapability wifiCapability = new WifiCapability();
    wifiCapability.setVpnIpAddress(vpnIpAddress);

    connectivityCapabilities.setCellular(cellularCapability);
    connectivityCapabilities.setSatellite(satelliteCapability);
    connectivityCapabilities.setWifi(wifiCapability);

    WecuAsset wecuAsset = new WecuAsset();
    wecuAsset.setVpi(vpi);
    wecuAsset.setConnectivityCapabilities(connectivityCapabilities);

    WecuChangeNotifyEvent wecuChangeNotifyEvent = new WecuChangeNotifyEvent();
    wecuChangeNotifyEvent.setAsset(wecuAsset);
    return wecuChangeNotifyEvent;
  }
}
