package com.volvo.connectivity.mtrouter.integration.util;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.connectivity.mtrouter.impl.conf.AppConfig;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.TestUtilSubscriptionRepo;
import com.volvo.tisp.framework.web.client.TispHeadersExchangeFilter;
import io.netty.handler.logging.LogLevel;
import io.opentelemetry.api.GlobalOpenTelemetry;
import java.io.File;
import java.io.IOException;
import java.time.Duration;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicReference;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.platform.launcher.LauncherSession;
import org.junit.platform.launcher.LauncherSessionListener;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.http.client.reactive.ReactorClientHttpConnector;
import org.springframework.test.web.reactive.server.WebTestClient;
import org.testcontainers.containers.ContainerState;
import org.testcontainers.containers.DockerComposeContainer;
import org.testcontainers.containers.output.Slf4jLogConsumer;
import org.testcontainers.containers.output.WaitingConsumer;
import org.testcontainers.containers.wait.strategy.Wait;
import reactor.blockhound.BlockHound;
import reactor.netty.http.client.HttpClient;
import reactor.netty.transport.logging.AdvancedByteBufFormat;

@Slf4j
@SpringBootTest(
    classes = AppConfig.class,
    properties = "server.port=" + TestcontainersBase.SERVER_PORT,
    webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class TestcontainersBase implements LauncherSessionListener, ApplicationContextAware {
  protected static final WebTestClient WEB_TEST_CLIENT;
  protected static DockerComposeContainer<?> DOCKER_COMPOSE;
  protected static final String CONTAINER_HOST;
  public static final String SERVER_PORT = "47910";

  private static final AtomicReference<ConfigurableApplicationContext> contextReference =
      new AtomicReference<>();

  static {
    DOCKER_COMPOSE =
        new DockerComposeContainer<>(new File("src/test/resources/docker-compose.yaml"))
            .withLogConsumer("artemis", new WaitingConsumer())
            .withLogConsumer("influxdb", new WaitingConsumer())
            .withLogConsumer("wiremock", new Slf4jLogConsumer(LoggerFactory.getLogger("wiremock")))
            .withLogConsumer("zoo", new WaitingConsumer())
            .withLogConsumer("mongo", new WaitingConsumer())
            .waitingFor("artemis", Wait.forLogMessage(".*Console available at.*", 1))
            .waitingFor("influxdb", Wait.forLogMessage(".*Listening for signals.*", 1))
            .waitingFor("wiremock", Wait.forLogMessage(".*verbose.*", 1))
            .waitingFor("zoo", Wait.forLogMessage(".*Started AdminServer on address.*", 1))
            .waitingFor("mongo", Wait.forListeningPort().withStartupTimeout(Duration.ofSeconds(30)))
            .withLocalCompose(true);

    DOCKER_COMPOSE.start();

    String containerHost = DOCKER_COMPOSE.getServiceHost(null, null);
    CONTAINER_HOST = Objects.equals(containerHost, "localhost") ? "127.0.0.1" : containerHost;
    executeInContainerToRunReplicaSet(CONTAINER_HOST);
    log.info("Container Host: {}", CONTAINER_HOST);
    System.setProperty("container.host", CONTAINER_HOST);
    System.setProperty("VGTZOOKEEPER", CONTAINER_HOST + ":2181");

    WEB_TEST_CLIENT =
        WebTestClient.bindToServer(
                new ReactorClientHttpConnector(
                    HttpClient.create()
                        .wiretap("web.test.client", LogLevel.INFO, AdvancedByteBufFormat.TEXTUAL)))
            .filter(new TispHeadersExchangeFilter())
            .baseUrl("http://127.0.0.1:" + SERVER_PORT)
            .build();

    WEB_TEST_CLIENT
        .post()
        .uri(
            "http://{host}:9086/query?q=CREATE DATABASE \"connectivity_services\" WITH NAME \"30days\"",
            CONTAINER_HOST)
        .exchange()
        .expectStatus()
        .is2xxSuccessful();

    System.setProperty("MOCK_HOST", containerHost);
    System.setProperty("VGTCOMPVERSION", "0-SNAPSHOT");
    WireMock.configureFor(containerHost, 3080);
  }

  private static void executeInContainerToRunReplicaSet(String containerHost) {

    Optional<ContainerState> mongoDBContainer = DOCKER_COMPOSE.getContainerByServiceName("mongo");
    try {
      mongoDBContainer
          .get()
          .execInContainer(
              "mongosh",
              "--eval",
              "rs.initiate({_id: 'rs0', members: [{_id: 0, host: '"
                  + containerHost
                  + ":27017'}]})");
    } catch (IOException | InterruptedException e) {
      throw new RuntimeException(e);
    }
  }

  @BeforeEach
  public void beforeEachBase() {
    WireMock.reset();
    TestUtilSubscriptionRepo.stubSubrepo();
  }

  @BeforeAll
  public static void globalOpenTelemetryReset() throws InterruptedException {
    BlockHound.install(
        builder ->
            builder
                .allowBlockingCallsInside(
                    "org.springframework.boot.validation.MessageSourceMessageInterpolator",
                    "interpolate")
                .allowBlockingCallsInside("com.mongodb.internal.Locks", "withInterruptibleLock")
                .allowBlockingCallsInside("com.mongodb.internal.Locks", "withUnfairLock")
                .allowBlockingCallsInside("com.mongodb.internal.Locks", "withLock")
                .allowBlockingCallsInside("java.security.SecureRandom", "nextBytes"));
    GlobalOpenTelemetry.resetForTest();
  }

  @Override
  public void setApplicationContext(ApplicationContext context) throws BeansException {
    if (context instanceof ConfigurableApplicationContext configurableContext) {
      TestcontainersBase.contextReference.set(configurableContext);
    }
  }

  @Override
  public void launcherSessionClosed(LauncherSession session) {
    ConfigurableApplicationContext context = TestcontainersBase.contextReference.get();
    if (context != null && context.isActive()) {
      log.info("Stopping Spring Boot");
      context.close();
    }
    log.info("Stopping Testcontainers");
    DOCKER_COMPOSE.stop();
  }
}
