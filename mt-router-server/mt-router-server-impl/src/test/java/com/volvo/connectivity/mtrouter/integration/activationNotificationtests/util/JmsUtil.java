package com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util;

import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.NotifyResponseMessage;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import jakarta.jms.JMSException;
import jakarta.jms.Message;
import jakarta.jms.TextMessage;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Unmarshaller;
import java.io.StringReader;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.springframework.jms.core.JmsTemplate;

@Slf4j
public class JmsUtil {

  public static void verifyResponseMessage(
      NotifyResponseMessage notifyResponseMessage,
      ResponseStatus responseStatus,
      ActivationMessageType responseType) {
    log.info("response status is {}", notifyResponseMessage.getResponseStatus());
    Assertions.assertEquals(responseType, notifyResponseMessage.getMessageType());
    Assertions.assertEquals(SecurityTestUtil.REFERENCE_ID, notifyResponseMessage.getReferenceId());
    Assertions.assertEquals(responseStatus, notifyResponseMessage.getResponseStatus());
  }

  public static void verifyIfResponseMessageReceivedInMtRouter(
      JmsTemplate jmsTemplate, ResponseStatus responseStatus, ActivationMessageType responseType)
      throws JAXBException, JMSException {
    Message receivedMessage = jmsTemplate.receive("NOTIFICATION.REPLY");
    if (receivedMessage instanceof TextMessage) {
      TextMessage textMessage = (TextMessage) receivedMessage;
      try {
        String xmlContent = textMessage.getText();
        JAXBContext jaxbContext = JAXBContext.newInstance(NotifyResponseMessage.class);
        Unmarshaller unmarshaller = jaxbContext.createUnmarshaller();
        NotifyResponseMessage notifyResponseMessage =
            (NotifyResponseMessage) unmarshaller.unmarshal(new StringReader(xmlContent));
        verifyResponseMessage(notifyResponseMessage, responseStatus, responseType);
      } catch (JAXBException | JMSException e) {
        log.error(
            "An error occurred while trying to convert "
                + "the received JMS message to NotifyResponseMessage: {}",
            e.getMessage());
        throw e;
      }
    }
  }
}
