package com.volvo.connectivity.mtrouter.integration.activationNotificationtests;

import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.entity.WtpVersion;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.JmsUtil;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.NotificationTestClient;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.SecurityTestUtil;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.vc.conrepo.MessageTypes;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEvent;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.ChangeStatus;
import com.volvo.vc.conrepo.api.v2.DeviceDetailedEntry;
import com.volvo.vc.conrepo.api.v2.DeviceSim;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import com.volvo.vc.conrepo.api.v2.SecureActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.State;
import com.volvo.vc.conrepo.api.v2.WtpProtocolVersion;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;

@Slf4j
@ExtendWith(TispContextExtension.class)
public class ActivationNotificationListenerIT extends TestcontainersBase {

  @Autowired private VehicleDeviceInfoRepository vehicleDeviceInfoRepository;
  @Autowired private JmsTemplate jmsTemplate;
  @Autowired private MessagePublisher.Builder messagePublisherBuilder;
  private MessagePublisher<SecureActivationNotifyEventMessage> activationNotificationPublisher;

  @BeforeEach
  public void setup() {
    activationNotificationPublisher =
        messagePublisherBuilder
            .messageType(
                MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE,
                SecureActivationNotifyEventMessage.class)
            .version(MessageTypes.VERSION_2_0)
            .build();
  }

  @AfterEach
  public void clear() {
    vehicleDeviceInfoRepository.deleteAll().block();
  }

  /**
   * Test conrepo2 secure activation notification where new device is activated for SMS and
   * satellite communication.
   *
   * @throws Exception
   */
  @Test
  void activationNotificationTest_activateNewDeviceForSmsAndSat() throws Exception {
    DeviceSim deviceSim = getDeviceSim();
    DeviceDetailedEntry deviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_1.toString(),
            DataUtil.OBS_ALIAS_1.toLong(),
            deviceSim,
            DataUtil.SATELLITE_ID_1,
            State.ACTIVATED);
    VehicleDeviceInfo expectdVehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_1.toLong())
            .setWtpVersion(WtpVersion.VERSION_2)
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_1)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setSatelliteId(DataUtil.SATELLITE_ID_1)
            .setTelematicUnit(TelematicUnit.TGW)
            .build();

    ActivationNotifyEvent activationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(ChangeStatus.UPDATED, deviceDetailedEntry);
    ActivationNotifyEventMessage activationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.NOTIFY, List.of(activationNotifyEvent));

    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.NOTIFY);
    DataUtil.verifyIfVehiclePersistedInDB(vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);

    log.info("testing if the same vpi can be inserted again..");
    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.NOTIFY);
    DataUtil.verifyIfVehiclePersistedInDB(vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);
  }

  /**
   * Test conrepo2 secure activation notification where new device is activated for SMS
   * communication.
   *
   * @throws Exception
   */
  @Test
  void activationNotificationTest_activateNewDeviceForSmsOnly() throws Exception {
    DeviceSim deviceSim = getDeviceSim();
    DeviceDetailedEntry deviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_1.toString(),
            DataUtil.OBS_ALIAS_1.toLong(),
            deviceSim,
            null,
            State.ACTIVATED);
    VehicleDeviceInfo expectdVehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_1.toLong())
            .setWtpVersion(WtpVersion.VERSION_2)
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_1)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setSatelliteId(null)
            .setTelematicUnit(TelematicUnit.TGW)
            .build();

    ActivationNotifyEvent activationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(ChangeStatus.UPDATED, deviceDetailedEntry);
    ActivationNotifyEventMessage activationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.NOTIFY, List.of(activationNotifyEvent));

    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.NOTIFY);
    DataUtil.verifyIfVehiclePersistedInDB(vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);
  }

  /**
   * Test conrepo2 secure activation notification where new device is activated for SMS
   * communication.
   *
   * @throws Exception
   */
  @Test
  void activationNotificationTest_updateExistingDevice() throws Exception {
    DeviceSim deviceSim = getDeviceSim();
    DeviceDetailedEntry deviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_1.toString(),
            DataUtil.OBS_ALIAS_1.toLong(),
            deviceSim,
            DataUtil.SATELLITE_ID_1,
            State.ACTIVATED);
    VehicleDeviceInfo expectdVehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_1.toLong())
            .setWtpVersion(WtpVersion.VERSION_2)
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_1)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setSatelliteId(DataUtil.SATELLITE_ID_1)
            .setTelematicUnit(TelematicUnit.TGW)
            .build();

    ActivationNotifyEvent activationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(ChangeStatus.UPDATED, deviceDetailedEntry);
    ActivationNotifyEventMessage activationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.NOTIFY, List.of(activationNotifyEvent));

    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.NOTIFY);
    DataUtil.verifyIfVehiclePersistedInDB(vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);

    deviceSim =
        SecurityTestUtil.createDeviceSim(
            DataUtil.IMSI_2.toString(),
            DataUtil.IPV4_ADDRESS_2.toString(),
            DataUtil.MSISDN_2.toString(),
            DataUtil.MOBILE_NETWORK_OPERATOR_2,
            DataUtil.IPV4_PORT_2);
    deviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_1.toString(),
            DataUtil.OBS_ALIAS_2.toLong(),
            deviceSim,
            DataUtil.SATELLITE_ID_2,
            State.ACTIVATED);
    deviceDetailedEntry.setWtpProtocolVersion(WtpProtocolVersion.VERSION_1);
    expectdVehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_2.toLong())
            .setWtpVersion(WtpVersion.VERSION_1)
            .setIpv4Address(DataUtil.IPV4_ADDRESS_2.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_2)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_2)
            .setMsisdn(DataUtil.MSISDN_2.toString())
            .setSatelliteId(DataUtil.SATELLITE_ID_2)
            .setTelematicUnit(TelematicUnit.TGW)
            .build();

    activationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(ChangeStatus.UPDATED, deviceDetailedEntry);
    activationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.NOTIFY, List.of(activationNotifyEvent));

    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.NOTIFY);
    DataUtil.verifyIfVehiclePersistedInDB(vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);
  }

  /**
   * Test conrepo2 secure activation notification where new device is activated for SMS and SAT
   * while taking sim and satellite id of existing active device.
   *
   * @throws Exception
   */
  @Test
  void activationNotificationTest_swapSatIdAndSim() throws Exception {
    VehicleDeviceInfo vehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_1.toLong())
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_1)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setSatelliteId(DataUtil.SATELLITE_ID_1)
            .setTelematicUnit(TelematicUnit.TGW)
            .setWtpVersion(WtpVersion.VERSION_2)
            .build();
    vehicleDeviceInfoRepository.save(vehicleDeviceInfo).block();

    DeviceSim deviceSim = getDeviceSim();
    DeviceDetailedEntry activeDeviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_2.toString(),
            DataUtil.OBS_ALIAS_2.toLong(),
            deviceSim,
            DataUtil.SATELLITE_ID_1,
            State.ACTIVATED);
    DeviceDetailedEntry deactiveDeviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_1.toString(),
            DataUtil.OBS_ALIAS_1.toLong(),
            null,
            null,
            State.DEACTIVATED);

    // VPI_2 taking sim and satellite id from VPI_1
    VehicleDeviceInfo expectdVehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_2.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_2.toLong())
            .setWtpVersion(WtpVersion.VERSION_2)
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_1)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setSatelliteId(DataUtil.SATELLITE_ID_1)
            .setTelematicUnit(TelematicUnit.TGW)
            .build();

    ActivationNotifyEvent activeDeviceActivationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(
            ChangeStatus.UPDATED, activeDeviceDetailedEntry);
    ActivationNotifyEvent deactiveDeviceActivationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(
            ChangeStatus.DELETED, deactiveDeviceDetailedEntry);
    ActivationNotifyEventMessage activationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.NOTIFY,
            List.of(activeDeviceActivationNotifyEvent, deactiveDeviceActivationNotifyEvent));

    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.NOTIFY);
    DataUtil.verifyIfVehiclePersistedInDB(vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);
    DataUtil.verifyVehicleRemovedFromDb(vehicleDeviceInfoRepository, DataUtil.VPI_1.toString());
  }

  /**
   * Test conrepo2 secure activation notification where request comes without activation events.
   *
   * @throws Exception
   */
  @Test
  void activationNotificationTest_emptyActivationEvents() throws Exception {
    ActivationNotifyEventMessage activationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.NOTIFY, List.of());

    NotificationTestClient.sendSecureActivationNotificationToMtRouter(
        activationNotifyEventMessage, activationNotificationPublisher);
    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.FAILED, ActivationMessageType.NOTIFY);
  }

  private static DeviceSim getDeviceSim() {
    DeviceSim deviceSim =
        SecurityTestUtil.createDeviceSim(
            DataUtil.IMSI_1.toString(),
            DataUtil.IPV4_ADDRESS_1.toString(),
            DataUtil.MSISDN_1.toString(),
            DataUtil.MOBILE_NETWORK_OPERATOR_1,
            DataUtil.IPV4_PORT_1);
    return deviceSim;
  }
}
