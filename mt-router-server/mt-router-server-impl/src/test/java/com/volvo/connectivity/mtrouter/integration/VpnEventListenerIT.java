package com.volvo.connectivity.mtrouter.integration;

import com.google.protobuf.Timestamp;
import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.impl.jms.VpnEventListener;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.connectivity.proto.VpnEvent;
import com.volvo.connectivity.proto.VpnStatus;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import java.time.Duration;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ExtendWith(TispContextExtension.class)
class VpnEventListenerIT extends TestcontainersBase {

  @Autowired private VehicleDeviceInfoRepository vehicleDeviceInfoRepository;
  @Autowired private MessagePublisher.Builder messagePublisherBuilder;
  private MessagePublisher<VpnEvent> vpnEventPublisher;

  @BeforeEach
  public void setup() {
    vpnEventPublisher =
        messagePublisherBuilder
            .messageType(ServiceConstant.VPN_EVENT_MESSAGE_TYPE, VpnEvent.class)
            .version(VpnEventListener.VERSION_1_0)
            .build();
  }

  @AfterEach
  public void clear() {
    vehicleDeviceInfoRepository.deleteAll().block();
  }

  /** Test update vpn ip address when ip is allocated. */
  @Test
  void testAllocateVpnIp() {
    createInitialVehicleDeviceInfo(DataUtil.VPI_1.toString(), TelematicUnit.TGW);
    final VpnEvent vpnEvent =
        buildVpnEvent(
            VpnStatus.ACTIVATED, DataUtil.VPI_1.toString(), DataUtil.IPV4_ADDRESS_1.toString());
    IntegrationTestHelper.publishVpnEventMessage(vpnEvent, vpnEventPublisher);

    verifyUpdateVpnIpAddress(DataUtil.VPI_1.toString(), DataUtil.IPV4_ADDRESS_1.toString());
  }

  /** Test clear vpn ip address when ip is deallocated. */
  @Test
  void testDeallocateVpnIp() {
    createInitialVehicleDeviceInfo(DataUtil.VPI_1.toString(), TelematicUnit.TGW);
    final VpnEvent vpnEvent =
        buildVpnEvent(
            VpnStatus.DEACTIVATED, DataUtil.VPI_1.toString(), DataUtil.IPV4_ADDRESS_1.toString());
    IntegrationTestHelper.publishVpnEventMessage(vpnEvent, vpnEventPublisher);

    verifyUpdateVpnIpAddress(DataUtil.VPI_1.toString(), null);
  }

  VehicleDeviceInfo createInitialVehicleDeviceInfo(String vpi, TelematicUnit telematicUnit) {
    VehicleDeviceInfo vehicleDeviceInfo =
        IntegrationTestHelper.createVehicleDeviceInfo(vpi, telematicUnit);
    vehicleDeviceInfoRepository.insert(vehicleDeviceInfo).block();
    return vehicleDeviceInfo;
  }

  private VpnEvent buildVpnEvent(VpnStatus vpnStatus, String vpi, String vpnIp) {
    final Instant instant = Instant.now();
    final VpnEvent vpnEvent =
        VpnEvent.newBuilder()
            .setTimestamp(
                Timestamp.newBuilder()
                    .setSeconds(instant.getEpochSecond())
                    .setNanos(instant.getNano()))
            .setStatus(vpnStatus)
            .setChassisNo("CHASSIS-NUMBER-123")
            .setChassisSerie("S")
            .setVpi(vpi)
            .setIp(vpnIp)
            .build();
    return vpnEvent;
  }

  private void verifyUpdateVpnIpAddress(String vpi, String vpnIp) {
    Awaitility.await()
        .atLeast(Duration.ofSeconds(1))
        .atMost(Duration.ofSeconds(3))
        .with()
        .pollInterval(Duration.ofSeconds(1))
        .untilAsserted(
            () -> DataUtil.verifyVpnIpUpdatedCorrectly(vehicleDeviceInfoRepository, vpi, vpnIp));
  }
}
