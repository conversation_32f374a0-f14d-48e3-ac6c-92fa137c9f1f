package com.volvo.connectivity.mtrouter.integration;

import com.volvo.connectivity.cms.MessageTypes;
import com.volvo.connectivity.cms.events.model.CapabilityState;
import com.volvo.connectivity.cms.events.model.ConnectivityCapability;
import com.volvo.connectivity.cms.events.model.TgwAsset;
import com.volvo.connectivity.cms.events.model.TgwNotifyEvent;
import com.volvo.connectivity.cms.events.model.Wifi;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ExtendWith(TispContextExtension.class)
class TgwNotifyEventListenerIT extends TestcontainersBase {

  @Autowired private VehicleDeviceInfoRepository vehicleDeviceInfoRepository;
  @Autowired private MessagePublisher.Builder messagePublisherBuilder;
  private MessagePublisher<TgwNotifyEvent> tgwNotifyEventPublisher;

  @BeforeEach
  public void setup() {
    tgwNotifyEventPublisher =
        messagePublisherBuilder
            .messageType(MessageTypes.TGW_NOTIFY_EVENT_MESSAGE, TgwNotifyEvent.class)
            .version(MessageTypes.VERSION_1_0)
            .build();
  }

  @AfterEach
  public void clear() {
    vehicleDeviceInfoRepository.deleteAll().block();
  }

  @Test
  void testWifiCapabilityAvailable() {

    String ip = DataUtil.IPV4_ADDRESS_1.toString();
    String vpi = DataUtil.VPI_1.toString();

    VehicleDeviceInfo vehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(vpi)
            .setIpv4Address("*********")
            .setMobileNetworkOperator("telenor")
            .setMsisdn("+8947184345")
            .setSatelliteId("SOFTCAR0000001")
            .setTelematicUnit(TelematicUnit.TGW)
            .build();

    createInitialVehicleDeviceInfo(vehicleDeviceInfo);

    final TgwNotifyEvent tgwNotifyEvent = new TgwNotifyEvent();
    // Populate asset with vehicle identifier (vpi)
    TgwAsset tgwAsset = new TgwAsset();
    tgwAsset.setVpi(vpi);
    tgwNotifyEvent.setAsset(tgwAsset);
    // Populate wifi capability AVAILABLE with vpnIpAddress
    Wifi wifiCapability = new Wifi();
    wifiCapability.setState(CapabilityState.AVAILABLE);
    wifiCapability.setVpnIpAddress(ip);
    ConnectivityCapability connectivityCapability = new ConnectivityCapability();
    connectivityCapability.setWifi(wifiCapability);
    tgwNotifyEvent.setConnectivityCapability(connectivityCapability);

    IntegrationTestHelper.publishTgwNotifyEventMessage(tgwNotifyEvent, tgwNotifyEventPublisher);

    verifyUpdateVpnIpAddress(vpi, ip);
  }

  @Test
  void testWifiCapabilityUnavailable() {
    createInitialVehicleDeviceInfo(DataUtil.VPI_1.toString(), TelematicUnit.TGW);
    String vpi = DataUtil.VPI_1.toString();

    final TgwNotifyEvent tgwNotifyEvent = new TgwNotifyEvent();
    TgwAsset tgwAsset = new TgwAsset();
    tgwAsset.setVpi(vpi);
    tgwNotifyEvent.setAsset(tgwAsset);
    Wifi wifiCapability = new Wifi();
    wifiCapability.setState(CapabilityState.UNAVAILABLE);
    ConnectivityCapability connectivityCapability = new ConnectivityCapability();
    connectivityCapability.setWifi(wifiCapability);
    tgwNotifyEvent.setConnectivityCapability(connectivityCapability);

    IntegrationTestHelper.publishTgwNotifyEventMessage(tgwNotifyEvent, tgwNotifyEventPublisher);

    verifyUpdateVpnIpAddress(vpi, null);
  }

  VehicleDeviceInfo createInitialVehicleDeviceInfo(String vpi, TelematicUnit telematicUnit) {
    VehicleDeviceInfo vehicleDeviceInfo =
        IntegrationTestHelper.createVehicleDeviceInfo(vpi, telematicUnit);
    return createInitialVehicleDeviceInfo(vehicleDeviceInfo);
  }

  VehicleDeviceInfo createInitialVehicleDeviceInfo(VehicleDeviceInfo vehicleDeviceInfo) {
    vehicleDeviceInfoRepository.insert(vehicleDeviceInfo).block();
    return vehicleDeviceInfo;
  }

  private void verifyUpdateVpnIpAddress(String vpi, String vpnIp) {
    Awaitility.await()
        .atLeast(Duration.ofSeconds(1))
        .atMost(Duration.ofSeconds(3))
        .with()
        .pollInterval(Duration.ofSeconds(1))
        .untilAsserted(
            () -> DataUtil.verifyVpnIpUpdatedCorrectly(vehicleDeviceInfoRepository, vpi, vpnIp));
  }
}
