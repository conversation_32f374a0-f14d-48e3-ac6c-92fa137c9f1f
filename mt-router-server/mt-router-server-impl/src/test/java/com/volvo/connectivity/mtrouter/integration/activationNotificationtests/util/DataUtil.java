package com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Imsi;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Ipv4Address;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Msisdn;
import com.volvo.tisp.vc.common.dto.lib.vehicle.ObsAlias;
import com.volvo.tisp.vc.common.dto.lib.vehicle.Vpi;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import java.io.IOException;
import java.io.StringWriter;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.awaitility.Awaitility;

public final class DataUtil {
  public static final Imsi IMSI_1 = Imsi.ofLong(123456789);
  public static final Imsi IMSI_2 = Imsi.ofLong(1234567890);
  public static final Ipv4Address IPV4_ADDRESS_1 = Ipv4Address.ofString("***********");
  public static final Ipv4Address IPV4_ADDRESS_2 = Ipv4Address.ofString("*************");
  public static final int IPV4_PORT_1 = 8090;
  public static final int IPV4_PORT_2 = 8091;
  public static final String MOBILE_NETWORK_OPERATOR_1 = "telenor";
  public static final String MOBILE_NETWORK_OPERATOR_2 = "att";
  public static final Msisdn MSISDN_1 = Msisdn.ofString("+461234567891");
  public static final Msisdn MSISDN_2 = Msisdn.ofString("+461234567892");
  public static final ObsAlias OBS_ALIAS_1 = ObsAlias.ofLong(42);
  public static final ObsAlias OBS_ALIAS_2 = ObsAlias.ofLong(50);
  public static final Vpi VPI_1 = Vpi.ofString("3A68513383AC4CBE9B7A9AF7ED1413B6");
  public static final Vpi VPI_2 = Vpi.ofString("504A28BA60074D278EA4026EFAA83B92");
  public static final String SATELLITE_ID_1 = "01813088SKYC21D";
  public static final String SATELLITE_ID_2 = "01813088SKYC21E";
  public static final String CORRELATION_ID = "1234567890";

  private DataUtil() {
    throw new IllegalStateException();
  }

  public static <T> String marshalToXml(T object) throws JAXBException, IOException {
    JAXBContext jaxbContext = JAXBContext.newInstance(object.getClass());
    Marshaller marshaller = jaxbContext.createMarshaller();

    try (StringWriter stringWriter = new StringWriter()) {
      marshaller.marshal(object, stringWriter);
      return stringWriter.toString();
    }
  }

  public static void verifyIfVehiclePersistedInDB(
      VehicleDeviceInfoRepository vehicleDeviceInfoRepository,
      VehicleDeviceInfo expectdVehicleDeviceInfo) {
    Optional<VehicleDeviceInfo> foundVehicleOptional =
        readMtRouterDbForVehileDeviceInfo(
            vehicleDeviceInfoRepository, expectdVehicleDeviceInfo.getVpi());
    assertTrue(foundVehicleOptional.isPresent());
    VehicleDeviceInfo actualVehicleDeviceInfo = foundVehicleOptional.get();
    assertEquals(expectdVehicleDeviceInfo, actualVehicleDeviceInfo);
  }

  public static void verifyIfVehiclePersistedInDBWithDelay(
      VehicleDeviceInfoRepository vehicleDeviceInfoRepository,
      VehicleDeviceInfo expectedVehicleDeviceInfo) {

    Optional<VehicleDeviceInfo> foundVehicleOptional =
        Awaitility.await()
            .atMost(5, TimeUnit.SECONDS)
            .pollInterval(500, TimeUnit.MILLISECONDS)
            .until(
                () ->
                    readMtRouterDbForVehileDeviceInfo(
                        vehicleDeviceInfoRepository, expectedVehicleDeviceInfo.getVpi()),
                Optional::isPresent);
    assertTrue("VehicleDeviceInfo should be present in DB", foundVehicleOptional.isPresent());
    assertEquals(expectedVehicleDeviceInfo, foundVehicleOptional.get());
  }

  public static void verifyVpnIpUpdatedCorrectly(
      VehicleDeviceInfoRepository vehicleDeviceInfoRepository, String vpi, String vpnIp) {
    Optional<VehicleDeviceInfo> foundVehicleOptional =
        readMtRouterDbForVehileDeviceInfo(vehicleDeviceInfoRepository, vpi);
    assertTrue(foundVehicleOptional.isPresent());
    assertEquals(vpnIp, foundVehicleOptional.get().getVpnIpv4Address());
  }

  public static void verifyVehicleRemovedFromDb(
      VehicleDeviceInfoRepository vehicleDeviceInfoRepository, String vpi) {
    Optional<VehicleDeviceInfo> optionalVehicle =
        readMtRouterDbForVehileDeviceInfo(vehicleDeviceInfoRepository, vpi);
    assertTrue(optionalVehicle.isEmpty());
  }

  private static Optional<VehicleDeviceInfo> readMtRouterDbForVehileDeviceInfo(
      VehicleDeviceInfoRepository vehicleDeviceInfoRepository, String vpi) {
    List<VehicleDeviceInfo> result = vehicleDeviceInfoRepository.findAll().collectList().block();
    return result.stream()
        .filter(vehicleDevice -> vehicleDevice.getVpi().equalsIgnoreCase(vpi))
        .findFirst();
  }
}
