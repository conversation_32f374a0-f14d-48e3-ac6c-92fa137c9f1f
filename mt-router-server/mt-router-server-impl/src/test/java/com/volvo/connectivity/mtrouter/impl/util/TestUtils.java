package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.ServiceName;
import com.volvo.tisp.tce.discovery.ServiceNameBuilder;
import com.volvo.tisp.tce.discovery.ZookeeperRootPathConstants;
import org.apache.curator.ensemble.fixed.FixedEnsembleProvider;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.retry.ExponentialBackoffRetry;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.apache.curator.x.discovery.ServiceDiscoveryBuilder;
import org.apache.curator.x.discovery.details.JsonInstanceSerializer;

public final class TestUtils {

  public static final ServiceName SERVICE_NAME =
      new ServiceNameBuilder()
          .setName("service")
          .setSolution("solution")
          .setSite("site")
          .setEnvironment("environment")
          .build();

  public static CuratorFramework createCuratorFramework(String connectionString) {
    return CuratorFrameworkFactory.builder()
        .sessionTimeoutMs(1000)
        .ensembleProvider(new FixedEnsembleProvider(connectionString, true))
        .retryPolicy(new ExponentialBackoffRetry(1000, 3))
        .build();
  }

  public static CuratorFramework createRunningCuratorFramework(String connectionString)
      throws InterruptedException {
    CuratorFramework client = createCuratorFramework(connectionString);
    client.start();
    client.getZookeeperClient().blockUntilConnectedOrTimedOut();
    return client;
  }

  public static ServiceDiscovery<InstanceDetails> createRunnningServiceDiscovery(
      String connectionString) throws Exception {
    ServiceDiscovery<InstanceDetails> serviceDiscovery =
        ServiceDiscoveryBuilder.builder(InstanceDetails.class)
            .client(
                createRunningCuratorFramework(connectionString)
                    .usingNamespace(ZookeeperRootPathConstants.ZK_SERVICE_DISCOVERY_NAMESPACE))
            .basePath("/")
            .serializer(new JsonInstanceSerializer<>(InstanceDetails.class, false))
            .build();
    serviceDiscovery.start();

    return serviceDiscovery;
  }

  private TestUtils() {
    throw new IllegalStateException();
  }
}
