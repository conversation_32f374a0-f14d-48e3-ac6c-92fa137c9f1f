package com.volvo.connectivity.mtrouter.integration.activationNotificationtests;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.entity.WtpVersion;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.JmsUtil;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.NotificationTestClient;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.SecurityTestUtil;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.vc.conrepo.MessageTypes;
import com.volvo.vc.conrepo.api.v2.ActivationMessageType;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEvent;
import com.volvo.vc.conrepo.api.v2.ActivationNotifyEventMessage;
import com.volvo.vc.conrepo.api.v2.ChangeStatus;
import com.volvo.vc.conrepo.api.v2.DeviceDetailedEntry;
import com.volvo.vc.conrepo.api.v2.ResponseStatus;
import com.volvo.vc.conrepo.api.v2.State;
import jakarta.jms.JMSException;
import jakarta.xml.bind.JAXBException;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;

@ExtendWith(TispContextExtension.class)
public class DeactivationNotificationIT extends TestcontainersBase {

  @Autowired private VehicleDeviceInfoRepository vehicleDeviceInfoRepository;

  @Autowired private JmsTemplate jmsTemplate;

  @Autowired private MessagePublisher.Builder messagePublisherBuilder;

  private MessagePublisher<ActivationNotifyEventMessage> deactivationNotificationPublisher;

  @BeforeEach
  public void setup() {
    deactivationNotificationPublisher =
        messagePublisherBuilder
            .messageType(
                MessageTypes.DEACTIVATION_NOTIFY_MESSAGE_TYPE, ActivationNotifyEventMessage.class)
            .version(MessageTypes.VERSION_2_0)
            .build();
  }

  @AfterEach
  public void clear() {
    vehicleDeviceInfoRepository.deleteAll().block();
  }

  @Test
  void deactivationNotificationIntegrationTest() throws JAXBException, JMSException {
    VehicleDeviceInfo vehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setObsAlias(DataUtil.OBS_ALIAS_1.toLong())
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setIpv4Port(DataUtil.IPV4_PORT_1)
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setSatelliteId(DataUtil.SATELLITE_ID_1)
            .setWtpVersion(WtpVersion.VERSION_2)
            .build();
    vehicleDeviceInfoRepository.save(vehicleDeviceInfo).block();

    DeviceDetailedEntry deactiveDeviceDetailedEntry =
        SecurityTestUtil.createDeviceDetailedEntry(
            DataUtil.VPI_1.toString(),
            DataUtil.OBS_ALIAS_1.toLong(),
            null,
            null,
            State.DEACTIVATED);
    ActivationNotifyEvent deactiveDeviceActivationNotifyEvent =
        SecurityTestUtil.createActivationNotifyEvent(
            ChangeStatus.DELETED, deactiveDeviceDetailedEntry);
    ActivationNotifyEventMessage deactivationNotifyEventMessage =
        SecurityTestUtil.createActivationNotifyEventMessage(
            ActivationMessageType.DEACTIVATION_NOTIFY,
            List.of(deactiveDeviceActivationNotifyEvent, deactiveDeviceActivationNotifyEvent));

    NotificationTestClient.publishDeactivationNotifyEventMessage(
        deactivationNotifyEventMessage, deactivationNotificationPublisher);

    JmsUtil.verifyIfResponseMessageReceivedInMtRouter(
        jmsTemplate, ResponseStatus.SUCCESS, ActivationMessageType.DEACTIVATION_NOTIFY);
    DataUtil.verifyVehicleRemovedFromDb(vehicleDeviceInfoRepository, DataUtil.VPI_1.toString());
  }
}
