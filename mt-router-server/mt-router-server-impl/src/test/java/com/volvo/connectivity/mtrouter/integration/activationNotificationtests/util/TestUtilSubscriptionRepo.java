package com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util;

import static com.volvo.connectivity.asset.repository.MessageTypes.WECU_CHANGE_NOTIFICATION_MESSAGE;

import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.mtrouter.impl.jms.TgwNotifyEventListener;
import com.volvo.connectivity.mtrouter.impl.jms.VpnEventListener;
import com.volvo.connectivity.mtrouter.impl.jms.WecuChangeNotificationListener;
import com.volvo.connectivity.mtrouter.impl.jms.WecuTestChangeNotificationListener;
import com.volvo.tisp.subscriptionrepository.client.Destination;
import com.volvo.tisp.subscriptionrepository.client.SubscriptionStubber;
import com.volvo.vc.conrepo.MessageTypes;

public final class TestUtilSubscriptionRepo {
  public static void stubSubrepo() {
    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                MessageTypes.ACTIVATION_NOTIFY_MESSAGE_TYPE,
                MessageTypes.VERSION_2_0,
                "activemq:queue:ACTIVATION.NOTIFICATION.IN",
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(MessageTypes.DEACTIVATION_NOTIFY_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                MessageTypes.DEACTIVATION_NOTIFY_MESSAGE_TYPE,
                MessageTypes.VERSION_2_0,
                "activemq:queue:DEACTIVATION.NOTIFICATION.IN",
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(ServiceConstant.MT_SATELLITE_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                ServiceConstant.MT_SATELLITE_MESSAGE_TYPE,
                "1.0",
                "activemq:queue:MT.USERMESSAGE.IN",
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(ServiceConstant.MT_TGW_SMS_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                ServiceConstant.MT_TGW_SMS_MESSAGE_TYPE,
                "1.0",
                "activemq:queue:MT.USERMESSAGE.IN",
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(MessageTypes.NOTIFY_RESPONSE_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                MessageTypes.NOTIFY_RESPONSE_MESSAGE_TYPE,
                MessageTypes.VERSION_2_0,
                "activemq:queue:NOTIFICATION.REPLY",
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(ServiceConstant.VPN_EVENT_MESSAGE_TYPE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                ServiceConstant.VPN_EVENT_MESSAGE_TYPE,
                VpnEventListener.VERSION_1_0,
                "activemq:queue:" + VpnEventListener.VPN_EVENT_MESSAGE_QUEUE,
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(
            com.volvo.connectivity.cms.MessageTypes.TGW_NOTIFY_EVENT_MESSAGE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                com.volvo.connectivity.cms.MessageTypes.TGW_NOTIFY_EVENT_MESSAGE,
                com.volvo.connectivity.cms.MessageTypes.VERSION_1_0,
                "activemq:queue:" + TgwNotifyEventListener.TGW_NOTIFY_IN_QUEUE,
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType(WECU_CHANGE_NOTIFICATION_MESSAGE)
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                WECU_CHANGE_NOTIFICATION_MESSAGE,
                WecuChangeNotificationListener.VERSION_1_0,
                "activemq:queue:" + WecuChangeNotificationListener.WECU_NOTIFY_IN_QUEUE,
                "destinationId"));

    SubscriptionStubber.builder()
        .whenPublisherWithName("compshrt")
        .triesToPublishMessageOfType("WECU_TEST_CHANGE_NOTIFICATION_MESSAGE")
        .thenMessageShouldBeDeliveredTo(
            new Destination(
                "WECU_TEST_CHANGE_NOTIFICATION_MESSAGE",
                WecuChangeNotificationListener.VERSION_1_0,
                "activemq:queue:" + WecuTestChangeNotificationListener.WECU_TEST_NOTIFY_IN_QUEUE,
                "destinationId"));
  }
}
