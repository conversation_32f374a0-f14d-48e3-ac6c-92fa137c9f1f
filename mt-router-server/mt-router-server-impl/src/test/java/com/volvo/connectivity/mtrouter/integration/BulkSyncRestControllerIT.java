package com.volvo.connectivity.mtrouter.integration;

import static com.volvo.connectivity.mtrouter.impl.util.BulkSyncDecryptionFilter.*;

import com.github.tomakehurst.wiremock.client.MappingBuilder;
import com.github.tomakehurst.wiremock.client.ResponseDefinitionBuilder;
import com.github.tomakehurst.wiremock.client.WireMock;
import com.google.protobuf.ByteString;
import com.volvo.connectivity.mtrouter.impl.conf.BulkSyncProperties;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.cos.conrepo.bulk_sync.v1.Region;
import com.volvo.cos.conrepo.bulk_sync.v1.SrpLevel;
import com.volvo.cos.conrepo.bulk_sync.v1.TgwSubType;
import com.volvo.cos.conrepo.bulk_sync.v1.VehicleInfo;
import com.volvo.cos.conrepo.bulk_sync.v1.WtpVersion;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigInteger;
import java.security.GeneralSecurityException;
import java.security.KeyFactory;
import java.security.PrivateKey;
import java.security.SecureRandom;
import java.security.Signature;
import java.security.cert.Certificate;
import java.security.cert.CertificateFactory;
import java.security.spec.PKCS8EncodedKeySpec;
import java.util.Base64;
import java.util.List;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.web.reactive.server.WebTestClient;

class BulkSyncRestControllerIT extends TestcontainersBase {
  private @Autowired BulkSyncProperties bulkSyncProperties;
  private @Autowired VehicleDeviceInfoRepository vehicleDeviceInfoRepository;
  private SecureRandom secureRandom = new SecureRandom();
  private BigInteger minVpiValue = BigInteger.valueOf(0x1000_0000_0000_0000L).shiftLeft(64);

  @BeforeEach
  public void beforeEach() {
    vehicleDeviceInfoRepository.deleteAll().block();
    List<VehicleDeviceInfo> vehicleDeviceInfos =
        List.of(
            IntegrationTestHelper.createVehicleDeviceInfo(
                "42635A6C40BDCE235353D3F2F28CD339", TelematicUnit.TGW),
            IntegrationTestHelper.createVehicleDeviceInfo(
                "42635A6C40BDCE235353D3F2F28CD331", TelematicUnit.TGW),
            IntegrationTestHelper.createVehicleDeviceInfo(
                "42635A6C40BDCE235353D3F2F28CD335", TelematicUnit.TGW));
    vehicleDeviceInfoRepository.saveAll(vehicleDeviceInfos).blockLast();
  }

  @Test
  void testSuccess() throws Exception {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream(23 * 192);
    generatePayload(outputStream, 23, 1);
    stubConrepo(outputStream, "EMEA");

    WebTestClient.ResponseSpec startBulkSync = startBulkSync("EMEA", false);
    startBulkSync.expectStatus().is2xxSuccessful();
    Assertions.assertEquals(23, vehicleDeviceInfoRepository.count().block());
    Assertions.assertNull(
        vehicleDeviceInfoRepository.findById("42635A6C40BDCE235353D3F2F28CD331").block());
  }

  @Test
  void testRollBack() throws Exception {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream(11 * 192);
    generatePayload(outputStream, 11, 1);
    generatePayload(outputStream, 1, 0);
    stubConrepo(outputStream, "EMEA");
    WebTestClient.ResponseSpec startBulkSync = startBulkSync("EMEA", false);
    startBulkSync.expectStatus().is5xxServerError();
    Assertions.assertEquals(3, vehicleDeviceInfoRepository.count().block());
  }

  @Test
  void testDryRun() throws Exception {
    ByteArrayOutputStream outputStream = new ByteArrayOutputStream(19 * 192);
    generatePayload(outputStream, 19, 1);
    stubConrepo(outputStream, null);

    WebTestClient.ResponseSpec startBulkSync = startBulkSync(null, true);
    startBulkSync.expectStatus().is2xxSuccessful();
    Assertions.assertEquals(3, vehicleDeviceInfoRepository.count().block());
    Assertions.assertNotNull(
        vehicleDeviceInfoRepository.findById("42635A6C40BDCE235353D3F2F28CD331").block());
  }

  private void generatePayload(
      ByteArrayOutputStream outputStream, int numberOfVehicles, int startingIndex)
      throws IOException {
    int index = startingIndex;
    for (; index < numberOfVehicles + startingIndex; index++) {
      VehicleInfo.newBuilder()
          .setActiveAesKey(ByteString.copyFromUtf8("ActiveAesKey_" + index))
          .setActiveAesKeyId("ActiveAesKeyId_" + index)
          .setHandle(index)
          .setIpV4Address(index % 5 == 0 ? 0 : index)
          .setMobileNetworkOperator("MobileNetworkOperator_" + index)
          .setMsisdn(index % 5 == 0 ? "" : "+46768" + index)
          .setObsAlias(index)
          .setPendingAesKey(ByteString.copyFromUtf8("PendingAesKey_" + index))
          .setPendingAesKeyId("PendingAesKeyId_" + index)
          .setPort(index % 5 == 0 ? 0 : 9062)
          .setRegion(Region.EMEA)
          .setSatelliteId(index % 3 == 0 ? String.format("%08dSKY09aZ", index) : "")
          .setSrpLevel(SrpLevel.SRP_11)
          .setTgwSubType(TgwSubType.TGW_3)
          .setVpi(ByteString.copyFrom(minVpiValue.add(BigInteger.valueOf(index)).toByteArray()))
          .setWtpVersion(WtpVersion.VERSION_2)
          .build()
          .writeDelimitedTo(outputStream);
    }
  }

  private static WebTestClient.ResponseSpec startBulkSync(String region, boolean dryRun) {
    return WEB_TEST_CLIENT
        .get()
        .uri(
            builder ->
                builder
                    .path("/api/v1/bulk-sync")
                    .queryParam("region", region)
                    .queryParam("dryRun", dryRun)
                    .build())
        .exchange();
  }

  private void stubConrepo(ByteArrayOutputStream outputStream, String region)
      throws GeneralSecurityException {
    byte[] ivBytes = new byte[16];
    secureRandom.nextBytes(ivBytes);
    KeyGenerator keyGenerator = KeyGenerator.getInstance("AES");
    keyGenerator.init(128, secureRandom);
    SecretKey aesKey = keyGenerator.generateKey();

    Cipher cipher = Cipher.getInstance(AES_CBC_PKCS5PADDING);
    cipher.init(Cipher.ENCRYPT_MODE, aesKey, new IvParameterSpec(ivBytes));

    CertificateFactory certificateFactory = CertificateFactory.getInstance("X.509");
    Certificate certificate =
        certificateFactory.generateCertificate(
            new ByteArrayInputStream(decodeBase64(bulkSyncProperties.publicCertificate())));
    KeyFactory keyFactory = KeyFactory.getInstance("RSA");
    PKCS8EncodedKeySpec privateKeySpec =
        new PKCS8EncodedKeySpec(decodeBase64(bulkSyncProperties.privateKey()));
    PrivateKey privateKey = keyFactory.generatePrivate(privateKeySpec);

    Cipher encryptCipher = Cipher.getInstance(RSA_ECB_PKCS1PADDING);
    encryptCipher.init(Cipher.ENCRYPT_MODE, certificate);
    byte[] encryptedAesKey = encryptCipher.doFinal(aesKey.getEncoded());

    Signature signature = Signature.getInstance(SHA256_WITH_RSA);
    signature.initSign(privateKey);
    signature.update(encryptedAesKey);
    ResponseDefinitionBuilder responseDefinitionBuilder =
        WireMock.ok()
            .withStatus(200)
            .withBody(cipher.doFinal(outputStream.toByteArray()))
            .withHeader("ENCRYPTED_AES_KEY", encodeToBase64(encryptedAesKey))
            .withHeader("IV", encodeToBase64(ivBytes))
            .withHeader("SIGNATURE", encodeToBase64(signature.sign()));

    String testUrl =
        "/api/v1/bulksync?clientKeyAlias=tcetisp-test-1&conrepoKeyAlias=tcetisp-test-1&softcarOnly=false";
    if (region != null) {
      testUrl =
          "/api/v1/bulksync?region="
              + region
              + "&clientKeyAlias=tcetisp-test-1&conrepoKeyAlias=tcetisp-test-1&softcarOnly=false";
    }

    MappingBuilder mappingBuilder =
        WireMock.get(WireMock.urlEqualTo(testUrl)).willReturn(responseDefinitionBuilder);
    WireMock.stubFor(mappingBuilder);
  }

  private static byte[] decodeBase64(String base64) {
    return Base64.getDecoder().decode(base64);
  }

  private static String encodeToBase64(byte[] bytes) {
    return Base64.getEncoder().encodeToString(bytes);
  }
}
