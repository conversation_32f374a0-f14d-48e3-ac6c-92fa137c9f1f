package com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util;

import com.volvo.tisp.vc.main.utils.lib.Validate;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.crypto.common.encoding.MessageEncoder;
import com.volvo.vc.crypto.common.encoding.MessageEncodingException;
import com.volvo.vc.crypto.common.entity.PlainTextPayload;
import java.nio.charset.StandardCharsets;

public final class MessageEncoderXmlImpl implements MessageEncoder {
  public static final MessageEncoder INSTANCE = new MessageEncoderXmlImpl();

  private MessageEncoderXmlImpl() {}

  @Override
  public <T> PlainTextPayload encode(T t, Class<T> type) {
    Validate.notNull(t, "t");
    Validate.notNull(type, "type");

    try {
      return PlainTextPayload.create(
          ImmutableByteArray.of(DataUtil.marshalToXml(t).getBytes(StandardCharsets.UTF_8)));
    } catch (Exception e) {
      throw new MessageEncodingException(e);
    }
  }
}
