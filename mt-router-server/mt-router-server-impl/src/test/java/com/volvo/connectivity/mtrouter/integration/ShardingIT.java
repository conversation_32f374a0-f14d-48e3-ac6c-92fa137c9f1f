package com.volvo.connectivity.mtrouter.integration;

import com.volvo.connectivity.mtrouter.impl.services.SharderServiceProvider;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import java.net.URI;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.apache.curator.x.discovery.ServiceInstance;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;

class ShardingIT extends TestcontainersBase {
  @Autowired ServiceDiscovery<InstanceDetails> serviceDiscovery;
  @Autowired SharderServiceProvider sharderServiceProvider;

  @Test
  void integrationTest() throws Exception {
    ServiceDiscoveryRegistration serviceDiscoveryRegistration =
        IntegrationTestHelper.createServiceDiscoveryRegistor(serviceDiscovery, 3080);
    serviceDiscoveryRegistration.open();
    Thread.sleep(2000);
    ServiceInstance<InstanceDetails> instance =
        sharderServiceProvider.getServiceInstanceFromObject(
            URI.create("http://127.0.0.1:" + 3080 + "/mtMessage"));
    String expected = "http://127.0.0.1:" + 3080 + "/mtMessage";
    Assertions.assertEquals(expected, instance.buildUriSpec());
    serviceDiscoveryRegistration.close();
  }

  @Test
  void integrationTestWithMultipleShards() throws Exception {
    try (AutoCloseable closeable =
        IntegrationTestHelper.createServiceDiscoveryRegistrationWrapper(3080, serviceDiscovery)) {
      Thread.sleep(1000);
      ServiceInstance<InstanceDetails> instance1 =
          sharderServiceProvider.getServiceInstanceFromObject(URI.create("*********"));
      ServiceInstance<InstanceDetails> instance2 =
          sharderServiceProvider.getServiceInstanceFromObject(URI.create("*********"));
      Assertions.assertNotEquals(instance2.buildUriSpec(), instance1.buildUriSpec());
    }
  }
}
