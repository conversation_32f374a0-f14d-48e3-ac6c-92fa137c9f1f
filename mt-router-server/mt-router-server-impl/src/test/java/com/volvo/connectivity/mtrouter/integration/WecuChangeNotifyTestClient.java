package com.volvo.connectivity.mtrouter.integration;

import com.volvo.connectivity.asset.repository.events.model.WecuChangeNotifyEvent;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import com.volvo.vc.conrepo.api.v2.SecureActivationNotifyEventMessage;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class WecuChangeNotifyTestClient {

  public WecuChangeNotifyTestClient() {
    throw new IllegalStateException();
  }

  /***
   * Method to publish the WecuChangeNotifyEventMessage using the subscription repository api
   * @param wecuChangeNotifyEvent an instance of {@link WecuChangeNotifyEvent}
   * @param wecuChangeNotifyEventMessagePublisher an instance of {@link MessagePublisher}&lt;{@link SecureActivationNotifyEventMessage}&gt;
   */
  public static void publishNotifyEventMessage(
      WecuChangeNotifyEvent wecuChangeNotifyEvent,
      MessagePublisher<WecuChangeNotifyEvent> wecuChangeNotifyEventMessagePublisher) {
    wecuChangeNotifyEventMessagePublisher
        .newMessage()
        .correlationId(DataUtil.CORRELATION_ID)
        .replyTo("activemq:queue:SHARED.EU-WEST-1.IOT1.LB.MTROUTER.WECU.CHANGE.NOTIFY.IN")
        .publish(wecuChangeNotifyEvent)
        .thenAccept(
            numberOfPublishedMessages -> {
              log.info("{} was published to sub repo..", numberOfPublishedMessages);
            })
        .exceptionally(
            throwable -> {
              throw new RuntimeException(
                  "Integration Testing-Failed to publish wecu change notification to mt-router-server. ",
                  throwable);
            });
  }
}
