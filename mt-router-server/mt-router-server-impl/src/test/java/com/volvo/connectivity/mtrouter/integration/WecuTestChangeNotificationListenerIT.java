package com.volvo.connectivity.mtrouter.integration;

import static com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper.buildWecuChangeNotifyEvent;

import com.volvo.connectivity.asset.repository.events.model.WecuChangeNotifyEvent;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.impl.jms.WecuChangeNotificationListener;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.subscriptionrepository.client.MessagePublisher;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@ExtendWith(TispContextExtension.class)
public class WecuTestChangeNotificationListenerIT extends TestcontainersBase {

  @Autowired private VehicleDeviceInfoRepository vehicleDeviceInfoRepository;
  @Autowired private MessagePublisher.Builder messagePublisherBuilder;
  private MessagePublisher<WecuChangeNotifyEvent> wecuChangeNotifyEventPublisher;

  @BeforeEach
  public void setup() {
    wecuChangeNotifyEventPublisher =
        messagePublisherBuilder
            .messageType("WECU_TEST_CHANGE_NOTIFICATION_MESSAGE", WecuChangeNotifyEvent.class)
            .version(WecuChangeNotificationListener.VERSION_1_0)
            .build();
  }

  @AfterEach
  public void clear() {
    vehicleDeviceInfoRepository.deleteAll().block();
  }

  @Disabled
  @Test
  void testWecuTestChangeNotifyEvent() {

    final WecuChangeNotifyEvent wecuChangeNotifyEvent =
        buildWecuChangeNotifyEvent(DataUtil.VPI_1.toString(), DataUtil.IPV4_ADDRESS_2.toString());

    WecuChangeNotifyTestClient.publishNotifyEventMessage(
        wecuChangeNotifyEvent, wecuChangeNotifyEventPublisher);

    VehicleDeviceInfo expectdVehicleDeviceInfo =
        VehicleDeviceInfo.builder()
            .setVpi(DataUtil.VPI_1.toString())
            .setIpv4Address(DataUtil.IPV4_ADDRESS_1.toString())
            .setMsisdn(DataUtil.MSISDN_1.toString())
            .setMobileNetworkOperator(DataUtil.MOBILE_NETWORK_OPERATOR_1)
            .setSatelliteId(DataUtil.SATELLITE_ID_1)
            .setTelematicUnit(TelematicUnit.WECU)
            .setVpnIpv4Address(DataUtil.IPV4_ADDRESS_2.toString())
            .build();

    DataUtil.verifyIfVehiclePersistedInDBWithDelay(
        vehicleDeviceInfoRepository, expectdVehicleDeviceInfo);
  }
}
