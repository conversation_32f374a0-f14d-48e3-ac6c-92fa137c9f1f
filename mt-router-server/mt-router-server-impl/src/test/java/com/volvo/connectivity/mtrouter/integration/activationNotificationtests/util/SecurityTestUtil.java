package com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util;

import com.volvo.tisp.vc.main.utils.lib.type.Either;
import com.volvo.tisp.vc.main.utils.lib.type.ImmutableByteArray;
import com.volvo.vc.conrepo.api.v2.*;
import com.volvo.vc.crypto.asymmetric.encryption.rsa.RsaEncryptionServiceImpl;
import com.volvo.vc.crypto.asymmetric.key.rsa.RsaKeyRepositoryImpl;
import com.volvo.vc.crypto.asymmetric.signature.rsa.RsaSigningService;
import com.volvo.vc.crypto.common.entity.KeyId;
import com.volvo.vc.crypto.common.keystore.KeyAlias;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepository;
import com.volvo.vc.crypto.common.keystore.KeyStoreRepositoryConfig;
import com.volvo.vc.crypto.common.keystore.impl.KeyStoreCacheRepositoryImpl;
import com.volvo.vc.crypto.common.keystore.impl.KeyStoreRepositoryImpl;
import com.volvo.vc.crypto.common.keystore.password.Password;
import com.volvo.vc.crypto.message.EncryptedMessage;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionConfig;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionService;
import com.volvo.vc.crypto.message.encryption.MessageEncryptionServiceImpl;
import com.volvo.vc.crypto.symmetric.encryption.gcm.AesGcmEncryptionResult;
import com.volvo.vc.crypto.symmetric.encryption.gcm.SymmetricAesGcmEncryptionServiceImpl;
import java.math.BigInteger;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class SecurityTestUtil {
  private static final String RECIEVER_PUBLIC_KEY_ALIAS = "mtrouter-test-1";
  private static final String SENDER_PRIVATE_KEY_ALIAS = "conrepo2-test-1";
  private static final String SENDER_KEYSTORE_PASSWORD = "conrepo2-test";
  public static final String REPLY_TO = "NOTIFICATION.REPLY";
  public static final String REFERENCE_ID = "testSecureActivationReferenceId1234";

  public static SecureActivationNotifyEventMessage encryptActivationNotifyEventMessage(
      ActivationNotifyEventMessage activationNotifyEventMessage) {
    MessageEncryptionConfig<ActivationNotifyEventMessage> messageEncryptionConfig =
        createMessageEncryptionConfig();
    MessageEncryptionService messageEncryptionService =
        createMessageEncryptionService(
            Paths.get("src/test/resources/conrepo2-transportkeystore.pkcs12"),
            SENDER_KEYSTORE_PASSWORD);
    Either<RuntimeException, EncryptedMessage> either =
        messageEncryptionService.encrypt(messageEncryptionConfig, activationNotifyEventMessage);
    return createSecureActivationNotifyEventMessage(either.getRight());
  }

  public static ActivationNotifyEventMessage createActivationNotifyEventMessage(
      ActivationMessageType activationMessageType,
      List<ActivationNotifyEvent> activationNotifyEventList) {
    ActivationNotifyEventMessage activationNotifyEventMessage = new ActivationNotifyEventMessage();

    activationNotifyEventMessage.getActivationNotifyEvents().addAll(activationNotifyEventList);
    activationNotifyEventMessage.setMessageType(activationMessageType);
    activationNotifyEventMessage.setReferenceId(REFERENCE_ID);
    activationNotifyEventMessage.setReplyTo(REPLY_TO);
    return activationNotifyEventMessage;
  }

  private static SecureActivationNotifyEventMessage createSecureActivationNotifyEventMessage(
      EncryptedMessage encryptedMessage) {
    SecureActivationNotifyEventMessage secureActivationNotifyEventMessage =
        new SecureActivationNotifyEventMessage();

    secureActivationNotifyEventMessage.setEncryptedAesKey(
        encryptedMessage.getEncryptedAesKey().getImmutableByteArray().toByteArray());
    secureActivationNotifyEventMessage.setEncryptedPayload(
        getPayload(encryptedMessage.getAesGcmEncryptionResult()).toByteArray());
    secureActivationNotifyEventMessage.setPrivateKeyId(
        BigInteger.valueOf(encryptedMessage.getPrivateKeyId().toLong()));
    secureActivationNotifyEventMessage.setPublicKeyId(
        BigInteger.valueOf(encryptedMessage.getPublicKeyId().toLong()));
    secureActivationNotifyEventMessage.setSignature(
        encryptedMessage.getSignature().getImmutableByteArray().toByteArray());
    return secureActivationNotifyEventMessage;
  }

  public static ActivationNotifyEvent createActivationNotifyEvent(
      ChangeStatus changeStatus, DeviceDetailedEntry deviceDetailedEntry) {
    ActivationNotifyEvent activationNotifyEvent = new ActivationNotifyEvent();
    activationNotifyEvent.setChangeStatus(changeStatus);
    activationNotifyEvent.setDeviceDetail(deviceDetailedEntry);

    return activationNotifyEvent;
  }

  private static MessageEncryptionConfig<ActivationNotifyEventMessage>
      createMessageEncryptionConfig() {
    return MessageEncryptionConfig.create(
        KeyAlias.ofString(RECIEVER_PUBLIC_KEY_ALIAS),
        KeyAlias.ofString(SENDER_PRIVATE_KEY_ALIAS),
        KeyId.ofLong(1),
        KeyId.ofLong(1),
        MessageEncoderXmlImpl.INSTANCE,
        ActivationNotifyEventMessage.class);
  }

  private static KeyStoreRepository createAsymmetricKeyStoreRepository(
      Path transportKeyStorePath, String transportKeyStorePassword) {
    KeyStoreRepositoryConfig keyStoreRepositoryConfig =
        new KeyStoreRepositoryConfig(
            Password.ofString(transportKeyStorePassword), transportKeyStorePath);
    KeyStoreRepository keyStoreRepository =
        KeyStoreRepositoryImpl.createKeyStoreRepository(keyStoreRepositoryConfig);
    return KeyStoreCacheRepositoryImpl.create(keyStoreRepository);
  }

  public static DeviceDetailedEntry createDeviceDetailedEntry(
      String vpi, long obsAlias, DeviceSim deviceSim, String satelliteId, State state) {
    DeviceDetailedEntry deviceDetailedEntry = new DeviceDetailedEntry();

    deviceDetailedEntry.setObsAlias(obsAlias);
    deviceDetailedEntry.setSimEntry(deviceSim);
    deviceDetailedEntry.setState(state);
    deviceDetailedEntry.setVehiclePlatformId(vpi);
    deviceDetailedEntry.setSatelliteId(satelliteId);
    deviceDetailedEntry.setWtpProtocolVersion(WtpProtocolVersion.VERSION_2);

    return deviceDetailedEntry;
  }

  public static DeviceSim createDeviceSim(
      String imsi, String ipAddress, String msisdn, String operator, int port) {
    DeviceSim deviceSim = new DeviceSim();
    deviceSim.setImsi(imsi);
    deviceSim.setIp(ipAddress);
    deviceSim.setMsisdn(msisdn);
    deviceSim.setOperator(operator);
    deviceSim.setPort(port);
    return deviceSim;
  }

  private static MessageEncryptionService createMessageEncryptionService(
      Path transportKeyStorePath, String transportKeyStorePassword) {
    log.debug(
        " transportKeyStorePath: {}, transportKeyStorePassword; {}",
        transportKeyStorePath,
        transportKeyStorePassword);
    return MessageEncryptionServiceImpl.create(
        RsaEncryptionServiceImpl.INSTANCE,
        RsaSigningService.INSTANCE,
        RsaKeyRepositoryImpl.create(
            createAsymmetricKeyStoreRepository(transportKeyStorePath, transportKeyStorePassword)),
        SymmetricAesGcmEncryptionServiceImpl.INSTANCE);
  }

  private static ImmutableByteArray getPayload(AesGcmEncryptionResult aesGcmEncryptionResult) {
    return aesGcmEncryptionResult
        .getInitializationVector()
        .getImmutableByteArray()
        .concat(aesGcmEncryptionResult.getEncryptedPayloadWithoutMac().getImmutableByteArray())
        .concat(aesGcmEncryptionResult.getMessageAuthenticationCode().getImmutableByteArray());
  }
}
