package com.volvo.connectivity.mtrouter.integration;

import com.github.tomakehurst.wiremock.client.WireMock;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.MtStatus;
import com.volvo.connectivity.proto.Status;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import jakarta.jms.Message;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jms.core.JmsTemplate;

@Slf4j
@ExtendWith(TispContextExtension.class)
class SendMtMessageSAT_IT extends TestcontainersBase {
  private static final String SERVICE_PATH = "/api/v1/mt/message";
  @Autowired private JmsTemplate jmsTemplate;
  @Autowired VehicleDeviceInfoRepository vehicleDeviceInfoRepository;

  @Test
  void integrationApiTest() throws Exception {
    VehicleDeviceInfo vehicleDeviceInfo =
        IntegrationTestHelper.createVehicleDeviceInfo(
            "42635A6C40BDCE235353D3F2F28CD337", TelematicUnit.TGW);
    vehicleDeviceInfoRepository.insert(vehicleDeviceInfo).block();
    MtMessage mtMessage =
        IntegrationTestHelper.createMtMessage(Transport.SAT, "42635A6C40BDCE235353D3F2F28CD337");

    WEB_TEST_CLIENT.post().uri(SERVICE_PATH).bodyValue(mtMessage).exchange().expectStatus().isOk();

    Message message = jmsTemplate.receive("MT.USERMESSAGE.IN");
    log.info("JMS Message: {}", message);
    byte[] jmsPayload = message.getBody(byte[].class);
    MtStatus mtStatusMessage = MtStatus.parseFrom(jmsPayload);
    Assertions.assertThat(mtStatusMessage.getStatus()).isEqualTo(Status.DELIVERED);
    Assertions.assertThat(mtStatusMessage.getMessageId())
        .isEqualTo("A4635A6C40B45E235353D3F2F28CD339");

    vehicleDeviceInfoRepository.deleteById(vehicleDeviceInfo.getVpi()).block();
  }

  @Test
  void integrationApiVPINotFound() throws Exception {
    MtMessage mtMessage =
        IntegrationTestHelper.createMtMessage(Transport.SAT, "42635A6C40BDCE235353D3F2F23CD336");
    WEB_TEST_CLIENT
        .post()
        .uri(SERVICE_PATH)
        .bodyValue(mtMessage)
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }

  @Test
  void integrationApiSubscriptionsWithNotFound() throws Exception {
    WireMock.reset();
    VehicleDeviceInfo vehicleDeviceInfo =
        IntegrationTestHelper.createVehicleDeviceInfo(
            "42635A6C40BDCE235353D3F2F28CD337", TelematicUnit.TGW);
    vehicleDeviceInfoRepository.insert(vehicleDeviceInfo).block();
    MtMessage mtMessage =
        IntegrationTestHelper.createMtMessage(Transport.SAT, "42635A6C40BDCE235353D3F2F28CD337");

    WEB_TEST_CLIENT
        .post()
        .uri(SERVICE_PATH)
        .bodyValue(mtMessage)
        .exchange()
        .expectStatus()
        .isNotFound()
        .expectBody(String.class)
        .value(response -> Assertions.assertThat(response).isNotEmpty());

    vehicleDeviceInfoRepository.deleteById(vehicleDeviceInfo.getVpi()).block();
  }
}
