package com.volvo.connectivity.mtrouter.integration;

import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

@ExtendWith(TispContextExtension.class)
class SendMtMessageVPN_IT extends TestcontainersBase {

  private static final String SERVICE_PATH = "/api/v1/mt/message";
  @Autowired ServiceDiscovery<InstanceDetails> serviceDiscovery;
  @Autowired VehicleDeviceInfoRepository vehicleDeviceInfoRepository;

  @Test
  void integrationApiTest() {
    try (ServiceDiscoveryRegistration serviceDiscoveryRegistration =
        IntegrationTestHelper.createServiceRegistration(
            serviceDiscovery, ServiceConstant.VWTP_INITIATOR_SERVICE)) {
      serviceDiscoveryRegistration.open();

      IntegrationTestHelper.stubWireMockForService();
      VehicleDeviceInfo vehicleDeviceInfo =
          IntegrationTestHelper.createVehicleDeviceInfo(
              "42635A6C40BDCE235353D3F2F28CD340", TelematicUnit.TGW);
      vehicleDeviceInfoRepository.insert(vehicleDeviceInfo).block();

      MtMessage mtMessage =
          IntegrationTestHelper.createMtMessage(Transport.VPN, "42635A6C40BDCE235353D3F2F28CD340");

      WEB_TEST_CLIENT
          .post()
          .uri(SERVICE_PATH)
          .bodyValue(mtMessage)
          .exchange()
          .expectStatus()
          .isOk();
      vehicleDeviceInfoRepository.deleteById(vehicleDeviceInfo.getVpi()).block();
    }
  }

  @Test
  void integrationApiTestForInstanceNotFound() throws Exception {
    MtMessage mtMessage =
        IntegrationTestHelper.createMtMessage(Transport.VPN, "42635A6C40BDCE235353D3F2F28CD340");
    WEB_TEST_CLIENT
        .post()
        .uri(SERVICE_PATH)
        .bodyValue(mtMessage)
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }
}
