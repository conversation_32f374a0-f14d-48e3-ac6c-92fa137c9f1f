package com.volvo.connectivity.mtrouter.integration;

import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.activationNotificationtests.util.DataUtil;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.connectivity.proto.AssetAddress;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.SmsAddress;
import com.volvo.connectivity.proto.TisMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

@ExtendWith(TispContextExtension.class)
class SendMtMessageWecu_IT extends TestcontainersBase {

  private static final String SERVICE_PATH = "/api/v1/mt/message";
  @Autowired VehicleDeviceInfoRepository vehicleDeviceInfoRepository;

  @Test
  void routeMtMessageViaTisRouter() {

    IntegrationTestHelper.stubWireMockForTisRouterApi();
    VehicleDeviceInfo vehicleDeviceInfo =
        IntegrationTestHelper.createVehicleDeviceInfoForWecu(DataUtil.VPI_1.toString());
    vehicleDeviceInfoRepository.insert(vehicleDeviceInfo).block();

    MtMessage mtMessage =
        IntegrationTestHelper.createMtMessage(Transport.SMS, DataUtil.VPI_1.toString());

    SmsAddress smsAddress =
        SmsAddress.newBuilder().setMsisdn("+8947184345").setOperator("telenor").build();

    AssetAddress assetAddress = AssetAddress.newBuilder().setSms(smsAddress).build();

    TisMessage expectedTisMessage =
        TisMessage.newBuilder()
            .setMessageId(mtMessage.getMessageId())
            .setPayload(mtMessage.getPayload())
            .setAddress(assetAddress)
            .build();

    WEB_TEST_CLIENT.post().uri(SERVICE_PATH).bodyValue(mtMessage).exchange().expectStatus().isOk();
    IntegrationTestHelper.verifyTisRouterApiInvocation(expectedTisMessage);
    vehicleDeviceInfoRepository.deleteById(vehicleDeviceInfo.getVpi()).block();
  }
}
