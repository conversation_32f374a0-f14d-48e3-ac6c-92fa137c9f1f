package com.volvo.connectivity.mtrouter.impl.util;

import com.volvo.tisp.tce.discovery.InstanceDetails;
import org.apache.curator.x.discovery.ServiceInstance;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class InstanceComparatorTest {

  private InstanceComparator instanceComparator;

  @BeforeEach
  void setUp() {
    instanceComparator = new InstanceComparator();
  }

  @Test
  void compare_ShouldReturnNegativeValue_WhenFirstAddressIsLessThanSecondAddress() {
    ServiceInstance<InstanceDetails> o1 = createServiceInstance("***********");
    ServiceInstance<InstanceDetails> o2 = createServiceInstance("***********");

    int result = instanceComparator.compare(o1, o2);

    Assertions.assertEquals(-1, result);
  }

  @Test
  void compare_ShouldReturnPositiveValue_WhenFirstAddressIsGreaterThanSecondAddress() {
    ServiceInstance<InstanceDetails> o1 = createServiceInstance("***********");
    ServiceInstance<InstanceDetails> o2 = createServiceInstance("***********");

    int result = instanceComparator.compare(o1, o2);

    Assertions.assertEquals(1, result);
  }

  @Test
  void compare_ShouldReturnZero_WhenAddressesAreEqual() {
    ServiceInstance<InstanceDetails> o1 = createServiceInstance("***********");
    ServiceInstance<InstanceDetails> o2 = createServiceInstance("***********");

    int result = instanceComparator.compare(o1, o2);

    Assertions.assertEquals(0, result);
  }

  private ServiceInstance<InstanceDetails> createServiceInstance(String address) {
    return new ServiceInstance<>("service", "mt-router", address, 8080, null, null, 0, null, null);
  }
}
