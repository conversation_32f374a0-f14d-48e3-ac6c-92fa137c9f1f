package com.volvo.connectivity.mtrouter.integration;

import static org.junit.jupiter.api.Assertions.assertNotNull;

import com.volvo.connectivity.ServiceConstant;
import com.volvo.connectivity.mtrouter.impl.db.entity.TelematicUnit;
import com.volvo.connectivity.mtrouter.impl.db.entity.VehicleDeviceInfo;
import com.volvo.connectivity.mtrouter.impl.db.repo.VehicleDeviceInfoRepository;
import com.volvo.connectivity.mtrouter.integration.util.IntegrationTestHelper;
import com.volvo.connectivity.mtrouter.integration.util.TestcontainersBase;
import com.volvo.connectivity.proto.MtMessage;
import com.volvo.connectivity.proto.Transport;
import com.volvo.tisp.framework.test.context.TispContextExtension;
import com.volvo.tisp.tce.discovery.InstanceDetails;
import com.volvo.tisp.tce.discovery.service.ServiceDiscoveryRegistration;
import java.util.concurrent.TimeUnit;
import org.apache.curator.x.discovery.ServiceDiscovery;
import org.awaitility.Awaitility;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.beans.factory.annotation.Autowired;

@ExtendWith(TispContextExtension.class)
class SendMtMessageUDP_IT extends TestcontainersBase {

  private static final String SERVICE_PATH = "/api/v1/mt/message";
  @Autowired ServiceDiscovery<InstanceDetails> serviceDiscovery;
  @Autowired VehicleDeviceInfoRepository vehicleDeviceInfoRepository;

  @Test
  void integrationApiTest() throws Exception {
    try (ServiceDiscoveryRegistration serviceDiscoveryRegistration =
        IntegrationTestHelper.createServiceRegistration(
            serviceDiscovery, ServiceConstant.VWTP_INITIATOR_SERVICE)) {
      serviceDiscoveryRegistration.open();
      Awaitility.await()
          .pollDelay(1, TimeUnit.SECONDS)
          .untilAsserted(
              () ->
                  assertNotNull(
                      serviceDiscoveryRegistration.getServiceInstance(),
                      "Service instance should not be null"));
      IntegrationTestHelper.stubWireMockForService();
      VehicleDeviceInfo vehicleDeviceInfo =
          IntegrationTestHelper.createVehicleDeviceInfo(
              "42635A6C40BDCE235353D3F2F28CD339", TelematicUnit.TGW);
      vehicleDeviceInfoRepository.insert(vehicleDeviceInfo).block();

      MtMessage mtMessage =
          IntegrationTestHelper.createMtMessage(Transport.UDP, "42635A6C40BDCE235353D3F2F28CD339");

      WEB_TEST_CLIENT
          .post()
          .uri(SERVICE_PATH)
          .bodyValue(mtMessage)
          .exchange()
          .expectStatus()
          .isOk();
      vehicleDeviceInfoRepository.deleteById(vehicleDeviceInfo.getVpi()).block();
    }
  }

  @Test
  void integrationApiTestForInstanceNotFound() throws Exception {
    MtMessage mtMessage =
        IntegrationTestHelper.createMtMessage(Transport.UDP, "42635A6C40BDCE235353D3F2F28CD339");
    WEB_TEST_CLIENT
        .post()
        .uri(SERVICE_PATH)
        .bodyValue(mtMessage)
        .exchange()
        .expectStatus()
        .is5xxServerError();
  }
}
