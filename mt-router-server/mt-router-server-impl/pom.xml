<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.connectivity.mtrouter</groupId>
    <artifactId>mt-router-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>mt-router-server-impl</artifactId>
  <packaging>jar</packaging>

  <name>mt Router :: Server :: impl</name>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (Management) -->
  <!-- +=============================================== -->
  <dependencies>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-artemis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-starter-web</artifactId>
      <exclusions>
        <exclusion>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-starter-web</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-webflux</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-validation</artifactId>
      <scope>runtime</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.connectivity</groupId>
      <artifactId>connectivity-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>vc-influxdb-event-reporter-lib</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>tce-common-discovery</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.connectivity.common</groupId>
      <artifactId>component-commons</artifactId>
    </dependency>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>vc-crypto-message</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.vc.conrepo2</groupId>
      <artifactId>connectivity-repository-client2-api</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.connectivity</groupId>
      <artifactId>asset-connectivity-repository-models</artifactId>
    </dependency>

    <dependency>
      <groupId>com.volvo.connectivity</groupId>
      <artifactId>connectivity-management-service-models</artifactId>
    </dependency>

    <dependency>
      <groupId>com.wirelesscar.subscriptionrepository</groupId>
      <artifactId>subscriptionrepository-client-impl</artifactId>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>common-dto-lib</artifactId>
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.apache.curator</groupId>
      <artifactId>curator-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp.framework</groupId>
      <artifactId>tisp-framework-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.volvo.tisp</groupId>
      <artifactId>unit-test-lib</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.wiremock</groupId>
      <artifactId>wiremock-standalone</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.awaitility</groupId>
      <artifactId>awaitility</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.testcontainers</groupId>
      <artifactId>testcontainers</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor.tools</groupId>
      <artifactId>blockhound</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.wirelesscar.subscriptionrepository</groupId>
      <artifactId>subscriptionrepository-client-test-util</artifactId>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <configuration>
          <formats>
            <format>XML</format>
            <format>HTML</format>
          </formats>
          <destFile>${project.build.directory}/jacoco.exec</destFile>
          <append>true</append>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
              <goal>prepare-agent-integration</goal>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
        <configuration>
          <trimStackTrace>false</trimStackTrace>
          <systemPropertyVariables>
            <CONSOLE_APPENDER>Console</CONSOLE_APPENDER>
            <FILE_LOG_CHARSET>${project.reporting.outputEncoding}</FILE_LOG_CHARSET>
            <CONSOLE_LOG_CHARSET>${project.reporting.outputEncoding}</CONSOLE_LOG_CHARSET>
          </systemPropertyVariables>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-failsafe-plugin</artifactId>
        <configuration>
          <trimStackTrace>false</trimStackTrace>
          <systemPropertyVariables>
            <CONSOLE_APPENDER>Console</CONSOLE_APPENDER>
            <FILE_LOG_CHARSET>${project.reporting.outputEncoding}</FILE_LOG_CHARSET>
            <CONSOLE_LOG_CHARSET>${project.reporting.outputEncoding}</CONSOLE_LOG_CHARSET>
          </systemPropertyVariables>
          <argLine>@{argLine} -XX:+AllowRedefinitionToAddDeleteMethods</argLine>
          <additionalClasspathElements>
            <additionalClasspathElement>${project.basedir}/src/it/resources</additionalClasspathElement>
          </additionalClasspathElements>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
