<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.connectivity.mtrouter</groupId>
    <artifactId>mt-router-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <groupId>com.wirelesscar.ngtp.cd.assemblies</groupId>
  <artifactId>mt-router-server-deployable</artifactId>
  <packaging>pom</packaging>

  <name>MT Router :: Server :: Deployable Assembly</name>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (management) settings -->
  <!-- +=============================================== -->
  <dependencies>
    <!-- Internal dependencies -->
    <dependency>
      <groupId>com.volvo.connectivity.mtrouter</groupId>
      <artifactId>mt-router-server-app</artifactId>
      <version>${project.version}</version>
      <type>jar</type>
    </dependency>

    <!-- Deployers -->
    <dependency>
      <groupId>com.wirelesscar.framework.deploy-engine</groupId>
      <artifactId>deploy-engine-jar-deployer</artifactId>
      <classifier>bundle</classifier>
      <type>zip</type>
    </dependency>

    <!-- Deploy Engine -->
    <dependency>
      <groupId>com.wirelesscar.framework.deploy-engine</groupId>
      <artifactId>deploy-engine</artifactId>
      <type>pom</type>
    </dependency>

    <!-- External dependencies / Infrastructure -->
    <dependency>
      <groupId>net.java.jdk</groupId>
      <artifactId>jdk17</artifactId>
      <type>npm</type>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.supervisord</groupId>
      <artifactId>supervisor3x</artifactId>
      <type>npm</type>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <!-- +=============================================== -->
  <!-- | Section 3: Build settings -->
  <!-- +=============================================== -->
  <build>
    <plugins>
      <plugin>
        <groupId>com.wirelesscar.framework.deploy-engine</groupId>
        <artifactId>deploy-engine-maven-plugin</artifactId>
        <executions>
          <execution>
            <goals>
              <goal>package</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <configuration>
          <rules>
            <requirePluginVersions>
              <unCheckedPluginList>com.wirelesscar.framework.deploy-engine:deploy-engine-maven-plugin</unCheckedPluginList>
            </requirePluginVersions>
          </rules>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
