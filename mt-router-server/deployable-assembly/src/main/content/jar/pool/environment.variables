JDK_VERSION=17
JAVA_OPTS= \
  -DVGTZOOKEEPER=mockhost \
<#if TOTAL_MEM lt 3072>
  -Xms${(TOTAL_MEM*0.45)?round}m \
  -Xmx${(TOTAL_MEM*0.45)?round}m \
<#elseif TOTAL_MEM gt 7168>
  -Xms${(TOTAL_MEM*0.75)?round}m \
  -Xmx${(TOTAL_MEM*0.75)?round}m \
<#else>
  -Xms${(TOTAL_MEM*0.7)?round}m \
  -Xmx${(TOTAL_MEM*0.7)?round}m \
</#if>
  -XX:+ExitOnOutOfMemoryError \
  -XX:+HeapDumpOnOutOfMemoryError \
  -XX:HeapDumpPath=/var/opt/vgt/logs/mt-router-server-deployable/ \
  -XX:+FlightRecorder \
  -XX:FlightRecorderOptions=stackdepth=512 \
  -Djava.net.preferIPv4Stack=true \
