<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
     xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <modelVersion>4.0.0</modelVersion>

  <!-- +=============================================== -->
  <!-- | Section 1: Project information                 -->
  <!-- +=============================================== -->
  <parent>
    <groupId>com.volvo.connectivity.mtrouter</groupId>
    <artifactId>mt-router-server</artifactId>
    <version>0-SNAPSHOT</version>
  </parent>

  <artifactId>mt-router-server-app</artifactId>
  <packaging>jar</packaging>
  <name>mt Router :: Server :: app</name>

  <!-- +=============================================== -->
  <!-- | Section 2: Dependency (Management)             -->
  <!-- +=============================================== -->
  <dependencies>
    <dependency>
      <groupId>com.volvo.connectivity.mtrouter</groupId>
      <artifactId>mt-router-server-impl</artifactId>
      <version>${project.version}</version>
    </dependency>

  </dependencies>

  <!-- +=============================================== -->
  <!-- | Section 3: Build plug-ins                      -->
  <!-- +=============================================== -->
  <build>
    <plugins>
      <plugin>
        <!-- spring-boot plugin enables: mvn spring-boot:run -->
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <mainClass>com.volvo.connectivity.mtrouter.impl.conf.AppConfig</mainClass>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
